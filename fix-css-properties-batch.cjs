const fs = require('fs');
const path = require('path');

// CSS属性映射表
const cssPropertyMap = {
  // 布局相关
  'flex-direction': 'flexDirection',
  'flex-wrap': 'flexWrap',
  'flex-grow': 'flexGrow',
  'flex-shrink': 'flexShrink',
  'flex-basis': 'flexBasis',
  'justify-content': 'justifyContent',
  'align-items': 'alignItems',
  'align-content': 'alignContent',
  'align-self': 'alignSelf',
  
  // 边框相关
  'border-radius': 'borderRadius',
  'border-width': 'borderWidth',
  'border-style': 'borderStyle',
  'border-color': 'borderColor',
  'border-top': 'borderTop',
  'border-right': 'borderRight',
  'border-bottom': 'borderBottom',
  'border-left': 'borderLeft',
  'border-top-left-radius': 'borderTopLeftRadius',
  'border-top-right-radius': 'borderTopRightRadius',
  'border-bottom-left-radius': 'borderBottomLeftRadius',
  'border-bottom-right-radius': 'borderBottomRightRadius',
  
  // 间距相关
  'margin-top': 'marginTop',
  'margin-right': 'marginRight',
  'margin-bottom': 'marginBottom',
  'margin-left': 'marginLeft',
  'padding-top': 'paddingTop',
  'padding-right': 'paddingRight',
  'padding-bottom': 'paddingBottom',
  'padding-left': 'paddingLeft',
  
  // 文本相关
  'font-size': 'fontSize',
  'font-weight': 'fontWeight',
  'font-family': 'fontFamily',
  'font-style': 'fontStyle',
  'line-height': 'lineHeight',
  'text-align': 'textAlign',
  'text-decoration': 'textDecoration',
  'text-transform': 'textTransform',
  'text-overflow': 'textOverflow',
  'white-space': 'whiteSpace',
  'word-wrap': 'wordWrap',
  'word-break': 'wordBreak',
  'letter-spacing': 'letterSpacing',
  
  // 背景相关
  'background-color': 'backgroundColor',
  'background-image': 'backgroundImage',
  'background-position': 'backgroundPosition',
  'background-size': 'backgroundSize',
  'background-repeat': 'backgroundRepeat',
  'background-attachment': 'backgroundAttachment',
  
  // 位置相关
  'z-index': 'zIndex',
  'min-width': 'minWidth',
  'max-width': 'maxWidth',
  'min-height': 'minHeight',
  'max-height': 'maxHeight',
  
  // 其他
  'box-shadow': 'boxShadow',
  'box-sizing': 'boxSizing',
  'overflow-x': 'overflowX',
  'overflow-y': 'overflowY',
  'user-select': 'userSelect',
  'pointer-events': 'pointerEvents',
  'list-style': 'listStyle',
  'list-style-type': 'listStyleType',
  'object-fit': 'objectFit',
  'grid-template-columns': 'gridTemplateColumns',
  'grid-template-rows': 'gridTemplateRows',
  'grid-gap': 'gridGap',
  'gap': 'gap'
};

function fixCssProperties(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    
    // 替换所有的 'property-name': 为 propertyName:
    for (const [kebabCase, camelCase] of Object.entries(cssPropertyMap)) {
      const regex = new RegExp(`'${kebabCase}'\\s*:`, 'g');
      if (content.match(regex)) {
        content = content.replace(regex, `${camelCase}:`);
        hasChanges = true;
      }
      
      // 也处理双引号的情况
      const regex2 = new RegExp(`"${kebabCase}"\\s*:`, 'g');
      if (content.match(regex2)) {
        content = content.replace(regex2, `${camelCase}:`);
        hasChanges = true;
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${path.basename(filePath)}`);
      return true;
    } else {
      console.log(`⏭️  Skipped: ${path.basename(filePath)} (no changes needed)`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dirPath) {
  const files = fs.readdirSync(dirPath);
  let totalFixed = 0;
  
  for (const file of files) {
    const fullPath = path.join(dirPath, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 递归处理子目录
      if (!file.startsWith('.') && file !== 'node_modules' && file !== 'dist') {
        totalFixed += processDirectory(fullPath);
      }
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      // 处理 TypeScript/TSX 文件
      if (fixCssProperties(fullPath)) {
        totalFixed++;
      }
    }
  }
  
  return totalFixed;
}

// 主函数
function main() {
  console.log('🔧 Starting CSS property fix...\n');
  
  const srcPath = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcPath)) {
    console.error('❌ src directory not found!');
    process.exit(1);
  }
  
  const totalFixed = processDirectory(srcPath);
  
  console.log('\n' + '='.repeat(50));
  console.log(`✨ Complete! Fixed ${totalFixed} files.`);
}

main();