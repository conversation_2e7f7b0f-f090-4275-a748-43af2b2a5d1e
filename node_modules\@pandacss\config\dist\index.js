"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all2) => {
  for (var name in all2)
    __defProp(target, name, { get: all2[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  bundleConfig: () => bundleConfig,
  convertTsPathsToRegexes: () => convertTsPathsToRegexes,
  diffConfigs: () => diffConfigs,
  findConfig: () => findConfig,
  getConfigDependencies: () => getConfigDependencies,
  getResolvedConfig: () => getResolvedConfig,
  loadConfig: () => loadConfig,
  mergeConfigs: () => mergeConfigs,
  resolveConfig: () => resolveConfig
});
module.exports = __toCommonJS(src_exports);

// src/bundle-config.ts
var import_logger = require("@pandacss/logger");
var import_shared2 = require("@pandacss/shared");
var import_bundle_n_require = require("bundle-n-require");

// src/find-config.ts
var import_shared = require("@pandacss/shared");
var import_sync = __toESM(require("escalade/sync"));
var import_path = require("path");

// src/is-panda-config.ts
var configName = "panda";
var pandaConfigFiles = /* @__PURE__ */ new Set([
  `${configName}.config.ts`,
  `${configName}.config.js`,
  `${configName}.config.mts`,
  `${configName}.config.mjs`,
  `${configName}.config.cts`,
  `${configName}.config.cjs`
]);
var isPandaConfig = (file) => pandaConfigFiles.has(file);

// src/find-config.ts
function findConfig(options) {
  const { cwd = process.cwd(), file } = options;
  if (file) {
    return (0, import_path.resolve)(cwd, file);
  }
  const configPath = (0, import_sync.default)(cwd, (_dir, paths) => paths.find(isPandaConfig));
  if (!configPath) {
    throw new import_shared.PandaError(
      "CONFIG_NOT_FOUND",
      `Cannot find config file \`panda.config.{ts,js,mjs,mts}\`. Did you forget to run \`panda init\`?`
    );
  }
  return configPath;
}

// src/bundle-config.ts
async function bundle(filepath, cwd) {
  const { mod, dependencies } = await (0, import_bundle_n_require.bundleNRequire)(filepath, {
    cwd,
    interopDefault: true
  });
  const config = mod?.default ?? mod;
  return {
    config,
    dependencies
  };
}
async function bundleConfig(options) {
  const { cwd, file } = options;
  const filePath = findConfig({ cwd, file });
  import_logger.logger.debug("config:path", filePath);
  const result = await bundle(filePath, cwd);
  if (typeof result.config !== "object") {
    throw new import_shared2.PandaError("CONFIG_ERROR", `\u{1F4A5} Config must export or return an object.`);
  }
  result.config.outdir ??= "styled-system";
  result.config.validation ??= "warn";
  return {
    ...result,
    config: result.config,
    path: filePath
  };
}

// src/diff-config.ts
var import_shared3 = require("@pandacss/shared");
var import_microdiff = __toESM(require("microdiff"));

// src/create-matcher.ts
function createMatcher(id, patterns) {
  if (!patterns?.length)
    return () => void 0;
  const includePatterns = [];
  const excludePatterns = [];
  const deduped = new Set(patterns);
  deduped.forEach((pattern) => {
    const regexString = pattern.replace(/\*/g, ".*");
    if (pattern.startsWith("!")) {
      excludePatterns.push(regexString.slice(1));
    } else {
      includePatterns.push(regexString);
    }
  });
  const include = new RegExp(includePatterns.join("|"));
  const exclude = new RegExp(excludePatterns.join("|"));
  return (path2) => {
    if (excludePatterns.length && exclude.test(path2))
      return;
    return include.test(path2) ? id : void 0;
  };
}

// src/config-deps.ts
var all = [
  "clean",
  "cwd",
  "eject",
  "outdir",
  "forceConsistentTypeExtension",
  "outExtension",
  "emitPackage",
  "emitTokensOnly",
  "presets",
  "plugins",
  "hooks"
];
var format = [
  "syntax",
  "hash",
  "prefix",
  "separator",
  "strictTokens",
  "strictPropertyValues",
  "shorthands"
];
var tokens = [
  "utilities",
  "conditions",
  "theme.tokens",
  "theme.semanticTokens",
  "theme.breakpoints",
  "theme.containerNames",
  "theme.containerSizes"
];
var jsx = ["jsxFramework", "jsxFactory", "jsxStyleProps", "syntax"];
var common = tokens.concat(jsx, format);
var artifactConfigDeps = {
  helpers: ["syntax", "jsxFramework"],
  keyframes: ["theme.keyframes", "layers"],
  "design-tokens": ["layers", "!utilities.*.className"].concat(tokens),
  types: ["!utilities.*.className"].concat(common),
  "css-fn": common,
  cva: ["syntax"],
  sva: ["syntax"],
  cx: [],
  "create-recipe": ["separator", "prefix", "hash"],
  "recipes-index": ["theme.recipes", "theme.slotRecipes"],
  recipes: ["theme.recipes", "theme.slotRecipes"],
  "patterns-index": ["syntax", "patterns"],
  patterns: ["syntax", "patterns"],
  "jsx-is-valid-prop": common,
  "jsx-factory": jsx,
  "jsx-helpers": jsx,
  "jsx-patterns": jsx.concat("patterns"),
  "jsx-patterns-index": jsx.concat("patterns"),
  "css-index": ["syntax"],
  "package.json": ["emitPackage", "forceConsistentTypeExtension", "outExtension"],
  "types-styles": ["shorthands"],
  "types-conditions": ["conditions"],
  "types-jsx": jsx,
  "types-entry": [],
  "types-gen": [],
  "types-gen-system": [],
  themes: ["themes"].concat(tokens)
};
var artifactMatchers = Object.entries(artifactConfigDeps).map(([key, paths]) => {
  if (!paths.length)
    return () => void 0;
  return createMatcher(key, paths.concat(all));
});

// src/diff-config.ts
var runIfFn = (fn) => typeof fn === "function" ? fn() : fn;
function diffConfigs(config, prevConfig) {
  const affected = {
    artifacts: /* @__PURE__ */ new Set(),
    hasConfigChanged: false,
    diffs: []
  };
  if (!prevConfig) {
    affected.hasConfigChanged = true;
    return affected;
  }
  const configDiff = (0, import_microdiff.default)(prevConfig, runIfFn(config));
  if (!configDiff.length) {
    return affected;
  }
  affected.hasConfigChanged = true;
  affected.diffs = configDiff;
  configDiff.forEach((change) => {
    const changePath = change.path.join(".");
    artifactMatchers.forEach((matcher) => {
      const id = matcher(changePath);
      if (!id)
        return;
      if (id === "recipes") {
        const name = (0, import_shared3.dashCase)(change.path.slice(1, 3).join("."));
        affected.artifacts.add(name);
      }
      if (id === "patterns") {
        const name = (0, import_shared3.dashCase)(change.path.slice(0, 2).join("."));
        affected.artifacts.add(name);
      }
      affected.artifacts.add(id);
    });
  });
  return affected;
}

// src/get-mod-deps.ts
var import_fs = __toESM(require("fs"));
var import_path4 = __toESM(require("path"));

// src/ts-config-paths.ts
var import_path2 = require("path");
function convertTsPathsToRegexes(paths, baseUrl) {
  const sortedPatterns = Object.keys(paths).sort((a, b) => getPrefixLength(b) - getPrefixLength(a));
  const resolved = [];
  for (let pattern of sortedPatterns) {
    const relativePaths = paths[pattern];
    pattern = escapeStringRegexp(pattern).replace(/\*/g, "(.+)");
    resolved.push({
      pattern: new RegExp("^" + pattern + "$"),
      paths: relativePaths.map((relativePath) => (0, import_path2.resolve)(baseUrl, relativePath))
    });
  }
  return resolved;
}
function getPrefixLength(pattern) {
  const prefixLength = pattern.indexOf("*");
  return pattern.substr(0, prefixLength).length;
}
function escapeStringRegexp(string) {
  return string.replace(/[|\\{}()[\]^$+?.]/g, "\\$&").replace(/-/g, "\\x2d");
}

// src/resolve-ts-path-pattern.ts
var import_path3 = require("path");
var resolveTsPathPattern = (pathMappings, moduleSpecifier) => {
  for (const mapping of pathMappings) {
    const match = moduleSpecifier.match(mapping.pattern);
    if (!match) {
      continue;
    }
    for (const pathTemplate of mapping.paths) {
      let starCount = 0;
      const mappedId = pathTemplate.replace(/\*/g, () => {
        const matchIndex = Math.min(++starCount, match.length - 1);
        return match[matchIndex];
      });
      return mappedId.split(import_path3.sep).join(import_path3.posix.sep);
    }
  }
};

// src/get-mod-deps.ts
var import_typescript = __toESM(require("typescript"));
var jsExtensions = [".js", ".cjs", ".mjs"];
var jsResolutionOrder = ["", ".js", ".cjs", ".mjs", ".ts", ".cts", ".mts", ".jsx", ".tsx"];
var tsResolutionOrder = ["", ".ts", ".cts", ".mts", ".tsx", ".js", ".cjs", ".mjs", ".jsx"];
function resolveWithExtension(file, extensions) {
  for (const ext of extensions) {
    const full = `${file}${ext}`;
    if (import_fs.default.existsSync(full) && import_fs.default.statSync(full).isFile()) {
      return full;
    }
  }
  for (const ext of extensions) {
    const full = `${file}/index${ext}`;
    if (import_fs.default.existsSync(full)) {
      return full;
    }
  }
  return null;
}
var importRegex = /import[\s\S]*?['"](.{3,}?)['"]/gi;
var importFromRegex = /import[\s\S]*from[\s\S]*?['"](.{3,}?)['"]/gi;
var requireRegex = /require\(['"`](.+)['"`]\)/gi;
var exportRegex = /export[\s\S]*from[\s\S]*?['"](.{3,}?)['"]/gi;
function getDeps(opts, fromAlias) {
  const { filename, seen } = opts;
  const { moduleResolution: _, ...compilerOptions } = opts.compilerOptions ?? {};
  const absoluteFile = resolveWithExtension(
    import_path4.default.resolve(opts.cwd, filename),
    jsExtensions.includes(opts.ext) ? jsResolutionOrder : tsResolutionOrder
  );
  if (absoluteFile === null)
    return;
  if (fromAlias) {
    opts.foundModuleAliases.set(fromAlias, absoluteFile);
  }
  if (seen.size > 1 && seen.has(absoluteFile))
    return;
  seen.add(absoluteFile);
  const contents = import_fs.default.readFileSync(absoluteFile, "utf-8");
  const fileDeps = [
    ...contents.matchAll(importRegex),
    ...contents.matchAll(importFromRegex),
    ...contents.matchAll(requireRegex),
    ...contents.matchAll(exportRegex)
  ];
  if (!fileDeps.length)
    return;
  const nextOpts = {
    // Resolve new base for new imports/requires
    cwd: import_path4.default.dirname(absoluteFile),
    ext: import_path4.default.extname(absoluteFile),
    seen,
    baseUrl: opts.baseUrl,
    pathMappings: opts.pathMappings,
    foundModuleAliases: opts.foundModuleAliases
  };
  fileDeps.forEach((match) => {
    const mod = match[1];
    if (mod[0] === ".") {
      getDeps(Object.assign({}, nextOpts, { filename: mod }));
      return;
    }
    try {
      const found = import_typescript.default.resolveModuleName(mod, absoluteFile, compilerOptions, import_typescript.default.sys).resolvedModule;
      if (found && found.extension.endsWith("ts")) {
        getDeps(Object.assign({}, nextOpts, { filename: found.resolvedFileName }));
        return;
      }
      if (!opts.pathMappings)
        return;
      const filename2 = resolveTsPathPattern(opts.pathMappings, mod);
      if (!filename2)
        return;
      getDeps(Object.assign({}, nextOpts, { filename: filename2 }), mod);
    } catch (err) {
    }
  });
}
function getConfigDependencies(filePath, tsOptions = { pathMappings: [] }, compilerOptions) {
  if (filePath === null)
    return { deps: /* @__PURE__ */ new Set(), aliases: /* @__PURE__ */ new Map() };
  const foundModuleAliases = /* @__PURE__ */ new Map();
  const deps = /* @__PURE__ */ new Set();
  deps.add(filePath);
  getDeps({
    filename: filePath,
    ext: import_path4.default.extname(filePath),
    cwd: import_path4.default.dirname(filePath),
    seen: deps,
    baseUrl: tsOptions.baseUrl,
    pathMappings: tsOptions.pathMappings ?? [],
    foundModuleAliases,
    compilerOptions
  });
  return { deps, aliases: foundModuleAliases };
}

// src/merge-config.ts
var import_shared5 = require("@pandacss/shared");
var import_merge_anything = require("merge-anything");

// src/merge-hooks.ts
var import_logger2 = require("@pandacss/logger");
var mergeHooks = (plugins) => {
  const hooksFns = {};
  plugins.forEach(({ name, hooks }) => {
    Object.entries(hooks ?? {}).forEach(([key, value]) => {
      if (!hooksFns[key]) {
        hooksFns[key] = [];
      }
      hooksFns[key].push([name, value]);
    });
  });
  const mergedHooks = Object.fromEntries(
    Object.entries(hooksFns).map(([key, entries]) => {
      const fns = entries.map(([name, fn]) => tryCatch(name, fn));
      const reducer = key in reducers ? reducers[key] : void 0;
      if (reducer) {
        return [key, reducer(fns)];
      }
      return [key, syncHooks.includes(key) ? callAll(...fns) : callAllAsync(...fns)];
    })
  );
  return mergedHooks;
};
var createReducer = (reducer) => reducer;
var reducers = {
  "config:resolved": createReducer((fns) => async (_args) => {
    const args = Object.assign({}, _args);
    const original = _args.config;
    let config = args.config;
    for (const hookFn of fns) {
      const result = await hookFn(Object.assign(args, { config, original }));
      if (result !== void 0) {
        config = result;
      }
    }
    return config;
  }),
  "parser:before": createReducer((fns) => (_args) => {
    const args = Object.assign({}, _args);
    const original = _args.content;
    let content = args.content;
    for (const hookFn of fns) {
      const result = hookFn(Object.assign(args, { content, original }));
      if (result !== void 0) {
        content = result;
      }
    }
    return content;
  }),
  "parser:preprocess": createReducer((fns) => (_args) => {
    const args = Object.assign({}, _args);
    const original = _args.data;
    let data = args.data;
    for (const hookFn of fns) {
      const result = hookFn(Object.assign(args, { data, original }));
      if (result !== void 0) {
        data = result;
      }
    }
    return data;
  }),
  "cssgen:done": createReducer((fns) => (_args) => {
    const args = Object.assign({}, _args);
    const original = _args.content;
    let content = args.content;
    for (const hookFn of fns) {
      const result = hookFn(Object.assign(args, { content, original }));
      if (result !== void 0) {
        content = result;
      }
    }
    return content;
  }),
  "codegen:prepare": createReducer((fns) => async (_args) => {
    const args = Object.assign({}, _args);
    const original = _args.artifacts;
    let artifacts = args.artifacts;
    for (const hookFn of fns) {
      const result = await hookFn(Object.assign(args, { artifacts, original }));
      if (result) {
        artifacts = result;
      }
    }
    return artifacts;
  })
};
var syncHooks = [
  "context:created",
  "parser:before",
  "parser:preprocess",
  "parser:after",
  "cssgen:done"
];
var callAllAsync = (...fns) => async (...a) => {
  for (const fn of fns) {
    await fn?.(...a);
  }
};
var callAll = (...fns) => (...a) => {
  fns.forEach((fn) => fn?.(...a));
};
var tryCatch = (name, fn) => {
  return (...args) => {
    try {
      return fn(...args);
    } catch (e) {
      import_logger2.logger.error("hooks", `The error below comes from the plugin ${name}`);
      console.error(e);
    }
  };
};

// src/validation/utils.ts
var import_shared4 = require("@pandacss/shared");
var REFERENCE_REGEX = /({([^}]*)})/g;
var curlyBracketRegex = /[{}]/g;
var isValidToken = (token) => (0, import_shared4.isObject)(token) && Object.hasOwnProperty.call(token, "value");
var isTokenReference = (value) => typeof value === "string" && REFERENCE_REGEX.test(value);
var formatPath = (path2) => path2;
var SEP = ".";
function getReferences(value) {
  if (typeof value !== "string")
    return [];
  const matches = value.match(REFERENCE_REGEX);
  if (!matches)
    return [];
  return matches.map((match) => match.replace(curlyBracketRegex, "")).map((value2) => {
    return value2.trim().split("/")[0];
  });
}
var serializeTokenValue = (value) => {
  if ((0, import_shared4.isString)(value)) {
    return value;
  }
  if ((0, import_shared4.isObject)(value)) {
    return Object.values(value).map((v) => serializeTokenValue(v)).join(" ");
  }
  if (Array.isArray(value)) {
    return value.map((v) => serializeTokenValue(v)).join(" ");
  }
  return value.toString();
};

// src/merge-config.ts
function getExtends(items) {
  return items.reduce((merged, { extend }) => {
    if (!extend)
      return merged;
    return (0, import_shared5.mergeWith)(merged, extend, (originalValue, newValue) => {
      if (newValue === void 0) {
        return originalValue ?? [];
      }
      if (originalValue === void 0) {
        return [newValue];
      }
      if (Array.isArray(originalValue)) {
        return [newValue, ...originalValue];
      }
      return [newValue, originalValue];
    });
  }, {});
}
function mergeRecords(records) {
  return {
    ...records.reduce((acc, record) => (0, import_shared5.assign)(acc, record), {}),
    extend: getExtends(records)
  };
}
function mergeExtensions(records) {
  const { extend = [], ...restProps } = mergeRecords(records);
  return (0, import_shared5.mergeWith)(restProps, extend, (obj, extensions) => {
    return (0, import_merge_anything.mergeAndConcat)({}, obj, ...extensions);
  });
}
var isEmptyObject = (obj) => typeof obj === "object" && Object.keys(obj).length === 0;
var compact = (obj) => {
  return Object.keys(obj).reduce((acc, key) => {
    if (obj[key] !== void 0 && !isEmptyObject(obj[key])) {
      acc[key] = obj[key];
    }
    return acc;
  }, {});
};
var tokenKeys = ["description", "extensions", "type", "value", "deprecated"];
function mergeConfigs(configs) {
  const [userConfig] = configs;
  const pluginHooks = userConfig.plugins ?? [];
  if (userConfig.hooks) {
    pluginHooks.push({ name: "__panda.config__", hooks: userConfig.hooks });
  }
  const mergedResult = (0, import_shared5.assign)(
    {
      conditions: mergeExtensions(configs.map((config) => config.conditions ?? {})),
      theme: mergeExtensions(configs.map((config) => config.theme ?? {})),
      patterns: mergeExtensions(configs.map((config) => config.patterns ?? {})),
      utilities: mergeExtensions(configs.map((config) => config.utilities ?? {})),
      globalCss: mergeExtensions(configs.map((config) => config.globalCss ?? {})),
      globalVars: mergeExtensions(configs.map((config) => config.globalVars ?? {})),
      staticCss: mergeExtensions(configs.map((config) => config.staticCss ?? {})),
      themes: mergeExtensions(configs.map((config) => config.themes ?? {})),
      hooks: mergeHooks(pluginHooks)
    },
    ...configs
  );
  const withoutEmpty = compact(mergedResult);
  if (withoutEmpty.theme?.tokens) {
    (0, import_shared5.walkObject)(withoutEmpty.theme.tokens, (args) => args, {
      stop(token) {
        if (!isValidToken(token))
          return false;
        const keys = Object.keys(token);
        const nestedKeys = keys.filter((k) => !tokenKeys.includes(k));
        const nested = nestedKeys.length > 0;
        if (nested) {
          token.DEFAULT ||= {};
          tokenKeys.forEach((key) => {
            if (token[key] == null)
              return;
            token.DEFAULT[key] ||= token[key];
            delete token[key];
          });
        }
        return true;
      }
    });
  }
  return withoutEmpty;
}

// src/get-resolved-config.ts
async function getResolvedConfig(config, cwd) {
  const presets = config.presets ?? [];
  const configs = [];
  while (presets.length > 0) {
    const preset = await presets.shift();
    if (typeof preset === "string") {
      const presetModule = await bundle(preset, cwd);
      configs.unshift(await presetModule.config);
      presets.unshift(...await presetModule.config.presets ?? []);
    } else {
      configs.unshift(preset);
      presets.unshift(...preset.presets ?? []);
    }
  }
  configs.unshift(config);
  return mergeConfigs(configs);
}

// src/resolve-config.ts
var import_logger4 = require("@pandacss/logger");
var import_shared10 = require("@pandacss/shared");

// src/bundled-preset.ts
var import_preset_base = require("@pandacss/preset-base");
var import_preset_panda = require("@pandacss/preset-panda");
var bundledPresets = {
  "@pandacss/preset-base": import_preset_base.preset,
  "@pandacss/preset-panda": import_preset_panda.preset,
  "@pandacss/dev/presets": import_preset_panda.preset
};
var bundledPresetsNames = Object.keys(bundledPresets);
var isBundledPreset = (preset) => bundledPresetsNames.includes(preset);
var getBundledPreset = (preset) => {
  return typeof preset === "string" && isBundledPreset(preset) ? bundledPresets[preset] : void 0;
};

// src/validate-config.ts
var import_logger3 = require("@pandacss/logger");
var import_shared9 = require("@pandacss/shared");

// src/validation/validate-artifact.ts
var validateArtifactNames = (names, addError) => {
  names.recipes.forEach((recipeName) => {
    if (names.slotRecipes.has(recipeName)) {
      addError("recipes", `This recipe name is already used in \`theme.slotRecipes\`: ${recipeName}`);
    }
    if (names.patterns.has(recipeName)) {
      addError("recipes", `This recipe name is already used in \`patterns\`: \`${recipeName}\``);
    }
  });
  names.slotRecipes.forEach((recipeName) => {
    if (names.patterns.has(recipeName)) {
      addError("recipes", `This recipe name is already used in \`patterns\`: ${recipeName}`);
    }
  });
};

// src/validation/validate-breakpoints.ts
var import_shared6 = require("@pandacss/shared");
var validateBreakpoints = (breakpoints, addError) => {
  if (!breakpoints)
    return;
  const units = /* @__PURE__ */ new Set();
  const values = Object.values(breakpoints);
  for (const value of values) {
    const unit = (0, import_shared6.getUnit)(value) ?? "px";
    units.add(unit);
  }
  if (units.size > 1) {
    addError("breakpoints", `All breakpoints must use the same unit: \`${values.join(", ")}\``);
  }
};

// src/validation/validate-condition.ts
var import_shared7 = require("@pandacss/shared");
var validateConditions = (conditions, addError) => {
  if (!conditions)
    return;
  Object.values(conditions).forEach((condition) => {
    if ((0, import_shared7.isString)(condition)) {
      if (!condition.startsWith("@") && !condition.includes("&")) {
        addError("conditions", `Selectors should contain the \`&\` character: \`${condition}\``);
      }
      return;
    }
    condition.forEach((c) => {
      if (!c.startsWith("@") && !c.includes("&")) {
        addError("conditions", `Selectors should contain the \`&\` character: \`${c}\``);
      }
    });
  });
};

// src/validation/validate-patterns.ts
var validatePatterns = (patterns, names) => {
  if (!patterns)
    return;
  Object.keys(patterns).forEach((patternName) => {
    names.patterns.add(patternName);
  });
};

// src/validation/validate-recipes.ts
var validateRecipes = (options) => {
  const {
    config: { theme },
    artifacts
  } = options;
  if (!theme)
    return;
  if (theme.recipes) {
    Object.keys(theme.recipes).forEach((recipeName) => {
      artifacts.recipes.add(recipeName);
    });
  }
  if (theme.slotRecipes) {
    Object.keys(theme.slotRecipes).forEach((recipeName) => {
      artifacts.slotRecipes.add(recipeName);
    });
  }
  return artifacts;
};

// src/validation/validate-tokens.ts
var import_shared8 = require("@pandacss/shared");

// src/validation/validate-token-references.ts
var validateTokenReferences = (props) => {
  const { valueAtPath, refsByPath, addError, typeByPath } = props;
  refsByPath.forEach((refs, path2) => {
    if (refs.has(path2)) {
      addError("tokens", `Self token reference: \`${path2}\``);
    }
    const stack = [path2];
    while (stack.length > 0) {
      let currentPath = stack.pop();
      if (currentPath.includes("/")) {
        const [tokenPath] = currentPath.split("/");
        currentPath = tokenPath;
      }
      const value = valueAtPath.get(currentPath);
      if (!value) {
        const configKey = typeByPath.get(path2);
        addError("tokens", `Missing token: \`${currentPath}\` used in \`theme.${configKey}.${path2}\``);
      }
      if (isTokenReference(value) && !refsByPath.has(value)) {
        addError("tokens", `Unknown token reference: \`${currentPath}\` used in \`${value}\``);
      }
      const deps = refsByPath.get(currentPath);
      if (!deps)
        continue;
      for (const transitiveDep of deps) {
        if (path2 === transitiveDep) {
          addError(
            "tokens",
            `Circular token reference: \`${transitiveDep}\` -> \`${currentPath}\` -> ... -> \`${path2}\``
          );
          break;
        }
        stack.push(transitiveDep);
      }
    }
  });
};

// src/validation/validate-tokens.ts
var validateTokens = (options) => {
  const {
    config: { theme },
    tokens: tokens2,
    addError
  } = options;
  if (!theme)
    return;
  const { tokenNames, semanticTokenNames, valueAtPath, refsByPath, typeByPath } = tokens2;
  if (theme.tokens) {
    const tokenPaths = /* @__PURE__ */ new Set();
    (0, import_shared8.walkObject)(
      theme.tokens,
      (value, paths) => {
        const path2 = paths.join(SEP);
        tokenNames.add(path2);
        tokenPaths.add(path2);
        valueAtPath.set(path2, value);
        if (path2.includes("DEFAULT")) {
          valueAtPath.set(path2.replace(SEP + "DEFAULT", ""), value);
        }
      },
      {
        stop: isValidToken
      }
    );
    tokenPaths.forEach((path2) => {
      const itemValue = valueAtPath.get(path2);
      const formattedPath = formatPath(path2);
      typeByPath.set(formattedPath, "tokens");
      if (!isValidToken(itemValue)) {
        addError("tokens", `Token must contain 'value': \`theme.tokens.${formattedPath}\``);
        return;
      }
      if (path2.includes(" ")) {
        addError("tokens", `Token key must not contain spaces: \`theme.tokens.${formattedPath}\``);
        return;
      }
      const valueStr = serializeTokenValue(itemValue.value || itemValue);
      if (isTokenReference(valueStr)) {
        refsByPath.set(formattedPath, /* @__PURE__ */ new Set([]));
      }
      const references = refsByPath.get(formattedPath);
      if (!references)
        return;
      getReferences(valueStr).forEach((reference) => {
        references.add(reference);
      });
    });
  }
  if (theme.semanticTokens) {
    const tokenPaths = /* @__PURE__ */ new Set();
    (0, import_shared8.walkObject)(
      theme.semanticTokens,
      (value, paths) => {
        const path2 = paths.join(SEP);
        semanticTokenNames.add(path2);
        valueAtPath.set(path2, value);
        tokenPaths.add(path2);
        if (path2.includes("DEFAULT")) {
          valueAtPath.set(path2.replace(SEP + "DEFAULT", ""), value);
        }
        if (!isValidToken(value))
          return;
        (0, import_shared8.walkObject)(value, (itemValue, paths2) => {
          const valuePath = paths2.join(SEP);
          const formattedPath = formatPath(path2);
          typeByPath.set(formattedPath, "semanticTokens");
          const fullPath = formattedPath + "." + paths2.join(SEP);
          if (valuePath.includes("value" + SEP + "value")) {
            addError("tokens", `You used \`value\` twice resulting in an invalid token \`theme.tokens.${fullPath}\``);
          }
          const valueStr = serializeTokenValue(itemValue.value || itemValue);
          if (isTokenReference(valueStr)) {
            if (!refsByPath.has(formattedPath)) {
              refsByPath.set(formattedPath, /* @__PURE__ */ new Set());
            }
            const references = refsByPath.get(formattedPath);
            if (!references)
              return;
            getReferences(valueStr).forEach((reference) => {
              references.add(reference);
            });
          }
        });
      },
      {
        stop: isValidToken
      }
    );
    tokenPaths.forEach((path2) => {
      const formattedPath = formatPath(path2);
      const value = valueAtPath.get(path2);
      if (path2.includes(" ")) {
        addError("tokens", `Token key must not contain spaces: \`theme.tokens.${formattedPath}\``);
        return;
      }
      if (!(0, import_shared8.isObject)(value) && !path2.includes("value")) {
        addError("tokens", `Token must contain 'value': \`theme.semanticTokens.${formattedPath}\``);
      }
    });
  }
  validateTokenReferences({ valueAtPath, refsByPath, addError, typeByPath });
};

// src/validate-config.ts
var validateConfig = (config) => {
  if (config.validation === "none")
    return;
  const warnings = /* @__PURE__ */ new Set();
  const addError = (scope, message) => {
    warnings.add(`[${scope}] ` + message);
  };
  validateBreakpoints(config.theme?.breakpoints, addError);
  validateConditions(config.conditions, addError);
  const artifacts = {
    recipes: /* @__PURE__ */ new Set(),
    slotRecipes: /* @__PURE__ */ new Set(),
    patterns: /* @__PURE__ */ new Set()
  };
  const tokens2 = {
    tokenNames: /* @__PURE__ */ new Set(),
    semanticTokenNames: /* @__PURE__ */ new Set(),
    valueAtPath: /* @__PURE__ */ new Map(),
    refsByPath: /* @__PURE__ */ new Map(),
    typeByPath: /* @__PURE__ */ new Map()
  };
  if (config.theme) {
    validateTokens({ config, tokens: tokens2, addError });
    validateRecipes({ config, tokens: tokens2, artifacts, addError });
  }
  validatePatterns(config.patterns, artifacts);
  validateArtifactNames(artifacts, addError);
  if (warnings.size) {
    const errors = `\u26A0\uFE0F Invalid config:
${Array.from(warnings).map((err) => "- " + err).join("\n")}
`;
    if (config.validation === "error") {
      throw new import_shared9.PandaError("CONFIG_ERROR", errors);
    }
    import_logger3.logger.warn("config", errors);
    return warnings;
  }
};

// src/resolve-config.ts
var hookUtils = {
  omit: import_shared10.omit,
  traverse: import_shared10.traverse
};
async function resolveConfig(result, cwd) {
  const presets = /* @__PURE__ */ new Set();
  if (!result.config.eject) {
    presets.add(import_preset_base.preset);
  }
  if (result.config.presets) {
    result.config.presets.forEach((preset) => {
      presets.add(getBundledPreset(preset) ?? preset);
    });
  } else if (!result.config.eject) {
    presets.add(import_preset_panda.preset);
  }
  result.config.presets = Array.from(presets);
  const mergedConfig = await getResolvedConfig(result.config, cwd);
  const hooks = mergedConfig.hooks ?? {};
  if (mergedConfig.logLevel) {
    import_logger4.logger.level = mergedConfig.logLevel;
  }
  validateConfig(mergedConfig);
  const loadConfigResult = {
    ...result,
    config: mergedConfig
  };
  if (hooks["config:resolved"]) {
    const result2 = await hooks["config:resolved"]({
      config: loadConfigResult.config,
      path: loadConfigResult.path,
      dependencies: loadConfigResult.dependencies,
      utils: hookUtils
    });
    if (result2) {
      loadConfigResult.config = result2;
    }
  }
  const serialized = (0, import_shared10.stringifyJson)(loadConfigResult.config);
  const deserialize = () => (0, import_shared10.parseJson)(serialized);
  return { ...loadConfigResult, serialized, deserialize, hooks };
}

// src/load-config.ts
async function loadConfig(options) {
  const result = await bundleConfig(options);
  return resolveConfig(result, options.cwd);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  bundleConfig,
  convertTsPathsToRegexes,
  diffConfigs,
  findConfig,
  getConfigDependencies,
  getResolvedConfig,
  loadConfig,
  mergeConfigs,
  resolveConfig
});
