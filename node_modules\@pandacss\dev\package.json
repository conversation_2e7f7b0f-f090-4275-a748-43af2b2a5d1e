{"name": "@pandacss/dev", "version": "0.39.2", "description": "The user facing package for panda css", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "bin": {"panda": "bin.js", "pandacss": "bin.js"}, "exports": {".": {"source": "./src/index.ts", "types": "./dist/index.d.ts", "require": "./dist/index.js", "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "./presets": {"source": "./src/presets.ts", "types": "./dist/presets.d.ts", "require": "./dist/presets.js", "import": {"types": "./dist/presets.d.mts", "default": "./dist/presets.mjs"}}, "./postcss": "./postcss.js", "./package.json": "./package.json"}, "sideEffects": false, "homepage": "https://panda-css.com", "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/panda.git", "directory": "packages/cli"}, "publishConfig": {"access": "public"}, "files": ["dist", "bin", "app", "bin.js", "*.d.ts", "postcss.js"], "dependencies": {"@clack/prompts": "0.7.0", "cac": "6.7.14", "@pandacss/config": "0.39.2", "@pandacss/logger": "0.39.2", "@pandacss/node": "0.39.2", "@pandacss/postcss": "0.39.2", "@pandacss/preset-panda": "0.39.2", "@pandacss/shared": "0.39.2", "@pandacss/token-dictionary": "0.39.2", "@pandacss/types": "0.39.2"}, "devDependencies": {"@types/update-notifier": "6.0.8", "kleur": "4.1.5", "update-notifier": "7.0.0"}, "scripts": {"build": "tsup src --format=esm,cjs --dts --no-splitting --shims", "build-fast": "tsup src --format=esm,cjs --no-dts --no-splitting --shims", "dev": "pnpm build-fast --watch src"}}