import { createSignal, For, Show } from 'solid-js'
import { css } from'../../styled-system/css'

interface PortfolioItem {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
}

interface PortfolioOverviewProps {
  title?: string
  showHeader?: boolean
}

export default function PortfolioOverview(props: PortfolioOverviewProps) {
 '// 模拟投资组合数据
  const [portfolioData] = createSignal<PortfolioItem[]>([
    {
      symbol: '沪深300',
     'name': '沪深300指数',
      price: 1.2456,
      change: 2.34,
      changePercent: 24.56
    },
    {
     'symbol': '中证500',
     'name': '中证500指数',
      price: 0.9876,
      change: -1.23,
      changePercent: -12.34
    },
    {
     'symbol': '创业板指',
     'name': '创业板指数',
      price: 1.5432,
      change: 5.67,
      changePercent: 45.67
    }
  ])

  const getChangeColor = (change: number) => {
    return change >= 0 ? '#52c41a' : '#ff4d4f'
  }

  const getChangeIcon = (change: number) => {
    return change >= 0 ? '↗' : '↘'
  }

  return (
    <div class={css({
     'bg': 'white',
     'border': '1px solid #e8e8e8',
     'borderRadius': '8px',
      overflow:'hidden'
    })}>
      {/* 标题栏 */}
      <Show when={props.showHeader !== false}>
        <div class={css({
         'display': 'flex',
         'alignItems': 'center',
         'justifyContent': 'space-between',
         'p': '16px',
         'borderBottom': '1px solid #e8e8e8',
          bg:'#fafafa'
        })}>
          <div class={css({
           'display': 'flex',
           'alignItems': 'center',
            gap:'8px'
          })}>
            <span class={css({ fontSize:'16px' })}>📊</span>
            <h3 class={css({
             'fontSize': '16px',
             'fontWeight': '600',
             'color': '#333',
              margin : 0
            })}>
              {props.title ||'投资组合概览'}
            </h3>
          </div>
          <button class={css({
           'fontSize': '12px',
           'color': '#1890ff',
           'bg': 'transparent',
           'border': 'none',
           'cursor': 'pointer',
            _hover: { textDecoration:'underline' }
          })}>
            查看详情 →
          </button>
        </div>
      </Show>

      {/* 表格头部 */}
      <div class={css({
       'display': 'grid',
       'gridTemplateColumns': '2fr 1fr 1fr 1fr',
       'gap': '12px',
       'p': '12px 16px',
       'bg': '#f8f9fa',
       'borderBottom': '1px solid #e8e8e8',
       'fontSize': '12px',
       'fontWeight': '500',
        color:'#666'
      })}>
        <div>品种名称</div>
        <div class={css({ textAlign:'right' })}>最新价</div>
        <div class={css({ textAlign:'right' })}>涨跌额</div>
        <div class={css({ textAlign:'right' })}>涨跌幅</div>
      </div>

      {/* 数据列表 */}
      <div class={css({ maxHeight: '300px', overflowY:'auto' })}>
        <For each={portfolioData()}>
          {(item) => (
            <div class={css({
             'display': 'grid',
             'gridTemplateColumns': '2fr 1fr 1fr 1fr',
             'gap': '12px',
             'p': '12px 16px',
             'borderBottom': '1px solid #f0f0f0',
              _hover: { bg:'#f8f9fa' },
              cursor:'pointer'
            })}>
              {/* 品种信息 */}
              <div class={css({
               'display': 'flex',
               'flexDirection': 'column',
                gap:'2px'
              })}>
                <span class={css({
                 'fontSize': '14px',
                 'fontWeight': '500',
                  color:'#333'
                })}>
                  {item.symbol}
                </span>
                <span class={css({
                 'fontSize': '12px',
                  color:'#999'
                })}>
                  {item.name}
                </span>
              </div>

              {/* 最新价 */}
              <div class={css({
               'textAlign': 'right',
               'fontSize': '14px',
               'fontWeight': '500',
                color:'#333'
              })}>
                {item.price.toFixed(4)}
              </div>

              {/* 涨跌额 */}
              <div class={css({
               'textAlign': 'right',
               'fontSize': '14px',
               'fontWeight': '500',
                color: getChangeColor(item.change),
               'display': 'flex',
               'alignItems': 'center',
               'justifyContent': 'flex-end',
                gap:'4px'
              })}>
                <span>{getChangeIcon(item.change)}</span>
                <span>
                  {item.change >= 0 ? '+' : ''}{item.change.toFixed(2)}
                </span>
              </div>

              {/* 涨跌幅 */}
              <div class={css({
               'textAlign': 'right',
               'fontSize': '14px',
               'fontWeight': '500',
                color : getChangeColor(item.changePercent)
              })}>
                {item.changePercent >= 0 ? '+' : ''}{item.changePercent.toFixed(2)}%
              </div>
            </div>
          )}
        </For>
      </div>

      {/* 底部操作 */}
      <div class={css({
       'p': '12px 16px',
       'borderTop': '1px solid #e8e8e8',
       'bg': '#fafafa',
        textAlign:'center'
      })}>
        <button class={css({
         'fontSize': '12px',
         'color': '#666',
         'bg': 'transparent',
         'border': 'none',
         'cursor': 'pointer',
          _hover: { color:'#1890ff' }
        })}>
          查看完整投资组合
        </button>
      </div>
    </div>
  )
}
