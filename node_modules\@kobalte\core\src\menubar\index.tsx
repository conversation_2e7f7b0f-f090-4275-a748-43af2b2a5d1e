import {
	MenuCheckboxItem as CheckboxItem,
	MenuContent as Content,
	MenuGroup as Group,
	MenuGroupLabel as GroupLabel,
	MenuIcon as Icon,
	MenuItem as Item,
	MenuItemDescription as ItemDescription,
	MenuItemIndicator as ItemIndicator,
	MenuItemLabel as ItemLabel,
	type MenuCheckboxItemCommonProps as MenubarCheckboxItemCommonProps,
	type MenuCheckboxItemOptions as MenubarCheckboxItemOptions,
	type MenuCheckboxItemProps as MenubarCheckboxItemProps,
	type MenuCheckboxItemRenderProps as MenubarCheckboxItemRenderProps,
	type MenuContentCommonProps as MenubarContentCommonProps,
	type MenuContentOptions as MenubarContentOptions,
	type MenuContentProps as MenubarContentProps,
	type MenuContentRenderProps as MenubarContentRenderProps,
	type MenuGroupCommonProps as MenubarGroupCommonProps,
	type MenuGroupLabelCommonProps as MenubarGroupLabelCommonProps,
	type MenuGroupLabelOptions as MenubarGroupLabelOptions,
	type MenuGroupLabelProps as MenubarGroupLabelProps,
	type MenuGroupLabelRenderProps as MenubarGroupLabelRenderProps,
	type MenuGroupOptions as MenubarGroupOptions,
	type MenuGroupProps as MenubarGroupProps,
	type MenuGroupRenderProps as MenubarGroupRenderProps,
	type MenuIconCommonProps as MenubarIconCommonProps,
	type MenuIconOptions as MenubarIconOptions,
	type MenuIconProps as MenubarIconProps,
	type MenuIconRenderProps as MenubarIconRenderProps,
	type MenuItemCommonProps as MenubarItemCommonProps,
	type MenuItemDescriptionCommonProps as MenubarItemDescriptionCommonProps,
	type MenuItemDescriptionOptions as MenubarItemDescriptionOptions,
	type MenuItemDescriptionProps as MenubarItemDescriptionProps,
	type MenuItemDescriptionRenderProps as MenubarItemDescriptionRenderProps,
	type MenuItemIndicatorCommonProps as MenubarItemIndicatorCommonProps,
	type MenuItemIndicatorOptions as MenubarItemIndicatorOptions,
	type MenuItemIndicatorProps as MenubarItemIndicatorProps,
	type MenuItemIndicatorRenderProps as MenubarItemIndicatorRenderProps,
	type MenuItemLabelCommonProps as MenubarItemLabelCommonProps,
	type MenuItemLabelOptions as MenubarItemLabelOptions,
	type MenuItemLabelProps as MenubarItemLabelProps,
	type MenuItemLabelRenderProps as MenubarItemLabelRenderProps,
	type MenuItemOptions as MenubarItemOptions,
	type MenuItemProps as MenubarItemProps,
	type MenuItemRenderProps as MenubarItemRenderProps,
	type MenuPortalProps as MenubarPortalProps,
	type MenuRadioGroupCommonProps as MenubarRadioGroupCommonProps,
	type MenuRadioGroupOptions as MenubarRadioGroupOptions,
	type MenuRadioGroupProps as MenubarRadioGroupProps,
	type MenuRadioGroupRenderProps as MenubarRadioGroupRenderProps,
	type MenuRadioItemCommonProps as MenubarRadioItemCommonProps,
	type MenuRadioItemOptions as MenubarRadioItemOptions,
	type MenuRadioItemRenderProps as MenubarRadioItemPRenderrops,
	type MenuRadioItemProps as MenubarRadioItemProps,
	type MenuSubContentCommonProps as MenubarSubContentCommonProps,
	type MenuSubContentOptions as MenubarSubContentOptions,
	type MenuSubContentProps as MenubarSubContentProps,
	type MenuSubContentRenderProps as MenubarSubContentRenderProps,
	type MenuSubOptions as MenubarSubOptions,
	type MenuSubProps as MenubarSubProps,
	type MenuSubTriggerCommonProps as MenubarSubTriggerCommonProps,
	type MenuSubTriggerOptions as MenubarSubTriggerOptions,
	type MenuSubTriggerProps as MenubarSubTriggerProps,
	type MenuSubTriggerRenderProps as MenubarSubTriggerRenderProps,
	type MenuTriggerCommonProps as MenubarTriggerCommonProps,
	type MenuTriggerOptions as MenubarTriggerOptions,
	type MenuTriggerProps as MenubarTriggerProps,
	type MenuTriggerRenderProps as MenubarTriggerRenderProps,
	MenuPortal as Portal,
	MenuRadioGroup as RadioGroup,
	MenuRadioItem as RadioItem,
	MenuSub as Sub,
	MenuSubContent as SubContent,
	MenuSubTrigger as SubTrigger,
} from "../menu";
import {
	Arrow,
	type PopperArrowCommonProps as MenubarArrowCommonProps,
	type PopperArrowOptions as MenubarArrowOptions,
	type PopperArrowProps as MenubarArrowProps,
	type PopperArrowRenderProps as MenubarArrowRenderProps,
} from "../popper";
import {
	type SeparatorRootCommonProps as MenubarSeparatorCommonProps,
	type SeparatorRootOptions as MenubarSeparatorOptions,
	type SeparatorRootProps as MenubarSeparatorProps,
	type SeparatorRootRenderProps as MenubarSeparatorRenderProps,
	Root as Separator,
} from "../separator";
import {
	MenubarMenu as Menu,
	type MenubarMenuOptions,
	type MenubarMenuProps,
} from "./menubar-menu";
import {
	type MenubarRootCommonProps,
	type MenubarRootOptions,
	type MenubarRootProps,
	type MenubarRootRenderProps,
	MenubarRoot as Root,
} from "./menubar-root";
import { MenubarTrigger as Trigger } from "./menubar-trigger";

export type {
	MenubarRootOptions,
	MenubarRootCommonProps,
	MenubarRootRenderProps,
	MenubarRootProps,
	MenubarMenuOptions,
	MenubarMenuProps,
	MenubarArrowOptions,
	MenubarArrowCommonProps,
	MenubarArrowRenderProps,
	MenubarArrowProps,
	MenubarCheckboxItemOptions,
	MenubarCheckboxItemCommonProps,
	MenubarCheckboxItemRenderProps,
	MenubarCheckboxItemProps,
	MenubarContentOptions,
	MenubarContentCommonProps,
	MenubarContentRenderProps,
	MenubarContentProps,
	MenubarGroupLabelOptions,
	MenubarGroupLabelCommonProps,
	MenubarGroupLabelRenderProps,
	MenubarGroupLabelProps,
	MenubarGroupOptions,
	MenubarGroupCommonProps,
	MenubarGroupRenderProps,
	MenubarGroupProps,
	MenubarIconOptions,
	MenubarIconCommonProps,
	MenubarIconRenderProps,
	MenubarIconProps,
	MenubarItemDescriptionOptions,
	MenubarItemDescriptionCommonProps,
	MenubarItemDescriptionRenderProps,
	MenubarItemDescriptionProps,
	MenubarItemIndicatorOptions,
	MenubarItemIndicatorCommonProps,
	MenubarItemIndicatorRenderProps,
	MenubarItemIndicatorProps,
	MenubarItemLabelOptions,
	MenubarItemLabelCommonProps,
	MenubarItemLabelRenderProps,
	MenubarItemLabelProps,
	MenubarItemOptions,
	MenubarItemCommonProps,
	MenubarItemRenderProps,
	MenubarItemProps,
	MenubarPortalProps,
	MenubarRadioGroupOptions,
	MenubarRadioGroupCommonProps,
	MenubarRadioGroupRenderProps,
	MenubarRadioGroupProps,
	MenubarRadioItemOptions,
	MenubarRadioItemCommonProps,
	MenubarRadioItemPRenderrops,
	MenubarRadioItemProps,
	MenubarSeparatorOptions,
	MenubarSeparatorCommonProps,
	MenubarSeparatorRenderProps,
	MenubarSeparatorProps,
	MenubarSubContentOptions,
	MenubarSubContentCommonProps,
	MenubarSubContentRenderProps,
	MenubarSubContentProps,
	MenubarSubOptions,
	MenubarSubProps,
	MenubarSubTriggerOptions,
	MenubarSubTriggerCommonProps,
	MenubarSubTriggerRenderProps,
	MenubarSubTriggerProps,
	MenubarTriggerOptions,
	MenubarTriggerCommonProps,
	MenubarTriggerRenderProps,
	MenubarTriggerProps,
};

export {
	Arrow,
	CheckboxItem,
	Content,
	Group,
	GroupLabel,
	Icon,
	Item,
	ItemDescription,
	ItemIndicator,
	ItemLabel,
	Portal,
	RadioGroup,
	RadioItem,
	Root,
	Menu,
	Separator,
	Sub,
	SubContent,
	SubTrigger,
	Trigger,
};

export const Menubar = Object.assign(Root, {
	Arrow,
	CheckboxItem,
	Content,
	Group,
	GroupLabel,
	Icon,
	Item,
	ItemDescription,
	ItemIndicator,
	ItemLabel,
	Portal,
	RadioGroup,
	RadioItem,
	Menu,
	Separator,
	Sub,
	SubContent,
	SubTrigger,
	Trigger,
});

/**
 * API will most probably change
 */
export { useMenubarContext, type MenubarContextValue } from "./menubar-context";
