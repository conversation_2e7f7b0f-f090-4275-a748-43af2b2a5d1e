<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="72" cy="72" r="70" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
  
  <!-- 量化交易图标 - 图表线条 -->
  <g stroke="#fff" stroke-width="3" fill="none">
    <!-- 上升趋势线 -->
    <path d="M30 90 L50 70 L70 50 L90 30" stroke-linecap="round"/>
    <!-- 下降趋势线 -->
    <path d="M54 90 L74 110 L94 90 L114 70" stroke-linecap="round"/>
  </g>
  
  <!-- 数据点 -->
  <g fill="#fff">
    <circle cx="30" cy="90" r="3"/>
    <circle cx="50" cy="70" r="3"/>
    <circle cx="70" cy="50" r="3"/>
    <circle cx="90" cy="30" r="3"/>
    <circle cx="54" cy="90" r="3"/>
    <circle cx="74" cy="110" r="3"/>
    <circle cx="94" cy="90" r="3"/>
    <circle cx="114" cy="70" r="3"/>
  </g>
  
  <!-- 中心字母 Q -->
  <text x="72" y="85" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#fff" text-anchor="middle">Q</text>
</svg>
