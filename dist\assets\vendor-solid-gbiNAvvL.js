const p={context:void 0,registry:void 0,effects:void 0,done:!1,getContextId(){return je(this.context.count)},getNextContextId(){return je(this.context.count++)}};function je(e){const t=String(e),n=t.length-1;return p.context.id+(n?String.fromCharCode(96+n):"")+t}function z(e){p.context=e}const Be=!1,at=(e,t)=>e===t,oe=Symbol("solid-proxy"),Ke=typeof Proxy=="function",ct=Symbol("solid-track"),ie={equals:at};let He=Ye;const M=1,le=2,We={owned:null,cleanups:null,context:null,owner:null},ge={};var b=null;let me=null,ut=null,S=null,T=null,F=null,ue=0;function Y(e,t){const n=S,r=b,s=e.length===0,o=t===void 0?r:t,l=s?We:{owned:null,cleanups:null,context:o?o.context:null,owner:o},i=s?e:()=>e(()=>$(()=>J(l)));b=l,S=null;try{return j(i,!0)}finally{S=n,b=r}}function I(e,t){t=t?Object.assign({},ie,t):ie;const n={value:e,observers:null,observerSlots:null,comparator:t.equals||void 0},r=s=>(typeof s=="function"&&(s=s(n.value)),ze(n,s));return[Xe.bind(n),r]}function ft(e,t,n){const r=fe(e,t,!0,M);G(r)}function U(e,t,n){const r=fe(e,t,!1,M);G(r)}function dt(e,t,n){He=At;const r=fe(e,t,!1,M);r.user=!0,F?F.push(r):G(r)}function x(e,t,n){n=n?Object.assign({},ie,n):ie;const r=fe(e,t,!0,0);return r.observers=null,r.observerSlots=null,r.comparator=n.equals||void 0,G(r),Xe.bind(r)}function ht(e){return e&&typeof e=="object"&&"then"in e}function gt(e,t,n){let r,s,o;r=!0,s=e,o={};let l=null,i=ge,a=null,c=!1,f="initialValue"in o,u=typeof r=="function"&&x(r);const h=new Set,[g,E]=(o.storage||I)(o.initialValue),[d,y]=I(void 0),[A,w]=I(void 0,{equals:!1}),[L,v]=I(f?"ready":"unresolved");p.context&&(a=p.getNextContextId(),o.ssrLoadFrom==="initial"?i=o.initialValue:p.load&&p.has(a)&&(i=p.load(a)));function C(R,O,k,m){return l===R&&(l=null,m!==void 0&&(f=!0),(R===i||O===i)&&o.onHydrated&&queueMicrotask(()=>o.onHydrated(m,{value:O})),i=ge,D(O,k)),O}function D(R,O){j(()=>{O===void 0&&E(()=>R),v(O!==void 0?"errored":f?"ready":"unresolved"),y(O);for(const k of h.keys())k.decrement();h.clear()},!1)}function X(){const R=wt,O=g(),k=d();if(k!==void 0&&!l)throw k;return S&&S.user,O}function H(R=!0){if(R!==!1&&c)return;c=!1;const O=u?u():r;if(O==null||O===!1){C(l,$(g));return}let k;const m=i!==ge?i:$(()=>{try{return s(O,{value:g(),refetching:R})}catch(P){k=P}});if(k!==void 0){C(l,void 0,re(k),O);return}else if(!ht(m))return C(l,m,void 0,O),m;return l=m,"v"in m?(m.s===1?C(l,m.v,void 0,O):C(l,void 0,re(m.v),O),m):(c=!0,queueMicrotask(()=>c=!1),j(()=>{v(f?"refreshing":"pending"),w()},!1),m.then(P=>C(m,P,void 0,O),P=>C(m,void 0,re(P),O)))}Object.defineProperties(X,{state:{get:()=>L()},error:{get:()=>d()},loading:{get(){const R=L();return R==="pending"||R==="refreshing"}},latest:{get(){if(!f)return X();const R=d();if(R&&!l)throw R;return g()}}});let ee=b;return u?ft(()=>(ee=b,H(!1))):H(!1),[X,{refetch:R=>Oe(ee,()=>H(R)),mutate:E}]}function mt(e){return j(e,!1)}function $(e){if(S===null)return e();const t=S;S=null;try{return e()}finally{S=t}}function xe(e,t,n){const r=Array.isArray(e);let s,o=n&&n.defer;return l=>{let i;if(r){i=Array(e.length);for(let c=0;c<e.length;c++)i[c]=e[c]()}else i=e();if(o)return o=!1,l;const a=$(()=>t(i,s,l));return s=i,a}}function Cn(e){dt(()=>$(e))}function Ce(e){return b===null||(b.cleanups===null?b.cleanups=[e]:b.cleanups.push(e)),e}function qe(){return b}function Oe(e,t){const n=b,r=S;b=e,S=null;try{return j(t,!0)}catch(s){Te(s)}finally{b=n,S=r}}function yt(e){const t=S,n=b;return Promise.resolve().then(()=>{S=t,b=n;let r;return j(e,!1),S=b=null,r?r.done:void 0})}const[On,Ln]=I(!1);function Ge(e,t){const n=Symbol("context");return{id:n,Provider:Pt(n),defaultValue:e}}function Le(e){let t;return b&&b.context&&(t=b.context[e.id])!==void 0?t:e.defaultValue}function Re(e){const t=x(e),n=x(()=>be(t()));return n.toArray=()=>{const r=n();return Array.isArray(r)?r:r!=null?[r]:[]},n}let wt;function Xe(){if(this.sources&&this.state)if(this.state===M)G(this);else{const e=T;T=null,j(()=>ce(this),!1),T=e}if(S){const e=this.observers?this.observers.length:0;S.sources?(S.sources.push(this),S.sourceSlots.push(e)):(S.sources=[this],S.sourceSlots=[e]),this.observers?(this.observers.push(S),this.observerSlots.push(S.sources.length-1)):(this.observers=[S],this.observerSlots=[S.sources.length-1])}return this.value}function ze(e,t,n){let r=e.value;return(!e.comparator||!e.comparator(r,t))&&(e.value=t,e.observers&&e.observers.length&&j(()=>{for(let s=0;s<e.observers.length;s+=1){const o=e.observers[s],l=me&&me.running;l&&me.disposed.has(o),(l?!o.tState:!o.state)&&(o.pure?T.push(o):F.push(o),o.observers&&Je(o)),l||(o.state=M)}if(T.length>1e6)throw T=[],new Error},!1)),t}function G(e){if(!e.fn)return;J(e);const t=ue;pt(e,e.value,t)}function pt(e,t,n){let r;const s=b,o=S;S=b=e;try{r=e.fn(t)}catch(l){return e.pure&&(e.state=M,e.owned&&e.owned.forEach(J),e.owned=null),e.updatedAt=n+1,Te(l)}finally{S=o,b=s}(!e.updatedAt||e.updatedAt<=n)&&(e.updatedAt!=null&&"observers"in e?ze(e,r):e.value=r,e.updatedAt=n)}function fe(e,t,n,r=M,s){const o={fn:e,state:r,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:t,owner:b,context:b?b.context:null,pure:n};return b===null||b!==We&&(b.owned?b.owned.push(o):b.owned=[o]),o}function ae(e){if(e.state===0)return;if(e.state===le)return ce(e);if(e.suspense&&$(e.suspense.inFallback))return e.suspense.effects.push(e);const t=[e];for(;(e=e.owner)&&(!e.updatedAt||e.updatedAt<ue);)e.state&&t.push(e);for(let n=t.length-1;n>=0;n--)if(e=t[n],e.state===M)G(e);else if(e.state===le){const r=T;T=null,j(()=>ce(e,t[0]),!1),T=r}}function j(e,t){if(T)return e();let n=!1;t||(T=[]),F?n=!0:F=[],ue++;try{const r=e();return bt(n),r}catch(r){n||(F=null),T=null,Te(r)}}function bt(e){if(T&&(Ye(T),T=null),e)return;const t=F;F=null,t.length&&j(()=>He(t),!1)}function Ye(e){for(let t=0;t<e.length;t++)ae(e[t])}function At(e){let t,n=0;for(t=0;t<e.length;t++){const r=e[t];r.user?e[n++]=r:ae(r)}if(p.context){if(p.count){p.effects||(p.effects=[]),p.effects.push(...e.slice(0,n));return}z()}for(p.effects&&(p.done||!p.count)&&(e=[...p.effects,...e],n+=p.effects.length,delete p.effects),t=0;t<n;t++)ae(e[t])}function ce(e,t){e.state=0;for(let n=0;n<e.sources.length;n+=1){const r=e.sources[n];if(r.sources){const s=r.state;s===M?r!==t&&(!r.updatedAt||r.updatedAt<ue)&&ae(r):s===le&&ce(r,t)}}}function Je(e){for(let t=0;t<e.observers.length;t+=1){const n=e.observers[t];n.state||(n.state=le,n.pure?T.push(n):F.push(n),n.observers&&Je(n))}}function J(e){let t;if(e.sources)for(;e.sources.length;){const n=e.sources.pop(),r=e.sourceSlots.pop(),s=n.observers;if(s&&s.length){const o=s.pop(),l=n.observerSlots.pop();r<s.length&&(o.sourceSlots[l]=r,s[r]=o,n.observerSlots[r]=l)}}if(e.tOwned){for(t=e.tOwned.length-1;t>=0;t--)J(e.tOwned[t]);delete e.tOwned}if(e.owned){for(t=e.owned.length-1;t>=0;t--)J(e.owned[t]);e.owned=null}if(e.cleanups){for(t=e.cleanups.length-1;t>=0;t--)e.cleanups[t]();e.cleanups=null}e.state=0}function re(e){return e instanceof Error?e:new Error(typeof e=="string"?e:"Unknown error",{cause:e})}function Te(e,t=b){throw re(e)}function be(e){if(typeof e=="function"&&!e.length)return be(e());if(Array.isArray(e)){const t=[];for(let n=0;n<e.length;n++){const r=be(e[n]);Array.isArray(r)?t.push.apply(t,r):t.push(r)}return t}return e}function Pt(e,t){return function(r){let s;return U(()=>s=$(()=>(b.context={...b.context,[e]:r.value},Re(()=>r.children))),void 0),s}}const St=Symbol("fallback");function De(e){for(let t=0;t<e.length;t++)e[t]()}function Et(e,t,n={}){let r=[],s=[],o=[],l=0,i=t.length>1?[]:null;return Ce(()=>De(o)),()=>{let a=e()||[],c=a.length,f,u;return a[ct],$(()=>{let g,E,d,y,A,w,L,v,C;if(c===0)l!==0&&(De(o),o=[],r=[],s=[],l=0,i&&(i=[])),n.fallback&&(r=[St],s[0]=Y(D=>(o[0]=D,n.fallback())),l=1);else if(l===0){for(s=new Array(c),u=0;u<c;u++)r[u]=a[u],s[u]=Y(h);l=c}else{for(d=new Array(c),y=new Array(c),i&&(A=new Array(c)),w=0,L=Math.min(l,c);w<L&&r[w]===a[w];w++);for(L=l-1,v=c-1;L>=w&&v>=w&&r[L]===a[v];L--,v--)d[v]=s[L],y[v]=o[L],i&&(A[v]=i[L]);for(g=new Map,E=new Array(v+1),u=v;u>=w;u--)C=a[u],f=g.get(C),E[u]=f===void 0?-1:f,g.set(C,u);for(f=w;f<=L;f++)C=r[f],u=g.get(C),u!==void 0&&u!==-1?(d[u]=s[f],y[u]=o[f],i&&(A[u]=i[f]),u=E[u],g.set(C,u)):o[f]();for(u=w;u<c;u++)u in d?(s[u]=d[u],o[u]=y[u],i&&(i[u]=A[u],i[u](u))):s[u]=Y(h);s=s.slice(0,l=c),r=a.slice(0)}return s});function h(g){if(o[u]=g,i){const[E,d]=I(u);return i[u]=d,t(a[u],E)}return t(a[u])}}}function _(e,t){return $(()=>e(t||{}))}function ne(){return!0}const Ae={get(e,t,n){return t===oe?n:e.get(t)},has(e,t){return t===oe?!0:e.has(t)},set:ne,deleteProperty:ne,getOwnPropertyDescriptor(e,t){return{configurable:!0,enumerable:!0,get(){return e.get(t)},set:ne,deleteProperty:ne}},ownKeys(e){return e.keys()}};function ye(e){return(e=typeof e=="function"?e():e)?e:{}}function vt(){for(let e=0,t=this.length;e<t;++e){const n=this[e]();if(n!==void 0)return n}}function Pe(...e){let t=!1;for(let l=0;l<e.length;l++){const i=e[l];t=t||!!i&&oe in i,e[l]=typeof i=="function"?(t=!0,x(i)):i}if(Ke&&t)return new Proxy({get(l){for(let i=e.length-1;i>=0;i--){const a=ye(e[i])[l];if(a!==void 0)return a}},has(l){for(let i=e.length-1;i>=0;i--)if(l in ye(e[i]))return!0;return!1},keys(){const l=[];for(let i=0;i<e.length;i++)l.push(...Object.keys(ye(e[i])));return[...new Set(l)]}},Ae);const n={},r=Object.create(null);for(let l=e.length-1;l>=0;l--){const i=e[l];if(!i)continue;const a=Object.getOwnPropertyNames(i);for(let c=a.length-1;c>=0;c--){const f=a[c];if(f==="__proto__"||f==="constructor")continue;const u=Object.getOwnPropertyDescriptor(i,f);if(!r[f])r[f]=u.get?{enumerable:!0,configurable:!0,get:vt.bind(n[f]=[u.get.bind(i)])}:u.value!==void 0?u:void 0;else{const h=n[f];h&&(u.get?h.push(u.get.bind(i)):u.value!==void 0&&h.push(()=>u.value))}}}const s={},o=Object.keys(r);for(let l=o.length-1;l>=0;l--){const i=o[l],a=r[i];a&&a.get?Object.defineProperty(s,i,a):s[i]=a?a.value:void 0}return s}function xt(e,...t){if(Ke&&oe in e){const s=new Set(t.length>1?t.flat():t[0]),o=t.map(l=>new Proxy({get(i){return l.includes(i)?e[i]:void 0},has(i){return l.includes(i)&&i in e},keys(){return l.filter(i=>i in e)}},Ae));return o.push(new Proxy({get(l){return s.has(l)?void 0:e[l]},has(l){return s.has(l)?!1:l in e},keys(){return Object.keys(e).filter(l=>!s.has(l))}},Ae)),o}const n={},r=t.map(()=>({}));for(const s of Object.getOwnPropertyNames(e)){const o=Object.getOwnPropertyDescriptor(e,s),l=!o.get&&!o.set&&o.enumerable&&o.writable&&o.configurable;let i=!1,a=0;for(const c of t)c.includes(s)&&(i=!0,l?r[a][s]=o.value:Object.defineProperty(r[a],s,o)),++a;i||(l?n[s]=o.value:Object.defineProperty(n,s,o))}return[...r,n]}function Rn(e){let t,n;const r=s=>{const o=p.context;if(o){const[i,a]=I();p.count||(p.count=0),p.count++,(n||(n=e())).then(c=>{!p.done&&z(o),p.count--,a(()=>c.default),z()}),t=i}else if(!t){const[i]=gt(()=>(n||(n=e())).then(a=>a.default));t=i}let l;return x(()=>(l=t())?$(()=>{if(!o||p.done)return l(s);const i=p.context;z(o);const a=l(s);return z(i),a}):"")};return r.preload=()=>n||((n=e()).then(s=>t=()=>s.default),n),r}const Ct=e=>`Stale read from <${e}>.`;function Tn(e){const t="fallback"in e&&{fallback:()=>e.fallback};return x(Et(()=>e.each,e.children,t||void 0))}function Qe(e){const t=e.keyed,n=x(()=>e.when,void 0,void 0),r=t?n:x(n,void 0,{equals:(s,o)=>!s==!o});return x(()=>{const s=r();if(s){const o=e.children;return typeof o=="function"&&o.length>0?$(()=>o(t?s:()=>{if(!$(r))throw Ct("Show");return n()})):o}return e.fallback},void 0,void 0)}const Ot=["allowfullscreen","async","alpha","autofocus","autoplay","checked","controls","default","disabled","formnovalidate","hidden","indeterminate","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","seamless","selected","adauctionheaders","browsingtopics","credentialless","defaultchecked","defaultmuted","defaultselected","defer","disablepictureinpicture","disableremoteplayback","preservespitch","shadowrootclonable","shadowrootcustomelementregistry","shadowrootdelegatesfocus","shadowrootserializable","sharedstoragewritable"],Lt=new Set(["className","value","readOnly","noValidate","formNoValidate","isMap","noModule","playsInline","adAuctionHeaders","allowFullscreen","browsingTopics","defaultChecked","defaultMuted","defaultSelected","disablePictureInPicture","disableRemotePlayback","preservesPitch","shadowRootClonable","shadowRootCustomElementRegistry","shadowRootDelegatesFocus","shadowRootSerializable","sharedStorageWritable",...Ot]),Rt=new Set(["innerHTML","textContent","innerText","children"]),Tt=Object.assign(Object.create(null),{className:"class",htmlFor:"for"}),$t=Object.assign(Object.create(null),{class:"className",novalidate:{$:"noValidate",FORM:1},formnovalidate:{$:"formNoValidate",BUTTON:1,INPUT:1},ismap:{$:"isMap",IMG:1},nomodule:{$:"noModule",SCRIPT:1},playsinline:{$:"playsInline",VIDEO:1},readonly:{$:"readOnly",INPUT:1,TEXTAREA:1},adauctionheaders:{$:"adAuctionHeaders",IFRAME:1},allowfullscreen:{$:"allowFullscreen",IFRAME:1},browsingtopics:{$:"browsingTopics",IMG:1},defaultchecked:{$:"defaultChecked",INPUT:1},defaultmuted:{$:"defaultMuted",AUDIO:1,VIDEO:1},defaultselected:{$:"defaultSelected",OPTION:1},disablepictureinpicture:{$:"disablePictureInPicture",VIDEO:1},disableremoteplayback:{$:"disableRemotePlayback",AUDIO:1,VIDEO:1},preservespitch:{$:"preservesPitch",AUDIO:1,VIDEO:1},shadowrootclonable:{$:"shadowRootClonable",TEMPLATE:1},shadowrootdelegatesfocus:{$:"shadowRootDelegatesFocus",TEMPLATE:1},shadowrootserializable:{$:"shadowRootSerializable",TEMPLATE:1},sharedstoragewritable:{$:"sharedStorageWritable",IFRAME:1,IMG:1}});function kt(e,t){const n=$t[e];return typeof n=="object"?n[t]?n.$:void 0:n}const It=new Set(["beforeinput","click","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"]),Nt=e=>x(()=>e());function jt(e,t,n){let r=n.length,s=t.length,o=r,l=0,i=0,a=t[s-1].nextSibling,c=null;for(;l<s||i<o;){if(t[l]===n[i]){l++,i++;continue}for(;t[s-1]===n[o-1];)s--,o--;if(s===l){const f=o<r?i?n[i-1].nextSibling:n[o-i]:a;for(;i<o;)e.insertBefore(n[i++],f)}else if(o===i)for(;l<s;)(!c||!c.has(t[l]))&&t[l].remove(),l++;else if(t[l]===n[o-1]&&n[i]===t[s-1]){const f=t[--s].nextSibling;e.insertBefore(n[i++],t[l++].nextSibling),e.insertBefore(n[--o],f),t[s]=n[o]}else{if(!c){c=new Map;let u=i;for(;u<o;)c.set(n[u],u++)}const f=c.get(t[l]);if(f!=null)if(i<f&&f<o){let u=l,h=1,g;for(;++u<s&&u<o&&!((g=c.get(t[u]))==null||g!==f+h);)h++;if(h>f-i){const E=t[l];for(;i<f;)e.insertBefore(n[i++],E)}else e.replaceChild(n[i++],t[l++])}else l++;else t[l++].remove()}}}const Fe="_$DX_DELEGATE";function $n(e,t,n,r={}){let s;return Y(o=>{s=o,t===document?e():Ht(t,e(),t.firstChild?null:void 0,n)},r.owner),()=>{s(),t.textContent=""}}function Dt(e,t,n,r){let s;const o=()=>{const i=document.createElement("template");return i.innerHTML=e,i.content.firstChild},l=()=>(s||(s=o())).cloneNode(!0);return l.cloneNode=l,l}function Ze(e,t=window.document){const n=t[Fe]||(t[Fe]=new Set);for(let r=0,s=e.length;r<s;r++){const o=e[r];n.has(o)||(n.add(o),t.addEventListener(o,Gt))}}function Se(e,t,n){Z(e)||(n==null?e.removeAttribute(t):e.setAttribute(t,n))}function Ft(e,t,n){Z(e)||(n?e.setAttribute(t,""):e.removeAttribute(t))}function Mt(e,t){Z(e)||(t==null?e.removeAttribute("class"):e.className=t)}function Ut(e,t,n,r){if(r)Array.isArray(n)?(e[`$$${t}`]=n[0],e[`$$${t}Data`]=n[1]):e[`$$${t}`]=n;else if(Array.isArray(n)){const s=n[0];e.addEventListener(t,n[0]=o=>s.call(e,n[1],o))}else e.addEventListener(t,n,typeof n!="function"&&n)}function _t(e,t,n={}){const r=Object.keys(t||{}),s=Object.keys(n);let o,l;for(o=0,l=s.length;o<l;o++){const i=s[o];!i||i==="undefined"||t[i]||(Me(e,i,!1),delete n[i])}for(o=0,l=r.length;o<l;o++){const i=r[o],a=!!t[i];!i||i==="undefined"||n[i]===a||!a||(Me(e,i,!0),n[i]=a)}return n}function Vt(e,t,n){if(!t)return n?Se(e,"style"):t;const r=e.style;if(typeof t=="string")return r.cssText=t;typeof n=="string"&&(r.cssText=n=void 0),n||(n={}),t||(t={});let s,o;for(o in n)t[o]==null&&r.removeProperty(o),delete n[o];for(o in t)s=t[o],s!==n[o]&&(r.setProperty(o,s),n[o]=s);return n}function kn(e,t,n){n!=null?e.style.setProperty(t,n):e.style.removeProperty(t)}function Bt(e,t={},n,r){const s={};return U(()=>s.children=Q(e,t.children,s.children)),U(()=>typeof t.ref=="function"&&Kt(t.ref,e)),U(()=>Wt(e,t,n,!0,s,!0)),s}function Kt(e,t,n){return $(()=>e(t,n))}function Ht(e,t,n,r){if(n!==void 0&&!r&&(r=[]),typeof t!="function")return Q(e,t,r,n);U(s=>Q(e,t(),s,n),r)}function Wt(e,t,n,r,s={},o=!1){t||(t={});for(const l in s)if(!(l in t)){if(l==="children")continue;s[l]=Ue(e,l,null,s[l],n,o,t)}for(const l in t){if(l==="children")continue;const i=t[l];s[l]=Ue(e,l,i,s[l],n,o,t)}}function Z(e){return!!p.context&&!p.done&&(!e||e.isConnected)}function qt(e){return e.toLowerCase().replace(/-([a-z])/g,(t,n)=>n.toUpperCase())}function Me(e,t,n){const r=t.trim().split(/\s+/);for(let s=0,o=r.length;s<o;s++)e.classList.toggle(r[s],n)}function Ue(e,t,n,r,s,o,l){let i,a,c,f,u;if(t==="style")return Vt(e,n,r);if(t==="classList")return _t(e,n,r);if(n===r)return r;if(t==="ref")o||n(e);else if(t.slice(0,3)==="on:"){const h=t.slice(3);r&&e.removeEventListener(h,r,typeof r!="function"&&r),n&&e.addEventListener(h,n,typeof n!="function"&&n)}else if(t.slice(0,10)==="oncapture:"){const h=t.slice(10);r&&e.removeEventListener(h,r,!0),n&&e.addEventListener(h,n,!0)}else if(t.slice(0,2)==="on"){const h=t.slice(2).toLowerCase(),g=It.has(h);if(!g&&r){const E=Array.isArray(r)?r[0]:r;e.removeEventListener(h,E)}(g||n)&&(Ut(e,h,n,g),g&&Ze([h]))}else if(t.slice(0,5)==="attr:")Se(e,t.slice(5),n);else if(t.slice(0,5)==="bool:")Ft(e,t.slice(5),n);else if((u=t.slice(0,5)==="prop:")||(c=Rt.has(t))||(f=kt(t,e.tagName))||(a=Lt.has(t))||(i=e.nodeName.includes("-")||"is"in l)){if(u)t=t.slice(5),a=!0;else if(Z(e))return n;t==="class"||t==="className"?Mt(e,n):i&&!a&&!c?e[qt(t)]=n:e[f||t]=n}else Se(e,Tt[t]||t,n);return n}function Gt(e){if(p.registry&&p.events&&p.events.find(([a,c])=>c===e))return;let t=e.target;const n=`$$${e.type}`,r=e.target,s=e.currentTarget,o=a=>Object.defineProperty(e,"target",{configurable:!0,value:a}),l=()=>{const a=t[n];if(a&&!t.disabled){const c=t[`${n}Data`];if(c!==void 0?a.call(t,c,e):a.call(t,e),e.cancelBubble)return}return t.host&&typeof t.host!="string"&&!t.host._$host&&t.contains(e.target)&&o(t.host),!0},i=()=>{for(;l()&&(t=t._$host||t.parentNode||t.host););};if(Object.defineProperty(e,"currentTarget",{configurable:!0,get(){return t||document}}),p.registry&&!p.done&&(p.done=_$HY.done=!0),e.composedPath){const a=e.composedPath();o(a[0]);for(let c=0;c<a.length-2&&(t=a[c],!!l());c++){if(t._$host){t=t._$host,i();break}if(t.parentNode===s)break}}else i();o(r)}function Q(e,t,n,r,s){const o=Z(e);if(o){!n&&(n=[...e.childNodes]);let a=[];for(let c=0;c<n.length;c++){const f=n[c];f.nodeType===8&&f.data.slice(0,2)==="!$"?f.remove():a.push(f)}n=a}for(;typeof n=="function";)n=n();if(t===n)return n;const l=typeof t,i=r!==void 0;if(e=i&&n[0]&&n[0].parentNode||e,l==="string"||l==="number"){if(o||l==="number"&&(t=t.toString(),t===n))return n;if(i){let a=n[0];a&&a.nodeType===3?a.data!==t&&(a.data=t):a=document.createTextNode(t),n=q(e,n,r,a)}else n!==""&&typeof n=="string"?n=e.firstChild.data=t:n=e.textContent=t}else if(t==null||l==="boolean"){if(o)return n;n=q(e,n,r)}else{if(l==="function")return U(()=>{let a=t();for(;typeof a=="function";)a=a();n=Q(e,a,n,r)}),()=>n;if(Array.isArray(t)){const a=[],c=n&&Array.isArray(n);if(Ee(a,t,n,s))return U(()=>n=Q(e,a,n,r,!0)),()=>n;if(o){if(!a.length)return n;if(r===void 0)return n=[...e.childNodes];let f=a[0];if(f.parentNode!==e)return n;const u=[f];for(;(f=f.nextSibling)!==r;)u.push(f);return n=u}if(a.length===0){if(n=q(e,n,r),i)return n}else c?n.length===0?_e(e,a,r):jt(e,n,a):(n&&q(e),_e(e,a));n=a}else if(t.nodeType){if(o&&t.parentNode)return n=i?[t]:t;if(Array.isArray(n)){if(i)return n=q(e,n,r,t);q(e,n,null,t)}else n==null||n===""||!e.firstChild?e.appendChild(t):e.replaceChild(t,e.firstChild);n=t}}return n}function Ee(e,t,n,r){let s=!1;for(let o=0,l=t.length;o<l;o++){let i=t[o],a=n&&n[e.length],c;if(!(i==null||i===!0||i===!1))if((c=typeof i)=="object"&&i.nodeType)e.push(i);else if(Array.isArray(i))s=Ee(e,i,a)||s;else if(c==="function")if(r){for(;typeof i=="function";)i=i();s=Ee(e,Array.isArray(i)?i:[i],Array.isArray(a)?a:[a])||s}else e.push(i),s=!0;else{const f=String(i);a&&a.nodeType===3&&a.data===f?e.push(a):e.push(document.createTextNode(f))}}return s}function _e(e,t,n=null){for(let r=0,s=t.length;r<s;r++)e.insertBefore(t[r],n)}function q(e,t,n,r){if(n===void 0)return e.textContent="";const s=r||document.createTextNode("");if(t.length){let o=!1;for(let l=t.length-1;l>=0;l--){const i=t[l];if(s!==i){const a=i.parentNode===e;!o&&!l?a?e.replaceChild(s,i):e.insertBefore(s,n):a&&i.remove()}else o=!0}}else e.insertBefore(s,n);return[s]}const Xt=!1;function et(){let e=new Set;function t(s){return e.add(s),()=>e.delete(s)}let n=!1;function r(s,o){if(n)return!(n=!1);const l={to:s,options:o,defaultPrevented:!1,preventDefault:()=>l.defaultPrevented=!0};for(const i of e)i.listener({...l,from:i.location,retry:a=>{a&&(n=!0),i.navigate(s,{...o,resolve:!1})}});return!l.defaultPrevented}return{subscribe:t,confirm:r}}let ve;function $e(){(!window.history.state||window.history.state._depth==null)&&window.history.replaceState({...window.history.state,_depth:window.history.length-1},""),ve=window.history.state._depth}$e();function zt(e){return{...e,_depth:window.history.state&&window.history.state._depth}}function Yt(e,t){let n=!1;return()=>{const r=ve;$e();const s=r==null?null:ve-r;if(n){n=!1;return}s&&t(s)?(n=!0,window.history.go(-s)):e()}}const Jt=/^(?:[a-z0-9]+:)?\/\//i,Qt=/^\/+|(\/)\/+$/g,tt="http://sr";function K(e,t=!1){const n=e.replace(Qt,"$1");return n?t||/^[?#]/.test(n)?n:"/"+n:""}function se(e,t,n){if(Jt.test(t))return;const r=K(e),s=n&&K(n);let o="";return!s||t.startsWith("/")?o=r:s.toLowerCase().indexOf(r.toLowerCase())!==0?o=r+s:o=s,(o||"/")+K(t,!o)}function Zt(e,t){if(e==null)throw new Error(t);return e}function en(e,t){return K(e).replace(/\/*(\*.*)?$/g,"")+K(t)}function nt(e){const t={};return e.searchParams.forEach((n,r)=>{t[r]=n}),t}function tn(e,t,n){const[r,s]=e.split("/*",2),o=r.split("/").filter(Boolean),l=o.length;return i=>{const a=i.split("/").filter(Boolean),c=a.length-l;if(c<0||c>0&&s===void 0&&!t)return null;const f={path:l?"":"/",params:{}},u=h=>n===void 0?void 0:n[h];for(let h=0;h<l;h++){const g=o[h],E=a[h],d=g[0]===":",y=d?g.slice(1):g;if(d&&we(E,u(y)))f.params[y]=E;else if(d||!we(E,g))return null;f.path+=`/${E}`}if(s){const h=c?a.slice(-c).join("/"):"";if(we(h,u(s)))f.params[s]=h;else return null}return f}}function we(e,t){const n=r=>r.localeCompare(e,void 0,{sensitivity:"base"})===0;return t===void 0?!0:typeof t=="string"?n(t):typeof t=="function"?t(e):Array.isArray(t)?t.some(n):t instanceof RegExp?t.test(e):!1}function nn(e){const[t,n]=e.pattern.split("/*",2),r=t.split("/").filter(Boolean);return r.reduce((s,o)=>s+(o.startsWith(":")?2:3),r.length-(n===void 0?0:1))}function rt(e){const t=new Map,n=qe();return new Proxy({},{get(r,s){return t.has(s)||Oe(n,()=>t.set(s,x(()=>e()[s]))),t.get(s)()},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}},ownKeys(){return Reflect.ownKeys(e())}})}function st(e){let t=/(\/?\:[^\/]+)\?/.exec(e);if(!t)return[e];let n=e.slice(0,t.index),r=e.slice(t.index+t[0].length);const s=[n,n+=t[1]];for(;t=/^(\/\:[^\/]+)\?/.exec(r);)s.push(n+=t[1]),r=r.slice(t[0].length);return st(r).reduce((o,l)=>[...o,...s.map(i=>i+l)],[])}const rn=100,ot=Ge(),ke=Ge(),Ie=()=>Zt(Le(ot),"<A> and 'use' router primitives can be only used inside a Route."),sn=()=>Le(ke)||Ie().base,on=e=>{const t=sn();return x(()=>t.resolvePath(e()))},ln=e=>{const t=Ie();return x(()=>{const n=e();return n!==void 0?t.renderPath(n):n})},an=()=>Ie().location;function cn(e,t=""){const{component:n,load:r,children:s,info:o}=e,l=!s||Array.isArray(s)&&!s.length,i={key:e,component:n,load:r,info:o};return it(e.path).reduce((a,c)=>{for(const f of st(c)){const u=en(t,f);let h=l?u:u.split("/*",1)[0];h=h.split("/").map(g=>g.startsWith(":")||g.startsWith("*")?g:encodeURIComponent(g)).join("/"),a.push({...i,originalPath:c,pattern:h,matcher:tn(h,!l,e.matchFilters)})}return a},[])}function un(e,t=0){return{routes:e,score:nn(e[e.length-1])*1e4-t,matcher(n){const r=[];for(let s=e.length-1;s>=0;s--){const o=e[s],l=o.matcher(n);if(!l)return null;r.unshift({...l,route:o})}return r}}}function it(e){return Array.isArray(e)?e:[e]}function lt(e,t="",n=[],r=[]){const s=it(e);for(let o=0,l=s.length;o<l;o++){const i=s[o];if(i&&typeof i=="object"){i.hasOwnProperty("path")||(i.path="");const a=cn(i,t);for(const c of a){n.push(c);const f=Array.isArray(i.children)&&i.children.length===0;if(i.children&&!f)lt(i.children,c.pattern,n,r);else{const u=un([...n],r.length);r.push(u)}n.pop()}}}return n.length?r:r.sort((o,l)=>l.score-o.score)}function pe(e,t){for(let n=0,r=e.length;n<r;n++){const s=e[n].matcher(t);if(s)return s}return[]}function fn(e,t){const n=new URL(tt),r=x(a=>{const c=e();try{return new URL(c,n)}catch{return console.error(`Invalid path ${c}`),a}},n,{equals:(a,c)=>a.href===c.href}),s=x(()=>r().pathname),o=x(()=>r().search,!0),l=x(()=>r().hash),i=()=>"";return{get pathname(){return s()},get search(){return o()},get hash(){return l()},get state(){return t()},get key(){return i()},query:rt(xe(o,()=>nt(r())))}}let B;function dn(){return B}function hn(e,t,n,r={}){const{signal:[s,o],utils:l={}}=e,i=l.parsePath||(m=>m),a=l.renderPath||(m=>m),c=l.beforeLeave||et(),f=se("",r.base||"");if(f===void 0)throw new Error(`${f} is not a valid base path`);f&&!s().value&&o({value:f,replace:!0,scroll:!1});const[u,h]=I(!1);let g;const E=(m,P)=>{P.value===d()&&P.state===A()||(g===void 0&&h(!0),B=m,g=P,yt(()=>{g===P&&(y(g.value),w(g.state),C[1]([]))}).finally(()=>{g===P&&mt(()=>{B=void 0,m==="navigate"&&O(g),h(!1),g=void 0})}))},[d,y]=I(s().value),[A,w]=I(s().state),L=fn(d,A),v=[],C=I([]),D=x(()=>typeof r.transformUrl=="function"?pe(t(),r.transformUrl(L.pathname)):pe(t(),L.pathname)),X=rt(()=>{const m=D(),P={};for(let N=0;N<m.length;N++)Object.assign(P,m[N].params);return P}),H={pattern:f,path:()=>f,outlet:()=>null,resolvePath(m){return se(f,m)}};return U(xe(s,m=>E("native",m),{defer:!0})),{base:H,location:L,params:X,isRouting:u,renderPath:a,parsePath:i,navigatorFactory:R,matches:D,beforeLeave:c,preloadRoute:k,singleFlight:r.singleFlight===void 0?!0:r.singleFlight,submissions:C};function ee(m,P,N){$(()=>{if(typeof P=="number"){P&&(l.go?l.go(P):console.warn("Router integration does not support relative routing"));return}const{replace:de,resolve:he,scroll:W,state:te}={replace:!1,resolve:!0,scroll:!0,...N},V=he?m.resolvePath(P):se("",P);if(V===void 0)throw new Error(`Path '${P}' is not a routable path`);if(v.length>=rn)throw new Error("Too many redirects");const Ne=d();(V!==Ne||te!==A())&&(Xt||c.confirm(V,N)&&(v.push({value:Ne,replace:de,scroll:W,state:A()}),E("navigate",{value:V,state:te})))})}function R(m){return m=m||Le(ke)||H,(P,N)=>ee(m,P,N)}function O(m){const P=v[0];P&&(o({...m,replace:P.replace,scroll:P.scroll}),v.length=0)}function k(m,P={}){const N=pe(t(),m.pathname),de=B;B="preload";for(let he in N){const{route:W,params:te}=N[he];W.component&&W.component.preload&&W.component.preload();const{load:V}=W;P.preloadData&&V&&Oe(n(),()=>V({params:te,location:{pathname:m.pathname,search:m.search,hash:m.hash,query:nt(m),state:null,key:""},intent:"preload"}))}B=de}}function gn(e,t,n,r){const{base:s,location:o,params:l}=e,{pattern:i,component:a,load:c}=r().route,f=x(()=>r().path);a&&a.preload&&a.preload();const u=c?c({params:l,location:o,intent:B||"initial"}):void 0;return{parent:t,pattern:i,path:f,outlet:()=>a?_(a,{params:l,location:o,data:u,get children(){return n()}}):n(),resolvePath(g){return se(s.path(),g,f())}}}const mn=e=>t=>{const{base:n}=t,r=Re(()=>t.children),s=x(()=>lt(r(),t.base||""));let o;const l=hn(e,s,()=>o,{base:n,singleFlight:t.singleFlight,transformUrl:t.transformUrl});return e.create&&e.create(l),_(ot.Provider,{value:l,get children(){return _(yn,{routerState:l,get root(){return t.root},get load(){return t.rootLoad},get children(){return[Nt(()=>(o=qe())&&null),_(wn,{routerState:l,get branches(){return s()}})]}})}})};function yn(e){const t=e.routerState.location,n=e.routerState.params,r=x(()=>e.load&&$(()=>{e.load({params:n,location:t,intent:dn()||"initial"})}));return _(Qe,{get when(){return e.root},keyed:!0,get fallback(){return e.children},children:s=>_(s,{params:n,location:t,get data(){return r()},get children(){return e.children}})})}function wn(e){const t=[];let n;const r=x(xe(e.routerState.matches,(s,o,l)=>{let i=o&&s.length===o.length;const a=[];for(let c=0,f=s.length;c<f;c++){const u=o&&o[c],h=s[c];l&&u&&h.route.key===u.route.key?a[c]=l[c]:(i=!1,t[c]&&t[c](),Y(g=>{t[c]=g,a[c]=gn(e.routerState,a[c-1]||e.routerState.base,Ve(()=>r()[c+1]),()=>e.routerState.matches()[c])}))}return t.splice(s.length).forEach(c=>c()),l&&i?l:(n=a[0],a)}));return Ve(()=>r()&&n)()}const Ve=e=>()=>_(Qe,{get when(){return e()},keyed:!0,children:t=>_(ke.Provider,{value:t,get children(){return t.outlet()}})}),In=e=>{const t=Re(()=>e.children);return Pe(e,{get children(){return t()}})};function pn([e,t],n,r){return[e,r?s=>t(r(s)):t]}function bn(e){if(e==="#")return null;try{return document.querySelector(e)}catch{return null}}function An(e){let t=!1;const n=s=>typeof s=="string"?{value:s}:s,r=pn(I(n(e.get()),{equals:(s,o)=>s.value===o.value&&s.state===o.state}),void 0,s=>(!t&&e.set(s),s));return e.init&&Ce(e.init((s=e.get())=>{t=!0,r[1](n(s)),t=!1})),mn({signal:r,create:e.create,utils:e.utils})}function Pn(e,t,n){return e.addEventListener(t,n),()=>e.removeEventListener(t,n)}function Sn(e,t){const n=bn(`#${e}`);n?n.scrollIntoView():t&&window.scrollTo(0,0)}const En=new Map;function vn(e=!0,t=!1,n="/_server",r){return s=>{const o=s.base.path(),l=s.navigatorFactory(s.base);let i={};function a(d){return d.namespaceURI==="http://www.w3.org/2000/svg"}function c(d){if(d.defaultPrevented||d.button!==0||d.metaKey||d.altKey||d.ctrlKey||d.shiftKey)return;const y=d.composedPath().find(D=>D instanceof Node&&D.nodeName.toUpperCase()==="A");if(!y||t&&!y.hasAttribute("link"))return;const A=a(y),w=A?y.href.baseVal:y.href;if((A?y.target.baseVal:y.target)||!w&&!y.hasAttribute("state"))return;const v=(y.getAttribute("rel")||"").split(/\s+/);if(y.hasAttribute("download")||v&&v.includes("external"))return;const C=A?new URL(w,document.baseURI):new URL(w);if(!(C.origin!==window.location.origin||o&&C.pathname&&!C.pathname.toLowerCase().startsWith(o.toLowerCase())))return[y,C]}function f(d){const y=c(d);if(!y)return;const[A,w]=y,L=s.parsePath(w.pathname+w.search+w.hash),v=A.getAttribute("state");d.preventDefault(),l(L,{resolve:!1,replace:A.hasAttribute("replace"),scroll:!A.hasAttribute("noscroll"),state:v&&JSON.parse(v)})}function u(d){const y=c(d);if(!y)return;const[A,w]=y;typeof r=="function"&&(w.pathname=r(w.pathname)),i[w.pathname]||s.preloadRoute(w,{preloadData:A.getAttribute("preload")!=="false"})}function h(d){const y=c(d);if(!y)return;const[A,w]=y;typeof r=="function"&&(w.pathname=r(w.pathname)),!i[w.pathname]&&(i[w.pathname]=setTimeout(()=>{s.preloadRoute(w,{preloadData:A.getAttribute("preload")!=="false"}),delete i[w.pathname]},200))}function g(d){const y=c(d);if(!y)return;const[,A]=y;typeof r=="function"&&(A.pathname=r(A.pathname)),i[A.pathname]&&(clearTimeout(i[A.pathname]),delete i[A.pathname])}function E(d){if(d.defaultPrevented)return;let y=d.submitter&&d.submitter.hasAttribute("formaction")?d.submitter.getAttribute("formaction"):d.target.getAttribute("action");if(!y)return;if(!y.startsWith("https://action/")){const w=new URL(y,tt);if(y=s.parsePath(w.pathname+w.search),!y.startsWith(n))return}if(d.target.method.toUpperCase()!=="POST")throw new Error("Only POST forms are supported for Actions");const A=En.get(y);if(A){d.preventDefault();const w=new FormData(d.target);d.submitter&&d.submitter.name&&w.append(d.submitter.name,d.submitter.value),A.call({r:s,f:d.target},w)}}Ze(["click","submit"]),document.addEventListener("click",f),e&&(document.addEventListener("mouseover",h),document.addEventListener("mouseout",g),document.addEventListener("focusin",u),document.addEventListener("touchstart",u)),document.addEventListener("submit",E),Ce(()=>{document.removeEventListener("click",f),e&&(document.removeEventListener("mouseover",h),document.removeEventListener("mouseout",g),document.removeEventListener("focusin",u),document.removeEventListener("touchstart",u)),document.removeEventListener("submit",E)})}}function Nn(e){const t=()=>{const r=window.location.pathname+window.location.search;return{value:e.transformUrl?e.transformUrl(r)+window.location.hash:r+window.location.hash,state:window.history.state}},n=et();return An({get:t,set({value:r,replace:s,scroll:o,state:l}){s?window.history.replaceState(zt(l),"",r):window.history.pushState(l,"",r),Sn(decodeURIComponent(window.location.hash.slice(1)),o),$e()},init:r=>Pn(window,"popstate",Yt(r,s=>{if(s&&s<0)return!n.confirm(s);{const o=t();return!n.confirm(o.value,{state:o.state})}})),create:vn(e.preload,e.explicitLinks,e.actionBase,e.transformUrl),utils:{go:r=>window.history.go(r),beforeLeave:n}})(e)}var xn=Dt("<a>");function jn(e){e=Pe({inactiveClass:"inactive",activeClass:"active"},e);const[,t]=xt(e,["href","state","class","activeClass","inactiveClass","end"]),n=on(()=>e.href),r=ln(n),s=an(),o=x(()=>{const l=n();if(l===void 0)return[!1,!1];const i=K(l.split(/[?#]/,1)[0]).toLowerCase(),a=K(s.pathname).toLowerCase();return[e.end?i===a:a.startsWith(i+"/")||a===i,i===a]});return(()=>{var l=xn();return Bt(l,Pe(t,{get href(){return r()||e.href},get state(){return JSON.stringify(e.state)},get classList(){return{...e.class&&{[e.class]:!0},[e.inactiveClass]:!o()[0],[e.activeClass]:o()[0],...t.classList}},link:"",get"aria-current"(){return o()[1]?"page":void 0}}),!1),l})()}export{jn as A,Tn as F,Nn as R,Qe as S,_ as a,U as b,I as c,Ze as d,Mt as e,In as f,Ce as g,Kt as h,Ht as i,dt as j,x as k,Rn as l,Nt as m,Cn as o,$n as r,kn as s,Dt as t,an as u};
