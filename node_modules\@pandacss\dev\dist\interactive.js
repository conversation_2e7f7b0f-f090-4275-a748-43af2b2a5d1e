"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/interactive.ts
var interactive_exports = {};
__export(interactive_exports, {
  interactive: () => interactive
});
module.exports = __toCommonJS(interactive_exports);
var p = __toESM(require("@clack/prompts"));

// package.json
var version = "0.39.2";

// src/interactive.ts
var interactive = async () => {
  p.intro(`panda v${version}`);
  const initFlags = await p.group(
    {
      usePostcss: () => p.select({
        message: "Would you like to use PostCSS ?",
        initialValue: "yes",
        options: [
          { value: "yes", label: "Yes" },
          { value: "no", label: "No" }
        ]
      }),
      useMjsExtension: () => p.select({
        message: "Use the mjs extension ?",
        initialValue: "yes",
        options: [
          { value: "yes", label: "Yes" },
          { value: "no", label: "No" }
        ]
      }),
      jsxOptions: () => p.group({
        styleProps: () => p.select({
          message: "Would you like to use JSX Style Props ?",
          initialValue: "yes",
          options: [
            { value: "yes", label: "Yes" },
            { value: "no", label: "No" }
          ]
        }),
        jsxFramework: () => p.select({
          message: "What JSX framework?",
          initialValue: "react",
          options: [
            { value: "react", label: "React" },
            { value: "vue", label: "Vue" },
            { value: "solid", label: "Solid" },
            { value: "qwik", label: "Qwik" }
          ]
        })
      }),
      whatSyntax: () => p.select({
        message: "What css syntax would you like to use?",
        initialValue: "object",
        options: [
          { value: "object-literal", label: "Object" },
          { value: "template-literal", label: "Template literal" }
        ]
      }),
      withStrictTokens: () => p.select({
        message: "Use strict tokens to enforce full type-safety?",
        initialValue: "no",
        options: [
          { value: "yes", label: "Yes" },
          { value: "no", label: "No" }
        ]
      }),
      shouldUpdateGitignore: () => p.select({
        message: "Update gitignore?",
        initialValue: "yes",
        options: [
          { value: "yes", label: "Yes" },
          { value: "no", label: "No" }
        ]
      })
    },
    {
      // On Cancel callback that wraps the group
      // So if the user cancels one of the prompts in the group this function will be called
      onCancel: () => {
        p.cancel("Operation cancelled.");
        process.exit(0);
      }
    }
  );
  p.outro("Let's get started! \u{1F43C}");
  return {
    postcss: initFlags.usePostcss === "yes",
    outExtension: initFlags.useMjsExtension === "yes" ? "mjs" : "js",
    jsxFramework: initFlags.jsxOptions.jsxFramework,
    syntax: initFlags.whatSyntax,
    strictTokens: initFlags.withStrictTokens === "yes",
    gitignore: initFlags.shouldUpdateGitignore === "yes"
  };
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  interactive
});
