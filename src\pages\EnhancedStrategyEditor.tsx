import { createEffect, createSignal, For, Show } from 'solid-js'
import { strategyStore } from '../stores'
import CodeEditor from '../components/CodeEditor'
import AIAssistant from '../components/AIAssistant'
import { useTheme } from '../context/ThemeContext'

export default function EnhancedStrategyEditor() {
  const { theme } = useTheme()
  const strategyState = () => strategyStore.state

  // 状态管理
  const [selectedStrategy, setSelectedStrategy] = createSignal<any>(null)
  const [, setIsCreating] = createSignal(false)
  const [searchTerm, setSearchTerm] = createSignal('')
  const [currentCode, setCurrentCode] = createSignal('')
  const [strategyName, setStrategyName] = createSignal('')
  const [strategyDescription, setStrategyDescription] = createSignal('')
  const [strategyType, setStrategyType] = createSignal('custom')
  const [isSaving, setIsSaving] = createSignal(false)
  const [activeTab, setActiveTab] = createSignal<'list' | 'editor'>('list')

  // 初始化数据
  createEffect(() => {
    if (strategyState().strategies.length === 0) {
      strategyStore.fetchStrategies()
    }
  })

  // 过滤策略
  const filteredStrategies = () => {
    const strategies = strategyState().strategies
    const term = searchTerm().toLowerCase()
    if (!term) return strategies
    return strategies.filter(strategy => 
      strategy.name.toLowerCase().includes(term) ||
      strategy.description.toLowerCase().includes(term)
    )
  }

  // 处理创建新策略
  const handleCreateStrategy = () => {
    setIsCreating(true)
    setSelectedStrategy(null)
    setCurrentCode(`# 新策略模板
import pandas as pd
import numpy as np

def strategy_logic(data):
    """
    策略逻辑函数
    
    参数:
    data: DataFrame - 包含 OHLCV 数据
    
    返回:
    DataFrame - 包含交易信号的数据
    """
    # 在这里编写您的策略逻辑
    data['signal'] = 0  # 0: 无操作, 1: 买入, -1: 卖出
    
    # 示例：简单移动平均策略
    data['ma_short'] = data['close'].rolling(5).mean()
    data['ma_long'] = data['close'].rolling(20).mean()
    
    # 生成信号
    data['signal'] = np.where(data['ma_short'] > data['ma_long'], 1, 0)
    
    return data

# 策略参数
PARAMETERS = {
    'short_window': 5,
    'long_window': 20
}
`)
    setStrategyName('')
    setStrategyDescription('')
    setStrategyType('custom')
    setActiveTab('editor')
  }

  // 处理选择策略
  const handleSelectStrategy = (strategy: any) => {
    setSelectedStrategy(strategy)
    setIsCreating(false)
    setCurrentCode(strategy.code || '')
    setStrategyName(strategy.name)
    setStrategyDescription(strategy.description)
    setStrategyType(strategy.type)
    setActiveTab('editor')
  }

  // 保存策略
  const handleSaveStrategy = async () => {
    if (!strategyName().trim()) {
      alert('请输入策略名称')
      return
    }

    setIsSaving(true)
    try {
      const strategyData = {
        name: strategyName(),
        description: strategyDescription(),
        code: currentCode(),
        type: strategyType() as any,
        status: 'draft' as const
      }

      if (selectedStrategy()) {
        await strategyStore.updateStrategy({ ...strategyData, id: selectedStrategy().id })
      } else {
        await strategyStore.createStrategy(strategyData)
        setIsCreating(false)
      }
      
      alert('策略保存成功！')
    } catch (error) {
      alert('保存失败：' + (error as Error).message)
    } finally {
      setIsSaving(false)
    }
  }

  // 删除策略
  const handleDeleteStrategy = async (strategy: any) => {
    if (confirm(`确定要删除策略 "${strategy.name}" 吗？`)) {
      try {
        await strategyStore.deleteStrategy(strategy.id)
        if (selectedStrategy()?.id === strategy.id) {
          setSelectedStrategy(null)
          setActiveTab('list')
        }
        alert('策略删除成功！')
      } catch (error) {
        alert('删除失败：' + (error as Error).message)
      }
    }
  }

  return (
    <div style={{
      width: '100%',
      maxWidth: '1400px',
      margin: '0 auto',
      padding: '24px',
      height: '100vh',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* 头部 */}
      <div style={{
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '2rem',
          fontWeight: 'bold',
          color: theme() === 'dark' ? '#f3f4f6' : '#111827',
          marginBottom: '8px'
        }}>
          策略编辑器
        </h1>
        <p style={{
          color: theme() === 'dark' ? '#9ca3af' : '#6b7280'
        }}>
          创建、编辑和管理您的量化交易策略
        </p>
      </div>

      {/* 标签页 */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid',
        borderColor: theme() === 'dark' ? '#374151' : '#e5e7eb',
        marginBottom: '24px'
      }}>
        <button
          style={{
            padding: '12px 24px',
            fontSize: '14px',
            fontWeight: '500',
            borderBottom: '2px solid',
            borderColor: activeTab() === 'list' ? '#3b82f6' : 'transparent',
            color: activeTab() === 'list' ? '#3b82f6' : theme() === 'dark' ? '#9ca3af' : '#6b7280',
            cursor: 'pointer',
            background: 'none',
            border: 'none'
          }}
          onClick={() => setActiveTab('list')}
        >
          策略列表
        </button>
        <button
          style={{
            padding: '12px 24px',
            fontSize: '14px',
            fontWeight: '500',
            borderBottom: '2px solid',
            borderColor: activeTab() === 'editor' ? '#3b82f6' : 'transparent',
            color: activeTab() === 'editor' ? '#3b82f6' : theme() === 'dark' ? '#9ca3af' : '#6b7280',
            cursor: 'pointer',
            background: 'none',
            border: 'none'
          }}
          onClick={() => setActiveTab('editor')}
        >
          代码编辑器
        </button>
      </div>

      {/* 内容区域 */}
      <div style={{ flex: 1, display: 'flex', gap: '24px' }}>
        {/* 策略列表 */}
        <Show when={activeTab() === 'list'}>
          <div style={{ width: '100%' }}>
            {/* 搜索和创建 */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '20px'
            }}>
              <input
                type="text"
                placeholder="搜索策略..."
                value={searchTerm()}
                onInput={(e) => setSearchTerm(e.target.value)}
                style={{
                  padding: '8px 12px',
                  border: '1px solid',
                  borderColor: theme() === 'dark' ? '#374151' : '#d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  width: '300px',
                  backgroundColor: theme() === 'dark' ? '#1f2937' : 'white',
                  color: theme() === 'dark' ? '#f3f4f6' : '#111827'
                }}
              />
              <button
                onClick={handleCreateStrategy}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                + 创建策略
              </button>
            </div>

            {/* 策略网格 */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
              gap: '16px'
            }}>
              <For each={filteredStrategies()}>
                {(strategy) => (
                  <div style={{
                    border: '1px solid',
                    borderColor: theme() === 'dark' ? '#374151' : '#e5e7eb',
                    borderRadius: '8px',
                    padding: '16px',
                    backgroundColor: theme() === 'dark' ? '#1f2937' : 'white',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      marginBottom: '12px'
                    }}>
                      <h3 style={{
                        fontSize: '1.1rem',
                        fontWeight: '600',
                        color: theme() === 'dark' ? '#f3f4f6' : '#111827'
                      }}>
                        {strategy.name}
                      </h3>
                      <span style={{
                        padding: '2px 8px',
                        fontSize: '12px',
                        borderRadius: '12px',
                        backgroundColor: strategy.status === 'active' ? '#dcfce7' : '#f3f4f6',
                        color: strategy.status === 'active' ? '#15803d' : '#6b7280'
                      }}>
                        {strategy.status}
                      </span>
                    </div>
                    <p style={{
                      color: theme() === 'dark' ? '#9ca3af' : '#6b7280',
                      fontSize: '14px',
                      marginBottom: '12px',
                      lineHeight: '1.4'
                    }}>
                      {strategy.description}
                    </p>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <button
                        onClick={() => handleSelectStrategy(strategy)}
                        style={{
                          padding: '6px 12px',
                          backgroundColor: '#3b82f6',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          fontSize: '12px',
                          cursor: 'pointer'
                        }}
                      >
                        编辑
                      </button>
                      <button
                        onClick={() => handleDeleteStrategy(strategy)}
                        style={{
                          padding: '6px 12px',
                          backgroundColor: '#ef4444',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          fontSize: '12px',
                          cursor: 'pointer'
                        }}
                      >
                        删除
                      </button>
                    </div>
                  </div>
                )}
              </For>
            </div>
          </div>
        </Show>

        {/* 代码编辑器 */}
        <Show when={activeTab() === 'editor'}>
          <div style={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* 策略信息表单 */}
            <div style={{
              backgroundColor: theme() === 'dark' ? '#1f2937' : '#f9fafb',
              padding: '16px',
              borderRadius: '8px',
              marginBottom: '16px'
            }}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '16px',
                marginBottom: '12px'
              }}>
                <input
                  type="text"
                  placeholder="策略名称"
                  value={strategyName()}
                  onInput={(e) => setStrategyName(e.target.value)}
                  style={{
                    padding: '8px 12px',
                    border: '1px solid',
                    borderColor: theme() === 'dark' ? '#374151' : '#d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: theme() === 'dark' ? '#374151' : 'white',
                    color: theme() === 'dark' ? '#f3f4f6' : '#111827'
                  }}
                />
                <select
                  value={strategyType()}
                  onChange={(e) => setStrategyType(e.target.value)}
                  style={{
                    padding: '8px 12px',
                    border: '1px solid',
                    borderColor: theme() === 'dark' ? '#374151' : '#d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: theme() === 'dark' ? '#374151' : 'white',
                    color: theme() === 'dark' ? '#f3f4f6' : '#111827'
                  }}
                >
                  <option value="custom">自定义策略</option>
                  <option value="trend">趋势策略</option>
                  <option value="mean_reversion">均值回归</option>
                  <option value="arbitrage">套利策略</option>
                </select>
              </div>
              <textarea
                placeholder="策略描述"
                value={strategyDescription()}
                onInput={(e) => setStrategyDescription(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid',
                  borderColor: theme() === 'dark' ? '#374151' : '#d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  resize: 'vertical',
                  minHeight: '60px',
                  backgroundColor: theme() === 'dark' ? '#374151' : 'white',
                  color: theme() === 'dark' ? '#f3f4f6' : '#111827'
                }}
              />
              <div style={{
                display: 'flex',
                justifyContent: 'flex-end',
                gap: '8px',
                marginTop: '12px'
              }}>
                <button
                  onClick={handleSaveStrategy}
                  disabled={isSaving()}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#22c55e',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    opacity: isSaving() ? 0.5 : 1
                  }}
                >
                  {isSaving() ? '保存中...' : '保存策略'}
                </button>
              </div>
            </div>

            {/* 代码编辑器 */}
            <div style={{ flex: 1 }}>
              <CodeEditor
                value={currentCode()}
                language="python"
                theme={theme()}
                height="500px"
                onChange={setCurrentCode}
                onSave={handleSaveStrategy}
              />
            </div>
          </div>
        </Show>
      </div>

      {/* AI 助手 */}
      <AIAssistant
        currentCode={currentCode()}
        onCodeGenerate={setCurrentCode}
        theme={theme()}
      />
    </div>
  )
}
