const fs = require('fs');
const path = require('path');

console.log('🚨 Emergency repair of corrupted codebase...');

function repairFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  const originalContent = content;

  // 1. Fix quoted CSS properties with colon inside quotes
  // 'property: value' -> 'property': value
  const quotedPropRegex = /'([a-zA-Z-]+):\s*([^']*?)'/g;
  content = content.replace(quotedPropRegex, "'$1': $2");
  if (content !== originalContent) modified = true;

  // 2. Fix broken ternary expressions in strings
  // ? '#color: 'other' -> ? '#color' : 'other'
  content = content.replace(/\?\s*'([^']*?):\s*'([^']*?)'/g, "? '$1' : '$2'");
  if (content !== originalContent) modified = true;

  // 3. Fix optional type annotations
  // theme? ' : 'type' -> theme?: 'type'
  content = content.replace(/(\w+)\?\s*'\s*:\s*'/g, '$1?: ');
  if (content !== originalContent) modified = true;

  // 4. Fix CSS property names with quotes around the whole thing
  // 'display: 'flex' -> display: 'flex'
  content = content.replace(/'([a-zA-Z-]+):\s*'([^']*?)'/g, "$1: '$2'");
  if (content !== originalContent) modified = true;

  // 5. Fix broken object property syntax
  // 'prop: value -> 'prop': value
  content = content.replace(/'([a-zA-Z-]+):\s*([^,}]+)/g, "'$1': $2");
  if (content !== originalContent) modified = true;

  // 6. Fix missing colons in ternary expressions
  // condition ?'value1' 'value2' -> condition ? 'value1' : 'value2'
  content = content.replace(/\?\s*'([^']*?)'\s+'([^']*?)'/g, "? '$1' : '$2'");
  if (content !== originalContent) modified = true;

  // 7. Fix broken arrow function return types
  // ()' : Type => -> (): Type =>
  content = content.replace(/\)\s*'\s*:\s*([A-Za-z_][A-Za-z0-9_]*)\s*=>/g, '): $1 =>');
  if (content !== originalContent) modified = true;

  // 8. Fix CSS property values missing quotes
  // 'property': value' -> 'property': 'value'
  content = content.replace(/'([a-zA-Z-]+)':\s*([a-zA-Z0-9#%.-]+)'/g, "'$1': '$2'");
  if (content !== originalContent) modified = true;

  // 9. Fix unquoted CSS values
  // 'property': value, -> 'property': 'value',
  content = content.replace(/'([a-zA-Z-]+)':\s*([a-zA-Z0-9#%.-]+),/g, "'$1': '$2',");
  if (content !== originalContent) modified = true;

  // 10. Fix unquoted CSS values at end of object
  // 'property': value } -> 'property': 'value' }
  content = content.replace(/'([a-zA-Z-]+)':\s*([a-zA-Z0-9#%.-]+)\s*}/g, "'$1': '$2' }");
  if (content !== originalContent) modified = true;

  // 11. Fix broken object property syntax in arrays/function calls
  // 'key: value -> 'key': value
  content = content.replace(/'([a-zA-Z0-9_-]+):\s*([^,\)]+)/g, "'$1': $2");
  if (content !== originalContent) modified = true;

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }
  return false;
}

function findTsxFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Find all TypeScript/TSX files in src directory
const srcDir = path.join(__dirname, 'src');
if (!fs.existsSync(srcDir)) {
  console.error('❌ src directory not found');
  process.exit(1);
}

const files = findTsxFiles(srcDir);
console.log(`📁 Found ${files.length} TypeScript/TSX files`);

let repairedCount = 0;
for (const file of files) {
  try {
    if (repairFile(file)) {
      console.log(`✅ Repaired: ${path.relative(__dirname, file)}`);
      repairedCount++;
    }
  } catch (error) {
    console.error(`❌ Error repairing ${file}:`, error.message);
  }
}

console.log(`\n🔧 Emergency repair complete: ${repairedCount}/${files.length} files repaired`);
