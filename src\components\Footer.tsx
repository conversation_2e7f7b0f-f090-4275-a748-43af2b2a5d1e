export default function Footer() {
  return (
    <footer style={{
      background: "white",
      borderTop: "1px solid #e5e7eb",
      padding: "24px 0"
    }}>
      <div style={{
        maxWidth: "1280px",
        margin: "0 auto",
        padding: "0 16px",
        textAlign: "center"
      }}>
        <div style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "space-between",
          gap: "16px"
        }}>
          {/* 版权信息 */}
          <div style={{
            color: "#6b7280",
            fontSize: "14px"
          }}>
            © 2024 量化交易前端平台. 基于 SolidJS 构建，专注性能与体验.
          </div>

          {/* 技术栈信息 */}
          <div style={{
            display: "flex",
            alignItems: "center",
            gap: "16px",
            fontSize: "12px",
            color: "#9ca3af"
          }}>
            <span style={{
              display: "flex",
              alignItems: "center",
              gap: "4px"
            }}>
              <div style={{
                width: "8px",
                height: "8px",
                background: "#3b82f6",
                borderRadius: "50%"
              }} />
              SolidJS
            </span>
            <span style={{
              display: "flex",
              alignItems: "center",
              gap: "4px"
            }}>
              <div style={{
                width: "8px",
                height: "8px",
                background: "#22c55e",
                borderRadius: "50%"
              }} />
              TypeScript
            </span>
            <span style={{
              display: "flex",
              alignItems: "center",
              gap: "4px"
            }}>
              <div style={{
                width: "8px",
                height: "8px",
                background: "#f59e0b",
                borderRadius: "50%"
              }} />
              Vite
            </span>
          </div>
        </div>
      </div>
    </footer>
  );
}
