import { defineConfig } from 'vite';
import solidPlugin from 'vite-plugin-solid';
import path from 'path';

export default defineConfig({
  plugins: [
    solidPlugin(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/pages': path.resolve(__dirname, './src/pages'),
      '@/stores': path.resolve(__dirname, './src/stores'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/context': path.resolve(__dirname, './src/context'),
      '@/workers': path.resolve(__dirname, './src/workers'),
    },
  },
  build: {
    target: 'esnext',
    minify: 'esbuild',
    sourcemap: false,
    rollupOptions: {
      external: ['@xenova/transformers'], // 将AI模型标记为外部依赖
      output: {
        manualChunks: {
          // 核心框架
          'vendor-solid': ['solid-js', '@solidjs/router'],
          // 状态管理
          'vendor-state': ['jotai'],
          // 图表库
          'vendor-charts': ['lightweight-charts'],
          // 代码编辑器
          'vendor-editor': [
            '@codemirror/basic-setup',
            '@codemirror/view',
            '@codemirror/state',
            '@codemirror/lang-python',
            '@codemirror/lang-javascript',
            '@codemirror/theme-one-dark',
            'monaco-editor',
            '@monaco-editor/loader'
          ],
          // 工具库
          'vendor-utils': ['date-fns', 'decimal.js', 'big.js', 'numeral', 'uuid'],
          // 网络通信
          'vendor-network': ['socket.io-client']
        }
      }
    }
  },

  server: {
    port: 3000,
    host: '0.0.0.0',
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        // 不需要重写路径，因为 http.ts 已经包含了 /api/v1
        // rewrite: (path) => path.replace(/^\/api/, '/api/v1'),
      },
    },
  },
  optimizeDeps: {
    include: [
      'solid-js',
      '@solidjs/router',
      'jotai',
      'decimal.js',
      'date-fns',
      'lightweight-charts',
      'socket.io-client'
    ],
    exclude: [
      '@xenova/transformers' // AI模型需要动态加载
    ]
  },
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
  },
  // 支持Web Workers和WASM
  worker: {
    format: 'es'
  },
  // 支持Transformers.js的WASM和模型文件
  assetsInclude: ['**/*.wasm', '**/*.onnx', '**/*.bin'],
});
