import { createSignal, createEffect, onCleanup, onMount, JSX, Show, For } from 'solid-js';

// 响应式断点
export type Breakpoint ='mobile' |'tablet' |'desktop' |'large';

export interface ResponsiveLayoutProps {
  children?: JSX.Element;
  theme?:'light' |'dark';
  showSidebar?: boolean;
  sidebarContent?: JSX.Element;
  headerContent?: JSX.Element;
  footerContent?: JSX.Element;
  className?: string;
}

// 断点配置
const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
  large: 1536,
};

// 获取当前断点
export function getCurrentBreakpoint(width: number)' : Breakpoint {
  if (width < BREAKPOINTS.mobile) return'mobile';
  if (width < BREAKPOINTS.tablet) return'tablet';
  if (width < BREAKPOINTS.desktop) return'desktop';
  return'large';
}

// 响应式布局Hook
export function useResponsive() {
  const [windowSize, setWindowSize] = createSignal({ 
    width: typeof window !=='undefined' ? window.innerWidth : 1024,
    height: typeof window !=='undefined' ? window.innerHeight : 768,
  });
  
  const [breakpoint, setBreakpoint] = createSignal<Breakpoint>(
    getCurrentBreakpoint(windowSize().width)
  );

  const handleResize = () => {
    const newSize = {
      width: window.innerWidth,
      height: window.innerHeight,
    };
    setWindowSize(newSize);
    setBreakpoint(getCurrentBreakpoint(newSize.width));
  };

  onMount(() => {
    window.addEventListener('resize', handleResize);
    onCleanup(() => {
      window.removeEventListener('resize', handleResize);
    });
  });

  return {
    windowSize,
    breakpoint,
    isMobile: () => breakpoint() ==='mobile',
    isTablet: () => breakpoint() ==='tablet',
    isDesktop: () => breakpoint() ==='desktop' || breakpoint() ==='large',
    isLarge: () => breakpoint() ==='large',
  };
}

// 主响应式布局组件
export default function ResponsiveLayout(props: ResponsiveLayoutProps) {
  const { windowSize, breakpoint, isMobile, isTablet, isDesktop } = useResponsive();
  const [sidebarOpen, setSidebarOpen] = createSignal(false);
  const [touchStart, setTouchStart] = createSignal<{ x: number; y: number } | null>(null);

  // 移动端侧边栏手势控制
  const handleTouchStart = (e: TouchEvent) => {
    setTouchStart({
      x: e.touches[0].clientX,
      y: e.touches[0].clientY,
    });
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!touchStart()) return;

    const deltaX = e.touches[0].clientX - touchStart()!.x;
    const deltaY = e.touches[0].clientY - touchStart()!.y;

    // 水平滑动距离大于垂直滑动距离，且滑动距离超过50px
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      if (deltaX > 0 && touchStart()!.x < 20) {
        // 从左边缘向右滑动，打开侧边栏
        setSidebarOpen(true);
      } else if (deltaX < 0 && sidebarOpen()) {
        // 向左滑动，关闭侧边栏
        setSidebarOpen(false);
      }
      setTouchStart(null);
    }
  };

  const handleTouchEnd = () => {
    setTouchStart(null);
  };

  // 移动端添加触摸事件监听
  onMount(() => {
    if (isMobile()) {
      document.addEventListener('touchstart', handleTouchStart);
      document.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('touchend', handleTouchEnd);

      onCleanup(() => {
        document.removeEventListener('touchstart', handleTouchStart);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      });
    }
  });

  // 响应式样式
  const containerStyle = () => ({
    display:'flex',
    flexDirection:'column',
    minHeight:'100vh',
    background: props.theme ==='dark' ?'#111827' :'#f9fafb',
    color: props.theme ==='dark' ?'#f9fafb':'#111827',
  });

  const headerStyle = () => ({
    zIndex:'1000',
    background: props.theme ==='dark' ?'#1f2937':'#ffffff',
    borderBottom: `1px solid ${props.theme ==='dark' ?'#374151' :'#e5e7eb'}`,
    position:'sticky' as const,
    top:'0',
  });

  const mainStyle = () => ({
    display:'flex',
    flex:'1',
    minHeight:'0',
  });

  const sidebarStyle = () => ({
    width: isDesktop() ?'280px' :''100%',
    background: props.theme ==='dark' ?'#1f2937':'#ffffff',
    borderRight: isDesktop() ? `1px solid ${props.theme ==='dark' ?'#374151' :'#e5e7eb'}`':'none',
    overflowY:'auto',
    transition:'all 0.3s ease-in-out',
    ...(isMobile() && {
      position:'fixed' as const,
      top:'0',
      left: sidebarOpen() ?'0' :''-100%',
      bottom:'0',
      zIndex:'1001',
      boxShadow: sidebarOpen() ?'0 0 20px rgba(0, 0, 0, 0.3)' :'none',
    }),
    ...(isTablet() && {
      position:'fixed' as const,
      top:'0',
      left: sidebarOpen() ?'0' :'-280px',
      bottom:'0',
      width:'280px',
      zIndex:'1001',
      boxShadow: sidebarOpen() ?'0 0 20px rgba(0, 0, 0, 0.3)' :'none',
    }),
  });

  const contentStyle = () => ({
    flex:'1',
    minHeight:'0',
    overflowY:'auto',
    padding: isMobile() ?'16px' :'24px',
  });

  const footerStyle = () => ({
    background: props.theme ==='dark' ?'#1f2937':'#ffffff',
    borderTop: `1px solid ${props.theme ==='dark' ?'#374151' :'#e5e7eb'}`,
    padding: isMobile() ?'16px' :'24px',
  });

  // 移动端汉堡菜单按钮
  const MobileMenuButton = () => (
    <Show when={!isDesktop() && props.showSidebar}>
      <button
        onClick={() => setSidebarOpen(!sidebarOpen())}
        style={{
          position:'fixed',
          top:'16px',
          left:'16px',
          zIndex:'1002',
          width:'44px',
          height:'44px',
          borderRadius:'8px',
          border:'none',
          background: props.theme ==='dark' ?'#374151' :'#f3f4f6',
          color: props.theme ==='dark' ?'#f9fafb':'#111827',
          cursor:'pointer',
          display:'flex',
          alignItems:'center',
          justifyContent:'center',
          boxShadow:'0 2px 4px rgba(0, 0, 0, 0.1)',
          transition:'all 0.2s',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform ='scale(1.05)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform ='scale(1)';
        }}
      >
        <svg
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          {sidebarOpen() ? (
            // 关闭图标
            <>
              <path d="m18 6-12 12" />
              <path d="m6 6 12 12" />
            </>
          ) : (
            // 汉堡图标
            <>
              <path d="M3 12h18" />
              <path d="M3 6h18" />
              <path d="M3 18h18" />
            </>
          )}
        </svg>
      </button>
    </Show>
  );

  // 侧边栏遮罩层
  const SidebarOverlay = () => (
    <Show when={!isDesktop() && sidebarOpen()}>
      <div
        onClick={() => setSidebarOpen(false)}
        style={{
          position:'fixed',
          top:'0',
          left:'0',
          right:'0',
          bottom:'0',
          background:'rgba(0, 0, 0, 0.5)',
          zIndex:'1000',
        }}
      />
    </Show>
  );

  return (
    <div style={containerStyle()} class={props.className}>
      {/* 移动端菜单按钮 */}
      <MobileMenuButton />

      {/* 侧边栏遮罩层 */}
      <SidebarOverlay />

      {/* 头部 */}
      <Show when={props.headerContent}>
        <header style={headerStyle()}>
          {props.headerContent}
        </header>
      </Show>

      {/* 主要内容区域 */}
      <div style={mainStyle()}>
        {/* 侧边栏 */}
        <Show when={props.showSidebar}>
          <aside style={sidebarStyle()}>
            <Show when={props.sidebarContent} fallback={
              <div style={{ padding:'24px' }}>
                <div style={{
                  color: props.theme ==='dark' ?'#9ca3af':'#6b7280',
                  textAlign:'center',
                }}>
                  侧边栏内容
                </div>
              </div>
            }>
              {props.sidebarContent}
            </Show>
          </aside>
        </Show>

        {/* 主内容区域 */}
        <main style={contentStyle()}>
          {props.children}
        </main>
      </div>

      {/* 底部 */}
      <Show when={props.footerContent}>
        <footer style={footerStyle()}>
          {props.footerContent}
        </footer>
      </Show>

      {/* 调试信息 (开发模式) */}
      <Show when={import.meta.env?.DEV}>
        <div style={{
          position:'fixed',
          bottom:'16px',
          right:'16px',
          padding:'8px 12px',
          background:'rgba(0, 0, 0, 0.8)',
          color:'white',
          borderRadius:'4px',
          fontSize:'12px',
          fontFamily:'monospace',
          zIndex:'9999',
          pointerEvents:'none',
        }}>
          {breakpoint()} ({windowSize().width}x{windowSize().height})
        </div>
      </Show>
    </div>
  );
}

// 响应式网格系统
export interface GridProps {
  children: JSX.Element[];
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
    large?: number;
  };
  gap?: string;
  theme?:'light' |'dark';
}

export function ResponsiveGrid(props: GridProps) {
  const { breakpoint } = useResponsive();

  const getColumns = () => {
    const bp = breakpoint();
    const cols = props.columns || {};
    
    switch (bp) {
      case'mobile' : return cols.mobile || 1;
      case'tablet' : return cols.tablet || 2;
      case'desktop' : return cols.desktop || 3;
      case'large' : return cols.large || 4;
      default: return 1;
    }
  };

  return (
    <div style={{
      display:'grid',
     'grid-template-columns': `repeat(${getColumns()}, 1fr)`,
      gap: props.gap ||'24px',
      width:''100%',
    }}>
      <For each={props.children}>
        {(child) => child}
      </For>
    </div>
  );
}

// 响应式容器
export interface ContainerProps {
  children: JSX.Element;
  maxWidth?: string;
  padding?: string;
  center?: boolean;
}

export function ResponsiveContainer(props: ContainerProps) {
  const { isMobile, isTablet } = useResponsive();

  const containerStyle = () => ({
    width:''100%',
    maxWidth: props.maxWidth ||'1280px',
    padding: props.padding || (isMobile() ?'16px' : isTablet() ?'24px' :'32px'),
    ...(props.center && {
      marginLeft:'auto',
      marginRight:'auto',
    }),
  });

  return (
    <div style={containerStyle()}>
      {props.children}
    </div>
  );
}

// 响应式文本
export interface ResponsiveTextProps {
  children: JSX.Element | string;
  size?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
    large?: string;
  };
  weight?: string;
  color?: string;
  theme?:'light' |'dark';
}

export function ResponsiveText(props: ResponsiveTextProps) {
  const { breakpoint } = useResponsive();

  const getFontSize = () => {
    const bp = breakpoint();
    const sizes = props.size || {};
    
    switch (bp) {
      case'mobile' : return sizes.mobile ||'14px';
      case'tablet' : return sizes.tablet ||'16px';
      case'desktop' : return sizes.desktop ||'18px';
      case'large' : return sizes.large ||'20px';
      default: return'16px';
    }
  };

  return (
    <span style={{
     'font-size': getFontSize(),
      font-weight: props.weight,
      color: props.color || (props.theme ==='dark' ?'#f9fafb':'#111827'),
    }}>
      {props.children}
    </span>
  );
}

// 导出类型和工具函数
export { BREAKPOINTS };