const fs = require('fs');
const path = require('path');

// CSS属性映射表
const cssPropertyMap = {
  'backgroundColor': 'background-color',
  'borderRadius': 'border-radius',
  'fontSize': 'font-size',
  'fontWeight': 'font-weight',
  'textAlign': 'text-align',
  'marginTop': 'margin-top',
  'marginBottom': 'margin-bottom',
  'marginLeft': 'margin-left',
  'marginRight': 'margin-right',
  'paddingTop': 'padding-top',
  'paddingBottom': 'padding-bottom',
  'paddingLeft': 'padding-left',
  'paddingRight': 'padding-right',
  'borderBottom': 'border-bottom',
  'borderTop': 'border-top',
  'borderLeft': 'border-left',
  'borderRight': 'border-right',
  'overflowY': 'overflow-y',
  'overflowX': 'overflow-x',
  'textDecoration': 'text-decoration',
  'flexDirection': 'flex-direction',
  'alignItems': 'align-items',
  'justifyContent': 'justify-content',
  'flexWrap': 'flex-wrap',
  'gridTemplateColumns': 'grid-template-columns',
  'gridGap': 'grid-gap',
  'lineHeight': 'line-height',
  'boxShadow': 'box-shadow',
  'borderColor': 'border-color',
  'borderStyle': 'border-style',
  'borderWidth': 'border-width',
  'maxWidth': 'max-width',
  'minWidth': 'min-width',
  'maxHeight': 'max-height',
  'minHeight': 'min-height',
  'whiteSpace': 'white-space',
  'wordBreak': 'word-break',
  'alignSelf': 'align-self',
  'justifySelf': 'justify-self',
  'pointerEvents': 'pointer-events',
  'userSelect': 'user-select',
  'objectFit': 'object-fit',
  'textTransform': 'text-transform',
  'letterSpacing': 'letter-spacing',
  'zIndex': 'z-index'
};

function fixCssPropertiesInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // 先进行一次通用修复：移除对象属性键名后面意外多出来的单引号，例如 fontSize' => fontSize
  // 仅匹配以标识符开头、紧跟一个单引号后面就是冒号的场景，避免误伤字符串内容
  const trailingQuoteKeyRegex = /([A-Za-z_][A-Za-z0-9_]*)'(\s*:)/g;
  const contentAfterTrailingFix = content.replace(trailingQuoteKeyRegex, '$1$2');
  if (contentAfterTrailingFix !== content) {
    content = contentAfterTrailingFix;
    modified = true;
  }

  // 通用修复2（更安全）：仅在冒号后紧跟单引号的情况下，修正缺失的字符串结束引号
  // 例：? '#1890ff : '#666' -> ? '#1890ff' : '#666'
  const unterminatedSafe = /'([A-Za-z0-9#._-]+)\s*:\s*'/g;
  const contentAfterSafe = content.replace(unterminatedSafe, "'$1' : '");
  if (contentAfterSafe !== content) {
    content = contentAfterSafe;
    modified = true;
  }

  // 纠正由于上一步可能造成的 URL 被拆分的问题：'https' :// 或 'http' :// -> 'https://', 'http://'
  content = content.replace(/'https'\s*:\/\//g, "'https://");
  content = content.replace(/'http'\s*:\/\//g, "'http://");

  // 纠正被拆分的时间字符串：'22' :00' -> '22:00'
  content = content.replace(/'(\d{1,2})'\s*:\s*(\d{2})'/g, "'$1:$2'");

  // 规范三元运算符内的字符串字面量（两侧都应有成对引号）
  // 例：? '64px : '240px' -> ? '64px' : '240px'
  const ternaryStringFix = /(\?|:)\s*'([^']+)\s*:\s*'([^']+)'/g;
  const contentAfterTernaryFix = content.replace(ternaryStringFix, (m, lead, left, right) => `${lead} '${left}' : '${right}'`);
  if (contentAfterTernaryFix !== content) {
    content = contentAfterTernaryFix;
    modified = true;
  }

  // 修正伪类键名被错误放置引号的情况：'&:hover: { -> '&:hover': {；'&:last-child: { -> '&:last-child': {
  content = content.replace(/'&:hover\s*:\s*\{/g, "'&:hover': {");
  content = content.replace(/'&:last-child\s*:\s*\{/g, "'&:last-child': {");

  // 对于Panda CSS，我们需要将kebab-case转换为camelCase
  // 检查文件是否使用了css()函数
  const usesPandaCSS = content.includes('css({') || content.includes('from \'../../styled-system/css\'') || content.includes('from \'../styled-system/css\'');

  if (usesPandaCSS) {
    // 处理Panda CSS - 将kebab-case转换为camelCase
    const pandaCssRegex = /css\(\{([^}]*(?:\{[^}]*\}[^}]*)*)\}\)/gs;

    content = content.replace(pandaCssRegex, (match, cssContent) => {
      let newCssContent = cssContent;

      // 将kebab-case属性转换为camelCase
      for (const [camelCase, kebabCase] of Object.entries(cssPropertyMap)) {
        const kebabRegex = new RegExp(`(['"])${kebabCase}(['"]\\s*:)`, 'g');
        if (kebabRegex.test(newCssContent)) {
          newCssContent = newCssContent.replace(kebabRegex, `${camelCase}$2`);
          modified = true;
        }
      }

      return `css({${newCssContent}})`;
    });
  } else {
    // 处理普通style对象 - 将camelCase转换为kebab-case
    const styleRegex = /style\s*=\s*\{\{([^}]*(?:\{[^}]*\}[^}]*)*)\}\}/gs;

    content = content.replace(styleRegex, (match, styleContent) => {
      let newStyleContent = styleContent;

      for (const [camelCase, kebabCase] of Object.entries(cssPropertyMap)) {
        const camelRegex = new RegExp(`(\\s|^)${camelCase}(\\s*:)`, 'g');
        if (camelRegex.test(newStyleContent)) {
          newStyleContent = newStyleContent.replace(camelRegex, (match, prefix, suffix) => {
            modified = true;
            return `${prefix}'${kebabCase}'${suffix}`;
          });
        }
      }

      return `style={{${newStyleContent}}}`;
    });
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed CSS properties in: ${path.relative(process.cwd(), filePath)}`);
    return true;
  }

  return false;
}

function processDirectory(dir) {
  let fixedCount = 0;
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      fixedCount += processDirectory(filePath);
    } else if (stat.isFile() && (file.endsWith('.tsx') || file.endsWith('.jsx'))) {
      if (fixCssPropertiesInFile(filePath)) {
        fixedCount++;
      }
    }
  }
  
  return fixedCount;
}

// 执行修复
console.log('🔧 Fixing CSS property names in TSX/JSX files...\n');
const srcDir = path.join(__dirname, 'src');
const fixedCount = processDirectory(srcDir);
console.log(`\n✨ Fixed ${fixedCount} files with CSS property issues.`);