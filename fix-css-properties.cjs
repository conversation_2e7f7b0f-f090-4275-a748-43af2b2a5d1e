const fs = require('fs');
const path = require('path');

// CSS属性映射表
const cssPropertyMap = {
  'backgroundColor': 'background-color',
  'borderRadius': 'border-radius',
  'fontSize': 'font-size',
  'fontWeight': 'font-weight',
  'textAlign': 'text-align',
  'marginTop': 'margin-top',
  'marginBottom': 'margin-bottom',
  'marginLeft': 'margin-left',
  'marginRight': 'margin-right',
  'paddingTop': 'padding-top',
  'paddingBottom': 'padding-bottom',
  'paddingLeft': 'padding-left',
  'paddingRight': 'padding-right',
  'borderBottom': 'border-bottom',
  'borderTop': 'border-top',
  'borderLeft': 'border-left',
  'borderRight': 'border-right',
  'overflowY': 'overflow-y',
  'overflowX': 'overflow-x',
  'textDecoration': 'text-decoration',
  'flexDirection': 'flex-direction',
  'alignItems': 'align-items',
  'justifyContent': 'justify-content',
  'flexWrap': 'flex-wrap',
  'gridTemplateColumns': 'grid-template-columns',
  'gridGap': 'grid-gap',
  'lineHeight': 'line-height',
  'boxShadow': 'box-shadow',
  'borderColor': 'border-color',
  'borderStyle': 'border-style',
  'borderWidth': 'border-width',
  'maxWidth': 'max-width',
  'minWidth': 'min-width',
  'maxHeight': 'max-height',
  'minHeight': 'min-height',
  'whiteSpace': 'white-space',
  'wordBreak': 'word-break',
  'alignSelf': 'align-self',
  'justifySelf': 'justify-self',
  'pointerEvents': 'pointer-events',
  'userSelect': 'user-select',
  'objectFit': 'object-fit',
  'textTransform': 'text-transform',
  'letterSpacing': 'letter-spacing',
  'zIndex': 'z-index'
};

function fixCssPropertiesInFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // 移除全局范围的“引号修复”，将修复限定到样式片段中（css({}) 或 style={{}}）。
  // 注意：以下全局修复已删除，避免污染 JSX/模板字符串等非样式上下文。
  // —— 原：trailingQuoteKeyRegex / missingClosingQuoteBeforeColon / unterminatedSafe / URL & 时间修复 / 三元字符串修复

  // 保留仅针对伪类键名的微调（这类一般也出现在样式片段中，但为了安全起见，可以稍后也移入片段内再处理）：
  content = content.replace(/'&:hover\s*:\s*\{/g, "'&:hover': {");
  content = content.replace(/'&:last-child\s*:\s*\{/g, "'&:last-child': {");

  // 全局、但非常窄化的修复：撤销之前错误替换造成的常见污染模式
  // 1) 标识符键名后多余的单引号：display' : → display :
  {
    const before = content;
    content = content.replace(/(^|[^A-Za-z0-9_'"])([A-Za-z_][A-Za-z0-9_]*)'\s*:/g, '$1$2:');
    if (content !== before) modified = true;
  }
  // 1b) TypeScript 可选属性被插入引号：theme? ' : → theme?:
  {
    const before = content;
    content = content.replace(/\b([A-Za-z_][A-Za-z0-9_]*)\?\s*'\s*:\s*/g, '$1?: ');
    if (content !== before) modified = true;
  }
  // 1c) 箭头函数返回类型前被插入引号：`()' : Type =>` → `(): Type =>`
  {
    const before = content;
    content = content.replace(/\)\s*'\s*:\s*(?=[A-Za-z_][A-Za-z0-9_]*\s*=>)/g, '): ');
    if (content !== before) modified = true;
  }
  // 2) 计算属性键名后多余的单引号：[name]' : → [name] :
  {
    const before = content;
    content = content.replace(/\[([^\]]+)\]'\s*:/g, '[$1]:');
    if (content !== before) modified = true;
  }
  // 3) 带引号的键名（含点/连字符）在引号内尾随空格：'editor.background ' : → 'editor.background' :
  {
    const before = content;
    content = content.replace(/'([A-Za-z0-9._-]+)\s+'(\s*:)/g, "'$1'$2");
    if (content !== before) modified = true;
  }
  // 4) 修正被拆分的 URL：'https' :// → 'https://'
  {
    const before = content;
    content = content.replace(/'https'\s*:\s*\/\//g, "'https://");
    content = content.replace(/'http'\s*:\s*\/\//g, "'http://");
    if (content !== before) modified = true;
  }
  // 5) 修正多余的引号夹在冒号两边：'#374151' ' : '#e5e7eb' → '#374151' : '#e5e7eb'
  {
    const before = content;
    content = content.replace(/'\s*'\s*:\s*'/g, "' : '");
    if (content !== before) modified = true;
  }
  // 6) 将被错误地放入引号内的冒号移出：'margin-bottom: '16px' → 'margin-bottom' : '16px'
  {
    const before = content;
    // 6.1 精确匹配：'kebab.key/path: 'value' 形式，统一为 'kebab.key/path' : 'value'
    content = content.replace(/'([A-Za-z0-9._\/-]+)\s*:\s*'([^']+)'/g, "'$1' : '$2'");
    // 6.1b 模板字符串作为值：'key: `value` → 'key' : `value`
    content = content.replace(/'([A-Za-z0-9._\/-]+)\s*:\s*`/g, "'$1' : `");
    // 6.1c 表达式作为值（以标识符/函数开头）：'key: expr → 'key' : expr
    content = content.replace(/'([A-Za-z0-9._\/-]+)\s*:\s*(?=[A-Za-z_$\(])/g, "'$1' : ");
    // 6.2 宽松匹配：只把引号内的冒号移出到外面（用于 ? '#e6f7ff: 'transparent' 这类场景）
    content = content.replace(/'([^']+):\s*'/g, "'$1' : '");
    if (content !== before) modified = true;
  }
  // 7) 处理标识符键名与引号之间误加的空格：display ' : → display :
  {
    const before = content;
    content = content.replace(/(^|[^A-Za-z0-9_'\"])([A-Za-z_][A-Za-z0-9_]*)\s*'\s*:/g, '$1$2:');
    if (content !== before) modified = true;
  }
  // 8) 修复被拆分的时间字符串：'2024-.. 14:32' :15' → '2024-.. 14:32:15'
  {
    const before = content;
    content = content.replace(/'(\d{4}-\d{2}-\d{2} \d{2}:\d{2})'\s*:\s*(\d{2})'/g, "'$1:$2'");
    content = content.replace(/'(\d{1,2})'\s*:\s*(\d{2})'/g, "'$1:$2'");
    if (content !== before) modified = true;
  }
  // 9) 修复字符串前后双引号导致的多余引号：'管理员' ' : → '管理员' :
  {
    const before = content;
    content = content.replace(/'([^']+)'\s*'\s*:/g, "'$1' :");
    if (content !== before) modified = true;
  }
  // 10) 修复字符串与逗号之间被插入的冒号：'错误' :', error) → '错误:', error)
  {
    const before = content;
    content = content.replace(/'([^']+)'\s*:\s*',/g, "'$1:',");
    if (content !== before) modified = true;
  }

  // 对于Panda CSS，我们需要将kebab-case转换为camelCase
  // 检查文件是否使用了css()函数
  const usesPandaCSS = content.includes('css({') || content.includes('from \'../../styled-system/css\'') || content.includes('from \'../styled-system/css\'');

  if (usesPandaCSS) {
    // 处理Panda CSS - 将kebab-case转换为camelCase
    const pandaCssRegex = /css\(\{([^}]*(?:\{[^}]*\}[^}]*)*)\}\)/gs;

    content = content.replace(pandaCssRegex, (match, cssContent) => {
      let newCssContent = cssContent;

      // 将kebab-case属性转换为camelCase
      for (const [camelCase, kebabCase] of Object.entries(cssPropertyMap)) {
        const kebabRegex = new RegExp(`(["'])${kebabCase}(["'])\\s*(:)`, 'g');
        if (kebabRegex.test(newCssContent)) {
          newCssContent = newCssContent.replace(kebabRegex, `${camelCase}$3`);
          modified = true;
        }
        // 仅在 css({}) 内容内做兜底清理：移除属性名后紧跟的多余单引号
        newCssContent = newCssContent.replace(/([A-Za-z_][A-Za-z0-9_-]*)'(\\s*:)/g, '$1$2');
      }
      // 将被错误地放入引号内的冒号移出（仅限 css({}) 片段内）
      newCssContent = newCssContent
        .replace(/'([A-Za-z0-9._&:-]+)\\s*:\\s*`/g, "'$1': `")
        .replace(/'([A-Za-z0-9._&:-]+)\\s*:\\s*'([^']*)'/g, "'$1': '$2'")
        .replace(/'([A-Za-z0-9._&:-]+)\\s*:\\s*(?=[A-Za-z_$\(\{])/g, "'$1': ");

      return `css({${newCssContent}})`;
    });
  } else {
    // 处理普通style对象 - 将camelCase转换为kebab-case
    const styleRegex = /style\s*=\s*\{\{([^}]*(?:\{[^}]*\}[^}]*)*)\}\}/gs;

    content = content.replace(styleRegex, (match, styleContent) => {
      let newStyleContent = styleContent;

      // 先做兜底清理：移除属性名后紧跟的多余单引号（例如 padding' : -> padding :）
      newStyleContent = newStyleContent.replace(/([A-Za-z_][A-Za-z0-9_-]*)'(\\s*:)/g, '$1$2');
      // 再清理带引号的 kebab 键名中的尾随空格：'border-radius ' : -> 'border-radius' :
      newStyleContent = newStyleContent.replace(/'([a-z-]+)\s+'(\s*:)/g, "'$1'$2");

      for (const [camelCase, kebabCase] of Object.entries(cssPropertyMap)) {
        const camelRegex = new RegExp(`(\\s|^)${camelCase}(\\s*:)`, 'g');
        if (camelRegex.test(newStyleContent)) {
          newStyleContent = newStyleContent.replace(camelRegex, (match, prefix, suffix) => {
            modified = true;
            return `${prefix}'${kebabCase}'${suffix}`;
          });
        }
      }

      return `style={{${newStyleContent}}}`;
    });
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed CSS properties in: ${path.relative(process.cwd(), filePath)}`);
    return true;
  }

  return false;
}

function processDirectory(dir) {
  let fixedCount = 0;
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      fixedCount += processDirectory(filePath);
    } else if (stat.isFile() && (file.endsWith('.tsx') || file.endsWith('.jsx'))) {
      if (fixCssPropertiesInFile(filePath)) {
        fixedCount++;
      }
    }
  }
  
  return fixedCount;
}

// 执行修复
console.log('🔧 Fixing CSS property names in TSX/JSX files...\n');
const srcDir = path.join(__dirname, 'src');
const fixedCount = processDirectory(srcDir);
console.log(`\n✨ Fixed ${fixedCount} files with CSS property issues.`);