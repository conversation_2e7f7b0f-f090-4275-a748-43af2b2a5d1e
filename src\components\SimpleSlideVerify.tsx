import { createSignal, onMount, onCleanup } from 'solid-js';

interface SimpleSlideVerifyProps {
  width?: number;
  height?: number;
  sliderText?: string;
  onSuccess?: () => void;
  onFail?: () => void;
  onRefresh?: () => void;
}

export default function SimpleSlideVerify(props: SimpleSlideVerifyProps) {
  const [isSuccess, setIsSuccess] = createSignal(false);
  const [isFailed, setIsFailed] = createSignal(false);
  const [sliderLeft, setSliderLeft] = createSignal(0);
  const [isDragging, setIsDragging] = createSignal(false);
  const [startX, setStartX] = createSignal(0);
  const [targetX, setTargetX] = createSignal(0);

  const width = () => props.width || 320;
  const height = () => props.height || 160;
  const sliderText = () => props.sliderText || '向右滑动验证';

  // 生成随机目标位置
  const generateTarget = () => {
    const target = Math.random() * (width() - 120) + 60;
    setTargetX(target);
    return target;
  };

  // 重置验证
  const reset = () => {
    setIsSuccess(false);
    setIsFailed(false);
    setSliderLeft(0);
    setIsDragging(false);
    generateTarget();
  };

  // 验证结果
  const verify = () => {
    const tolerance = 10; // 允许的误差范围
    const isValid = Math.abs(sliderLeft() - targetX()) < tolerance;
    
    if (isValid) {
      setIsSuccess(true);
      setIsFailed(false);
      props.onSuccess?.();
    } else {
      setIsFailed(true);
      setIsSuccess(false);
      props.onFail?.();
      // 1秒后重置
      setTimeout(reset, 1000);
    }
  };

  // 鼠标/触摸事件处理
  const handleStart = (e: MouseEvent | TouchEvent) => {
    if (isSuccess()) return;
    
    setIsDragging(true);
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    setStartX(clientX - sliderLeft());
  };

  const handleMove = (e: MouseEvent | TouchEvent) => {
    if (!isDragging() || isSuccess()) return;
    
    e.preventDefault();
    const clientX = 'touches' in e ? e.touches[0].clientX: e.clientX;
    const newLeft = Math.max(0, Math.min(clientX - startX(), width() - 60));
    setSliderLeft(newLeft);
  };

  const handleEnd = () => {
    if (!isDragging() || isSuccess()) return;
    
    setIsDragging(false);
    verify();
  };

  // 组件挂载时初始化
  onMount(() => {
    generateTarget();

    // 添加全局事件监听
    document.addEventListener('mousemove', handleMove);
    document.addEventListener('mouseup', handleEnd);
    document.addEventListener('touchmove', handleMove, { passive: false });
    document.addEventListener('touchend', handleEnd);
  });

  // 组件卸载时清理事件
  onCleanup(() => {
    document.removeEventListener('mousemove', handleMove);
    document.removeEventListener('mouseup', handleEnd);
    document.removeEventListener('touchmove', handleMove);
    document.removeEventListener('touchend', handleEnd);
  });

  return (
    <div style={{
      width: `${width()}px`,
      border: '1px solid #e1e5e9',
      'border-radius: '8px',
      'background-color: '#f7f9fa',
      overflow: 'hidden',
      'user-select: 'none'
    }}>
      {/* 拼图区域 */}
      <div style={{
        height: `${height()}px`,
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        position: 'relative',
        display: 'flex',
        'align-items: 'center',
        'justify-content: 'center'
      }}>
        {/* 背景装饰 */}
        <div style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          backgroundImage: `
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.2) 2px, transparent 2px),
            radial-gradient(circle at 80% 80%, rgba(255,255,255,0.2) 2px, transparent 2px),
            radial-gradient(circle at 40% 60%, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px, 30px 30px, 20px 20px'
        }} />

        {/* 目标区域指示 */}
        <div style={{
          position: 'absolute',
          left: `${targetX()}px`,
          top: '50%',
          transform: 'translateY(-50%)',
          width: '50px',
          height: '50px',
          border: '2px dashed rgba(255,255,255,0.6)',
          'border-radius: '8px',
          display: 'flex',
          'align-items: 'center',
          'justify-content: 'center',
          color: 'rgba(255,255,255,0.8)',
          'font-size: '20px'
        }}>
          🧩
        </div>

        {/* 拼图块 */}
        <div style={{
          position: 'absolute',
          left: `${sliderLeft()}px`,
          top: '50%',
          transform: 'translateY(-50%)',
          width: '50px',
          height: '50px',
          'background-color: isSuccess() ? '#52c41a ' : isFailed() ? '#ff4d4f ' ' : '#1890ff',
          'border-radius: '8px',
          display: 'flex',
          'align-items: 'center',
          'justify-content: 'center',
          color: 'white',
          'font-size: '20px',
          'box-shadow: '0 4px 8px rgba(0,0,0,0.2)',
          transition: isDragging() ? 'none' ' : 'all 0.3s ease',
          cursor: isDragging() ? 'grabbing' ' : 'grab'
        }}>
          {isSuccess() ? '✓' ' : isFailed() ? '✗' ' : '🧩'}
        </div>

        {/* 成功/失败提示 */}
        {(isSuccess() || isFailed()) && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            'background-color: 'rgba(0,0,0,0.8)',
            color: 'white',
            padding: '8px 16px',
            'border-radius: '20px',
            'font-size: '14px',
            'font-weight: '500'
          }}>
            {isSuccess() ? '✅ 验证成功' ' : '❌ 验证失败'}
          </div>
        )}
      </div>

      {/* 滑块轨道 */}
      <div style={{
        height: '50px',
        'background-color: '#f7f9fa',
        'border-top: '1px solid #e1e5e9',
        position: 'relative',
        display: 'flex',
        'align-items: 'center'
      }}>
        {/* 滑块 */}
        <div
          style={{
            position: 'absolute',
            left: `${sliderLeft()}px`,
            width: '60px',
            height: '40px',
            'background-color: isSuccess() ? '#52c41a ' : isFailed() ? '#ff4d4f ' ' : '#1890ff',
            border: '1px solid #d9d9d9',
            'border-radius: '6px',
            cursor: isDragging() ? 'grabbing' ' : 'grab',
            display: 'flex',
            'align-items: 'center',
            'justify-content: 'center',
            color: 'white',
            'font-size: '18px',
            transition: isDragging() ? 'none' ' : 'all 0.3s ease',
            'box-shadow: '0 2px 4px rgba(0,0,0,0.1)',
            'z-index: 10
          }}
          onMouseDown={handleStart}
          onTouchStart={handleStart}
        >
          {isSuccess() ? '✓' ' : isFailed() ? '✗' ' : '→'}
        </div>

        {/* 滑块轨道背景 */}
        <div style={{
          position: 'absolute',
          left: '0',
          top: '50%',
          transform: 'translateY(-50%)',
          width: `${sliderLeft() + 30}px`,
          height: '40px',
          'background-color: isSuccess() ? '#f6ffed: isFailed() ? '#fff2f0 ' ' : '#e6f7ff',
          'border-radius: '6px',
          transition: 'all 0.3s ease'
        }} />

        {/* 滑块文本 */}
        <div style={{
          width: '100%',
          'text-align: 'center',
          color: '#999',
          'font-size: '14px',
          'pointer-events: 'none',
          'z-index: 5
        }}>
          {isSuccess() ? '验证成功' ' : isFailed() ? '验证失败，请重试' : sliderText()}
        </div>

        {/* 刷新按钮 */}
        <button
          style={{
            position: 'absolute',
            right: '10px',
            width: '30px',
            height: '30px',
            border: 'none',
            'background-color: 'transparent',
            cursor: 'pointer',
            'font-size: '16px',
            color: '#999',
            'border-radius: '50%',
            display: 'flex',
            'align-items: 'center',
            'justify-content: 'center',
            'z-index: 10
          }}
          onClick={() => {
            reset();
            props.onRefresh?.();
          }}
          title="刷新验证码"
        >
          ↻
        </button>
      </div>
    </div>
  );
}
