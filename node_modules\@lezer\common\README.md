# @lezer/common

[ [**WEBSITE**](http://lezer.codemirror.net) | [**ISSUES**](https://github.com/lezer-parser/lezer/issues) | [**FORUM**](https://discuss.codemirror.net/c/lezer) | [**CHANGELOG**](https://github.com/lezer-parser/common/blob/master/CHANGELOG.md) ]

[Lezer](https://lezer.codemirror.net/) is an incremental parser system
intended for use in an editor or similar system.

@lezer/common provides the syntax tree data structure and parser
abstractions for Lezer parsers.

Its programming interface is documented on [the
website](https://lezer.codemirror.net/docs/ref/#common).

This code is licensed under an MIT license.
