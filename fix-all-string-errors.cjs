const fs = require('fs');
const path = require('path');

// 修复所有字符串语法错误的模式
function fixAllStringErrors(content) {
  let fixedContent = content;
  
  // 1. 修复三元运算符中的字符串引号错误
  // 模式: 'string' ' : 'string'
  fixedContent = fixedContent.replace(/'([^']*?)'\s*'\s*:\s*'([^']*?)'/g, "'$1' : '$2'");
  
  // 2. 修复三元运算符中的字符串引号错误 (另一种模式)
  // 模式: 'string ' ' : 'string'
  fixedContent = fixedContent.replace(/'([^']*?)\s+'\s*'\s*:\s*'([^']*?)'/g, "'$1' : '$2'");
  
  // 3. 修复颜色值后面的多余空格和引号
  // 模式: '#color ' : 或 '#color ' '
  fixedContent = fixedContent.replace(/'(#[a-fA-F0-9]{3,6})\s+'\s*'/g, "'$1'");
  fixedContent = fixedContent.replace(/'(#[a-fA-F0-9]{3,6})\s+'/g, "'$1'");
  
  // 4. 修复字符串后面多余的引号
  // 模式: 'text' ' :
  fixedContent = fixedContent.replace(/'([^']*?)'\s*'\s*:/g, "'$1' :");
  
  // 5. 修复CSS属性值中的错误
  // 模式: property === value ? 'val1' ' : 'val2'
  fixedContent = fixedContent.replace(/\?\s*'([^']*?)'\s*'\s*:\s*'([^']*?)'/g, "? '$1' : '$2'");
  
  // 6. 修复多行三元运算符中的错误
  // 处理跨行的情况
  fixedContent = fixedContent.replace(/\?\s*'([^']*?)'\s*'\s*:\s*\n\s*'([^']*?)'\s*'\s*:\s*'([^']*?)'/g, "? '$1' :\n                   '$2' : '$3'");
  
  // 7. 修复对象属性中的错误
  // 模式: bg : condition ? 'val1 ' : 'val2'
  fixedContent = fixedContent.replace(/:\s*([^?]+)\?\s*'([^']*?)\s+'\s*:\s*'([^']*?)'/g, ": $1? '$2' : '$3'");
  
  // 8. 修复更复杂的三元运算符
  fixedContent = fixedContent.replace(/===\s*([^?]+)\?\s*'([^']*?)'\s*'\s*:\s*'([^']*?)'/g, "=== $1? '$2' : '$3'");
  
  return fixedContent;
}

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    
    // 应用修复
    content = fixAllStringErrors(content);
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${path.basename(filePath)}: string syntax errors`);
      return true;
    } else {
      console.log(`✓  ${path.basename(filePath)}: no string syntax errors found`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔧 Fixing all string syntax errors...\n');
  
  // 需要修复的文件
  const filesToFix = [
    'src/pages/Dashboard.tsx',
    'src/pages/StrategyEditor.tsx',
    'src/pages/BacktestAnalysis.tsx',
    'src/pages/MarketData.tsx',
    'src/pages/ParameterOptimization.tsx',
    'src/components/AdvancedLayout.tsx',
    'src/components/BacktestCharts.tsx',
    'src/components/ParameterOptimizer.tsx',
    'src/components/MarketNews.tsx',
    'src/components/PortfolioOverview.tsx',
    'src/App.tsx'
  ];
  
  let totalFixed = 0;
  
  for (const filePath of filesToFix) {
    if (fs.existsSync(filePath)) {
      if (fixFile(filePath)) {
        totalFixed++;
      }
    } else {
      console.warn(`⚠️  File not found: ${filePath}`);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`✨ Complete! Fixed ${totalFixed} files with string syntax errors.`);
  
  if (totalFixed > 0) {
    console.log('\n📋 Next steps:');
    console.log('1. Run "npm run build" to test production build');
    console.log('2. Run "npm run dev" to test development server');
  }
}

main();
