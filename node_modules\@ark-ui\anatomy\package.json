{"name": "@ark-ui/anatomy", "version": "2.1.0", "description": "Contains all the anatomy information for the Ark UI components", "keywords": ["accordion", "avatar", "carousel", "checkbox", "color picker", "combobox", "date picker", "dialog", "editable", "file upload", "hover card", "menu", "number input", "pagination", "pin input", "popover", "progress", "radio group", "rating group", "segment group", "select", "slider", "splitter", "switch", "tabs", "tags input", "toast", "toggle group", "tooltip", "tree view"], "homepage": "https://ark-ui.com", "license": "MIT", "sideEffects": false, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/ark.git", "directory": "packages/anatomy"}, "bugs": {"url": "https://github.com/chakra-ui/ark/issues"}, "scripts": {"before:pack": "pnpm --filter=@ark-ui/scripts release:prepare @ark-ui/anatomy", "build": "vite build", "release-it": "release-it --config ../../release-it.json"}, "dependencies": {"@zag-js/accordion": "0.34.0", "@zag-js/anatomy": "0.34.0", "@zag-js/avatar": "0.34.0", "@zag-js/carousel": "0.34.0", "@zag-js/checkbox": "0.34.0", "@zag-js/color-picker": "0.34.0", "@zag-js/color-utils": "0.34.0", "@zag-js/combobox": "0.34.0", "@zag-js/date-picker": "0.34.0", "@zag-js/date-utils": "0.34.0", "@zag-js/dialog": "0.34.0", "@zag-js/editable": "0.34.0", "@zag-js/file-upload": "0.34.0", "@zag-js/hover-card": "0.34.0", "@zag-js/menu": "0.34.0", "@zag-js/number-input": "0.34.0", "@zag-js/pagination": "0.34.0", "@zag-js/pin-input": "0.34.0", "@zag-js/popover": "0.34.0", "@zag-js/presence": "0.34.0", "@zag-js/progress": "0.34.0", "@zag-js/radio-group": "0.34.0", "@zag-js/rating-group": "0.34.0", "@zag-js/select": "0.34.0", "@zag-js/slider": "0.34.0", "@zag-js/splitter": "0.34.0", "@zag-js/switch": "0.34.0", "@zag-js/tabs": "0.34.0", "@zag-js/tags-input": "0.34.0", "@zag-js/toast": "0.34.0", "@zag-js/toggle-group": "0.34.0", "@zag-js/tooltip": "0.34.0", "@zag-js/tree-view": "0.34.0"}, "devDependencies": {"@release-it/keep-a-changelog": "5.0.0", "globby": "14.0.0", "release-it": "17.0.3", "typescript": "5.3.3", "vite": "5.0.12", "vite-plugin-dts": "3.7.2"}, "main": "index.cjs", "module": "index.mjs", "types": "index.d.ts", "files": ["./"], "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.cjs"}, "./accordion": {"types": "./accordion/index.d.ts", "import": "./accordion/index.mjs", "require": "./accordion/index.cjs"}, "./avatar": {"types": "./avatar/index.d.ts", "import": "./avatar/index.mjs", "require": "./avatar/index.cjs"}, "./carousel": {"types": "./carousel/index.d.ts", "import": "./carousel/index.mjs", "require": "./carousel/index.cjs"}, "./checkbox": {"types": "./checkbox/index.d.ts", "import": "./checkbox/index.mjs", "require": "./checkbox/index.cjs"}, "./color-picker": {"types": "./color-picker/index.d.ts", "import": "./color-picker/index.mjs", "require": "./color-picker/index.cjs"}, "./combobox": {"types": "./combobox/index.d.ts", "import": "./combobox/index.mjs", "require": "./combobox/index.cjs"}, "./date-picker": {"types": "./date-picker/index.d.ts", "import": "./date-picker/index.mjs", "require": "./date-picker/index.cjs"}, "./dialog": {"types": "./dialog/index.d.ts", "import": "./dialog/index.mjs", "require": "./dialog/index.cjs"}, "./editable": {"types": "./editable/index.d.ts", "import": "./editable/index.mjs", "require": "./editable/index.cjs"}, "./file-upload": {"types": "./file-upload/index.d.ts", "import": "./file-upload/index.mjs", "require": "./file-upload/index.cjs"}, "./hover-card": {"types": "./hover-card/index.d.ts", "import": "./hover-card/index.mjs", "require": "./hover-card/index.cjs"}, "./menu": {"types": "./menu/index.d.ts", "import": "./menu/index.mjs", "require": "./menu/index.cjs"}, "./number-input": {"types": "./number-input/index.d.ts", "import": "./number-input/index.mjs", "require": "./number-input/index.cjs"}, "./pagination": {"types": "./pagination/index.d.ts", "import": "./pagination/index.mjs", "require": "./pagination/index.cjs"}, "./pin-input": {"types": "./pin-input/index.d.ts", "import": "./pin-input/index.mjs", "require": "./pin-input/index.cjs"}, "./popover": {"types": "./popover/index.d.ts", "import": "./popover/index.mjs", "require": "./popover/index.cjs"}, "./progress": {"types": "./progress/index.d.ts", "import": "./progress/index.mjs", "require": "./progress/index.cjs"}, "./radio-group": {"types": "./radio-group/index.d.ts", "import": "./radio-group/index.mjs", "require": "./radio-group/index.cjs"}, "./rating-group": {"types": "./rating-group/index.d.ts", "import": "./rating-group/index.mjs", "require": "./rating-group/index.cjs"}, "./segment-group": {"types": "./segment-group/index.d.ts", "import": "./segment-group/index.mjs", "require": "./segment-group/index.cjs"}, "./select": {"types": "./select/index.d.ts", "import": "./select/index.mjs", "require": "./select/index.cjs"}, "./slider": {"types": "./slider/index.d.ts", "import": "./slider/index.mjs", "require": "./slider/index.cjs"}, "./splitter": {"types": "./splitter/index.d.ts", "import": "./splitter/index.mjs", "require": "./splitter/index.cjs"}, "./switch": {"types": "./switch/index.d.ts", "import": "./switch/index.mjs", "require": "./switch/index.cjs"}, "./tabs": {"types": "./tabs/index.d.ts", "import": "./tabs/index.mjs", "require": "./tabs/index.cjs"}, "./tags-input": {"types": "./tags-input/index.d.ts", "import": "./tags-input/index.mjs", "require": "./tags-input/index.cjs"}, "./toast": {"types": "./toast/index.d.ts", "import": "./toast/index.mjs", "require": "./toast/index.cjs"}, "./toggle-group": {"types": "./toggle-group/index.d.ts", "import": "./toggle-group/index.mjs", "require": "./toggle-group/index.cjs"}, "./tooltip": {"types": "./tooltip/index.d.ts", "import": "./tooltip/index.mjs", "require": "./tooltip/index.cjs"}, "./tree-view": {"types": "./tree-view/index.d.ts", "import": "./tree-view/index.mjs", "require": "./tree-view/index.cjs"}, "./factory": {"types": "./factory.d.ts", "import": "./factory.mjs", "require": "./factory.cjs"}, "./package.json": "./package.json"}}