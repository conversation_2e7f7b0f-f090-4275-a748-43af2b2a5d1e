import { AnatomyInstance } from '@zag-js/anatomy';
export declare const anatomy: AnatomyInstance<"root" | "nextTrigger" | "prevTrigger" | "control" | "label" | "trigger" | "positioner" | "content" | "view" | "clearTrigger" | "input" | "monthSelect" | "rangeText" | "table" | "tableBody" | "tableCell" | "tableCellTrigger" | "tableHead" | "tableHeader" | "tableRow" | "viewTrigger" | "viewControl" | "yearSelect">;
