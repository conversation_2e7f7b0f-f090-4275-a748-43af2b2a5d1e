import type { Orientation } from "@kobalte/utils";
import {
	MenuCheckboxItem as CheckboxItem,
	MenuGroup as Group,
	MenuGroupLabel as GroupLabel,
	MenuIcon as Icon,
	MenuItemDescription as ItemDescription,
	MenuItemIndicator as ItemIndicator,
	MenuItemLabel as ItemLabel,
	type MenuCheckboxItemCommonProps as NavigationMenuCheckboxItemCommonProps,
	type MenuCheckboxItemOptions as NavigationMenuCheckboxItemOptions,
	type MenuCheckboxItemProps as NavigationMenuCheckboxItemProps,
	type MenuCheckboxItemRenderProps as NavigationMenuCheckboxItemRenderProps,
	type MenuGroupCommonProps as NavigationMenuGroupCommonProps,
	type MenuGroupLabelCommonProps as NavigationMenuGroupLabelCommonProps,
	type MenuGroupLabelOptions as NavigationMenuGroupLabelOptions,
	type MenuGroupLabelProps as NavigationMenuGroupLabelProps,
	type MenuGroupLabelRenderProps as NavigationMenuGroupLabelRenderProps,
	type MenuGroupOptions as NavigationMenuGroupOptions,
	type MenuGroupProps as NavigationMenuGroupProps,
	type MenuGroupRenderProps as NavigationMenuGroupRenderProps,
	type MenuIconCommonProps as NavigationMenuIconCommonProps,
	type MenuIconOptions as NavigationMenuIconOptions,
	type MenuIconProps as NavigationMenuIconProps,
	type MenuIconRenderProps as NavigationMenuIconRenderProps,
	type MenuItemCommonProps as NavigationMenuItemCommonProps,
	type MenuItemDescriptionCommonProps as NavigationMenuItemDescriptionCommonProps,
	type MenuItemDescriptionOptions as NavigationMenuItemDescriptionOptions,
	type MenuItemDescriptionProps as NavigationMenuItemDescriptionProps,
	type MenuItemDescriptionRenderProps as NavigationMenuItemDescriptionRenderProps,
	type MenuItemIndicatorCommonProps as NavigationMenuItemIndicatorCommonProps,
	type MenuItemIndicatorOptions as NavigationMenuItemIndicatorOptions,
	type MenuItemIndicatorProps as NavigationMenuItemIndicatorProps,
	type MenuItemIndicatorRenderProps as NavigationMenuItemIndicatorRenderProps,
	type MenuItemLabelCommonProps as NavigationMenuItemLabelCommonProps,
	type MenuItemLabelOptions as NavigationMenuItemLabelOptions,
	type MenuItemLabelProps as NavigationMenuItemLabelProps,
	type MenuItemLabelRenderProps as NavigationMenuItemLabelRenderProps,
	type MenuItemOptions as NavigationMenuItemOptions,
	type MenuItemProps as NavigationMenuItemProps,
	type MenuItemRenderProps as NavigationMenuItemRenderProps,
	type MenuRadioGroupCommonProps as NavigationMenuRadioGroupCommonProps,
	type MenuRadioGroupOptions as NavigationMenuRadioGroupOptions,
	type MenuRadioGroupProps as NavigationMenuRadioGroupProps,
	type MenuRadioGroupRenderProps as NavigationMenuRadioGroupRenderProps,
	type MenuRadioItemCommonProps as NavigationMenuRadioItemCommonProps,
	type MenuRadioItemOptions as NavigationMenuRadioItemOptions,
	type MenuRadioItemRenderProps as NavigationMenuRadioItemPRenderrops,
	type MenuRadioItemProps as NavigationMenuRadioItemProps,
	type MenuSubContentCommonProps as NavigationMenuSubContentCommonProps,
	type MenuSubContentOptions as NavigationMenuSubContentOptions,
	type MenuSubContentProps as NavigationMenuSubContentProps,
	type MenuSubContentRenderProps as NavigationMenuSubContentRenderProps,
	type MenuSubOptions as NavigationMenuSubOptions,
	type MenuSubProps as NavigationMenuSubProps,
	type MenuSubTriggerCommonProps as NavigationMenuSubTriggerCommonProps,
	type MenuSubTriggerOptions as NavigationMenuSubTriggerOptions,
	type MenuSubTriggerProps as NavigationMenuSubTriggerProps,
	type MenuSubTriggerRenderProps as NavigationMenuSubTriggerRenderProps,
	MenuRadioGroup as RadioGroup,
	MenuRadioItem as RadioItem,
	MenuSub as Sub,
	MenuSubContent as SubContent,
	MenuSubTrigger as SubTrigger,
} from "../menu";
import {
	type SeparatorRootCommonProps as NavigationMenuSeparatorCommonProps,
	type SeparatorRootOptions as NavigationMenuSeparatorOptions,
	type SeparatorRootProps as NavigationMenuSeparatorProps,
	type SeparatorRootRenderProps as NavigationMenuSeparatorRenderProps,
	Root as Separator,
} from "../separator";
import {
	NavigationMenuArrow as Arrow,
	type NavigationMenuArrowCommonProps,
	type NavigationMenuArrowOptions,
	type NavigationMenuArrowProps,
	type NavigationMenuArrowRenderProps,
} from "./navigation-menu-arrow";
import {
	NavigationMenuContent as Content,
	type Motion,
	type NavigationMenuContentCommonProps,
	type NavigationMenuContentOptions,
	type NavigationMenuContentProps,
	type NavigationMenuContentRenderProps,
} from "./navigation-menu-content";
import { NavigationMenuItem as Item } from "./navigation-menu-item";
import {
	NavigationMenuMenu as Menu,
	type NavigationMenuMenuOptions,
	type NavigationMenuMenuProps,
} from "./navigation-menu-menu";
import {
	type NavigationMenuPortalProps,
	NavigationMenuPortal as Portal,
} from "./navigation-menu-portal";
import {
	type NavigationMenuRootCommonProps,
	type NavigationMenuRootOptions,
	type NavigationMenuRootProps,
	type NavigationMenuRootRenderProps,
	NavigationMenuRoot as Root,
} from "./navigation-menu-root";
import {
	type NavigationMenuTriggerCommonProps,
	type NavigationMenuTriggerOptions,
	type NavigationMenuTriggerProps,
	type NavigationMenuTriggerRenderProps,
	NavigationMenuTrigger as Trigger,
} from "./navigation-menu-trigger";
import {
	type NavigationMenuViewportCommonProps,
	type NavigationMenuViewportOptions,
	type NavigationMenuViewportProps,
	type NavigationMenuViewportRenderProps,
	NavigationMenuViewport as Viewport,
} from "./navigation-menu-viewport";

export type {
	NavigationMenuRootOptions,
	NavigationMenuRootCommonProps,
	NavigationMenuRootRenderProps,
	NavigationMenuRootProps,
	NavigationMenuMenuOptions,
	NavigationMenuMenuProps,
	NavigationMenuArrowOptions,
	NavigationMenuArrowCommonProps,
	NavigationMenuArrowRenderProps,
	NavigationMenuArrowProps,
	NavigationMenuCheckboxItemOptions,
	NavigationMenuCheckboxItemCommonProps,
	NavigationMenuCheckboxItemRenderProps,
	NavigationMenuCheckboxItemProps,
	NavigationMenuContentOptions,
	NavigationMenuContentCommonProps,
	NavigationMenuContentRenderProps,
	NavigationMenuContentProps,
	NavigationMenuGroupLabelOptions,
	NavigationMenuGroupLabelCommonProps,
	NavigationMenuGroupLabelRenderProps,
	NavigationMenuGroupLabelProps,
	NavigationMenuGroupOptions,
	NavigationMenuGroupCommonProps,
	NavigationMenuGroupRenderProps,
	NavigationMenuGroupProps,
	NavigationMenuIconOptions,
	NavigationMenuIconCommonProps,
	NavigationMenuIconRenderProps,
	NavigationMenuIconProps,
	NavigationMenuItemDescriptionOptions,
	NavigationMenuItemDescriptionCommonProps,
	NavigationMenuItemDescriptionRenderProps,
	NavigationMenuItemDescriptionProps,
	NavigationMenuItemIndicatorOptions,
	NavigationMenuItemIndicatorCommonProps,
	NavigationMenuItemIndicatorRenderProps,
	NavigationMenuItemIndicatorProps,
	NavigationMenuItemLabelOptions,
	NavigationMenuItemLabelCommonProps,
	NavigationMenuItemLabelRenderProps,
	NavigationMenuItemLabelProps,
	NavigationMenuItemOptions,
	NavigationMenuItemCommonProps,
	NavigationMenuItemRenderProps,
	NavigationMenuItemProps,
	NavigationMenuPortalProps,
	NavigationMenuRadioGroupOptions,
	NavigationMenuRadioGroupCommonProps,
	NavigationMenuRadioGroupRenderProps,
	NavigationMenuRadioGroupProps,
	NavigationMenuRadioItemOptions,
	NavigationMenuRadioItemCommonProps,
	NavigationMenuRadioItemPRenderrops,
	NavigationMenuRadioItemProps,
	NavigationMenuSeparatorOptions,
	NavigationMenuSeparatorCommonProps,
	NavigationMenuSeparatorRenderProps,
	NavigationMenuSeparatorProps,
	NavigationMenuSubContentOptions,
	NavigationMenuSubContentCommonProps,
	NavigationMenuSubContentRenderProps,
	NavigationMenuSubContentProps,
	NavigationMenuSubOptions,
	NavigationMenuSubProps,
	NavigationMenuSubTriggerOptions,
	NavigationMenuSubTriggerCommonProps,
	NavigationMenuSubTriggerRenderProps,
	NavigationMenuSubTriggerProps,
	NavigationMenuTriggerOptions,
	NavigationMenuTriggerCommonProps,
	NavigationMenuTriggerRenderProps,
	NavigationMenuTriggerProps,
	NavigationMenuViewportOptions,
	NavigationMenuViewportCommonProps,
	NavigationMenuViewportRenderProps,
	NavigationMenuViewportProps,
	Motion,
	Orientation,
};

export {
	Arrow,
	CheckboxItem,
	Content,
	Group,
	GroupLabel,
	Icon,
	Item,
	ItemDescription,
	ItemIndicator,
	ItemLabel,
	Portal,
	RadioGroup,
	RadioItem,
	Root,
	Menu,
	Separator,
	Sub,
	SubContent,
	SubTrigger,
	Trigger,
	Viewport,
};

export const NavigationMenu = Object.assign(Root, {
	Arrow,
	CheckboxItem,
	Content,
	Group,
	GroupLabel,
	Icon,
	Item,
	ItemDescription,
	ItemIndicator,
	ItemLabel,
	Portal,
	RadioGroup,
	RadioItem,
	Menu,
	Separator,
	Sub,
	SubContent,
	SubTrigger,
	Trigger,
	Viewport,
});

/**
 * API will most probably change
 */
export {
	useNavigationMenuContext,
	type NavigationMenuContextValue,
} from "./navigation-menu-context";
