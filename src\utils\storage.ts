/**
 * 本地存储和缓存管理工具
 */

// 存储类型
export type StorageType = 'localStorage' | 'sessionStorage' | 'memory';

// 缓存项结构
export interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  expires?: number; // 过期时间（毫秒）
  key: string;
  version?: string;
}

// 存储配置
export interface StorageConfig {
  prefix?: string;
  type?: StorageType;
  enableEncryption?: boolean;
  defaultExpire?: number; // 默认过期时间（毫秒）
  maxSize?: number; // 最大缓存条目数
  enableCompression?: boolean;
}

/**
 * 统一存储管理器
 */
export class StorageManager {
  private config: Required<StorageConfig>;
  private memoryStorage = new Map<string, any>();
  private encryptionKey = 'quant-platform-2024';

  constructor(config: StorageConfig = {}) {
    this.config = {
      prefix: 'qp_',
      type: 'localStorage',
      enableEncryption: false,
      defaultExpire: 24 * 60 * 60 * 1000, // 24小时
      maxSize: 1000,
      enableCompression: false,
      ...config,
    };
  }

  /**
   * 获取存储引擎
   */
  private getStorage(): Storage | Map<string, any> {
    switch (this.config.type) {
      case 'localStorage': 'if (typeof window !== 'undefined' && window.localStorage) {
          return window.localStorage;
        }
        return this.memoryStorage;
      case 'sessionStorage': 'if (typeof window !== 'undefined' && window.sessionStorage) {
          return window.sessionStorage;
        }
        return this.memoryStorage;
      case 'memory':
      default:
        return this.memoryStorage;
    }
  }

  /**
   * 生成完整的键名
   */
  private getFullKey(key: string): string {
    return `${this.config.prefix}${key}`;
  }

  /**
   * 简单加密
   */
  private encrypt(data: string): string {
    if (!this.config.enableEncryption) return data;
    
    let encrypted = '';
    for (let i = 0; i < data.length; i++) {
      const keyChar = this.encryptionKey[i % this.encryptionKey.length];
      encrypted += String.fromCharCode(data.charCodeAt(i) ^ keyChar.charCodeAt(0));
    }
    return btoa(encrypted);
  }

  /**
   * 简单解密
   */
  private decrypt(encryptedData: string): string {
    if (!this.config.enableEncryption) return encryptedData;
    
    try {
      const data = atob(encryptedData);
      let decrypted = '';
      for (let i = 0; i < data.length; i++) {
        const keyChar = this.encryptionKey[i % this.encryptionKey.length];
        decrypted += String.fromCharCode(data.charCodeAt(i) ^ keyChar.charCodeAt(0));
      }
      return decrypted;
    } catch (error) {
      console.error('解密失败:', error);
      return '';
    }
  }

  /**
   * 压缩数据（简单实现）
   */
  private compress(data: string): string {
    if (!this.config.enableCompression) return data;
    // 这里可以集成更复杂的压缩算法，现在使用简单的重复字符压缩
    return data.replace(/(.)\1+/g, (match, char) => `${char}*${match.length}`);
  }

  /**
   * 解压数据
   */
  private decompress(compressedData: string): string {
    if (!this.config.enableCompression) return compressedData;
    return compressedData.replace(/(.)\*(\d+)/g, (_, char, count) => char.repeat(parseInt(count)));
  }

  /**
   * 设置数据
   */
  set<T>(key: string, value: T, expire?: number): boolean {
    try {
      const storage = this.getStorage();
      const fullKey = this.getFullKey(key);
      
      const cacheItem: CacheItem<T> = {
        data: value,
        timestamp: Date.now(),
        expires: expire || this.config.defaultExpire,
        key: fullKey,
        version: '1.0',
      };

      let serializedData = JSON.stringify(cacheItem);
      serializedData = this.compress(serializedData);
      serializedData = this.encrypt(serializedData);

      if (storage instanceof Map) {
        storage.set(fullKey, serializedData);
      } else {
        storage.setItem(fullKey, serializedData);
      }

      // 检查并清理过期数据
      this.cleanup();
      
      return true;
    } catch (error) {
      console.error('存储数据失败:', error);
      return false;
    }
  }

  /**
   * 获取数据
   */
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const storage = this.getStorage();
      const fullKey = this.getFullKey(key);

      let serializedData: string | null = null;
      
      if (storage instanceof Map) {
        serializedData = storage.get(fullKey) || null;
      } else {
        serializedData = storage.getItem(fullKey);
      }

      if (!serializedData) {
        return defaultValue || null;
      }

      serializedData = this.decrypt(serializedData);
      serializedData = this.decompress(serializedData);

      const cacheItem: CacheItem<T> = JSON.parse(serializedData);

      // 检查是否过期
      if (this.isExpired(cacheItem)) {
        this.remove(key);
        return defaultValue || null;
      }

      return cacheItem.data;
    } catch (error) {
      console.error('获取数据失败:', error);
      return defaultValue || null;
    }
  }

  /**
   * 删除数据
   */
  remove(key: string): boolean {
    try {
      const storage = this.getStorage();
      const fullKey = this.getFullKey(key);

      if (storage instanceof Map) {
        return storage.delete(fullKey);
      } else {
        storage.removeItem(fullKey);
        return true;
      }
    } catch (error) {
      console.error('删除数据失败:', error);
      return false;
    }
  }

  /**
   * 检查键是否存在
   */
  has(key: string): boolean {
    const storage = this.getStorage();
    const fullKey = this.getFullKey(key);

    if (storage instanceof Map) {
      return storage.has(fullKey);
    } else {
      return storage.getItem(fullKey) !== null;
    }
  }

  /**
   * 清空所有数据
   */
  clear(): void {
    try {
      const storage = this.getStorage();

      if (storage instanceof Map) {
        // 只清除带有前缀的数据
        const keysToDelete = Array.from(storage.keys()).filter(key => 
          key.startsWith(this.config.prefix)
        );
        keysToDelete.forEach(key => storage.delete(key));
      } else {
        // 只清除带有前缀的数据
        const keysToDelete: string[] = [];
        for (let i = 0; i < storage.length; i++) {
          const key = storage.key(i);
          if (key && key.startsWith(this.config.prefix)) {
            keysToDelete.push(key);
          }
        }
        keysToDelete.forEach(key => storage.removeItem(key));
      }
    } catch (error) {
      console.error('清空数据失败:', error);
    }
  }

  /**
   * 获取所有键
   */
  keys(): string[] {
    const storage = this.getStorage();
    const prefixLength = this.config.prefix.length;

    if (storage instanceof Map) {
      return Array.from(storage.keys())
        .filter(key => key.startsWith(this.config.prefix))
        .map(key => key.substring(prefixLength));
    } else {
      const keys: string[] = [];
      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (key && key.startsWith(this.config.prefix)) {
          keys.push(key.substring(prefixLength));
        }
      }
      return keys;
    }
  }

  /**
   * 获取存储大小信息
   */
  size(): { count: number; bytes: number } {
    const storage = this.getStorage();
    let count = 0;
    let bytes = 0;

    if (storage instanceof Map) {
      storage.forEach((value, key) => {
        if (key.startsWith(this.config.prefix)) {
          count++;
          bytes += key.length + JSON.stringify(value).length;
        }
      });
    } else {
      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i);
        if (key && key.startsWith(this.config.prefix)) {
          count++;
          const value = storage.getItem(key);
          bytes += key.length + (value?.length || 0);
        }
      }
    }

    return { count, bytes };
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(cacheItem: CacheItem): boolean {
    if (!cacheItem.expires) return false;
    return Date.now() - cacheItem.timestamp > cacheItem.expires;
  }

  /**
   * 清理过期数据
   */
  cleanup(): void {
    const keys = this.keys();
    let cleaned = 0;

    keys.forEach(key => {
      const data = this.get(key);
      if (data === null) {
        cleaned++;
      }
    });

    // 如果数据量超过最大限制，清理最老的数据
    if (keys.length > this.config.maxSize) {
      const itemsWithTime = keys.map(key => {
        const fullKey = this.getFullKey(key);
        const storage = this.getStorage();
        
        let serializedData: string | null = null;
        if (storage instanceof Map) {
          serializedData = storage.get(fullKey) || null;
        } else {
          serializedData = storage.getItem(fullKey);
        }

        if (serializedData) {
          try {
            serializedData = this.decrypt(serializedData);
            serializedData = this.decompress(serializedData);
            const cacheItem: CacheItem = JSON.parse(serializedData);
            return { key, timestamp: cacheItem.timestamp };
          } catch (error) {
            return { key, timestamp: 0 };
          }
        }
        return { key, timestamp: 0 };
      });

      // 按时间排序，删除最老的数据
      itemsWithTime
        .sort((a, b) => a.timestamp - b.timestamp)
        .slice(0, keys.length - this.config.maxSize)
        .forEach(item => {
          this.remove(item.key);
          cleaned++;
        });
    }

    if (cleaned > 0) {
      console.log(`清理了 ${cleaned} 个过期或多余的缓存项`);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const { count, bytes } = this.size();
    return {
      type: this.config.type,
      prefix: this.config.prefix,
      count,
      bytes,
      sizeString: this.formatBytes(bytes),
      maxSize: this.config.maxSize,
      usage: count / this.config.maxSize,
      encryption: this.config.enableEncryption,
      compression: this.config.enableCompression,
    };
  }

  /**
   * 格式化字节大小
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 预设的存储实例
export const localStorage = new StorageManager({
  type: 'localStorage',
  prefix: 'qp_local_',
  enableEncryption: false,
  defaultExpire: 7 * 24 * 60 * 60 * 1000, // 7天
});

export const sessionStorage = new StorageManager({
  type: 'sessionStorage',
  prefix: 'qp_session_',
  enableEncryption: false,
  defaultExpire: 60 * 60 * 1000, // 1小时
});

export const secureStorage = new StorageManager({
  type: 'localStorage',
  prefix: 'qp_secure_',
  enableEncryption: true,
  defaultExpire: 24 * 60 * 60 * 1000, // 24小时
});

export const memoryStorage = new StorageManager({
  type: 'memory',
  prefix: 'qp_memory_',
  enableEncryption: false,
  maxSize: 500,
});

/**
 * 缓存装饰器（用于函数结果缓存）
 */
export function cached<T extends (...args: any[]) => any>(
  options: {
    key?: string;
    expire?: number;
    storage?: StorageManager;
  } = {}
) {
  const storage = options.storage || memoryStorage;
  const expire = options.expire || 5 * 60 * 1000; // 5分钟

  return function (_: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: Parameters<T>) {
      const cacheKey = options.key || `${propertyKey}_${JSON.stringify(args)}`;
      
      // 尝试从缓存获取
      const cached = storage.get(cacheKey);
      if (cached !== null) {
        return cached;
      }

      // 执行原始方法
      const result = originalMethod.apply(this, args);

      // 如果是Promise，等待结果后缓存
      if (result instanceof Promise) {
        return result.then(value => {
          storage.set(cacheKey, value, expire);
          return value;
        });
      } else {
        // 直接缓存结果
        storage.set(cacheKey, result, expire);
        return result;
      }
    };

    return descriptor;
  };
}

/**
 * 防抖缓存（防止重复请求）
 */
export class DebounceCache {
  private pendingRequests = new Map<string, Promise<any>>();

  async get<T>(key: string, factory: () => Promise<T>): Promise<T> {
    // 如果有正在进行的请求，直接返回
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key);
    }

    // 创建新请求
    const promise = factory().finally(() => {
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, promise);
    return promise;
  }

  clear(key?: string): void {
    if (key) {
      this.pendingRequests.delete(key);
    } else {
      this.pendingRequests.clear();
    }
  }
}

export const debounceCache = new DebounceCache();