import { lazy, Suspense, createEffect } from 'solid-js';
import { Router, Route, RouteSectionProps } from '@solidjs/router';
import { ThemeProvider } from './context/ThemeContext';
import { I18nProvider } from './context/I18nContext';
import JotaiProvider from './context/JotaiProvider';
import Header from './components/Header';
import Footer from './components/Footer';
import LoadingSpinner from './components/LoadingSpinner';
import { AuthGuard, AdminGuard } from './components/RouteGuard';
import { apiManager } from './api';
import globalStore from './stores';
import { css } from '../styled-system/css';

// 懒加载页面组件以优化性能
const Dashboard = lazy(() => import('./pages/Dashboard'));
const StrategyEditor = lazy(() => import('./pages/StrategyEditor'));
const EnhancedStrategyEditor = lazy(() => import('./pages/EnhancedStrategyEditor'));
const BacktestAnalysis = lazy(() => import('./pages/BacktestAnalysis'));
const MarketData = lazy(() => import('./pages/MarketData'));
const Login = lazy(() => import('./pages/Login'));
const Unauthorized = lazy(() => import('./pages/Unauthorized'));

// 根布局组件
function AppLayout(props: RouteSectionProps) {
  // 初始化API管理器和全局状态
  createEffect(async () => {
    console.log('应用开始初始化...');
    try {
      await apiManager.initialize();
      await globalStore.initialize();
      console.log('应用初始化完成');
    } catch (error) {
      console.error('应用初始化失败: ', error);
    }
  });

  return (
    <JotaiProvider>
      <I18nProvider defaultLanguage="zh-CN">
        <ThemeProvider>
          <div class={css({
            minHeight' : '100vh',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: 'gray.50',
            color: 'gray.900',
            transition: 'all 0.2s ease'
          })}>
            {/* 顶部导航 */}
            <Header />

            {/* 主要内容区域 */}
            <main class={css({
              flex: 1,
              maxWidth: '1400px',
              margin: '0 auto',
              padding: '24px 16px',
              width: '100%'
            })}>
              <Suspense fallback={<LoadingSpinner />}>
                {props.children}
              </Suspense>
            </main>

            {/* 底部信息 */}
            <Footer />
          </div>
        </ThemeProvider>
      </I18nProvider>
    </JotaiProvider>
  );
}

export default function EnhancedApp() {
  return (
    <Router root={AppLayout}>
      {/* 公开路由 */}
      <Route path="/" component={Dashboard} />
      <Route path="/login" component={Login} />
      <Route path="/unauthorized" component={Unauthorized} />
      
      {/* 需要认证的路由 */}
      <Route 
        path="/market" 
        component={() => (
          <AuthGuard>
            <MarketData />
          </AuthGuard>
        )} 
      />
      
      <Route 
        path="/strategy" 
        component={() => (
          <AuthGuard>
            <StrategyEditor />
          </AuthGuard>
        )} 
      />
      
      <Route 
        path="/strategy-enhanced" 
        component={() => (
          <AuthGuard>
            <EnhancedStrategyEditor />
          </AuthGuard>
        )} 
      />
      
      <Route 
        path="/backtest" 
        component={() => (
          <AuthGuard>
            <BacktestAnalysis />
          </AuthGuard>
        )} 
      />
      
      {/* 管理员路由 */}
      <Route 
        path="/admin/*" 
        component={() => (
          <AdminGuard>
            <div class={css({
              textAlign: 'center',
              padding: '80px 0'
            })}>
              <h1 class={css({
                fontSize: '2xl',
                fontWeight: 'bold',
                marginBottom: '16px'
              })}>
                管理员面板
              </h1>
              <p class={css({ color: 'gray.600' })}>
                管理员功能正在开发中...
              </p>
            </div>
          </AdminGuard>
        )} 
      />
      
      {/* 404 页面 */}
      <Route path="*" component={() => (
        <div class={css({
          textAlign: 'center',
          padding: '80px 0'
        })}>
          <div class={css({
            fontSize: '6xl',
            marginBottom: '20px'
          })}>
            🔍
          </div>
          <h1 class={css({
            fontSize: '2xl',
            fontWeight: 'bold',
            marginBottom: '16px',
            color: 'gray.900'
          })}>
            404 - 页面未找到
          </h1>
          <p class={css({
            color: 'gray.600',
            marginBottom: '24px'
          })}>
            您访问的页面不存在
          </p>
          <a
            href="/"
            class={css({
              display: 'inline-block',
              padding: '12px 24px',
              backgroundColor: 'blue.600',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              fontWeight: '500',
              transition: 'all 0.2s',
              _hover: {
                backgroundColor: 'blue.700'
              }
            })}
          >
            返回首页
          </a>
        </div>
      )} />
    </Router>
  );
}
