{"name": "@pandacss/config", "version": "0.39.2", "description": "Find and load panda config", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "sideEffects": false, "homepage": "https://panda-css.com", "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/panda.git", "directory": "packages/config"}, "publishConfig": {"access": "public"}, "exports": {".": {"source": "./src/index.ts", "types": "./dist/index.d.ts", "require": "./dist/index.js", "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "./merge": {"source": "./src/merge-config.ts", "types": "./dist/merge-config.d.ts", "require": "./dist/merge-config.js", "import": {"types": "./dist/merge-config.d.mts", "default": "./dist/merge-config.mjs"}}, "./diff": {"source": "./src/diff-config.ts", "types": "./dist/diff-config.d.ts", "require": "./dist/diff-config.js", "import": {"types": "./dist/diff-config.d.mts", "default": "./dist/diff-config.mjs"}}, "./ts-path": {"source": "./src/resolve-ts-path-pattern.ts", "types": "./dist/resolve-ts-path-pattern.d.ts", "require": "./dist/resolve-ts-path-pattern.js", "import": {"types": "./dist/resolve-ts-path-pattern.d.mts", "default": "./dist/resolve-ts-path-pattern.mjs"}}, "./utils": {"source": "./src/utils.ts", "types": "./dist/utils.d.ts", "require": "./dist/utils.js", "import": {"types": "./dist/utils.d.mts", "default": "./dist/utils.mjs"}}, "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"bundle-n-require": "1.1.1", "escalade": "3.1.2", "merge-anything": "5.1.7", "microdiff": "1.3.2", "typescript": "5.3.3", "@pandacss/logger": "0.39.2", "@pandacss/preset-base": "0.39.2", "@pandacss/preset-panda": "0.39.2", "@pandacss/shared": "0.39.2", "@pandacss/types": "0.39.2"}, "devDependencies": {"pkg-types": "1.0.3"}, "scripts": {"build": "tsup --tsconfig tsconfig.build.json --dts", "build-fast": "tsup --no-dts", "dev": "pnpm build-fast --watch"}}