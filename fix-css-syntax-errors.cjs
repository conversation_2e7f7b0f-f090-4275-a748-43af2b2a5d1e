const fs = require('fs');
const path = require('path');

// 精确匹配需要修复的CSS属性名模式
const cssPropertyPatterns = [
  // 常见的CSS属性名错误模式
  { from: /(\s+)([a-zA-Z][a-zA-Z0-9]*)':\s*([^,}]+)/g, to: '$1$2: $3' },
  
  // 特定的常见错误
  { from: /borderBottom':/g, to: 'borderBottom:' },
  { from: /borderTop':/g, to: 'borderTop:' },
  { from: /borderLeft':/g, to: 'borderLeft:' },
  { from: /borderRight':/g, to: 'borderRight:' },
  { from: /borderRadius':/g, to: 'borderRadius:' },
  { from: /fontSize':/g, to: 'fontSize:' },
  { from: /fontWeight':/g, to: 'fontWeight:' },
  { from: /fontFamily':/g, to: 'fontFamily:' },
  { from: /lineHeight':/g, to: 'lineHeight:' },
  { from: /textAlign':/g, to: 'textAlign:' },
  { from: /textDecoration':/g, to: 'textDecoration:' },
  { from: /backgroundColor':/g, to: 'backgroundColor:' },
  { from: /flexDirection':/g, to: 'flexDirection:' },
  { from: /alignItems':/g, to: 'alignItems:' },
  { from: /justifyContent':/g, to: 'justifyContent:' },
  { from: /alignSelf':/g, to: 'alignSelf:' },
  { from: /flexWrap':/g, to: 'flexWrap:' },
  { from: /flexGrow':/g, to: 'flexGrow:' },
  { from: /flexShrink':/g, to: 'flexShrink:' },
  { from: /flexBasis':/g, to: 'flexBasis:' },
  { from: /marginTop':/g, to: 'marginTop:' },
  { from: /marginBottom':/g, to: 'marginBottom:' },
  { from: /marginLeft':/g, to: 'marginLeft:' },
  { from: /marginRight':/g, to: 'marginRight:' },
  { from: /paddingTop':/g, to: 'paddingTop:' },
  { from: /paddingBottom':/g, to: 'paddingBottom:' },
  { from: /paddingLeft':/g, to: 'paddingLeft:' },
  { from: /paddingRight':/g, to: 'paddingRight:' },
  { from: /maxWidth':/g, to: 'maxWidth:' },
  { from: /maxHeight':/g, to: 'maxHeight:' },
  { from: /minWidth':/g, to: 'minWidth:' },
  { from: /minHeight':/g, to: 'minHeight:' },
  { from: /overflowX':/g, to: 'overflowX:' },
  { from: /overflowY':/g, to: 'overflowY:' },
  { from: /boxShadow':/g, to: 'boxShadow:' },
  { from: /boxSizing':/g, to: 'boxSizing:' },
  { from: /whiteSpace':/g, to: 'whiteSpace:' },
  { from: /wordBreak':/g, to: 'wordBreak:' },
  { from: /wordWrap':/g, to: 'wordWrap:' },
  { from: /zIndex':/g, to: 'zIndex:' },
  { from: /gridTemplateColumns':/g, to: 'gridTemplateColumns:' },
  { from: /gridTemplateRows':/g, to: 'gridTemplateRows:' },
  { from: /gridColumn':/g, to: 'gridColumn:' },
  { from: /gridRow':/g, to: 'gridRow:' },
  { from: /gridGap':/g, to: 'gridGap:' },
  { from: /gridColumnGap':/g, to: 'gridColumnGap:' },
  { from: /gridRowGap':/g, to: 'gridRowGap:' },
  { from: /listStyle':/g, to: 'listStyle:' },
  { from: /listStyleType':/g, to: 'listStyleType:' },
  { from: /textTransform':/g, to: 'textTransform:' },
  { from: /letterSpacing':/g, to: 'letterSpacing:' },
  { from: /wordSpacing':/g, to: 'wordSpacing:' },
  { from: /verticalAlign':/g, to: 'verticalAlign:' },
  { from: /tableLayout':/g, to: 'tableLayout:' },
  { from: /borderCollapse':/g, to: 'borderCollapse:' },
  { from: /borderSpacing':/g, to: 'borderSpacing:' },
  { from: /captionSide':/g, to: 'captionSide:' },
  { from: /emptyCells':/g, to: 'emptyCells:' },
  { from: /borderStyle':/g, to: 'borderStyle:' },
  { from: /borderWidth':/g, to: 'borderWidth:' },
  { from: /borderColor':/g, to: 'borderColor:' },
  { from: /outlineStyle':/g, to: 'outlineStyle:' },
  { from: /outlineWidth':/g, to: 'outlineWidth:' },
  { from: /outlineColor':/g, to: 'outlineColor:' },
  { from: /outlineOffset':/g, to: 'outlineOffset:' }
];

function fixCssPropertiesInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let fixCount = 0;
    let originalContent = content;
    
    // 应用所有修复模式
    cssPropertyPatterns.forEach(pattern => {
      const matches = content.match(pattern.from);
      if (matches) {
        fixCount += matches.length;
        content = content.replace(pattern.from, pattern.to);
      }
    });
    
    if (fixCount > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${path.basename(filePath)}: ${fixCount} CSS property errors`);
      return true;
    } else {
      console.log(`✓  ${path.basename(filePath)}: no CSS property errors found`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function processFiles(filePaths) {
  let totalFixed = 0;
  
  for (const filePath of filePaths) {
    if (fs.existsSync(filePath)) {
      if (fixCssPropertiesInFile(filePath)) {
        totalFixed++;
      }
    } else {
      console.warn(`⚠️  File not found: ${filePath}`);
    }
  }
  
  return totalFixed;
}

// 主函数
function main() {
  console.log('🔧 Fixing CSS property syntax errors...\n');
  
  // 按优先级处理文件
  const priorityFiles = [
    // P0: Trading components (最多错误)
    'src/components/trading/OrderManagement.tsx',
    'src/components/trading/QuickOrderForm.tsx', 
    'src/components/trading/StockSelector.tsx',
    'src/components/trading/OrderBook.tsx',
    'src/components/trading/OrderForm.tsx',
    'src/components/trading/PositionManagement.tsx',
    'src/components/trading/RiskControlPanel.tsx',
    'src/components/trading/TradingTerminal.tsx',
    
    // P0: Pages
    'src/pages/NotificationCenter.tsx',
    'src/pages/PortfolioManagement.tsx',
    'src/pages/RiskManagement.tsx',
    'src/pages/TradingCenter.tsx',
    'src/pages/TradingTerminal.tsx',
    
    // P1: Other components
    'src/components/PortfolioOverview.tsx',
    'src/components/MarketNews.tsx',
    'src/components/BacktestCharts.tsx',
    'src/components/ParameterOptimizer.tsx'
  ];
  
  const totalFixed = processFiles(priorityFiles);
  
  console.log('\n' + '='.repeat(50));
  console.log(`✨ Complete! Fixed ${totalFixed} files with CSS property errors.`);
  
  if (totalFixed > 0) {
    console.log('\n📋 Next steps:');
    console.log('1. Run "npm run type-check" to verify fixes');
    console.log('2. Run "npm run dev" to test development server');
    console.log('3. Run "npm run build" to test production build');
  }
}

main();
