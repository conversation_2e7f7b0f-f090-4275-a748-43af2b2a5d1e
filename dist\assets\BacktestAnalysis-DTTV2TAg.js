import{c as It,o as me,t as zt,i as N,d as vt,f as L,e as $,s as Rt,u as kt,g as Fs,h as ve,a as ji,j as qi,b as Gt,m as pe,F as Ki}from"./index-Bmv3aZuw.js";function k(n){var t=n.width,i=n.height;if(t<0)throw new Error("Negative width is not allowed for Size");if(i<0)throw new Error("Negative height is not allowed for Size");return{width:t,height:i}}function tt(n,t){return n.width===t.width&&n.height===t.height}var ge=function(){function n(t){var i=this;this._resolutionListener=function(){return i._onResolutionChanged()},this._resolutionMediaQueryList=null,this._observers=[],this._window=t,this._installResolutionListener()}return n.prototype.dispose=function(){this._uninstallResolutionListener(),this._window=null},Object.defineProperty(n.prototype,"value",{get:function(){return this._window.devicePixelRatio},enumerable:!1,configurable:!0}),n.prototype.subscribe=function(t){var i=this,s={next:t};return this._observers.push(s),{unsubscribe:function(){i._observers=i._observers.filter(function(e){return e!==s})}}},n.prototype._installResolutionListener=function(){if(this._resolutionMediaQueryList!==null)throw new Error("Resolution listener is already installed");var t=this._window.devicePixelRatio;this._resolutionMediaQueryList=this._window.matchMedia("all and (resolution: ".concat(t,"dppx)")),this._resolutionMediaQueryList.addListener(this._resolutionListener)},n.prototype._uninstallResolutionListener=function(){this._resolutionMediaQueryList!==null&&(this._resolutionMediaQueryList.removeListener(this._resolutionListener),this._resolutionMediaQueryList=null)},n.prototype._reinstallResolutionListener=function(){this._uninstallResolutionListener(),this._installResolutionListener()},n.prototype._onResolutionChanged=function(){var t=this;this._observers.forEach(function(i){return i.next(t._window.devicePixelRatio)}),this._reinstallResolutionListener()},n}();function be(n){return new ge(n)}var we=function(){function n(t,i,s){var e;this._canvasElement=null,this._bitmapSizeChangedListeners=[],this._suggestedBitmapSize=null,this._suggestedBitmapSizeChangedListeners=[],this._devicePixelRatioObservable=null,this._canvasElementResizeObserver=null,this._canvasElement=t,this._canvasElementClientSize=k({width:this._canvasElement.clientWidth,height:this._canvasElement.clientHeight}),this._transformBitmapSize=i??function(h){return h},this._allowResizeObserver=(e=s?.allowResizeObserver)!==null&&e!==void 0?e:!0,this._chooseAndInitObserver()}return n.prototype.dispose=function(){var t,i;if(this._canvasElement===null)throw new Error("Object is disposed");(t=this._canvasElementResizeObserver)===null||t===void 0||t.disconnect(),this._canvasElementResizeObserver=null,(i=this._devicePixelRatioObservable)===null||i===void 0||i.dispose(),this._devicePixelRatioObservable=null,this._suggestedBitmapSizeChangedListeners.length=0,this._bitmapSizeChangedListeners.length=0,this._canvasElement=null},Object.defineProperty(n.prototype,"canvasElement",{get:function(){if(this._canvasElement===null)throw new Error("Object is disposed");return this._canvasElement},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"canvasElementClientSize",{get:function(){return this._canvasElementClientSize},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"bitmapSize",{get:function(){return k({width:this.canvasElement.width,height:this.canvasElement.height})},enumerable:!1,configurable:!0}),n.prototype.resizeCanvasElement=function(t){this._canvasElementClientSize=k(t),this.canvasElement.style.width="".concat(this._canvasElementClientSize.width,"px"),this.canvasElement.style.height="".concat(this._canvasElementClientSize.height,"px"),this._invalidateBitmapSize()},n.prototype.subscribeBitmapSizeChanged=function(t){this._bitmapSizeChangedListeners.push(t)},n.prototype.unsubscribeBitmapSizeChanged=function(t){this._bitmapSizeChangedListeners=this._bitmapSizeChangedListeners.filter(function(i){return i!==t})},Object.defineProperty(n.prototype,"suggestedBitmapSize",{get:function(){return this._suggestedBitmapSize},enumerable:!1,configurable:!0}),n.prototype.subscribeSuggestedBitmapSizeChanged=function(t){this._suggestedBitmapSizeChangedListeners.push(t)},n.prototype.unsubscribeSuggestedBitmapSizeChanged=function(t){this._suggestedBitmapSizeChangedListeners=this._suggestedBitmapSizeChangedListeners.filter(function(i){return i!==t})},n.prototype.applySuggestedBitmapSize=function(){if(this._suggestedBitmapSize!==null){var t=this._suggestedBitmapSize;this._suggestedBitmapSize=null,this._resizeBitmap(t),this._emitSuggestedBitmapSizeChanged(t,this._suggestedBitmapSize)}},n.prototype._resizeBitmap=function(t){var i=this.bitmapSize;tt(i,t)||(this.canvasElement.width=t.width,this.canvasElement.height=t.height,this._emitBitmapSizeChanged(i,t))},n.prototype._emitBitmapSizeChanged=function(t,i){var s=this;this._bitmapSizeChangedListeners.forEach(function(e){return e.call(s,t,i)})},n.prototype._suggestNewBitmapSize=function(t){var i=this._suggestedBitmapSize,s=k(this._transformBitmapSize(t,this._canvasElementClientSize)),e=tt(this.bitmapSize,s)?null:s;i===null&&e===null||i!==null&&e!==null&&tt(i,e)||(this._suggestedBitmapSize=e,this._emitSuggestedBitmapSizeChanged(i,e))},n.prototype._emitSuggestedBitmapSizeChanged=function(t,i){var s=this;this._suggestedBitmapSizeChangedListeners.forEach(function(e){return e.call(s,t,i)})},n.prototype._chooseAndInitObserver=function(){var t=this;if(!this._allowResizeObserver){this._initDevicePixelRatioObservable();return}xe().then(function(i){return i?t._initResizeObserver():t._initDevicePixelRatioObservable()})},n.prototype._initDevicePixelRatioObservable=function(){var t=this;if(this._canvasElement!==null){var i=Hi(this._canvasElement);if(i===null)throw new Error("No window is associated with the canvas");this._devicePixelRatioObservable=be(i),this._devicePixelRatioObservable.subscribe(function(){return t._invalidateBitmapSize()}),this._invalidateBitmapSize()}},n.prototype._invalidateBitmapSize=function(){var t,i;if(this._canvasElement!==null){var s=Hi(this._canvasElement);if(s!==null){var e=(i=(t=this._devicePixelRatioObservable)===null||t===void 0?void 0:t.value)!==null&&i!==void 0?i:s.devicePixelRatio,h=this._canvasElement.getClientRects(),r=h[0]!==void 0?Se(h[0],e):k({width:this._canvasElementClientSize.width*e,height:this._canvasElementClientSize.height*e});this._suggestNewBitmapSize(r)}}},n.prototype._initResizeObserver=function(){var t=this;this._canvasElement!==null&&(this._canvasElementResizeObserver=new ResizeObserver(function(i){var s=i.find(function(r){return r.target===t._canvasElement});if(!(!s||!s.devicePixelContentBoxSize||!s.devicePixelContentBoxSize[0])){var e=s.devicePixelContentBoxSize[0],h=k({width:e.inlineSize,height:e.blockSize});t._suggestNewBitmapSize(h)}}),this._canvasElementResizeObserver.observe(this._canvasElement,{box:"device-pixel-content-box"}))},n}();function ye(n,t){return new we(n,t.transform,t.options)}function Hi(n){return n.ownerDocument.defaultView}function xe(){return new Promise(function(n){var t=new ResizeObserver(function(i){n(i.every(function(s){return"devicePixelContentBoxSize"in s})),t.disconnect()});t.observe(document.body,{box:"device-pixel-content-box"})}).catch(function(){return!1})}function Se(n,t){return k({width:Math.round(n.left*t+n.width*t)-Math.round(n.left*t),height:Math.round(n.top*t+n.height*t)-Math.round(n.top*t)})}var Me=function(){function n(t,i,s){if(i.width===0||i.height===0)throw new TypeError("Rendering target could only be created on a media with positive width and height");if(this._mediaSize=i,s.width===0||s.height===0)throw new TypeError("Rendering target could only be created using a bitmap with positive integer width and height");this._bitmapSize=s,this._context=t}return n.prototype.useMediaCoordinateSpace=function(t){try{return this._context.save(),this._context.setTransform(1,0,0,1,0,0),this._context.scale(this._horizontalPixelRatio,this._verticalPixelRatio),t({context:this._context,mediaSize:this._mediaSize})}finally{this._context.restore()}},n.prototype.useBitmapCoordinateSpace=function(t){try{return this._context.save(),this._context.setTransform(1,0,0,1,0,0),t({context:this._context,mediaSize:this._mediaSize,bitmapSize:this._bitmapSize,horizontalPixelRatio:this._horizontalPixelRatio,verticalPixelRatio:this._verticalPixelRatio})}finally{this._context.restore()}},Object.defineProperty(n.prototype,"_horizontalPixelRatio",{get:function(){return this._bitmapSize.width/this._mediaSize.width},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"_verticalPixelRatio",{get:function(){return this._bitmapSize.height/this._mediaSize.height},enumerable:!1,configurable:!0}),n}();function it(n,t){var i=n.canvasElementClientSize;if(i.width===0||i.height===0)return null;var s=n.bitmapSize;if(s.width===0||s.height===0)return null;var e=n.canvasElement.getContext("2d",t);return e===null?null:new Me(e,i,s)}/*!
 * @license
 * TradingView Lightweight Charts™ v4.2.3
 * Copyright (c) 2025 TradingView, Inc.
 * Licensed under Apache License 2.0 https://www.apache.org/licenses/LICENSE-2.0
 */const _e={upColor:"#26a69a",downColor:"#ef5350",wickVisible:!0,borderVisible:!0,borderColor:"#378658",borderUpColor:"#26a69a",borderDownColor:"#ef5350",wickColor:"#737375",wickUpColor:"#26a69a",wickDownColor:"#ef5350"},ze={upColor:"#26a69a",downColor:"#ef5350",openVisible:!0,thinBars:!0},Ce={color:"#2196f3",lineStyle:0,lineWidth:3,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},Ee={topColor:"rgba( 46, 220, 135, 0.4)",bottomColor:"rgba( 40, 221, 100, 0)",invertFilledArea:!1,lineColor:"#33D778",lineStyle:0,lineWidth:3,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},Re={baseValue:{type:"price",price:0},topFillColor1:"rgba(38, 166, 154, 0.28)",topFillColor2:"rgba(38, 166, 154, 0.05)",topLineColor:"rgba(38, 166, 154, 1)",bottomFillColor1:"rgba(239, 83, 80, 0.05)",bottomFillColor2:"rgba(239, 83, 80, 0.28)",bottomLineColor:"rgba(239, 83, 80, 1)",lineWidth:3,lineStyle:0,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},ke={color:"#26a69a",base:0},js={color:"#2196f3"},qs={title:"",visible:!0,lastValueVisible:!0,priceLineVisible:!0,priceLineSource:0,priceLineWidth:1,priceLineColor:"",priceLineStyle:2,baseLineVisible:!0,baseLineWidth:1,baseLineColor:"#B2B5BE",baseLineStyle:0,priceFormat:{type:"price",precision:2,minMove:.01}};var Xi,Ai;function st(n,t){const i={0:[],1:[n.lineWidth,n.lineWidth],2:[2*n.lineWidth,2*n.lineWidth],3:[6*n.lineWidth,6*n.lineWidth],4:[n.lineWidth,4*n.lineWidth]}[t];n.setLineDash(i)}function Ks(n,t,i,s){n.beginPath();const e=n.lineWidth%2?.5:0;n.moveTo(i,t+e),n.lineTo(s,t+e),n.stroke()}function Y(n,t){if(!n)throw new Error("Assertion failed"+(t?": "+t:""))}function D(n){if(n===void 0)throw new Error("Value is undefined");return n}function p(n){if(n===null)throw new Error("Value is null");return n}function at(n){return p(D(n))}(function(n){n[n.Simple=0]="Simple",n[n.WithSteps=1]="WithSteps",n[n.Curved=2]="Curved"})(Xi||(Xi={})),function(n){n[n.Solid=0]="Solid",n[n.Dotted=1]="Dotted",n[n.Dashed=2]="Dashed",n[n.LargeDashed=3]="LargeDashed",n[n.SparseDotted=4]="SparseDotted"}(Ai||(Ai={}));const Ui={khaki:"#f0e68c",azure:"#f0ffff",aliceblue:"#f0f8ff",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",gray:"#808080",green:"#008000",honeydew:"#f0fff0",floralwhite:"#fffaf0",lightblue:"#add8e6",lightcoral:"#f08080",lemonchiffon:"#fffacd",hotpink:"#ff69b4",lightyellow:"#ffffe0",greenyellow:"#adff2f",lightgoldenrodyellow:"#fafad2",limegreen:"#32cd32",linen:"#faf0e6",lightcyan:"#e0ffff",magenta:"#f0f",maroon:"#800000",olive:"#808000",orange:"#ffa500",oldlace:"#fdf5e6",mediumblue:"#0000cd",transparent:"#0000",lime:"#0f0",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",midnightblue:"#191970",orchid:"#da70d6",mediumorchid:"#ba55d3",mediumturquoise:"#48d1cc",orangered:"#ff4500",royalblue:"#4169e1",powderblue:"#b0e0e6",red:"#f00",coral:"#ff7f50",turquoise:"#40e0d0",white:"#fff",whitesmoke:"#f5f5f5",wheat:"#f5deb3",teal:"#008080",steelblue:"#4682b4",bisque:"#ffe4c4",aquamarine:"#7fffd4",aqua:"#0ff",sienna:"#a0522d",silver:"#c0c0c0",springgreen:"#00ff7f",antiquewhite:"#faebd7",burlywood:"#deb887",brown:"#a52a2a",beige:"#f5f5dc",chocolate:"#d2691e",chartreuse:"#7fff00",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cadetblue:"#5f9ea0",tomato:"#ff6347",fuchsia:"#f0f",blue:"#00f",salmon:"#fa8072",blanchedalmond:"#ffebcd",slateblue:"#6a5acd",slategray:"#708090",thistle:"#d8bfd8",tan:"#d2b48c",cyan:"#0ff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",blueviolet:"#8a2be2",black:"#000",darkmagenta:"#8b008b",darkslateblue:"#483d8b",darkkhaki:"#bdb76b",darkorchid:"#9932cc",darkorange:"#ff8c00",darkgreen:"#006400",darkred:"#8b0000",dodgerblue:"#1e90ff",darkslategray:"#2f4f4f",dimgray:"#696969",deepskyblue:"#00bfff",firebrick:"#b22222",forestgreen:"#228b22",indigo:"#4b0082",ivory:"#fffff0",lavenderblush:"#fff0f5",feldspar:"#d19275",indianred:"#cd5c5c",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightskyblue:"#87cefa",lightslategray:"#789",lightslateblue:"#8470ff",snow:"#fffafa",lightseagreen:"#20b2aa",lightsalmon:"#ffa07a",darksalmon:"#e9967a",darkviolet:"#9400d3",mediumpurple:"#9370d8",mediumaquamarine:"#66cdaa",skyblue:"#87ceeb",lavender:"#e6e6fa",lightsteelblue:"#b0c4de",mediumvioletred:"#c71585",mintcream:"#f5fffa",navajowhite:"#ffdead",navy:"#000080",olivedrab:"#6b8e23",palevioletred:"#d87093",violetred:"#d02090",yellow:"#ff0",yellowgreen:"#9acd32",lawngreen:"#7cfc00",pink:"#ffc0cb",paleturquoise:"#afeeee",palegoldenrod:"#eee8aa",darkolivegreen:"#556b2f",darkseagreen:"#8fbc8f",darkturquoise:"#00ced1",peachpuff:"#ffdab9",deeppink:"#ff1493",violet:"#ee82ee",palegreen:"#98fb98",mediumseagreen:"#3cb371",peru:"#cd853f",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",purple:"#800080",seagreen:"#2e8b57",seashell:"#fff5ee",papayawhip:"#ffefd5",mediumslateblue:"#7b68ee",plum:"#dda0dd",mediumspringgreen:"#00fa9a"};function O(n){return n<0?0:n>255?255:Math.round(n)||0}function Hs(n){return n<=0||n>1?Math.min(Math.max(n,0),1):Math.round(1e4*n)/1e4}const Le=/^#([0-9a-f])([0-9a-f])([0-9a-f])([0-9a-f])?$/i,$e=/^#([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})?$/i,Ve=/^rgb\(\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*\)$/,Te=/^rgba\(\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?\d*\.?\d+)\s*\)$/;function yt(n){(n=n.toLowerCase())in Ui&&(n=Ui[n]);{const t=Te.exec(n)||Ve.exec(n);if(t)return[O(parseInt(t[1],10)),O(parseInt(t[2],10)),O(parseInt(t[3],10)),Hs(t.length<5?1:parseFloat(t[4]))]}{const t=$e.exec(n);if(t)return[O(parseInt(t[1],16)),O(parseInt(t[2],16)),O(parseInt(t[3],16)),1]}{const t=Le.exec(n);if(t)return[O(17*parseInt(t[1],16)),O(17*parseInt(t[2],16)),O(17*parseInt(t[3],16)),1]}throw new Error(`Cannot parse color: ${n}`)}function Xs(n){return .199*n[0]+.687*n[1]+.114*n[2]}function Kt(n){const t=yt(n);return{t:`rgb(${t[0]}, ${t[1]}, ${t[2]})`,i:Xs(t)>160?"black":"white"}}class V{constructor(){this.h=[]}l(t,i,s){const e={o:t,_:i,u:s===!0};this.h.push(e)}v(t){const i=this.h.findIndex(s=>t===s.o);i>-1&&this.h.splice(i,1)}p(t){this.h=this.h.filter(i=>i._!==t)}m(t,i,s){const e=[...this.h];this.h=this.h.filter(h=>!h.u),e.forEach(h=>h.o(t,i,s))}M(){return this.h.length>0}S(){this.h=[]}}function F(n,...t){for(const i of t)for(const s in i)i[s]!==void 0&&Object.prototype.hasOwnProperty.call(i,s)&&!["__proto__","constructor","prototype"].includes(s)&&(typeof i[s]!="object"||n[s]===void 0||Array.isArray(i[s])?n[s]=i[s]:F(n[s],i[s]));return n}function H(n){return typeof n=="number"&&isFinite(n)}function xt(n){return typeof n=="number"&&n%1==0}function Ct(n){return typeof n=="string"}function Lt(n){return typeof n=="boolean"}function X(n){const t=n;if(!t||typeof t!="object")return t;let i,s,e;for(s in i=Array.isArray(t)?[]:{},t)t.hasOwnProperty(s)&&(e=t[s],i[s]=e&&typeof e=="object"?X(e):e);return i}function Ne(n){return n!==null}function St(n){return n===null?void 0:n}const Ci="-apple-system, BlinkMacSystemFont, 'Trebuchet MS', Roboto, Ubuntu, sans-serif";function dt(n,t,i){return t===void 0&&(t=Ci),`${i=i!==void 0?`${i} `:""}${n}px ${t}`}class We{constructor(t){this.k={C:1,T:5,P:NaN,R:"",D:"",V:"",O:"",B:0,A:0,I:0,L:0,N:0},this.F=t}W(){const t=this.k,i=this.j(),s=this.H();return t.P===i&&t.D===s||(t.P=i,t.D=s,t.R=dt(i,s),t.L=2.5/12*i,t.B=t.L,t.A=i/12*t.T,t.I=i/12*t.T,t.N=0),t.V=this.$(),t.O=this.U(),this.k}$(){return this.F.W().layout.textColor}U(){return this.F.q()}j(){return this.F.W().layout.fontSize}H(){return this.F.W().layout.fontFamily}}class Ei{constructor(){this.Y=[]}Z(t){this.Y=t}X(t,i,s){this.Y.forEach(e=>{e.X(t,i,s)})}}class j{X(t,i,s){t.useBitmapCoordinateSpace(e=>this.K(e,i,s))}}class De extends j{constructor(){super(...arguments),this.G=null}J(t){this.G=t}K({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(this.G===null||this.G.tt===null)return;const e=this.G.tt,h=this.G,r=Math.max(1,Math.floor(i))%2/2,o=l=>{t.beginPath();for(let a=e.to-1;a>=e.from;--a){const u=h.it[a],c=Math.round(u.nt*i)+r,d=u.st*s,f=l*s+r;t.moveTo(c,d),t.arc(c,d,f,0,2*Math.PI)}t.fill()};h.et>0&&(t.fillStyle=h.rt,o(h.ht+h.et)),t.fillStyle=h.lt,o(h.ht)}}function Pe(){return{it:[{nt:0,st:0,ot:0,_t:0}],lt:"",rt:"",ht:0,et:0,tt:null}}const Oe={from:0,to:1};class Be{constructor(t,i){this.ut=new Ei,this.ct=[],this.dt=[],this.ft=!0,this.F=t,this.vt=i,this.ut.Z(this.ct)}bt(t){const i=this.F.wt();i.length!==this.ct.length&&(this.dt=i.map(Pe),this.ct=this.dt.map(s=>{const e=new De;return e.J(s),e}),this.ut.Z(this.ct)),this.ft=!0}gt(){return this.ft&&(this.Mt(),this.ft=!1),this.ut}Mt(){const t=this.vt.W().mode===2,i=this.F.wt(),s=this.vt.xt(),e=this.F.St();i.forEach((h,r)=>{var o;const l=this.dt[r],a=h.kt(s);if(t||a===null||!h.yt())return void(l.tt=null);const u=p(h.Ct());l.lt=a.Tt,l.ht=a.ht,l.et=a.Pt,l.it[0]._t=a._t,l.it[0].st=h.Dt().Rt(a._t,u.Vt),l.rt=(o=a.Ot)!==null&&o!==void 0?o:this.F.Bt(l.it[0].st/h.Dt().At()),l.it[0].ot=s,l.it[0].nt=e.It(s),l.tt=Oe})}}class Ie extends j{constructor(t){super(),this.zt=t}K({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:e}){if(this.zt===null)return;const h=this.zt.Lt.yt,r=this.zt.Et.yt;if(!h&&!r)return;const o=Math.round(this.zt.nt*s),l=Math.round(this.zt.st*e);t.lineCap="butt",h&&o>=0&&(t.lineWidth=Math.floor(this.zt.Lt.et*s),t.strokeStyle=this.zt.Lt.V,t.fillStyle=this.zt.Lt.V,st(t,this.zt.Lt.Nt),function(a,u,c,d){a.beginPath();const f=a.lineWidth%2?.5:0;a.moveTo(u+f,c),a.lineTo(u+f,d),a.stroke()}(t,o,0,i.height)),r&&l>=0&&(t.lineWidth=Math.floor(this.zt.Et.et*e),t.strokeStyle=this.zt.Et.V,t.fillStyle=this.zt.Et.V,st(t,this.zt.Et.Nt),Ks(t,l,0,i.width))}}class Fe{constructor(t){this.ft=!0,this.Ft={Lt:{et:1,Nt:0,V:"",yt:!1},Et:{et:1,Nt:0,V:"",yt:!1},nt:0,st:0},this.Wt=new Ie(this.Ft),this.jt=t}bt(){this.ft=!0}gt(){return this.ft&&(this.Mt(),this.ft=!1),this.Wt}Mt(){const t=this.jt.yt(),i=p(this.jt.Ht()),s=i.$t().W().crosshair,e=this.Ft;if(s.mode===2)return e.Et.yt=!1,void(e.Lt.yt=!1);e.Et.yt=t&&this.jt.Ut(i),e.Lt.yt=t&&this.jt.qt(),e.Et.et=s.horzLine.width,e.Et.Nt=s.horzLine.style,e.Et.V=s.horzLine.color,e.Lt.et=s.vertLine.width,e.Lt.Nt=s.vertLine.style,e.Lt.V=s.vertLine.color,e.nt=this.jt.Yt(),e.st=this.jt.Zt()}}function je(n,t,i,s,e,h){n.fillRect(t+h,i,s-2*h,h),n.fillRect(t+h,i+e-h,s-2*h,h),n.fillRect(t,i,h,e),n.fillRect(t+s-h,i,h,e)}function Ht(n,t,i,s,e,h){n.save(),n.globalCompositeOperation="copy",n.fillStyle=h,n.fillRect(t,i,s,e),n.restore()}function Ji(n,t,i,s,e,h){n.beginPath(),n.roundRect?n.roundRect(t,i,s,e,h):(n.lineTo(t+s-h[1],i),h[1]!==0&&n.arcTo(t+s,i,t+s,i+h[1],h[1]),n.lineTo(t+s,i+e-h[2]),h[2]!==0&&n.arcTo(t+s,i+e,t+s-h[2],i+e,h[2]),n.lineTo(t+h[3],i+e),h[3]!==0&&n.arcTo(t,i+e,t,i+e-h[3],h[3]),n.lineTo(t,i+h[0]),h[0]!==0&&n.arcTo(t,i,t+h[0],i,h[0]))}function Gi(n,t,i,s,e,h,r=0,o=[0,0,0,0],l=""){if(n.save(),!r||!l||l===h)return Ji(n,t,i,s,e,o),n.fillStyle=h,n.fill(),void n.restore();const a=r/2;var u;Ji(n,t+a,i+a,s-r,e-r,(u=-a,o.map(c=>c===0?c:c+u))),h!=="transparent"&&(n.fillStyle=h,n.fill()),l!=="transparent"&&(n.lineWidth=r,n.strokeStyle=l,n.closePath(),n.stroke()),n.restore()}function As(n,t,i,s,e,h,r){n.save(),n.globalCompositeOperation="copy";const o=n.createLinearGradient(0,0,0,e);o.addColorStop(0,h),o.addColorStop(1,r),n.fillStyle=o,n.fillRect(t,i,s,e),n.restore()}class Qi{constructor(t,i){this.J(t,i)}J(t,i){this.zt=t,this.Xt=i}At(t,i){return this.zt.yt?t.P+t.L+t.B:0}X(t,i,s,e){if(!this.zt.yt||this.zt.Kt.length===0)return;const h=this.zt.V,r=this.Xt.t,o=t.useBitmapCoordinateSpace(l=>{const a=l.context;a.font=i.R;const u=this.Gt(l,i,s,e),c=u.Jt;return u.Qt?Gi(a,c.ti,c.ii,c.ni,c.si,r,c.ei,[c.ht,0,0,c.ht],r):Gi(a,c.ri,c.ii,c.ni,c.si,r,c.ei,[0,c.ht,c.ht,0],r),this.zt.hi&&(a.fillStyle=h,a.fillRect(c.ri,c.li,c.ai-c.ri,c.oi)),this.zt._i&&(a.fillStyle=i.O,a.fillRect(u.Qt?c.ui-c.ei:0,c.ii,c.ei,c.ci-c.ii)),u});t.useMediaCoordinateSpace(({context:l})=>{const a=o.di;l.font=i.R,l.textAlign=o.Qt?"right":"left",l.textBaseline="middle",l.fillStyle=h,l.fillText(this.zt.Kt,a.fi,(a.ii+a.ci)/2+a.pi)})}Gt(t,i,s,e){var h;const{context:r,bitmapSize:o,mediaSize:l,horizontalPixelRatio:a,verticalPixelRatio:u}=t,c=this.zt.hi||!this.zt.mi?i.T:0,d=this.zt.bi?i.C:0,f=i.L+this.Xt.wi,v=i.B+this.Xt.gi,m=i.A,g=i.I,b=this.zt.Kt,w=i.P,x=s.Mi(r,b),y=Math.ceil(s.xi(r,b)),S=w+f+v,R=i.C+m+g+y+c,C=Math.max(1,Math.floor(u));let _=Math.round(S*u);_%2!=C%2&&(_+=1);const E=d>0?Math.max(1,Math.floor(d*a)):0,M=Math.round(R*a),T=Math.round(c*a),B=(h=this.Xt.Si)!==null&&h!==void 0?h:this.Xt.ki,I=Math.round(B*u)-Math.floor(.5*u),z=Math.floor(I+C/2-_/2),U=z+_,q=e==="right",J=q?l.width-d:d,G=q?o.width-E:E;let ht,rt,ot;return q?(ht=G-M,rt=G-T,ot=J-c-m-d):(ht=G+M,rt=G+T,ot=J+c+m),{Qt:q,Jt:{ii:z,li:I,ci:U,ni:M,si:_,ht:2*a,ei:E,ti:ht,ri:G,ai:rt,oi:C,ui:o.width},di:{ii:z/u,ci:U/u,fi:ot,pi:x}}}}class Xt{constructor(t){this.yi={ki:0,t:"#000",gi:0,wi:0},this.Ci={Kt:"",yt:!1,hi:!0,mi:!1,Ot:"",V:"#FFF",_i:!1,bi:!1},this.Ti={Kt:"",yt:!1,hi:!1,mi:!0,Ot:"",V:"#FFF",_i:!0,bi:!0},this.ft=!0,this.Pi=new(t||Qi)(this.Ci,this.yi),this.Ri=new(t||Qi)(this.Ti,this.yi)}Kt(){return this.Di(),this.Ci.Kt}ki(){return this.Di(),this.yi.ki}bt(){this.ft=!0}At(t,i=!1){return Math.max(this.Pi.At(t,i),this.Ri.At(t,i))}Vi(){return this.yi.Si||0}Oi(t){this.yi.Si=t}Bi(){return this.Di(),this.Ci.yt||this.Ti.yt}Ai(){return this.Di(),this.Ci.yt}gt(t){return this.Di(),this.Ci.hi=this.Ci.hi&&t.W().ticksVisible,this.Ti.hi=this.Ti.hi&&t.W().ticksVisible,this.Pi.J(this.Ci,this.yi),this.Ri.J(this.Ti,this.yi),this.Pi}Ii(){return this.Di(),this.Pi.J(this.Ci,this.yi),this.Ri.J(this.Ti,this.yi),this.Ri}Di(){this.ft&&(this.Ci.hi=!0,this.Ti.hi=!1,this.zi(this.Ci,this.Ti,this.yi))}}class qe extends Xt{constructor(t,i,s){super(),this.jt=t,this.Li=i,this.Ei=s}zi(t,i,s){if(t.yt=!1,this.jt.W().mode===2)return;const e=this.jt.W().horzLine;if(!e.labelVisible)return;const h=this.Li.Ct();if(!this.jt.yt()||this.Li.Ni()||h===null)return;const r=Kt(e.labelBackgroundColor);s.t=r.t,t.V=r.i;const o=2/12*this.Li.P();s.wi=o,s.gi=o;const l=this.Ei(this.Li);s.ki=l.ki,t.Kt=this.Li.Fi(l._t,h),t.yt=!0}}const Ke=/[1-9]/g;class Us{constructor(){this.zt=null}J(t){this.zt=t}X(t,i){if(this.zt===null||this.zt.yt===!1||this.zt.Kt.length===0)return;const s=t.useMediaCoordinateSpace(({context:d})=>(d.font=i.R,Math.round(i.Wi.xi(d,p(this.zt).Kt,Ke))));if(s<=0)return;const e=i.ji,h=s+2*e,r=h/2,o=this.zt.Hi;let l=this.zt.ki,a=Math.floor(l-r)+.5;a<0?(l+=Math.abs(0-a),a=Math.floor(l-r)+.5):a+h>o&&(l-=Math.abs(o-(a+h)),a=Math.floor(l-r)+.5);const u=a+h,c=Math.ceil(0+i.C+i.T+i.L+i.P+i.B);t.useBitmapCoordinateSpace(({context:d,horizontalPixelRatio:f,verticalPixelRatio:v})=>{const m=p(this.zt);d.fillStyle=m.t;const g=Math.round(a*f),b=Math.round(0*v),w=Math.round(u*f),x=Math.round(c*v),y=Math.round(2*f);if(d.beginPath(),d.moveTo(g,b),d.lineTo(g,x-y),d.arcTo(g,x,g+y,x,y),d.lineTo(w-y,x),d.arcTo(w,x,w,x-y,y),d.lineTo(w,b),d.fill(),m.hi){const S=Math.round(m.ki*f),R=b,C=Math.round((R+i.T)*v);d.fillStyle=m.V;const _=Math.max(1,Math.floor(f)),E=Math.floor(.5*f);d.fillRect(S-E,R,_,C-R)}}),t.useMediaCoordinateSpace(({context:d})=>{const f=p(this.zt),v=0+i.C+i.T+i.L+i.P/2;d.font=i.R,d.textAlign="left",d.textBaseline="middle",d.fillStyle=f.V;const m=i.Wi.Mi(d,"Apr0");d.translate(a+e,v+m),d.fillText(f.Kt,0,0)})}}class He{constructor(t,i,s){this.ft=!0,this.Wt=new Us,this.Ft={yt:!1,t:"#4c525e",V:"white",Kt:"",Hi:0,ki:NaN,hi:!0},this.vt=t,this.$i=i,this.Ei=s}bt(){this.ft=!0}gt(){return this.ft&&(this.Mt(),this.ft=!1),this.Wt.J(this.Ft),this.Wt}Mt(){const t=this.Ft;if(t.yt=!1,this.vt.W().mode===2)return;const i=this.vt.W().vertLine;if(!i.labelVisible)return;const s=this.$i.St();if(s.Ni())return;t.Hi=s.Hi();const e=this.Ei();if(e===null)return;t.ki=e.ki;const h=s.Ui(this.vt.xt());t.Kt=s.qi(p(h)),t.yt=!0;const r=Kt(i.labelBackgroundColor);t.t=r.t,t.V=r.i,t.hi=s.W().ticksVisible}}class Ri{constructor(){this.Yi=null,this.Zi=0}Xi(){return this.Zi}Ki(t){this.Zi=t}Dt(){return this.Yi}Gi(t){this.Yi=t}Ji(t){return[]}Qi(){return[]}yt(){return!0}}var Yi;(function(n){n[n.Normal=0]="Normal",n[n.Magnet=1]="Magnet",n[n.Hidden=2]="Hidden"})(Yi||(Yi={}));class Xe extends Ri{constructor(t,i){super(),this.tn=null,this.nn=NaN,this.sn=0,this.en=!0,this.rn=new Map,this.hn=!1,this.ln=NaN,this.an=NaN,this._n=NaN,this.un=NaN,this.$i=t,this.cn=i,this.dn=new Be(t,this),this.fn=((e,h)=>r=>{const o=h(),l=e();if(r===p(this.tn).vn())return{_t:l,ki:o};{const a=p(r.Ct());return{_t:r.pn(o,a),ki:o}}})(()=>this.nn,()=>this.an);const s=((e,h)=>()=>{const r=this.$i.St().mn(e()),o=h();return r&&Number.isFinite(o)?{ot:r,ki:o}:null})(()=>this.sn,()=>this.Yt());this.bn=new He(this,t,s),this.wn=new Fe(this)}W(){return this.cn}gn(t,i){this._n=t,this.un=i}Mn(){this._n=NaN,this.un=NaN}xn(){return this._n}Sn(){return this.un}kn(t,i,s){this.hn||(this.hn=!0),this.en=!0,this.yn(t,i,s)}xt(){return this.sn}Yt(){return this.ln}Zt(){return this.an}yt(){return this.en}Cn(){this.en=!1,this.Tn(),this.nn=NaN,this.ln=NaN,this.an=NaN,this.tn=null,this.Mn()}Pn(t){return this.tn!==null?[this.wn,this.dn]:[]}Ut(t){return t===this.tn&&this.cn.horzLine.visible}qt(){return this.cn.vertLine.visible}Rn(t,i){this.en&&this.tn===t||this.rn.clear();const s=[];return this.tn===t&&s.push(this.Dn(this.rn,i,this.fn)),s}Qi(){return this.en?[this.bn]:[]}Ht(){return this.tn}Vn(){this.wn.bt(),this.rn.forEach(t=>t.bt()),this.bn.bt(),this.dn.bt()}On(t){return t&&!t.vn().Ni()?t.vn():null}yn(t,i,s){this.Bn(t,i,s)&&this.Vn()}Bn(t,i,s){const e=this.ln,h=this.an,r=this.nn,o=this.sn,l=this.tn,a=this.On(s);this.sn=t,this.ln=isNaN(t)?NaN:this.$i.St().It(t),this.tn=s;const u=a!==null?a.Ct():null;return a!==null&&u!==null?(this.nn=i,this.an=a.Rt(i,u)):(this.nn=NaN,this.an=NaN),e!==this.ln||h!==this.an||o!==this.sn||r!==this.nn||l!==this.tn}Tn(){const t=this.$i.wt().map(s=>s.In().An()).filter(Ne),i=t.length===0?null:Math.max(...t);this.sn=i!==null?i:NaN}Dn(t,i,s){let e=t.get(i);return e===void 0&&(e=new qe(this,i,s),t.set(i,e)),e}}function At(n){return n==="left"||n==="right"}class W{constructor(t){this.zn=new Map,this.Ln=[],this.En=t}Nn(t,i){const s=function(e,h){return e===void 0?h:{Fn:Math.max(e.Fn,h.Fn),Wn:e.Wn||h.Wn}}(this.zn.get(t),i);this.zn.set(t,s)}jn(){return this.En}Hn(t){const i=this.zn.get(t);return i===void 0?{Fn:this.En}:{Fn:Math.max(this.En,i.Fn),Wn:i.Wn}}$n(){this.Un(),this.Ln=[{qn:0}]}Yn(t){this.Un(),this.Ln=[{qn:1,Vt:t}]}Zn(t){this.Xn(),this.Ln.push({qn:5,Vt:t})}Un(){this.Xn(),this.Ln.push({qn:6})}Kn(){this.Un(),this.Ln=[{qn:4}]}Gn(t){this.Un(),this.Ln.push({qn:2,Vt:t})}Jn(t){this.Un(),this.Ln.push({qn:3,Vt:t})}Qn(){return this.Ln}ts(t){for(const i of t.Ln)this.ns(i);this.En=Math.max(this.En,t.En),t.zn.forEach((i,s)=>{this.Nn(s,i)})}static ss(){return new W(2)}static es(){return new W(3)}ns(t){switch(t.qn){case 0:this.$n();break;case 1:this.Yn(t.Vt);break;case 2:this.Gn(t.Vt);break;case 3:this.Jn(t.Vt);break;case 4:this.Kn();break;case 5:this.Zn(t.Vt);break;case 6:this.Xn()}}Xn(){const t=this.Ln.findIndex(i=>i.qn===5);t!==-1&&this.Ln.splice(t,1)}}const Zi=".";function A(n,t){if(!H(n))return"n/a";if(!xt(t))throw new TypeError("invalid length");if(t<0||t>16)throw new TypeError("invalid length");return t===0?n.toString():("0000000000000000"+n.toString()).slice(-t)}class Ut{constructor(t,i){if(i||(i=1),H(t)&&xt(t)||(t=100),t<0)throw new TypeError("invalid base");this.Li=t,this.rs=i,this.hs()}format(t){const i=t<0?"−":"";return t=Math.abs(t),i+this.ls(t)}hs(){if(this._s=0,this.Li>0&&this.rs>0){let t=this.Li;for(;t>1;)t/=10,this._s++}}ls(t){const i=this.Li/this.rs;let s=Math.floor(t),e="";const h=this._s!==void 0?this._s:NaN;if(i>1){let r=+(Math.round(t*i)-s*i).toFixed(this._s);r>=i&&(r-=i,s+=1),e=Zi+A(+r.toFixed(this._s)*this.rs,h)}else s=Math.round(s*i)/i,h>0&&(e=Zi+A(0,h));return s.toFixed(0)+e}}class Js extends Ut{constructor(t=100){super(t)}format(t){return`${super.format(t)}%`}}class Ae{constructor(t){this.us=t}format(t){let i="";return t<0&&(i="-",t=-t),t<995?i+this.cs(t):t<999995?i+this.cs(t/1e3)+"K":t<999999995?(t=1e3*Math.round(t/1e3),i+this.cs(t/1e6)+"M"):(t=1e6*Math.round(t/1e6),i+this.cs(t/1e9)+"B")}cs(t){let i;const s=Math.pow(10,this.us);return i=(t=Math.round(t*s)/s)>=1e-15&&t<1?t.toFixed(this.us).replace(/\.?0+$/,""):String(t),i.replace(/(\.[1-9]*)0+$/,(e,h)=>h)}}function Gs(n,t,i,s,e,h,r){if(t.length===0||s.from>=t.length||s.to<=0)return;const{context:o,horizontalPixelRatio:l,verticalPixelRatio:a}=n,u=t[s.from];let c=h(n,u),d=u;if(s.to-s.from<2){const f=e/2;o.beginPath();const v={nt:u.nt-f,st:u.st},m={nt:u.nt+f,st:u.st};o.moveTo(v.nt*l,v.st*a),o.lineTo(m.nt*l,m.st*a),r(n,c,v,m)}else{const f=(m,g)=>{r(n,c,d,g),o.beginPath(),c=m,d=g};let v=d;o.beginPath(),o.moveTo(u.nt*l,u.st*a);for(let m=s.from+1;m<s.to;++m){v=t[m];const g=h(n,v);switch(i){case 0:o.lineTo(v.nt*l,v.st*a);break;case 1:o.lineTo(v.nt*l,t[m-1].st*a),g!==c&&(f(g,v),o.lineTo(v.nt*l,t[m-1].st*a)),o.lineTo(v.nt*l,v.st*a);break;case 2:{const[b,w]=Ue(t,m-1,m);o.bezierCurveTo(b.nt*l,b.st*a,w.nt*l,w.st*a,v.nt*l,v.st*a);break}}i!==1&&g!==c&&(f(g,v),o.moveTo(v.nt*l,v.st*a))}(d!==v||d===v&&i===1)&&r(n,c,d,v)}}const ts=6;function Qt(n,t){return{nt:n.nt-t.nt,st:n.st-t.st}}function is(n,t){return{nt:n.nt/t,st:n.st/t}}function Ue(n,t,i){const s=Math.max(0,t-1),e=Math.min(n.length-1,i+1);var h,r;return[(h=n[t],r=is(Qt(n[i],n[s]),ts),{nt:h.nt+r.nt,st:h.st+r.st}),Qt(n[i],is(Qt(n[e],n[t]),ts))]}function Je(n,t,i,s,e){const{context:h,horizontalPixelRatio:r,verticalPixelRatio:o}=t;h.lineTo(e.nt*r,n*o),h.lineTo(s.nt*r,n*o),h.closePath(),h.fillStyle=i,h.fill()}class Qs extends j{constructor(){super(...arguments),this.G=null}J(t){this.G=t}K(t){var i;if(this.G===null)return;const{it:s,tt:e,ds:h,et:r,Nt:o,fs:l}=this.G,a=(i=this.G.vs)!==null&&i!==void 0?i:this.G.ps?0:t.mediaSize.height;if(e===null)return;const u=t.context;u.lineCap="butt",u.lineJoin="round",u.lineWidth=r,st(u,o),u.lineWidth=1,Gs(t,s,l,e,h,this.bs.bind(this),Je.bind(null,a))}}function yi(n,t,i){return Math.min(Math.max(n,t),i)}function $t(n,t,i){return t-n<=i}function Ys(n){const t=Math.ceil(n);return t%2==0?t-1:t}class ki{ws(t,i){const s=this.gs,{Ms:e,xs:h,Ss:r,ks:o,ys:l,vs:a}=i;if(this.Cs===void 0||s===void 0||s.Ms!==e||s.xs!==h||s.Ss!==r||s.ks!==o||s.vs!==a||s.ys!==l){const u=t.context.createLinearGradient(0,0,0,l);if(u.addColorStop(0,e),a!=null){const c=yi(a*t.verticalPixelRatio/l,0,1);u.addColorStop(c,h),u.addColorStop(c,r)}u.addColorStop(1,o),this.Cs=u,this.gs=i}return this.Cs}}class Ge extends Qs{constructor(){super(...arguments),this.Ts=new ki}bs(t,i){return this.Ts.ws(t,{Ms:i.Ps,xs:"",Ss:"",ks:i.Rs,ys:t.bitmapSize.height})}}function Qe(n,t){const i=n.context;i.strokeStyle=t,i.stroke()}class Zs extends j{constructor(){super(...arguments),this.G=null}J(t){this.G=t}K(t){if(this.G===null)return;const{it:i,tt:s,ds:e,fs:h,et:r,Nt:o,Ds:l}=this.G;if(s===null)return;const a=t.context;a.lineCap="butt",a.lineWidth=r*t.verticalPixelRatio,st(a,o),a.lineJoin="round";const u=this.Vs.bind(this);h!==void 0&&Gs(t,i,h,s,e,u,Qe),l&&function(c,d,f,v,m){const{horizontalPixelRatio:g,verticalPixelRatio:b,context:w}=c;let x=null;const y=Math.max(1,Math.floor(g))%2/2,S=f*b+y;for(let R=v.to-1;R>=v.from;--R){const C=d[R];if(C){const _=m(c,C);_!==x&&(w.beginPath(),x!==null&&w.fill(),w.fillStyle=_,x=_);const E=Math.round(C.nt*g)+y,M=C.st*b;w.moveTo(E,M),w.arc(E,M,S,0,2*Math.PI)}}w.fill()}(t,i,l,s,u)}}class te extends Zs{Vs(t,i){return i.lt}}function ie(n,t,i,s,e=0,h=t.length){let r=h-e;for(;0<r;){const o=r>>1,l=e+o;s(t[l],i)===n?(e=l+1,r-=o+1):r=o}return e}const Et=ie.bind(null,!0),se=ie.bind(null,!1);function Ye(n,t){return n.ot<t}function Ze(n,t){return t<n.ot}function ee(n,t,i){const s=t.Os(),e=t.ui(),h=Et(n,s,Ye),r=se(n,e,Ze);if(!i)return{from:h,to:r};let o=h,l=r;return h>0&&h<n.length&&n[h].ot>=s&&(o=h-1),r>0&&r<n.length&&n[r-1].ot<=e&&(l=r+1),{from:o,to:l}}class Li{constructor(t,i,s){this.Bs=!0,this.As=!0,this.Is=!0,this.zs=[],this.Ls=null,this.Es=t,this.Ns=i,this.Fs=s}bt(t){this.Bs=!0,t==="data"&&(this.As=!0),t==="options"&&(this.Is=!0)}gt(){return this.Es.yt()?(this.Ws(),this.Ls===null?null:this.js):null}Hs(){this.zs=this.zs.map(t=>Object.assign(Object.assign({},t),this.Es.Us().$s(t.ot)))}qs(){this.Ls=null}Ws(){this.As&&(this.Ys(),this.As=!1),this.Is&&(this.Hs(),this.Is=!1),this.Bs&&(this.Zs(),this.Bs=!1)}Zs(){const t=this.Es.Dt(),i=this.Ns.St();if(this.qs(),i.Ni()||t.Ni())return;const s=i.Xs();if(s===null||this.Es.In().Ks()===0)return;const e=this.Es.Ct();e!==null&&(this.Ls=ee(this.zs,s,this.Fs),this.Gs(t,i,e.Vt),this.Js())}}class Jt extends Li{constructor(t,i){super(t,i,!0)}Gs(t,i,s){i.Qs(this.zs,St(this.Ls)),t.te(this.zs,s,St(this.Ls))}ie(t,i){return{ot:t,_t:i,nt:NaN,st:NaN}}Ys(){const t=this.Es.Us();this.zs=this.Es.In().ne().map(i=>{const s=i.Vt[3];return this.se(i.ee,s,t)})}}class tn extends Jt{constructor(t,i){super(t,i),this.js=new Ei,this.re=new Ge,this.he=new te,this.js.Z([this.re,this.he])}se(t,i,s){return Object.assign(Object.assign({},this.ie(t,i)),s.$s(t))}Js(){const t=this.Es.W();this.re.J({fs:t.lineType,it:this.zs,Nt:t.lineStyle,et:t.lineWidth,vs:null,ps:t.invertFilledArea,tt:this.Ls,ds:this.Ns.St().le()}),this.he.J({fs:t.lineVisible?t.lineType:void 0,it:this.zs,Nt:t.lineStyle,et:t.lineWidth,tt:this.Ls,ds:this.Ns.St().le(),Ds:t.pointMarkersVisible?t.pointMarkersRadius||t.lineWidth/2+2:void 0})}}class sn extends j{constructor(){super(...arguments),this.zt=null,this.ae=0,this.oe=0}J(t){this.zt=t}K({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(this.zt===null||this.zt.In.length===0||this.zt.tt===null)return;this.ae=this._e(i),this.ae>=2&&Math.max(1,Math.floor(i))%2!=this.ae%2&&this.ae--,this.oe=this.zt.ue?Math.min(this.ae,Math.floor(i)):this.ae;let e=null;const h=this.oe<=this.ae&&this.zt.le>=Math.floor(1.5*i);for(let r=this.zt.tt.from;r<this.zt.tt.to;++r){const o=this.zt.In[r];e!==o.ce&&(t.fillStyle=o.ce,e=o.ce);const l=Math.floor(.5*this.oe),a=Math.round(o.nt*i),u=a-l,c=this.oe,d=u+c-1,f=Math.min(o.de,o.fe),v=Math.max(o.de,o.fe),m=Math.round(f*s)-l,g=Math.round(v*s)+l,b=Math.max(g-m,this.oe);t.fillRect(u,m,c,b);const w=Math.ceil(1.5*this.ae);if(h){if(this.zt.ve){const R=a-w;let C=Math.max(m,Math.round(o.pe*s)-l),_=C+c-1;_>m+b-1&&(_=m+b-1,C=_-c+1),t.fillRect(R,C,u-R,_-C+1)}const x=a+w;let y=Math.max(m,Math.round(o.me*s)-l),S=y+c-1;S>m+b-1&&(S=m+b-1,y=S-c+1),t.fillRect(d+1,y,x-d,S-y+1)}}}_e(t){const i=Math.floor(t);return Math.max(i,Math.floor(function(s,e){return Math.floor(.3*s*e)}(p(this.zt).le,t)))}}class ne extends Li{constructor(t,i){super(t,i,!1)}Gs(t,i,s){i.Qs(this.zs,St(this.Ls)),t.be(this.zs,s,St(this.Ls))}we(t,i,s){return{ot:t,ge:i.Vt[0],Me:i.Vt[1],xe:i.Vt[2],Se:i.Vt[3],nt:NaN,pe:NaN,de:NaN,fe:NaN,me:NaN}}Ys(){const t=this.Es.Us();this.zs=this.Es.In().ne().map(i=>this.se(i.ee,i,t))}}class en extends ne{constructor(){super(...arguments),this.js=new sn}se(t,i,s){return Object.assign(Object.assign({},this.we(t,i,s)),s.$s(t))}Js(){const t=this.Es.W();this.js.J({In:this.zs,le:this.Ns.St().le(),ve:t.openVisible,ue:t.thinBars,tt:this.Ls})}}class nn extends Qs{constructor(){super(...arguments),this.Ts=new ki}bs(t,i){const s=this.G;return this.Ts.ws(t,{Ms:i.ke,xs:i.ye,Ss:i.Ce,ks:i.Te,ys:t.bitmapSize.height,vs:s.vs})}}class hn extends Zs{constructor(){super(...arguments),this.Pe=new ki}Vs(t,i){const s=this.G;return this.Pe.ws(t,{Ms:i.Re,xs:i.Re,Ss:i.De,ks:i.De,ys:t.bitmapSize.height,vs:s.vs})}}class rn extends Jt{constructor(t,i){super(t,i),this.js=new Ei,this.Ve=new nn,this.Oe=new hn,this.js.Z([this.Ve,this.Oe])}se(t,i,s){return Object.assign(Object.assign({},this.ie(t,i)),s.$s(t))}Js(){const t=this.Es.Ct();if(t===null)return;const i=this.Es.W(),s=this.Es.Dt().Rt(i.baseValue.price,t.Vt),e=this.Ns.St().le();this.Ve.J({it:this.zs,et:i.lineWidth,Nt:i.lineStyle,fs:i.lineType,vs:s,ps:!1,tt:this.Ls,ds:e}),this.Oe.J({it:this.zs,et:i.lineWidth,Nt:i.lineStyle,fs:i.lineVisible?i.lineType:void 0,Ds:i.pointMarkersVisible?i.pointMarkersRadius||i.lineWidth/2+2:void 0,vs:s,tt:this.Ls,ds:e})}}class on extends j{constructor(){super(...arguments),this.zt=null,this.ae=0}J(t){this.zt=t}K(t){if(this.zt===null||this.zt.In.length===0||this.zt.tt===null)return;const{horizontalPixelRatio:i}=t;this.ae=function(h,r){if(h>=2.5&&h<=4)return Math.floor(3*r);const o=1-.2*Math.atan(Math.max(4,h)-4)/(.5*Math.PI),l=Math.floor(h*o*r),a=Math.floor(h*r),u=Math.min(l,a);return Math.max(Math.floor(r),u)}(this.zt.le,i),this.ae>=2&&Math.floor(i)%2!=this.ae%2&&this.ae--;const s=this.zt.In;this.zt.Be&&this.Ae(t,s,this.zt.tt),this.zt._i&&this.Ie(t,s,this.zt.tt);const e=this.ze(i);(!this.zt._i||this.ae>2*e)&&this.Le(t,s,this.zt.tt)}Ae(t,i,s){if(this.zt===null)return;const{context:e,horizontalPixelRatio:h,verticalPixelRatio:r}=t;let o="",l=Math.min(Math.floor(h),Math.floor(this.zt.le*h));l=Math.max(Math.floor(h),Math.min(l,this.ae));const a=Math.floor(.5*l);let u=null;for(let c=s.from;c<s.to;c++){const d=i[c];d.Ee!==o&&(e.fillStyle=d.Ee,o=d.Ee);const f=Math.round(Math.min(d.pe,d.me)*r),v=Math.round(Math.max(d.pe,d.me)*r),m=Math.round(d.de*r),g=Math.round(d.fe*r);let b=Math.round(h*d.nt)-a;const w=b+l-1;u!==null&&(b=Math.max(u+1,b),b=Math.min(b,w));const x=w-b+1;e.fillRect(b,m,x,f-m),e.fillRect(b,v+1,x,g-v),u=w}}ze(t){let i=Math.floor(1*t);this.ae<=2*i&&(i=Math.floor(.5*(this.ae-1)));const s=Math.max(Math.floor(t),i);return this.ae<=2*s?Math.max(Math.floor(t),Math.floor(1*t)):s}Ie(t,i,s){if(this.zt===null)return;const{context:e,horizontalPixelRatio:h,verticalPixelRatio:r}=t;let o="";const l=this.ze(h);let a=null;for(let u=s.from;u<s.to;u++){const c=i[u];c.Ne!==o&&(e.fillStyle=c.Ne,o=c.Ne);let d=Math.round(c.nt*h)-Math.floor(.5*this.ae);const f=d+this.ae-1,v=Math.round(Math.min(c.pe,c.me)*r),m=Math.round(Math.max(c.pe,c.me)*r);if(a!==null&&(d=Math.max(a+1,d),d=Math.min(d,f)),this.zt.le*h>2*l)je(e,d,v,f-d+1,m-v+1,l);else{const g=f-d+1;e.fillRect(d,v,g,m-v+1)}a=f}}Le(t,i,s){if(this.zt===null)return;const{context:e,horizontalPixelRatio:h,verticalPixelRatio:r}=t;let o="";const l=this.ze(h);for(let a=s.from;a<s.to;a++){const u=i[a];let c=Math.round(Math.min(u.pe,u.me)*r),d=Math.round(Math.max(u.pe,u.me)*r),f=Math.round(u.nt*h)-Math.floor(.5*this.ae),v=f+this.ae-1;if(u.ce!==o){const m=u.ce;e.fillStyle=m,o=m}this.zt._i&&(f+=l,c+=l,v-=l,d-=l),c>d||e.fillRect(f,c,v-f+1,d-c+1)}}}class ln extends ne{constructor(){super(...arguments),this.js=new on}se(t,i,s){return Object.assign(Object.assign({},this.we(t,i,s)),s.$s(t))}Js(){const t=this.Es.W();this.js.J({In:this.zs,le:this.Ns.St().le(),Be:t.wickVisible,_i:t.borderVisible,tt:this.Ls})}}class an{constructor(t,i){this.Fe=t,this.Li=i}X(t,i,s){this.Fe.draw(t,this.Li,i,s)}}class Yt extends Li{constructor(t,i,s){super(t,i,!1),this.wn=s,this.js=new an(this.wn.renderer(),e=>{const h=t.Ct();return h===null?null:t.Dt().Rt(e,h.Vt)})}We(t){return this.wn.priceValueBuilder(t)}je(t){return this.wn.isWhitespace(t)}Ys(){const t=this.Es.Us();this.zs=this.Es.In().ne().map(i=>Object.assign(Object.assign({ot:i.ee,nt:NaN},t.$s(i.ee)),{He:i.$e}))}Gs(t,i){i.Qs(this.zs,St(this.Ls))}Js(){this.wn.update({bars:this.zs.map(un),barSpacing:this.Ns.St().le(),visibleRange:this.Ls},this.Es.W())}}function un(n){return{x:n.nt,time:n.ot,originalData:n.He,barColor:n.ce}}class cn extends j{constructor(){super(...arguments),this.zt=null,this.Ue=[]}J(t){this.zt=t,this.Ue=[]}K({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(this.zt===null||this.zt.it.length===0||this.zt.tt===null)return;this.Ue.length||this.qe(i);const e=Math.max(1,Math.floor(s)),h=Math.round(this.zt.Ye*s)-Math.floor(e/2),r=h+e;for(let o=this.zt.tt.from;o<this.zt.tt.to;o++){const l=this.zt.it[o],a=this.Ue[o-this.zt.tt.from],u=Math.round(l.st*s);let c,d;t.fillStyle=l.ce,u<=h?(c=u,d=r):(c=h,d=u-Math.floor(e/2)+e),t.fillRect(a.Os,c,a.ui-a.Os+1,d-c)}}qe(t){if(this.zt===null||this.zt.it.length===0||this.zt.tt===null)return void(this.Ue=[]);const i=Math.ceil(this.zt.le*t)<=1?0:Math.max(1,Math.floor(t)),s=Math.round(this.zt.le*t)-i;this.Ue=new Array(this.zt.tt.to-this.zt.tt.from);for(let h=this.zt.tt.from;h<this.zt.tt.to;h++){const r=this.zt.it[h],o=Math.round(r.nt*t);let l,a;if(s%2){const u=(s-1)/2;l=o-u,a=o+u}else{const u=s/2;l=o-u,a=o+u-1}this.Ue[h-this.zt.tt.from]={Os:l,ui:a,Ze:o,Xe:r.nt*t,ot:r.ot}}for(let h=this.zt.tt.from+1;h<this.zt.tt.to;h++){const r=this.Ue[h-this.zt.tt.from],o=this.Ue[h-this.zt.tt.from-1];r.ot===o.ot+1&&r.Os-o.ui!==i+1&&(o.Ze>o.Xe?o.ui=r.Os-i-1:r.Os=o.ui+i+1)}let e=Math.ceil(this.zt.le*t);for(let h=this.zt.tt.from;h<this.zt.tt.to;h++){const r=this.Ue[h-this.zt.tt.from];r.ui<r.Os&&(r.ui=r.Os);const o=r.ui-r.Os+1;e=Math.min(o,e)}if(i>0&&e<4)for(let h=this.zt.tt.from;h<this.zt.tt.to;h++){const r=this.Ue[h-this.zt.tt.from];r.ui-r.Os+1>e&&(r.Ze>r.Xe?r.ui-=1:r.Os+=1)}}}class dn extends Jt{constructor(){super(...arguments),this.js=new cn}se(t,i,s){return Object.assign(Object.assign({},this.ie(t,i)),s.$s(t))}Js(){const t={it:this.zs,le:this.Ns.St().le(),tt:this.Ls,Ye:this.Es.Dt().Rt(this.Es.W().base,p(this.Es.Ct()).Vt)};this.js.J(t)}}class fn extends Jt{constructor(){super(...arguments),this.js=new te}se(t,i,s){return Object.assign(Object.assign({},this.ie(t,i)),s.$s(t))}Js(){const t=this.Es.W(),i={it:this.zs,Nt:t.lineStyle,fs:t.lineVisible?t.lineType:void 0,et:t.lineWidth,Ds:t.pointMarkersVisible?t.pointMarkersRadius||t.lineWidth/2+2:void 0,tt:this.Ls,ds:this.Ns.St().le()};this.js.J(i)}}const mn=/[2-9]/g;class Mt{constructor(t=50){this.Ke=0,this.Ge=1,this.Je=1,this.Qe={},this.tr=new Map,this.ir=t}nr(){this.Ke=0,this.tr.clear(),this.Ge=1,this.Je=1,this.Qe={}}xi(t,i,s){return this.sr(t,i,s).width}Mi(t,i,s){const e=this.sr(t,i,s);return((e.actualBoundingBoxAscent||0)-(e.actualBoundingBoxDescent||0))/2}sr(t,i,s){const e=s||mn,h=String(i).replace(e,"0");if(this.tr.has(h))return D(this.tr.get(h)).er;if(this.Ke===this.ir){const o=this.Qe[this.Je];delete this.Qe[this.Je],this.tr.delete(o),this.Je++,this.Ke--}t.save(),t.textBaseline="middle";const r=t.measureText(h);return t.restore(),r.width===0&&i.length||(this.tr.set(h,{er:r,rr:this.Ge}),this.Qe[this.Ge]=h,this.Ke++,this.Ge++),r}}class vn{constructor(t){this.hr=null,this.k=null,this.lr="right",this.ar=t}_r(t,i,s){this.hr=t,this.k=i,this.lr=s}X(t){this.k!==null&&this.hr!==null&&this.hr.X(t,this.k,this.ar,this.lr)}}class he{constructor(t,i,s){this.ur=t,this.ar=new Mt(50),this.cr=i,this.F=s,this.j=-1,this.Wt=new vn(this.ar)}gt(){const t=this.F.dr(this.cr);if(t===null)return null;const i=t.vr(this.cr)?t.pr():this.cr.Dt();if(i===null)return null;const s=t.mr(i);if(s==="overlay")return null;const e=this.F.br();return e.P!==this.j&&(this.j=e.P,this.ar.nr()),this.Wt._r(this.ur.Ii(),e,s),this.Wt}}class pn extends j{constructor(){super(...arguments),this.zt=null}J(t){this.zt=t}wr(t,i){var s;if(!(!((s=this.zt)===null||s===void 0)&&s.yt))return null;const{st:e,et:h,gr:r}=this.zt;return i>=e-h-7&&i<=e+h+7?{Mr:this.zt,gr:r}:null}K({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:e}){if(this.zt===null||this.zt.yt===!1)return;const h=Math.round(this.zt.st*e);h<0||h>i.height||(t.lineCap="butt",t.strokeStyle=this.zt.V,t.lineWidth=Math.floor(this.zt.et*s),st(t,this.zt.Nt),Ks(t,h,0,i.width))}}class $i{constructor(t){this.Sr={st:0,V:"rgba(0, 0, 0, 0)",et:1,Nt:0,yt:!1},this.kr=new pn,this.ft=!0,this.Es=t,this.Ns=t.$t(),this.kr.J(this.Sr)}bt(){this.ft=!0}gt(){return this.Es.yt()?(this.ft&&(this.yr(),this.ft=!1),this.kr):null}}class gn extends $i{constructor(t){super(t)}yr(){this.Sr.yt=!1;const t=this.Es.Dt(),i=t.Cr().Cr;if(i!==2&&i!==3)return;const s=this.Es.W();if(!s.baseLineVisible||!this.Es.yt())return;const e=this.Es.Ct();e!==null&&(this.Sr.yt=!0,this.Sr.st=t.Rt(e.Vt,e.Vt),this.Sr.V=s.baseLineColor,this.Sr.et=s.baseLineWidth,this.Sr.Nt=s.baseLineStyle)}}class bn extends j{constructor(){super(...arguments),this.zt=null}J(t){this.zt=t}$e(){return this.zt}K({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){const e=this.zt;if(e===null)return;const h=Math.max(1,Math.floor(i)),r=h%2/2,o=Math.round(e.Xe.x*i)+r,l=e.Xe.y*s;t.fillStyle=e.Tr,t.beginPath();const a=Math.max(2,1.5*e.Pr)*i;t.arc(o,l,a,0,2*Math.PI,!1),t.fill(),t.fillStyle=e.Rr,t.beginPath(),t.arc(o,l,e.ht*i,0,2*Math.PI,!1),t.fill(),t.lineWidth=h,t.strokeStyle=e.Dr,t.beginPath(),t.arc(o,l,e.ht*i+h/2,0,2*Math.PI,!1),t.stroke()}}const wn=[{Vr:0,Or:.25,Br:4,Ar:10,Ir:.25,zr:0,Lr:.4,Er:.8},{Vr:.25,Or:.525,Br:10,Ar:14,Ir:0,zr:0,Lr:.8,Er:0},{Vr:.525,Or:1,Br:14,Ar:14,Ir:0,zr:0,Lr:0,Er:0}];function ss(n,t,i,s){return function(e,h){if(e==="transparent")return e;const r=yt(e),o=r[3];return`rgba(${r[0]}, ${r[1]}, ${r[2]}, ${h*o})`}(n,i+(s-i)*t)}function es(n,t){const i=n%2600/2600;let s;for(const l of wn)if(i>=l.Vr&&i<=l.Or){s=l;break}Y(s!==void 0,"Last price animation internal logic error");const e=(i-s.Vr)/(s.Or-s.Vr);return{Rr:ss(t,e,s.Ir,s.zr),Dr:ss(t,e,s.Lr,s.Er),ht:(h=e,r=s.Br,o=s.Ar,r+(o-r)*h)};var h,r,o}class yn{constructor(t){this.Wt=new bn,this.ft=!0,this.Nr=!0,this.Fr=performance.now(),this.Wr=this.Fr-1,this.jr=t}Hr(){this.Wr=this.Fr-1,this.bt()}$r(){if(this.bt(),this.jr.W().lastPriceAnimation===2){const t=performance.now(),i=this.Wr-t;if(i>0)return void(i<650&&(this.Wr+=2600));this.Fr=t,this.Wr=t+2600}}bt(){this.ft=!0}Ur(){this.Nr=!0}yt(){return this.jr.W().lastPriceAnimation!==0}qr(){switch(this.jr.W().lastPriceAnimation){case 0:return!1;case 1:return!0;case 2:return performance.now()<=this.Wr}}gt(){return this.ft?(this.Mt(),this.ft=!1,this.Nr=!1):this.Nr&&(this.Yr(),this.Nr=!1),this.Wt}Mt(){this.Wt.J(null);const t=this.jr.$t().St(),i=t.Xs(),s=this.jr.Ct();if(i===null||s===null)return;const e=this.jr.Zr(!0);if(e.Xr||!i.Kr(e.ee))return;const h={x:t.It(e.ee),y:this.jr.Dt().Rt(e._t,s.Vt)},r=e.V,o=this.jr.W().lineWidth,l=es(this.Gr(),r);this.Wt.J({Tr:r,Pr:o,Rr:l.Rr,Dr:l.Dr,ht:l.ht,Xe:h})}Yr(){const t=this.Wt.$e();if(t!==null){const i=es(this.Gr(),t.Tr);t.Rr=i.Rr,t.Dr=i.Dr,t.ht=i.ht}}Gr(){return this.qr()?performance.now()-this.Fr:2599}}function pt(n,t){return Ys(Math.min(Math.max(n,12),30)*t)}function _t(n,t){switch(n){case"arrowDown":case"arrowUp":return pt(t,1);case"circle":return pt(t,.8);case"square":return pt(t,.7)}}function re(n){return function(t){const i=Math.ceil(t);return i%2!=0?i-1:i}(pt(n,1))}function ns(n){return Math.max(pt(n,.1),3)}function hs(n,t,i){return t?n:i?Math.ceil(n/2):0}function oe(n,t,i,s,e){const h=_t("square",i),r=(h-1)/2,o=n-r,l=t-r;return s>=o&&s<=o+h&&e>=l&&e<=l+h}function rs(n,t,i,s){const e=(_t("arrowUp",s)-1)/2*i.Jr,h=(Ys(s/2)-1)/2*i.Jr;t.beginPath(),n?(t.moveTo(i.nt-e,i.st),t.lineTo(i.nt,i.st-e),t.lineTo(i.nt+e,i.st),t.lineTo(i.nt+h,i.st),t.lineTo(i.nt+h,i.st+e),t.lineTo(i.nt-h,i.st+e),t.lineTo(i.nt-h,i.st)):(t.moveTo(i.nt-e,i.st),t.lineTo(i.nt,i.st+e),t.lineTo(i.nt+e,i.st),t.lineTo(i.nt+h,i.st),t.lineTo(i.nt+h,i.st-e),t.lineTo(i.nt-h,i.st-e),t.lineTo(i.nt-h,i.st)),t.fill()}function xn(n,t,i,s,e,h){return oe(t,i,s,e,h)}class Sn extends j{constructor(){super(...arguments),this.zt=null,this.ar=new Mt,this.j=-1,this.H="",this.Qr=""}J(t){this.zt=t}_r(t,i){this.j===t&&this.H===i||(this.j=t,this.H=i,this.Qr=dt(t,i),this.ar.nr())}wr(t,i){if(this.zt===null||this.zt.tt===null)return null;for(let s=this.zt.tt.from;s<this.zt.tt.to;s++){const e=this.zt.it[s];if(_n(e,t,i))return{Mr:e.th,gr:e.gr}}return null}K({context:t,horizontalPixelRatio:i,verticalPixelRatio:s},e,h){if(this.zt!==null&&this.zt.tt!==null){t.textBaseline="middle",t.font=this.Qr;for(let r=this.zt.tt.from;r<this.zt.tt.to;r++){const o=this.zt.it[r];o.Kt!==void 0&&(o.Kt.Hi=this.ar.xi(t,o.Kt.ih),o.Kt.At=this.j,o.Kt.nt=o.nt-o.Kt.Hi/2),Mn(o,t,i,s)}}}}function Mn(n,t,i,s){t.fillStyle=n.V,n.Kt!==void 0&&function(e,h,r,o,l,a){e.save(),e.scale(l,a),e.fillText(h,r,o),e.restore()}(t,n.Kt.ih,n.Kt.nt,n.Kt.st,i,s),function(e,h,r){if(e.Ks!==0){switch(e.nh){case"arrowDown":return void rs(!1,h,r,e.Ks);case"arrowUp":return void rs(!0,h,r,e.Ks);case"circle":return void function(o,l,a){const u=(_t("circle",a)-1)/2;o.beginPath(),o.arc(l.nt,l.st,u*l.Jr,0,2*Math.PI,!1),o.fill()}(h,r,e.Ks);case"square":return void function(o,l,a){const u=_t("square",a),c=(u-1)*l.Jr/2,d=l.nt-c,f=l.st-c;o.fillRect(d,f,u*l.Jr,u*l.Jr)}(h,r,e.Ks)}e.nh}}(n,t,function(e,h,r){const o=Math.max(1,Math.floor(h))%2/2;return{nt:Math.round(e.nt*h)+o,st:e.st*r,Jr:h}}(n,i,s))}function _n(n,t,i){return!(n.Kt===void 0||!function(s,e,h,r,o,l){const a=r/2;return o>=s&&o<=s+h&&l>=e-a&&l<=e+a}(n.Kt.nt,n.Kt.st,n.Kt.Hi,n.Kt.At,t,i))||function(s,e,h){if(s.Ks===0)return!1;switch(s.nh){case"arrowDown":case"arrowUp":return xn(0,s.nt,s.st,s.Ks,e,h);case"circle":return function(r,o,l,a,u){const c=2+_t("circle",l)/2,d=r-a,f=o-u;return Math.sqrt(d*d+f*f)<=c}(s.nt,s.st,s.Ks,e,h);case"square":return oe(s.nt,s.st,s.Ks,e,h)}}(n,t,i)}function zn(n,t,i,s,e,h,r,o,l){const a=H(i)?i:i.Se,u=H(i)?i:i.Me,c=H(i)?i:i.xe,d=H(t.size)?Math.max(t.size,0):1,f=re(o.le())*d,v=f/2;switch(n.Ks=f,t.position){case"inBar":return n.st=r.Rt(a,l),void(n.Kt!==void 0&&(n.Kt.st=n.st+v+h+.6*e));case"aboveBar":return n.st=r.Rt(u,l)-v-s.sh,n.Kt!==void 0&&(n.Kt.st=n.st-v-.6*e,s.sh+=1.2*e),void(s.sh+=f+h);case"belowBar":return n.st=r.Rt(c,l)+v+s.eh,n.Kt!==void 0&&(n.Kt.st=n.st+v+h+.6*e,s.eh+=1.2*e),void(s.eh+=f+h)}t.position}class Cn{constructor(t,i){this.ft=!0,this.rh=!0,this.hh=!0,this.ah=null,this.oh=null,this.Wt=new Sn,this.jr=t,this.$i=i,this.zt={it:[],tt:null}}bt(t){this.ft=!0,this.hh=!0,t==="data"&&(this.rh=!0,this.oh=null)}gt(t){if(!this.jr.yt())return null;this.ft&&this._h();const i=this.$i.W().layout;return this.Wt._r(i.fontSize,i.fontFamily),this.Wt.J(this.zt),this.Wt}uh(){if(this.hh){if(this.jr.dh().length>0){const t=this.$i.St().le(),i=ns(t),s=1.5*re(t)+2*i,e=this.fh();this.ah={above:hs(s,e.aboveBar,e.inBar),below:hs(s,e.belowBar,e.inBar)}}else this.ah=null;this.hh=!1}return this.ah}fh(){return this.oh===null&&(this.oh=this.jr.dh().reduce((t,i)=>(t[i.position]||(t[i.position]=!0),t),{inBar:!1,aboveBar:!1,belowBar:!1})),this.oh}_h(){const t=this.jr.Dt(),i=this.$i.St(),s=this.jr.dh();this.rh&&(this.zt.it=s.map(u=>({ot:u.time,nt:0,st:0,Ks:0,nh:u.shape,V:u.color,th:u.th,gr:u.id,Kt:void 0})),this.rh=!1);const e=this.$i.W().layout;this.zt.tt=null;const h=i.Xs();if(h===null)return;const r=this.jr.Ct();if(r===null||this.zt.it.length===0)return;let o=NaN;const l=ns(i.le()),a={sh:l,eh:l};this.zt.tt=ee(this.zt.it,h,!0);for(let u=this.zt.tt.from;u<this.zt.tt.to;u++){const c=s[u];c.time!==o&&(a.sh=l,a.eh=l,o=c.time);const d=this.zt.it[u];d.nt=i.It(c.time),c.text!==void 0&&c.text.length>0&&(d.Kt={ih:c.text,nt:0,st:0,Hi:0,At:0});const f=this.jr.ph(c.time);f!==null&&zn(d,c,f,a,e.fontSize,l,t,i,r.Vt)}this.ft=!1}}class En extends $i{constructor(t){super(t)}yr(){const t=this.Sr;t.yt=!1;const i=this.Es.W();if(!i.priceLineVisible||!this.Es.yt())return;const s=this.Es.Zr(i.priceLineSource===0);s.Xr||(t.yt=!0,t.st=s.ki,t.V=this.Es.mh(s.V),t.et=i.priceLineWidth,t.Nt=i.priceLineStyle)}}class Rn extends Xt{constructor(t){super(),this.jt=t}zi(t,i,s){t.yt=!1,i.yt=!1;const e=this.jt;if(!e.yt())return;const h=e.W(),r=h.lastValueVisible,o=e.bh()!=="",l=h.seriesLastValueMode===0,a=e.Zr(!1);if(a.Xr)return;r&&(t.Kt=this.wh(a,r,l),t.yt=t.Kt.length!==0),(o||l)&&(i.Kt=this.gh(a,r,o,l),i.yt=i.Kt.length>0);const u=e.mh(a.V),c=Kt(u);s.t=c.t,s.ki=a.ki,i.Ot=e.$t().Bt(a.ki/e.Dt().At()),t.Ot=u,t.V=c.i,i.V=c.i}gh(t,i,s,e){let h="";const r=this.jt.bh();return s&&r.length!==0&&(h+=`${r} `),i&&e&&(h+=this.jt.Dt().Mh()?t.xh:t.Sh),h.trim()}wh(t,i,s){return i?s?this.jt.Dt().Mh()?t.Sh:t.xh:t.Kt:""}}function os(n,t,i,s){const e=Number.isFinite(t),h=Number.isFinite(i);return e&&h?n(t,i):e||h?e?t:i:s}class P{constructor(t,i){this.kh=t,this.yh=i}Ch(t){return t!==null&&this.kh===t.kh&&this.yh===t.yh}Th(){return new P(this.kh,this.yh)}Ph(){return this.kh}Rh(){return this.yh}Dh(){return this.yh-this.kh}Ni(){return this.yh===this.kh||Number.isNaN(this.yh)||Number.isNaN(this.kh)}ts(t){return t===null?this:new P(os(Math.min,this.Ph(),t.Ph(),-1/0),os(Math.max,this.Rh(),t.Rh(),1/0))}Vh(t){if(!H(t)||this.yh-this.kh===0)return;const i=.5*(this.yh+this.kh);let s=this.yh-i,e=this.kh-i;s*=t,e*=t,this.yh=i+s,this.kh=i+e}Oh(t){H(t)&&(this.yh+=t,this.kh+=t)}Bh(){return{minValue:this.kh,maxValue:this.yh}}static Ah(t){return t===null?null:new P(t.minValue,t.maxValue)}}class jt{constructor(t,i){this.Ih=t,this.zh=i||null}Lh(){return this.Ih}Eh(){return this.zh}Bh(){return this.Ih===null?null:{priceRange:this.Ih.Bh(),margins:this.zh||void 0}}static Ah(t){return t===null?null:new jt(P.Ah(t.priceRange),t.margins)}}class kn extends $i{constructor(t,i){super(t),this.Nh=i}yr(){const t=this.Sr;t.yt=!1;const i=this.Nh.W();if(!this.Es.yt()||!i.lineVisible)return;const s=this.Nh.Fh();s!==null&&(t.yt=!0,t.st=s,t.V=i.color,t.et=i.lineWidth,t.Nt=i.lineStyle,t.gr=this.Nh.W().id)}}class Ln extends Xt{constructor(t,i){super(),this.jr=t,this.Nh=i}zi(t,i,s){t.yt=!1,i.yt=!1;const e=this.Nh.W(),h=e.axisLabelVisible,r=e.title!=="",o=this.jr;if(!h||!o.yt())return;const l=this.Nh.Fh();if(l===null)return;r&&(i.Kt=e.title,i.yt=!0),i.Ot=o.$t().Bt(l/o.Dt().At()),t.Kt=this.Wh(e.price),t.yt=!0;const a=Kt(e.axisLabelColor||e.color);s.t=a.t;const u=e.axisLabelTextColor||a.i;t.V=u,i.V=u,s.ki=l}Wh(t){const i=this.jr.Ct();return i===null?"":this.jr.Dt().Fi(t,i.Vt)}}class $n{constructor(t,i){this.jr=t,this.cn=i,this.jh=new kn(t,this),this.ur=new Ln(t,this),this.Hh=new he(this.ur,t,t.$t())}$h(t){F(this.cn,t),this.bt(),this.jr.$t().Uh()}W(){return this.cn}qh(){return this.jh}Yh(){return this.Hh}Zh(){return this.ur}bt(){this.jh.bt(),this.ur.bt()}Fh(){const t=this.jr,i=t.Dt();if(t.$t().St().Ni()||i.Ni())return null;const s=t.Ct();return s===null?null:i.Rt(this.cn.price,s.Vt)}}class Vn extends Ri{constructor(t){super(),this.$i=t}$t(){return this.$i}}const Tn={Bar:(n,t,i,s)=>{var e;const h=t.upColor,r=t.downColor,o=p(n(i,s)),l=at(o.Vt[0])<=at(o.Vt[3]);return{ce:(e=o.V)!==null&&e!==void 0?e:l?h:r}},Candlestick:(n,t,i,s)=>{var e,h,r;const o=t.upColor,l=t.downColor,a=t.borderUpColor,u=t.borderDownColor,c=t.wickUpColor,d=t.wickDownColor,f=p(n(i,s)),v=at(f.Vt[0])<=at(f.Vt[3]);return{ce:(e=f.V)!==null&&e!==void 0?e:v?o:l,Ne:(h=f.Ot)!==null&&h!==void 0?h:v?a:u,Ee:(r=f.Xh)!==null&&r!==void 0?r:v?c:d}},Custom:(n,t,i,s)=>{var e;return{ce:(e=p(n(i,s)).V)!==null&&e!==void 0?e:t.color}},Area:(n,t,i,s)=>{var e,h,r,o;const l=p(n(i,s));return{ce:(e=l.lt)!==null&&e!==void 0?e:t.lineColor,lt:(h=l.lt)!==null&&h!==void 0?h:t.lineColor,Ps:(r=l.Ps)!==null&&r!==void 0?r:t.topColor,Rs:(o=l.Rs)!==null&&o!==void 0?o:t.bottomColor}},Baseline:(n,t,i,s)=>{var e,h,r,o,l,a;const u=p(n(i,s));return{ce:u.Vt[3]>=t.baseValue.price?t.topLineColor:t.bottomLineColor,Re:(e=u.Re)!==null&&e!==void 0?e:t.topLineColor,De:(h=u.De)!==null&&h!==void 0?h:t.bottomLineColor,ke:(r=u.ke)!==null&&r!==void 0?r:t.topFillColor1,ye:(o=u.ye)!==null&&o!==void 0?o:t.topFillColor2,Ce:(l=u.Ce)!==null&&l!==void 0?l:t.bottomFillColor1,Te:(a=u.Te)!==null&&a!==void 0?a:t.bottomFillColor2}},Line:(n,t,i,s)=>{var e,h;const r=p(n(i,s));return{ce:(e=r.V)!==null&&e!==void 0?e:t.color,lt:(h=r.V)!==null&&h!==void 0?h:t.color}},Histogram:(n,t,i,s)=>{var e;return{ce:(e=p(n(i,s)).V)!==null&&e!==void 0?e:t.color}}};class Nn{constructor(t){this.Kh=(i,s)=>s!==void 0?s.Vt:this.jr.In().Gh(i),this.jr=t,this.Jh=Tn[t.Qh()]}$s(t,i){return this.Jh(this.Kh,this.jr.W(),t,i)}}var ls;(function(n){n[n.NearestLeft=-1]="NearestLeft",n[n.None=0]="None",n[n.NearestRight=1]="NearestRight"})(ls||(ls={}));const Q=30;class Wn{constructor(){this.tl=[],this.il=new Map,this.nl=new Map}sl(){return this.Ks()>0?this.tl[this.tl.length-1]:null}el(){return this.Ks()>0?this.rl(0):null}An(){return this.Ks()>0?this.rl(this.tl.length-1):null}Ks(){return this.tl.length}Ni(){return this.Ks()===0}Kr(t){return this.hl(t,0)!==null}Gh(t){return this.ll(t)}ll(t,i=0){const s=this.hl(t,i);return s===null?null:Object.assign(Object.assign({},this.al(s)),{ee:this.rl(s)})}ne(){return this.tl}ol(t,i,s){if(this.Ni())return null;let e=null;for(const h of s)e=Vt(e,this._l(t,i,h));return e}J(t){this.nl.clear(),this.il.clear(),this.tl=t}rl(t){return this.tl[t].ee}al(t){return this.tl[t]}hl(t,i){const s=this.ul(t);if(s===null&&i!==0)switch(i){case-1:return this.cl(t);case 1:return this.dl(t);default:throw new TypeError("Unknown search mode")}return s}cl(t){let i=this.fl(t);return i>0&&(i-=1),i!==this.tl.length&&this.rl(i)<t?i:null}dl(t){const i=this.vl(t);return i!==this.tl.length&&t<this.rl(i)?i:null}ul(t){const i=this.fl(t);return i===this.tl.length||t<this.tl[i].ee?null:i}fl(t){return Et(this.tl,t,(i,s)=>i.ee<s)}vl(t){return se(this.tl,t,(i,s)=>i.ee>s)}pl(t,i,s){let e=null;for(let h=t;h<i;h++){const r=this.tl[h].Vt[s];Number.isNaN(r)||(e===null?e={ml:r,bl:r}:(r<e.ml&&(e.ml=r),r>e.bl&&(e.bl=r)))}return e}_l(t,i,s){if(this.Ni())return null;let e=null;const h=p(this.el()),r=p(this.An()),o=Math.max(t,h),l=Math.min(i,r),a=Math.ceil(o/Q)*Q,u=Math.max(a,Math.floor(l/Q)*Q);{const d=this.fl(o),f=this.vl(Math.min(l,a,i));e=Vt(e,this.pl(d,f,s))}let c=this.il.get(s);c===void 0&&(c=new Map,this.il.set(s,c));for(let d=Math.max(a+1,o);d<u;d+=Q){const f=Math.floor(d/Q);let v=c.get(f);if(v===void 0){const m=this.fl(f*Q),g=this.vl((f+1)*Q-1);v=this.pl(m,g,s),c.set(f,v)}e=Vt(e,v)}{const d=this.fl(u),f=this.vl(l);e=Vt(e,this.pl(d,f,s))}return e}}function Vt(n,t){return n===null?t:t===null?n:{ml:Math.min(n.ml,t.ml),bl:Math.max(n.bl,t.bl)}}class Dn{constructor(t){this.wl=t}X(t,i,s){this.wl.draw(t)}gl(t,i,s){var e,h;(h=(e=this.wl).drawBackground)===null||h===void 0||h.call(e,t)}}class Zt{constructor(t){this.tr=null,this.wn=t}gt(){var t;const i=this.wn.renderer();if(i===null)return null;if(((t=this.tr)===null||t===void 0?void 0:t.Ml)===i)return this.tr.xl;const s=new Dn(i);return this.tr={Ml:i,xl:s},s}Sl(){var t,i,s;return(s=(i=(t=this.wn).zOrder)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:"normal"}}function le(n){var t,i,s,e,h;return{Kt:n.text(),ki:n.coordinate(),Si:(t=n.fixedCoordinate)===null||t===void 0?void 0:t.call(n),V:n.textColor(),t:n.backColor(),yt:(s=(i=n.visible)===null||i===void 0?void 0:i.call(n))===null||s===void 0||s,hi:(h=(e=n.tickVisible)===null||e===void 0?void 0:e.call(n))===null||h===void 0||h}}class Pn{constructor(t,i){this.Wt=new Us,this.kl=t,this.yl=i}gt(){return this.Wt.J(Object.assign({Hi:this.yl.Hi()},le(this.kl))),this.Wt}}class On extends Xt{constructor(t,i){super(),this.kl=t,this.Li=i}zi(t,i,s){const e=le(this.kl);s.t=e.t,t.V=e.V;const h=2/12*this.Li.P();s.wi=h,s.gi=h,s.ki=e.ki,s.Si=e.Si,t.Kt=e.Kt,t.yt=e.yt,t.hi=e.hi}}class Bn{constructor(t,i){this.Cl=null,this.Tl=null,this.Pl=null,this.Rl=null,this.Dl=null,this.Vl=t,this.jr=i}Ol(){return this.Vl}Vn(){var t,i;(i=(t=this.Vl).updateAllViews)===null||i===void 0||i.call(t)}Pn(){var t,i,s,e;const h=(s=(i=(t=this.Vl).paneViews)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:[];if(((e=this.Cl)===null||e===void 0?void 0:e.Ml)===h)return this.Cl.xl;const r=h.map(o=>new Zt(o));return this.Cl={Ml:h,xl:r},r}Qi(){var t,i,s,e;const h=(s=(i=(t=this.Vl).timeAxisViews)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:[];if(((e=this.Tl)===null||e===void 0?void 0:e.Ml)===h)return this.Tl.xl;const r=this.jr.$t().St(),o=h.map(l=>new Pn(l,r));return this.Tl={Ml:h,xl:o},o}Rn(){var t,i,s,e;const h=(s=(i=(t=this.Vl).priceAxisViews)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:[];if(((e=this.Pl)===null||e===void 0?void 0:e.Ml)===h)return this.Pl.xl;const r=this.jr.Dt(),o=h.map(l=>new On(l,r));return this.Pl={Ml:h,xl:o},o}Bl(){var t,i,s,e;const h=(s=(i=(t=this.Vl).priceAxisPaneViews)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:[];if(((e=this.Rl)===null||e===void 0?void 0:e.Ml)===h)return this.Rl.xl;const r=h.map(o=>new Zt(o));return this.Rl={Ml:h,xl:r},r}Al(){var t,i,s,e;const h=(s=(i=(t=this.Vl).timeAxisPaneViews)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:[];if(((e=this.Dl)===null||e===void 0?void 0:e.Ml)===h)return this.Dl.xl;const r=h.map(o=>new Zt(o));return this.Dl={Ml:h,xl:r},r}Il(t,i){var s,e,h;return(h=(e=(s=this.Vl).autoscaleInfo)===null||e===void 0?void 0:e.call(s,t,i))!==null&&h!==void 0?h:null}wr(t,i){var s,e,h;return(h=(e=(s=this.Vl).hitTest)===null||e===void 0?void 0:e.call(s,t,i))!==null&&h!==void 0?h:null}}function ti(n,t,i,s){n.forEach(e=>{t(e).forEach(h=>{h.Sl()===i&&s.push(h)})})}function ii(n){return n.Pn()}function In(n){return n.Bl()}function Fn(n){return n.Al()}class Vi extends Vn{constructor(t,i,s,e,h){super(t),this.zt=new Wn,this.jh=new En(this),this.zl=[],this.Ll=new gn(this),this.El=null,this.Nl=null,this.Fl=[],this.Wl=[],this.jl=null,this.Hl=[],this.cn=i,this.$l=s;const r=new Rn(this);this.rn=[r],this.Hh=new he(r,this,t),s!=="Area"&&s!=="Line"&&s!=="Baseline"||(this.El=new yn(this)),this.Ul(),this.ql(h)}S(){this.jl!==null&&clearTimeout(this.jl)}mh(t){return this.cn.priceLineColor||t}Zr(t){const i={Xr:!0},s=this.Dt();if(this.$t().St().Ni()||s.Ni()||this.zt.Ni())return i;const e=this.$t().St().Xs(),h=this.Ct();if(e===null||h===null)return i;let r,o;if(t){const c=this.zt.sl();if(c===null)return i;r=c,o=c.ee}else{const c=this.zt.ll(e.ui(),-1);if(c===null||(r=this.zt.Gh(c.ee),r===null))return i;o=c.ee}const l=r.Vt[3],a=this.Us().$s(o,{Vt:r}),u=s.Rt(l,h.Vt);return{Xr:!1,_t:l,Kt:s.Fi(l,h.Vt),xh:s.Yl(l),Sh:s.Zl(l,h.Vt),V:a.ce,ki:u,ee:o}}Us(){return this.Nl!==null||(this.Nl=new Nn(this)),this.Nl}W(){return this.cn}$h(t){const i=t.priceScaleId;i!==void 0&&i!==this.cn.priceScaleId&&this.$t().Xl(this,i),F(this.cn,t),t.priceFormat!==void 0&&(this.Ul(),this.$t().Kl()),this.$t().Gl(this),this.$t().Jl(),this.wn.bt("options")}J(t,i){this.zt.J(t),this.Ql(),this.wn.bt("data"),this.dn.bt("data"),this.El!==null&&(i&&i.ta?this.El.$r():t.length===0&&this.El.Hr());const s=this.$t().dr(this);this.$t().ia(s),this.$t().Gl(this),this.$t().Jl(),this.$t().Uh()}na(t){this.Fl=t,this.Ql();const i=this.$t().dr(this);this.dn.bt("data"),this.$t().ia(i),this.$t().Gl(this),this.$t().Jl(),this.$t().Uh()}sa(){return this.Fl}dh(){return this.Wl}ea(t){const i=new $n(this,t);return this.zl.push(i),this.$t().Gl(this),i}ra(t){const i=this.zl.indexOf(t);i!==-1&&this.zl.splice(i,1),this.$t().Gl(this)}Qh(){return this.$l}Ct(){const t=this.ha();return t===null?null:{Vt:t.Vt[3],la:t.ot}}ha(){const t=this.$t().St().Xs();if(t===null)return null;const i=t.Os();return this.zt.ll(i,1)}In(){return this.zt}ph(t){const i=this.zt.Gh(t);return i===null?null:this.$l==="Bar"||this.$l==="Candlestick"||this.$l==="Custom"?{ge:i.Vt[0],Me:i.Vt[1],xe:i.Vt[2],Se:i.Vt[3]}:i.Vt[3]}aa(t){const i=[];ti(this.Hl,ii,"top",i);const s=this.El;return s!==null&&s.yt()&&(this.jl===null&&s.qr()&&(this.jl=setTimeout(()=>{this.jl=null,this.$t().oa()},0)),s.Ur(),i.unshift(s)),i}Pn(){const t=[];this._a()||t.push(this.Ll),t.push(this.wn,this.jh,this.dn);const i=this.zl.map(s=>s.qh());return t.push(...i),ti(this.Hl,ii,"normal",t),t}ua(){return this.ca(ii,"bottom")}da(t){return this.ca(In,t)}fa(t){return this.ca(Fn,t)}va(t,i){return this.Hl.map(s=>s.wr(t,i)).filter(s=>s!==null)}Ji(t){return[this.Hh,...this.zl.map(i=>i.Yh())]}Rn(t,i){if(i!==this.Yi&&!this._a())return[];const s=[...this.rn];for(const e of this.zl)s.push(e.Zh());return this.Hl.forEach(e=>{s.push(...e.Rn())}),s}Qi(){const t=[];return this.Hl.forEach(i=>{t.push(...i.Qi())}),t}Il(t,i){if(this.cn.autoscaleInfoProvider!==void 0){const s=this.cn.autoscaleInfoProvider(()=>{const e=this.pa(t,i);return e===null?null:e.Bh()});return jt.Ah(s)}return this.pa(t,i)}ma(){return this.cn.priceFormat.minMove}ba(){return this.wa}Vn(){var t;this.wn.bt(),this.dn.bt();for(const i of this.rn)i.bt();for(const i of this.zl)i.bt();this.jh.bt(),this.Ll.bt(),(t=this.El)===null||t===void 0||t.bt(),this.Hl.forEach(i=>i.Vn())}Dt(){return p(super.Dt())}kt(t){if(!((this.$l==="Line"||this.$l==="Area"||this.$l==="Baseline")&&this.cn.crosshairMarkerVisible))return null;const i=this.zt.Gh(t);return i===null?null:{_t:i.Vt[3],ht:this.ga(),Ot:this.Ma(),Pt:this.xa(),Tt:this.Sa(t)}}bh(){return this.cn.title}yt(){return this.cn.visible}ka(t){this.Hl.push(new Bn(t,this))}ya(t){this.Hl=this.Hl.filter(i=>i.Ol()!==t)}Ca(){if(this.wn instanceof Yt)return t=>this.wn.We(t)}Ta(){if(this.wn instanceof Yt)return t=>this.wn.je(t)}_a(){return!At(this.Dt().Pa())}pa(t,i){if(!xt(t)||!xt(i)||this.zt.Ni())return null;const s=this.$l==="Line"||this.$l==="Area"||this.$l==="Baseline"||this.$l==="Histogram"?[3]:[2,1],e=this.zt.ol(t,i,s);let h=e!==null?new P(e.ml,e.bl):null;if(this.Qh()==="Histogram"){const o=this.cn.base,l=new P(o,o);h=h!==null?h.ts(l):l}let r=this.dn.uh();return this.Hl.forEach(o=>{const l=o.Il(t,i);if(l?.priceRange){const f=new P(l.priceRange.minValue,l.priceRange.maxValue);h=h!==null?h.ts(f):f}var a,u,c,d;l?.margins&&(a=r,u=l.margins,r={above:Math.max((c=a?.above)!==null&&c!==void 0?c:0,u.above),below:Math.max((d=a?.below)!==null&&d!==void 0?d:0,u.below)})}),new jt(h,r)}ga(){switch(this.$l){case"Line":case"Area":case"Baseline":return this.cn.crosshairMarkerRadius}return 0}Ma(){switch(this.$l){case"Line":case"Area":case"Baseline":{const t=this.cn.crosshairMarkerBorderColor;if(t.length!==0)return t}}return null}xa(){switch(this.$l){case"Line":case"Area":case"Baseline":return this.cn.crosshairMarkerBorderWidth}return 0}Sa(t){switch(this.$l){case"Line":case"Area":case"Baseline":{const i=this.cn.crosshairMarkerBackgroundColor;if(i.length!==0)return i}}return this.Us().$s(t).ce}Ul(){switch(this.cn.priceFormat.type){case"custom":this.wa={format:this.cn.priceFormat.formatter};break;case"volume":this.wa=new Ae(this.cn.priceFormat.precision);break;case"percent":this.wa=new Js(this.cn.priceFormat.precision);break;default:{const t=Math.pow(10,this.cn.priceFormat.precision);this.wa=new Ut(t,this.cn.priceFormat.minMove*t)}}this.Yi!==null&&this.Yi.Ra()}Ql(){const t=this.$t().St();if(!t.Da()||this.zt.Ni())return void(this.Wl=[]);const i=p(this.zt.el());this.Wl=this.Fl.map((s,e)=>{const h=p(t.Va(s.time,!0)),r=h<i?1:-1;return{time:p(this.zt.ll(h,r)).ee,position:s.position,shape:s.shape,color:s.color,id:s.id,th:e,text:s.text,size:s.size,originalTime:s.originalTime}})}ql(t){switch(this.dn=new Cn(this,this.$t()),this.$l){case"Bar":this.wn=new en(this,this.$t());break;case"Candlestick":this.wn=new ln(this,this.$t());break;case"Line":this.wn=new fn(this,this.$t());break;case"Custom":this.wn=new Yt(this,this.$t(),D(t));break;case"Area":this.wn=new tn(this,this.$t());break;case"Baseline":this.wn=new rn(this,this.$t());break;case"Histogram":this.wn=new dn(this,this.$t());break;default:throw Error("Unknown chart style assigned: "+this.$l)}}ca(t,i){const s=[];return ti(this.Hl,t,i,s),s}}class jn{constructor(t){this.cn=t}Oa(t,i,s){let e=t;if(this.cn.mode===0)return e;const h=s.vn(),r=h.Ct();if(r===null)return e;const o=h.Rt(t,r),l=s.Ba().filter(u=>u instanceof Vi).reduce((u,c)=>{if(s.vr(c)||!c.yt())return u;const d=c.Dt(),f=c.In();if(d.Ni()||!f.Kr(i))return u;const v=f.Gh(i);if(v===null)return u;const m=at(c.Ct());return u.concat([d.Rt(v.Vt[3],m.Vt)])},[]);if(l.length===0)return e;l.sort((u,c)=>Math.abs(u-o)-Math.abs(c-o));const a=l[0];return e=h.pn(a,r),e}}class qn extends j{constructor(){super(...arguments),this.zt=null}J(t){this.zt=t}K({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:e}){if(this.zt===null)return;const h=Math.max(1,Math.floor(s));t.lineWidth=h,function(r,o){r.save(),r.lineWidth%2&&r.translate(.5,.5),o(),r.restore()}(t,()=>{const r=p(this.zt);if(r.Aa){t.strokeStyle=r.Ia,st(t,r.za),t.beginPath();for(const o of r.La){const l=Math.round(o.Ea*s);t.moveTo(l,-h),t.lineTo(l,i.height+h)}t.stroke()}if(r.Na){t.strokeStyle=r.Fa,st(t,r.Wa),t.beginPath();for(const o of r.ja){const l=Math.round(o.Ea*e);t.moveTo(-h,l),t.lineTo(i.width+h,l)}t.stroke()}})}}class Kn{constructor(t){this.Wt=new qn,this.ft=!0,this.tn=t}bt(){this.ft=!0}gt(){if(this.ft){const t=this.tn.$t().W().grid,i={Na:t.horzLines.visible,Aa:t.vertLines.visible,Fa:t.horzLines.color,Ia:t.vertLines.color,Wa:t.horzLines.style,za:t.vertLines.style,ja:this.tn.vn().Ha(),La:(this.tn.$t().St().Ha()||[]).map(s=>({Ea:s.coord}))};this.Wt.J(i),this.ft=!1}return this.Wt}}class Hn{constructor(t){this.wn=new Kn(t)}qh(){return this.wn}}const si={$a:4,Ua:1e-4};function ut(n,t){const i=100*(n-t)/t;return t<0?-i:i}function Xn(n,t){const i=ut(n.Ph(),t),s=ut(n.Rh(),t);return new P(i,s)}function gt(n,t){const i=100*(n-t)/t+100;return t<0?-i:i}function An(n,t){const i=gt(n.Ph(),t),s=gt(n.Rh(),t);return new P(i,s)}function qt(n,t){const i=Math.abs(n);if(i<1e-15)return 0;const s=Math.log10(i+t.Ua)+t.$a;return n<0?-s:s}function bt(n,t){const i=Math.abs(n);if(i<1e-15)return 0;const s=Math.pow(10,i-t.$a)-t.Ua;return n<0?-s:s}function mt(n,t){if(n===null)return null;const i=qt(n.Ph(),t),s=qt(n.Rh(),t);return new P(i,s)}function Tt(n,t){if(n===null)return null;const i=bt(n.Ph(),t),s=bt(n.Rh(),t);return new P(i,s)}function ei(n){if(n===null)return si;const t=Math.abs(n.Rh()-n.Ph());if(t>=1||t<1e-15)return si;const i=Math.ceil(Math.abs(Math.log10(t))),s=si.$a+i;return{$a:s,Ua:1/Math.pow(10,s)}}class ni{constructor(t,i){if(this.qa=t,this.Ya=i,function(s){if(s<0)return!1;for(let e=s;e>1;e/=10)if(e%10!=0)return!1;return!0}(this.qa))this.Za=[2,2.5,2];else{this.Za=[];for(let s=this.qa;s!==1;){if(s%2==0)this.Za.push(2),s/=2;else{if(s%5!=0)throw new Error("unexpected base");this.Za.push(2,2.5),s/=5}if(this.Za.length>100)throw new Error("something wrong with base")}}}Xa(t,i,s){const e=this.qa===0?0:1/this.qa;let h=Math.pow(10,Math.max(0,Math.ceil(Math.log10(t-i)))),r=0,o=this.Ya[0];for(;;){const c=$t(h,e,1e-14)&&h>e+1e-14,d=$t(h,s*o,1e-14),f=$t(h,1,1e-14);if(!(c&&d&&f))break;h/=o,o=this.Ya[++r%this.Ya.length]}if(h<=e+1e-14&&(h=e),h=Math.max(1,h),this.Za.length>0&&(l=h,a=1,u=1e-14,Math.abs(l-a)<u))for(r=0,o=this.Za[0];$t(h,s*o,1e-14)&&h>e+1e-14;)h/=o,o=this.Za[++r%this.Za.length];var l,a,u;return h}}class as{constructor(t,i,s,e){this.Ka=[],this.Li=t,this.qa=i,this.Ga=s,this.Ja=e}Xa(t,i){if(t<i)throw new Error("high < low");const s=this.Li.At(),e=(t-i)*this.Qa()/s,h=new ni(this.qa,[2,2.5,2]),r=new ni(this.qa,[2,2,2.5]),o=new ni(this.qa,[2.5,2,2]),l=[];return l.push(h.Xa(t,i,e),r.Xa(t,i,e),o.Xa(t,i,e)),function(a){if(a.length<1)throw Error("array is empty");let u=a[0];for(let c=1;c<a.length;++c)a[c]<u&&(u=a[c]);return u}(l)}io(){const t=this.Li,i=t.Ct();if(i===null)return void(this.Ka=[]);const s=t.At(),e=this.Ga(s-1,i),h=this.Ga(0,i),r=this.Li.W().entireTextOnly?this.no()/2:0,o=r,l=s-1-r,a=Math.max(e,h),u=Math.min(e,h);if(a===u)return void(this.Ka=[]);let c=this.Xa(a,u),d=a%c;d+=d<0?c:0;const f=a>=u?1:-1;let v=null,m=0;for(let g=a-d;g>u;g-=c){const b=this.Ja(g,i,!0);v!==null&&Math.abs(b-v)<this.Qa()||b<o||b>l||(m<this.Ka.length?(this.Ka[m].Ea=b,this.Ka[m].so=t.eo(g)):this.Ka.push({Ea:b,so:t.eo(g)}),m++,v=b,t.ro()&&(c=this.Xa(g*f,u)))}this.Ka.length=m}Ha(){return this.Ka}no(){return this.Li.P()}Qa(){return Math.ceil(2.5*this.no())}}function ae(n){return n.slice().sort((t,i)=>p(t.Xi())-p(i.Xi()))}var us;(function(n){n[n.Normal=0]="Normal",n[n.Logarithmic=1]="Logarithmic",n[n.Percentage=2]="Percentage",n[n.IndexedTo100=3]="IndexedTo100"})(us||(us={}));const cs=new Js,ds=new Ut(100,1);class Un{constructor(t,i,s,e){this.ho=0,this.lo=null,this.Ih=null,this.ao=null,this.oo={_o:!1,uo:null},this.co=0,this.do=0,this.fo=new V,this.vo=new V,this.po=[],this.mo=null,this.bo=null,this.wo=null,this.Mo=null,this.wa=ds,this.xo=ei(null),this.So=t,this.cn=i,this.ko=s,this.yo=e,this.Co=new as(this,100,this.To.bind(this),this.Po.bind(this))}Pa(){return this.So}W(){return this.cn}$h(t){if(F(this.cn,t),this.Ra(),t.mode!==void 0&&this.Ro({Cr:t.mode}),t.scaleMargins!==void 0){const i=D(t.scaleMargins.top),s=D(t.scaleMargins.bottom);if(i<0||i>1)throw new Error(`Invalid top margin - expect value between 0 and 1, given=${i}`);if(s<0||s>1)throw new Error(`Invalid bottom margin - expect value between 0 and 1, given=${s}`);if(i+s>1)throw new Error(`Invalid margins - sum of margins must be less than 1, given=${i+s}`);this.Do(),this.bo=null}}Vo(){return this.cn.autoScale}ro(){return this.cn.mode===1}Mh(){return this.cn.mode===2}Oo(){return this.cn.mode===3}Cr(){return{Wn:this.cn.autoScale,Bo:this.cn.invertScale,Cr:this.cn.mode}}Ro(t){const i=this.Cr();let s=null;t.Wn!==void 0&&(this.cn.autoScale=t.Wn),t.Cr!==void 0&&(this.cn.mode=t.Cr,t.Cr!==2&&t.Cr!==3||(this.cn.autoScale=!0),this.oo._o=!1),i.Cr===1&&t.Cr!==i.Cr&&(function(h,r){if(h===null)return!1;const o=bt(h.Ph(),r),l=bt(h.Rh(),r);return isFinite(o)&&isFinite(l)}(this.Ih,this.xo)?(s=Tt(this.Ih,this.xo),s!==null&&this.Ao(s)):this.cn.autoScale=!0),t.Cr===1&&t.Cr!==i.Cr&&(s=mt(this.Ih,this.xo),s!==null&&this.Ao(s));const e=i.Cr!==this.cn.mode;e&&(i.Cr===2||this.Mh())&&this.Ra(),e&&(i.Cr===3||this.Oo())&&this.Ra(),t.Bo!==void 0&&i.Bo!==t.Bo&&(this.cn.invertScale=t.Bo,this.Io()),this.vo.m(i,this.Cr())}zo(){return this.vo}P(){return this.ko.fontSize}At(){return this.ho}Lo(t){this.ho!==t&&(this.ho=t,this.Do(),this.bo=null)}Eo(){if(this.lo)return this.lo;const t=this.At()-this.No()-this.Fo();return this.lo=t,t}Lh(){return this.Wo(),this.Ih}Ao(t,i){const s=this.Ih;(i||s===null&&t!==null||s!==null&&!s.Ch(t))&&(this.bo=null,this.Ih=t)}Ni(){return this.Wo(),this.ho===0||!this.Ih||this.Ih.Ni()}jo(t){return this.Bo()?t:this.At()-1-t}Rt(t,i){return this.Mh()?t=ut(t,i):this.Oo()&&(t=gt(t,i)),this.Po(t,i)}te(t,i,s){this.Wo();const e=this.Fo(),h=p(this.Lh()),r=h.Ph(),o=h.Rh(),l=this.Eo()-1,a=this.Bo(),u=l/(o-r),c=s===void 0?0:s.from,d=s===void 0?t.length:s.to,f=this.Ho();for(let v=c;v<d;v++){const m=t[v],g=m._t;if(isNaN(g))continue;let b=g;f!==null&&(b=f(m._t,i));const w=e+u*(b-r),x=a?w:this.ho-1-w;m.st=x}}be(t,i,s){this.Wo();const e=this.Fo(),h=p(this.Lh()),r=h.Ph(),o=h.Rh(),l=this.Eo()-1,a=this.Bo(),u=l/(o-r),c=s===void 0?0:s.from,d=s===void 0?t.length:s.to,f=this.Ho();for(let v=c;v<d;v++){const m=t[v];let g=m.ge,b=m.Me,w=m.xe,x=m.Se;f!==null&&(g=f(m.ge,i),b=f(m.Me,i),w=f(m.xe,i),x=f(m.Se,i));let y=e+u*(g-r),S=a?y:this.ho-1-y;m.pe=S,y=e+u*(b-r),S=a?y:this.ho-1-y,m.de=S,y=e+u*(w-r),S=a?y:this.ho-1-y,m.fe=S,y=e+u*(x-r),S=a?y:this.ho-1-y,m.me=S}}pn(t,i){const s=this.To(t,i);return this.$o(s,i)}$o(t,i){let s=t;return this.Mh()?s=function(e,h){return h<0&&(e=-e),e/100*h+h}(s,i):this.Oo()&&(s=function(e,h){return e-=100,h<0&&(e=-e),e/100*h+h}(s,i)),s}Ba(){return this.po}Uo(){if(this.mo)return this.mo;let t=[];for(let i=0;i<this.po.length;i++){const s=this.po[i];s.Xi()===null&&s.Ki(i+1),t.push(s)}return t=ae(t),this.mo=t,this.mo}qo(t){this.po.indexOf(t)===-1&&(this.po.push(t),this.Ra(),this.Yo())}Zo(t){const i=this.po.indexOf(t);if(i===-1)throw new Error("source is not attached to scale");this.po.splice(i,1),this.po.length===0&&(this.Ro({Wn:!0}),this.Ao(null)),this.Ra(),this.Yo()}Ct(){let t=null;for(const i of this.po){const s=i.Ct();s!==null&&(t===null||s.la<t.la)&&(t=s)}return t===null?null:t.Vt}Bo(){return this.cn.invertScale}Ha(){const t=this.Ct()===null;if(this.bo!==null&&(t||this.bo.Xo===t))return this.bo.Ha;this.Co.io();const i=this.Co.Ha();return this.bo={Ha:i,Xo:t},this.fo.m(),i}Ko(){return this.fo}Go(t){this.Mh()||this.Oo()||this.wo===null&&this.ao===null&&(this.Ni()||(this.wo=this.ho-t,this.ao=p(this.Lh()).Th()))}Jo(t){if(this.Mh()||this.Oo()||this.wo===null)return;this.Ro({Wn:!1}),(t=this.ho-t)<0&&(t=0);let i=(this.wo+.2*(this.ho-1))/(t+.2*(this.ho-1));const s=p(this.ao).Th();i=Math.max(i,.1),s.Vh(i),this.Ao(s)}Qo(){this.Mh()||this.Oo()||(this.wo=null,this.ao=null)}t_(t){this.Vo()||this.Mo===null&&this.ao===null&&(this.Ni()||(this.Mo=t,this.ao=p(this.Lh()).Th()))}i_(t){if(this.Vo()||this.Mo===null)return;const i=p(this.Lh()).Dh()/(this.Eo()-1);let s=t-this.Mo;this.Bo()&&(s*=-1);const e=s*i,h=p(this.ao).Th();h.Oh(e),this.Ao(h,!0),this.bo=null}n_(){this.Vo()||this.Mo!==null&&(this.Mo=null,this.ao=null)}ba(){return this.wa||this.Ra(),this.wa}Fi(t,i){switch(this.cn.mode){case 2:return this.s_(ut(t,i));case 3:return this.ba().format(gt(t,i));default:return this.Wh(t)}}eo(t){switch(this.cn.mode){case 2:return this.s_(t);case 3:return this.ba().format(t);default:return this.Wh(t)}}Yl(t){return this.Wh(t,p(this.e_()).ba())}Zl(t,i){return t=ut(t,i),this.s_(t,cs)}r_(){return this.po}h_(t){this.oo={uo:t,_o:!1}}Vn(){this.po.forEach(t=>t.Vn())}Ra(){this.bo=null;const t=this.e_();let i=100;t!==null&&(i=Math.round(1/t.ma())),this.wa=ds,this.Mh()?(this.wa=cs,i=100):this.Oo()?(this.wa=new Ut(100,1),i=100):t!==null&&(this.wa=t.ba()),this.Co=new as(this,i,this.To.bind(this),this.Po.bind(this)),this.Co.io()}Yo(){this.mo=null}e_(){return this.po[0]||null}No(){return this.Bo()?this.cn.scaleMargins.bottom*this.At()+this.do:this.cn.scaleMargins.top*this.At()+this.co}Fo(){return this.Bo()?this.cn.scaleMargins.top*this.At()+this.co:this.cn.scaleMargins.bottom*this.At()+this.do}Wo(){this.oo._o||(this.oo._o=!0,this.l_())}Do(){this.lo=null}Po(t,i){if(this.Wo(),this.Ni())return 0;t=this.ro()&&t?qt(t,this.xo):t;const s=p(this.Lh()),e=this.Fo()+(this.Eo()-1)*(t-s.Ph())/s.Dh();return this.jo(e)}To(t,i){if(this.Wo(),this.Ni())return 0;const s=this.jo(t),e=p(this.Lh()),h=e.Ph()+e.Dh()*((s-this.Fo())/(this.Eo()-1));return this.ro()?bt(h,this.xo):h}Io(){this.bo=null,this.Co.io()}l_(){const t=this.oo.uo;if(t===null)return;let i=null;const s=this.r_();let e=0,h=0;for(const l of s){if(!l.yt())continue;const a=l.Ct();if(a===null)continue;const u=l.Il(t.Os(),t.ui());let c=u&&u.Lh();if(c!==null){switch(this.cn.mode){case 1:c=mt(c,this.xo);break;case 2:c=Xn(c,a.Vt);break;case 3:c=An(c,a.Vt)}if(i=i===null?c:i.ts(p(c)),u!==null){const d=u.Eh();d!==null&&(e=Math.max(e,d.above),h=Math.max(h,d.below))}}}if(e===this.co&&h===this.do||(this.co=e,this.do=h,this.bo=null,this.Do()),i!==null){if(i.Ph()===i.Rh()){const l=this.e_(),a=5*(l===null||this.Mh()||this.Oo()?1:l.ma());this.ro()&&(i=Tt(i,this.xo)),i=new P(i.Ph()-a,i.Rh()+a),this.ro()&&(i=mt(i,this.xo))}if(this.ro()){const l=Tt(i,this.xo),a=ei(l);if(r=a,o=this.xo,r.$a!==o.$a||r.Ua!==o.Ua){const u=this.ao!==null?Tt(this.ao,this.xo):null;this.xo=a,i=mt(l,a),u!==null&&(this.ao=mt(u,a))}}this.Ao(i)}else this.Ih===null&&(this.Ao(new P(-.5,.5)),this.xo=ei(null));var r,o;this.oo._o=!0}Ho(){return this.Mh()?ut:this.Oo()?gt:this.ro()?t=>qt(t,this.xo):null}a_(t,i,s){return i===void 0?(s===void 0&&(s=this.ba()),s.format(t)):i(t)}Wh(t,i){return this.a_(t,this.yo.priceFormatter,i)}s_(t,i){return this.a_(t,this.yo.percentageFormatter,i)}}class Jn{constructor(t,i){this.po=[],this.o_=new Map,this.ho=0,this.__=0,this.u_=1e3,this.mo=null,this.c_=new V,this.yl=t,this.$i=i,this.d_=new Hn(this);const s=i.W();this.f_=this.v_("left",s.leftPriceScale),this.p_=this.v_("right",s.rightPriceScale),this.f_.zo().l(this.m_.bind(this,this.f_),this),this.p_.zo().l(this.m_.bind(this,this.p_),this),this.b_(s)}b_(t){if(t.leftPriceScale&&this.f_.$h(t.leftPriceScale),t.rightPriceScale&&this.p_.$h(t.rightPriceScale),t.localization&&(this.f_.Ra(),this.p_.Ra()),t.overlayPriceScales){const i=Array.from(this.o_.values());for(const s of i){const e=p(s[0].Dt());e.$h(t.overlayPriceScales),t.localization&&e.Ra()}}}w_(t){switch(t){case"left":return this.f_;case"right":return this.p_}return this.o_.has(t)?D(this.o_.get(t))[0].Dt():null}S(){this.$t().g_().p(this),this.f_.zo().p(this),this.p_.zo().p(this),this.po.forEach(t=>{t.S&&t.S()}),this.c_.m()}M_(){return this.u_}x_(t){this.u_=t}$t(){return this.$i}Hi(){return this.__}At(){return this.ho}S_(t){this.__=t,this.k_()}Lo(t){this.ho=t,this.f_.Lo(t),this.p_.Lo(t),this.po.forEach(i=>{if(this.vr(i)){const s=i.Dt();s!==null&&s.Lo(t)}}),this.k_()}Ba(){return this.po}vr(t){const i=t.Dt();return i===null||this.f_!==i&&this.p_!==i}qo(t,i,s){const e=s!==void 0?s:this.C_().y_+1;this.T_(t,i,e)}Zo(t){const i=this.po.indexOf(t);Y(i!==-1,"removeDataSource: invalid data source"),this.po.splice(i,1);const s=p(t.Dt()).Pa();if(this.o_.has(s)){const h=D(this.o_.get(s)),r=h.indexOf(t);r!==-1&&(h.splice(r,1),h.length===0&&this.o_.delete(s))}const e=t.Dt();e&&e.Ba().indexOf(t)>=0&&e.Zo(t),e!==null&&(e.Yo(),this.P_(e)),this.mo=null}mr(t){return t===this.f_?"left":t===this.p_?"right":"overlay"}R_(){return this.f_}D_(){return this.p_}V_(t,i){t.Go(i)}O_(t,i){t.Jo(i),this.k_()}B_(t){t.Qo()}A_(t,i){t.t_(i)}I_(t,i){t.i_(i),this.k_()}z_(t){t.n_()}k_(){this.po.forEach(t=>{t.Vn()})}vn(){let t=null;return this.$i.W().rightPriceScale.visible&&this.p_.Ba().length!==0?t=this.p_:this.$i.W().leftPriceScale.visible&&this.f_.Ba().length!==0?t=this.f_:this.po.length!==0&&(t=this.po[0].Dt()),t===null&&(t=this.p_),t}pr(){let t=null;return this.$i.W().rightPriceScale.visible?t=this.p_:this.$i.W().leftPriceScale.visible&&(t=this.f_),t}P_(t){t!==null&&t.Vo()&&this.L_(t)}E_(t){const i=this.yl.Xs();t.Ro({Wn:!0}),i!==null&&t.h_(i),this.k_()}N_(){this.L_(this.f_),this.L_(this.p_)}F_(){this.P_(this.f_),this.P_(this.p_),this.po.forEach(t=>{this.vr(t)&&this.P_(t.Dt())}),this.k_(),this.$i.Uh()}Uo(){return this.mo===null&&(this.mo=ae(this.po)),this.mo}W_(){return this.c_}j_(){return this.d_}L_(t){const i=t.r_();if(i&&i.length>0&&!this.yl.Ni()){const s=this.yl.Xs();s!==null&&t.h_(s)}t.Vn()}C_(){const t=this.Uo();if(t.length===0)return{H_:0,y_:0};let i=0,s=0;for(let e=0;e<t.length;e++){const h=t[e].Xi();h!==null&&(h<i&&(i=h),h>s&&(s=h))}return{H_:i,y_:s}}T_(t,i,s){let e=this.w_(i);if(e===null&&(e=this.v_(i,this.$i.W().overlayPriceScales)),this.po.push(t),!At(i)){const h=this.o_.get(i)||[];h.push(t),this.o_.set(i,h)}e.qo(t),t.Gi(e),t.Ki(s),this.P_(e),this.mo=null}m_(t,i,s){i.Cr!==s.Cr&&this.L_(t)}v_(t,i){const s=Object.assign({visible:!0,autoScale:!0},X(i)),e=new Un(t,s,this.$i.W().layout,this.$i.W().localization);return e.Lo(this.At()),e}}class Gn{constructor(t,i,s=50){this.Ke=0,this.Ge=1,this.Je=1,this.tr=new Map,this.Qe=new Map,this.U_=t,this.q_=i,this.ir=s}Y_(t){const i=t.time,s=this.q_.cacheKey(i),e=this.tr.get(s);if(e!==void 0)return e.Z_;if(this.Ke===this.ir){const r=this.Qe.get(this.Je);this.Qe.delete(this.Je),this.tr.delete(D(r)),this.Je++,this.Ke--}const h=this.U_(t);return this.tr.set(s,{Z_:h,rr:this.Ge}),this.Qe.set(this.Ge,s),this.Ke++,this.Ge++,h}}class wt{constructor(t,i){Y(t<=i,"right should be >= left"),this.X_=t,this.K_=i}Os(){return this.X_}ui(){return this.K_}G_(){return this.K_-this.X_+1}Kr(t){return this.X_<=t&&t<=this.K_}Ch(t){return this.X_===t.Os()&&this.K_===t.ui()}}function fs(n,t){return n===null||t===null?n===t:n.Ch(t)}class Qn{constructor(){this.J_=new Map,this.tr=null,this.Q_=!1}tu(t){this.Q_=t,this.tr=null}iu(t,i){this.nu(i),this.tr=null;for(let s=i;s<t.length;++s){const e=t[s];let h=this.J_.get(e.timeWeight);h===void 0&&(h=[],this.J_.set(e.timeWeight,h)),h.push({index:s,time:e.time,weight:e.timeWeight,originalTime:e.originalTime})}}su(t,i){const s=Math.ceil(i/t);return this.tr!==null&&this.tr.eu===s||(this.tr={Ha:this.ru(s),eu:s}),this.tr.Ha}nu(t){if(t===0)return void this.J_.clear();const i=[];this.J_.forEach((s,e)=>{t<=s[0].index?i.push(e):s.splice(Et(s,t,h=>h.index<t),1/0)});for(const s of i)this.J_.delete(s)}ru(t){let i=[];for(const s of Array.from(this.J_.keys()).sort((e,h)=>h-e)){if(!this.J_.get(s))continue;const e=i;i=[];const h=e.length;let r=0;const o=D(this.J_.get(s)),l=o.length;let a=1/0,u=-1/0;for(let c=0;c<l;c++){const d=o[c],f=d.index;for(;r<h;){const v=e[r],m=v.index;if(!(m<f)){a=m;break}r++,i.push(v),u=m,a=1/0}if(a-f>=t&&f-u>=t)i.push(d),u=f;else if(this.Q_)return e}for(;r<h;r++)i.push(e[r])}return i}}class ct{constructor(t){this.hu=t}lu(){return this.hu===null?null:new wt(Math.floor(this.hu.Os()),Math.ceil(this.hu.ui()))}au(){return this.hu}static ou(){return new ct(null)}}function Yn(n,t){return n.weight>t.weight?n:t}class Zn{constructor(t,i,s,e){this.__=0,this._u=null,this.uu=[],this.Mo=null,this.wo=null,this.cu=new Qn,this.du=new Map,this.fu=ct.ou(),this.vu=!0,this.pu=new V,this.mu=new V,this.bu=new V,this.wu=null,this.gu=null,this.Mu=[],this.cn=i,this.yo=s,this.xu=i.rightOffset,this.Su=i.barSpacing,this.$i=t,this.q_=e,this.ku(),this.cu.tu(i.uniformDistribution)}W(){return this.cn}yu(t){F(this.yo,t),this.Cu(),this.ku()}$h(t,i){var s;F(this.cn,t),this.cn.fixLeftEdge&&this.Tu(),this.cn.fixRightEdge&&this.Pu(),t.barSpacing!==void 0&&this.$i.Gn(t.barSpacing),t.rightOffset!==void 0&&this.$i.Jn(t.rightOffset),t.minBarSpacing!==void 0&&this.$i.Gn((s=t.barSpacing)!==null&&s!==void 0?s:this.Su),this.Cu(),this.ku(),this.bu.m()}mn(t){var i,s;return(s=(i=this.uu[t])===null||i===void 0?void 0:i.time)!==null&&s!==void 0?s:null}Ui(t){var i;return(i=this.uu[t])!==null&&i!==void 0?i:null}Va(t,i){if(this.uu.length<1)return null;if(this.q_.key(t)>this.q_.key(this.uu[this.uu.length-1].time))return i?this.uu.length-1:null;const s=Et(this.uu,this.q_.key(t),(e,h)=>this.q_.key(e.time)<h);return this.q_.key(t)<this.q_.key(this.uu[s].time)?i?s:null:s}Ni(){return this.__===0||this.uu.length===0||this._u===null}Da(){return this.uu.length>0}Xs(){return this.Ru(),this.fu.lu()}Du(){return this.Ru(),this.fu.au()}Vu(){const t=this.Xs();if(t===null)return null;const i={from:t.Os(),to:t.ui()};return this.Ou(i)}Ou(t){const i=Math.round(t.from),s=Math.round(t.to),e=p(this.Bu()),h=p(this.Au());return{from:p(this.Ui(Math.max(e,i))),to:p(this.Ui(Math.min(h,s)))}}Iu(t){return{from:p(this.Va(t.from,!0)),to:p(this.Va(t.to,!0))}}Hi(){return this.__}S_(t){if(!isFinite(t)||t<=0||this.__===t)return;const i=this.Du(),s=this.__;if(this.__=t,this.vu=!0,this.cn.lockVisibleTimeRangeOnResize&&s!==0){const e=this.Su*t/s;this.Su=e}if(this.cn.fixLeftEdge&&i!==null&&i.Os()<=0){const e=s-t;this.xu-=Math.round(e/this.Su)+1,this.vu=!0}this.zu(),this.Lu()}It(t){if(this.Ni()||!xt(t))return 0;const i=this.Eu()+this.xu-t;return this.__-(i+.5)*this.Su-1}Qs(t,i){const s=this.Eu(),e=i===void 0?0:i.from,h=i===void 0?t.length:i.to;for(let r=e;r<h;r++){const o=t[r].ot,l=s+this.xu-o,a=this.__-(l+.5)*this.Su-1;t[r].nt=a}}Nu(t){return Math.ceil(this.Fu(t))}Jn(t){this.vu=!0,this.xu=t,this.Lu(),this.$i.Wu(),this.$i.Uh()}le(){return this.Su}Gn(t){this.ju(t),this.Lu(),this.$i.Wu(),this.$i.Uh()}Hu(){return this.xu}Ha(){if(this.Ni())return null;if(this.gu!==null)return this.gu;const t=this.Su,i=5*(this.$i.W().layout.fontSize+4)/8*(this.cn.tickMarkMaxCharacterLength||8),s=Math.round(i/t),e=p(this.Xs()),h=Math.max(e.Os(),e.Os()-s),r=Math.max(e.ui(),e.ui()-s),o=this.cu.su(t,i),l=this.Bu()+s,a=this.Au()-s,u=this.$u(),c=this.cn.fixLeftEdge||u,d=this.cn.fixRightEdge||u;let f=0;for(const v of o){if(!(h<=v.index&&v.index<=r))continue;let m;f<this.Mu.length?(m=this.Mu[f],m.coord=this.It(v.index),m.label=this.Uu(v),m.weight=v.weight):(m={needAlignCoordinate:!1,coord:this.It(v.index),label:this.Uu(v),weight:v.weight},this.Mu.push(m)),this.Su>i/2&&!u?m.needAlignCoordinate=!1:m.needAlignCoordinate=c&&v.index<=l||d&&v.index>=a,f++}return this.Mu.length=f,this.gu=this.Mu,this.Mu}qu(){this.vu=!0,this.Gn(this.cn.barSpacing),this.Jn(this.cn.rightOffset)}Yu(t){this.vu=!0,this._u=t,this.Lu(),this.Tu()}Zu(t,i){const s=this.Fu(t),e=this.le(),h=e+i*(e/10);this.Gn(h),this.cn.rightBarStaysOnScroll||this.Jn(this.Hu()+(s-this.Fu(t)))}Go(t){this.Mo&&this.n_(),this.wo===null&&this.wu===null&&(this.Ni()||(this.wo=t,this.Xu()))}Jo(t){if(this.wu===null)return;const i=yi(this.__-t,0,this.__),s=yi(this.__-p(this.wo),0,this.__);i!==0&&s!==0&&this.Gn(this.wu.le*i/s)}Qo(){this.wo!==null&&(this.wo=null,this.Ku())}t_(t){this.Mo===null&&this.wu===null&&(this.Ni()||(this.Mo=t,this.Xu()))}i_(t){if(this.Mo===null)return;const i=(this.Mo-t)/this.le();this.xu=p(this.wu).Hu+i,this.vu=!0,this.Lu()}n_(){this.Mo!==null&&(this.Mo=null,this.Ku())}Gu(){this.Ju(this.cn.rightOffset)}Ju(t,i=400){if(!isFinite(t))throw new RangeError("offset is required and must be finite number");if(!isFinite(i)||i<=0)throw new RangeError("animationDuration (optional) must be finite positive number");const s=this.xu,e=performance.now();this.$i.Zn({Qu:h=>(h-e)/i>=1,tc:h=>{const r=(h-e)/i;return r>=1?t:s+(t-s)*r}})}bt(t,i){this.vu=!0,this.uu=t,this.cu.iu(t,i),this.Lu()}nc(){return this.pu}sc(){return this.mu}ec(){return this.bu}Eu(){return this._u||0}rc(t){const i=t.G_();this.ju(this.__/i),this.xu=t.ui()-this.Eu(),this.Lu(),this.vu=!0,this.$i.Wu(),this.$i.Uh()}hc(){const t=this.Bu(),i=this.Au();t!==null&&i!==null&&this.rc(new wt(t,i+this.cn.rightOffset))}lc(t){const i=new wt(t.from,t.to);this.rc(i)}qi(t){return this.yo.timeFormatter!==void 0?this.yo.timeFormatter(t.originalTime):this.q_.formatHorzItem(t.time)}$u(){const{handleScroll:t,handleScale:i}=this.$i.W();return!(t.horzTouchDrag||t.mouseWheel||t.pressedMouseMove||t.vertTouchDrag||i.axisDoubleClickReset.time||i.axisPressedMouseMove.time||i.mouseWheel||i.pinch)}Bu(){return this.uu.length===0?null:0}Au(){return this.uu.length===0?null:this.uu.length-1}ac(t){return(this.__-1-t)/this.Su}Fu(t){const i=this.ac(t),s=this.Eu()+this.xu-i;return Math.round(1e6*s)/1e6}ju(t){const i=this.Su;this.Su=t,this.zu(),i!==this.Su&&(this.vu=!0,this.oc())}Ru(){if(!this.vu)return;if(this.vu=!1,this.Ni())return void this._c(ct.ou());const t=this.Eu(),i=this.__/this.Su,s=this.xu+t,e=new wt(s-i+1,s);this._c(new ct(e))}zu(){const t=this.uc();if(this.Su<t&&(this.Su=t,this.vu=!0),this.__!==0){const i=.5*this.__;this.Su>i&&(this.Su=i,this.vu=!0)}}uc(){return this.cn.fixLeftEdge&&this.cn.fixRightEdge&&this.uu.length!==0?this.__/this.uu.length:this.cn.minBarSpacing}Lu(){const t=this.cc();t!==null&&this.xu<t&&(this.xu=t,this.vu=!0);const i=this.dc();this.xu>i&&(this.xu=i,this.vu=!0)}cc(){const t=this.Bu(),i=this._u;return t===null||i===null?null:t-i-1+(this.cn.fixLeftEdge?this.__/this.Su:Math.min(2,this.uu.length))}dc(){return this.cn.fixRightEdge?0:this.__/this.Su-Math.min(2,this.uu.length)}Xu(){this.wu={le:this.le(),Hu:this.Hu()}}Ku(){this.wu=null}Uu(t){let i=this.du.get(t.weight);return i===void 0&&(i=new Gn(s=>this.fc(s),this.q_),this.du.set(t.weight,i)),i.Y_(t)}fc(t){return this.q_.formatTickmark(t,this.yo)}_c(t){const i=this.fu;this.fu=t,fs(i.lu(),this.fu.lu())||this.pu.m(),fs(i.au(),this.fu.au())||this.mu.m(),this.oc()}oc(){this.gu=null}Cu(){this.oc(),this.du.clear()}ku(){this.q_.updateFormatter(this.yo)}Tu(){if(!this.cn.fixLeftEdge)return;const t=this.Bu();if(t===null)return;const i=this.Xs();if(i===null)return;const s=i.Os()-t;if(s<0){const e=this.xu-s-1;this.Jn(e)}this.zu()}Pu(){this.Lu(),this.zu()}}class th{X(t,i,s){t.useMediaCoordinateSpace(e=>this.K(e,i,s))}gl(t,i,s){t.useMediaCoordinateSpace(e=>this.vc(e,i,s))}vc(t,i,s){}}class ih extends th{constructor(t){super(),this.mc=new Map,this.zt=t}K(t){}vc(t){if(!this.zt.yt)return;const{context:i,mediaSize:s}=t;let e=0;for(const r of this.zt.bc){if(r.Kt.length===0)continue;i.font=r.R;const o=this.wc(i,r.Kt);o>s.width?r.Zu=s.width/o:r.Zu=1,e+=r.gc*r.Zu}let h=0;switch(this.zt.Mc){case"top":h=0;break;case"center":h=Math.max((s.height-e)/2,0);break;case"bottom":h=Math.max(s.height-e,0)}i.fillStyle=this.zt.V;for(const r of this.zt.bc){i.save();let o=0;switch(this.zt.xc){case"left":i.textAlign="left",o=r.gc/2;break;case"center":i.textAlign="center",o=s.width/2;break;case"right":i.textAlign="right",o=s.width-1-r.gc/2}i.translate(o,h),i.textBaseline="top",i.font=r.R,i.scale(r.Zu,r.Zu),i.fillText(r.Kt,0,r.Sc),i.restore(),h+=r.gc*r.Zu}}wc(t,i){const s=this.kc(t.font);let e=s.get(i);return e===void 0&&(e=t.measureText(i).width,s.set(i,e)),e}kc(t){let i=this.mc.get(t);return i===void 0&&(i=new Map,this.mc.set(t,i)),i}}class sh{constructor(t){this.ft=!0,this.Ft={yt:!1,V:"",bc:[],Mc:"center",xc:"center"},this.Wt=new ih(this.Ft),this.jt=t}bt(){this.ft=!0}gt(){return this.ft&&(this.Mt(),this.ft=!1),this.Wt}Mt(){const t=this.jt.W(),i=this.Ft;i.yt=t.visible,i.yt&&(i.V=t.color,i.xc=t.horzAlign,i.Mc=t.vertAlign,i.bc=[{Kt:t.text,R:dt(t.fontSize,t.fontFamily,t.fontStyle),gc:1.2*t.fontSize,Sc:0,Zu:0}])}}class eh extends Ri{constructor(t,i){super(),this.cn=i,this.wn=new sh(this)}Rn(){return[]}Pn(){return[this.wn]}W(){return this.cn}Vn(){this.wn.bt()}}var ms,vs,ps,gs,bs;(function(n){n[n.OnTouchEnd=0]="OnTouchEnd",n[n.OnNextTap=1]="OnNextTap"})(ms||(ms={}));class nh{constructor(t,i,s){this.yc=[],this.Cc=[],this.__=0,this.Tc=null,this.Pc=new V,this.Rc=new V,this.Dc=null,this.Vc=t,this.cn=i,this.q_=s,this.Oc=new We(this),this.yl=new Zn(this,i.timeScale,this.cn.localization,s),this.vt=new Xe(this,i.crosshair),this.Bc=new jn(i.crosshair),this.Ac=new eh(this,i.watermark),this.Ic(),this.yc[0].x_(2e3),this.zc=this.Lc(0),this.Ec=this.Lc(1)}Kl(){this.Nc(W.es())}Uh(){this.Nc(W.ss())}oa(){this.Nc(new W(1))}Gl(t){const i=this.Fc(t);this.Nc(i)}Wc(){return this.Tc}jc(t){const i=this.Tc;this.Tc=t,i!==null&&this.Gl(i.Hc),t!==null&&this.Gl(t.Hc)}W(){return this.cn}$h(t){F(this.cn,t),this.yc.forEach(i=>i.b_(t)),t.timeScale!==void 0&&this.yl.$h(t.timeScale),t.localization!==void 0&&this.yl.yu(t.localization),(t.leftPriceScale||t.rightPriceScale)&&this.Pc.m(),this.zc=this.Lc(0),this.Ec=this.Lc(1),this.Kl()}$c(t,i){if(t==="left")return void this.$h({leftPriceScale:i});if(t==="right")return void this.$h({rightPriceScale:i});const s=this.Uc(t);s!==null&&(s.Dt.$h(i),this.Pc.m())}Uc(t){for(const i of this.yc){const s=i.w_(t);if(s!==null)return{Ht:i,Dt:s}}return null}St(){return this.yl}qc(){return this.yc}Yc(){return this.Ac}Zc(){return this.vt}Xc(){return this.Rc}Kc(t,i){t.Lo(i),this.Wu()}S_(t){this.__=t,this.yl.S_(this.__),this.yc.forEach(i=>i.S_(t)),this.Wu()}Ic(t){const i=new Jn(this.yl,this);t!==void 0?this.yc.splice(t,0,i):this.yc.push(i);const s=t===void 0?this.yc.length-1:t,e=W.es();return e.Nn(s,{Fn:0,Wn:!0}),this.Nc(e),i}V_(t,i,s){t.V_(i,s)}O_(t,i,s){t.O_(i,s),this.Jl(),this.Nc(this.Gc(t,2))}B_(t,i){t.B_(i),this.Nc(this.Gc(t,2))}A_(t,i,s){i.Vo()||t.A_(i,s)}I_(t,i,s){i.Vo()||(t.I_(i,s),this.Jl(),this.Nc(this.Gc(t,2)))}z_(t,i){i.Vo()||(t.z_(i),this.Nc(this.Gc(t,2)))}E_(t,i){t.E_(i),this.Nc(this.Gc(t,2))}Jc(t){this.yl.Go(t)}Qc(t,i){const s=this.St();if(s.Ni()||i===0)return;const e=s.Hi();t=Math.max(1,Math.min(t,e)),s.Zu(t,i),this.Wu()}td(t){this.nd(0),this.sd(t),this.ed()}rd(t){this.yl.Jo(t),this.Wu()}hd(){this.yl.Qo(),this.Uh()}nd(t){this.yl.t_(t)}sd(t){this.yl.i_(t),this.Wu()}ed(){this.yl.n_(),this.Uh()}wt(){return this.Cc}ld(t,i,s,e,h){this.vt.gn(t,i);let r=NaN,o=this.yl.Nu(t);const l=this.yl.Xs();l!==null&&(o=Math.min(Math.max(l.Os(),o),l.ui()));const a=e.vn(),u=a.Ct();u!==null&&(r=a.pn(i,u)),r=this.Bc.Oa(r,o,e),this.vt.kn(o,r,e),this.oa(),h||this.Rc.m(this.vt.xt(),{x:t,y:i},s)}ad(t,i,s){const e=s.vn(),h=e.Ct(),r=e.Rt(t,p(h)),o=this.yl.Va(i,!0),l=this.yl.It(p(o));this.ld(l,r,null,s,!0)}od(t){this.Zc().Cn(),this.oa(),t||this.Rc.m(null,null,null)}Jl(){const t=this.vt.Ht();if(t!==null){const i=this.vt.xn(),s=this.vt.Sn();this.ld(i,s,null,t)}this.vt.Vn()}_d(t,i,s){const e=this.yl.mn(0);i!==void 0&&s!==void 0&&this.yl.bt(i,s);const h=this.yl.mn(0),r=this.yl.Eu(),o=this.yl.Xs();if(o!==null&&e!==null&&h!==null){const l=o.Kr(r),a=this.q_.key(e)>this.q_.key(h),u=t!==null&&t>r&&!a,c=this.yl.W().allowShiftVisibleRangeOnWhitespaceReplacement,d=l&&(s!==void 0||c)&&this.yl.W().shiftVisibleRangeOnNewBar;if(u&&!d){const f=t-r;this.yl.Jn(this.yl.Hu()-f)}}this.yl.Yu(t)}ia(t){t!==null&&t.F_()}dr(t){const i=this.yc.find(s=>s.Uo().includes(t));return i===void 0?null:i}Wu(){this.Ac.Vn(),this.yc.forEach(t=>t.F_()),this.Jl()}S(){this.yc.forEach(t=>t.S()),this.yc.length=0,this.cn.localization.priceFormatter=void 0,this.cn.localization.percentageFormatter=void 0,this.cn.localization.timeFormatter=void 0}ud(){return this.Oc}br(){return this.Oc.W()}g_(){return this.Pc}dd(t,i,s){const e=this.yc[0],h=this.fd(i,t,e,s);return this.Cc.push(h),this.Cc.length===1?this.Kl():this.Uh(),h}vd(t){const i=this.dr(t),s=this.Cc.indexOf(t);Y(s!==-1,"Series not found"),this.Cc.splice(s,1),p(i).Zo(t),t.S&&t.S()}Xl(t,i){const s=p(this.dr(t));s.Zo(t);const e=this.Uc(i);if(e===null){const h=t.Xi();s.qo(t,i,h)}else{const h=e.Ht===s?t.Xi():void 0;e.Ht.qo(t,i,h)}}hc(){const t=W.ss();t.$n(),this.Nc(t)}pd(t){const i=W.ss();i.Yn(t),this.Nc(i)}Kn(){const t=W.ss();t.Kn(),this.Nc(t)}Gn(t){const i=W.ss();i.Gn(t),this.Nc(i)}Jn(t){const i=W.ss();i.Jn(t),this.Nc(i)}Zn(t){const i=W.ss();i.Zn(t),this.Nc(i)}Un(){const t=W.ss();t.Un(),this.Nc(t)}md(){return this.cn.rightPriceScale.visible?"right":"left"}bd(){return this.Ec}q(){return this.zc}Bt(t){const i=this.Ec,s=this.zc;if(i===s)return i;if(t=Math.max(0,Math.min(100,Math.round(100*t))),this.Dc===null||this.Dc.Ps!==s||this.Dc.Rs!==i)this.Dc={Ps:s,Rs:i,wd:new Map};else{const h=this.Dc.wd.get(t);if(h!==void 0)return h}const e=function(h,r,o){const[l,a,u,c]=yt(h),[d,f,v,m]=yt(r),g=[O(l+o*(d-l)),O(a+o*(f-a)),O(u+o*(v-u)),Hs(c+o*(m-c))];return`rgba(${g[0]}, ${g[1]}, ${g[2]}, ${g[3]})`}(s,i,t/100);return this.Dc.wd.set(t,e),e}Gc(t,i){const s=new W(i);if(t!==null){const e=this.yc.indexOf(t);s.Nn(e,{Fn:i})}return s}Fc(t,i){return i===void 0&&(i=2),this.Gc(this.dr(t),i)}Nc(t){this.Vc&&this.Vc(t),this.yc.forEach(i=>i.j_().qh().bt())}fd(t,i,s,e){const h=new Vi(this,t,i,s,e),r=t.priceScaleId!==void 0?t.priceScaleId:this.md();return s.qo(h,r),At(r)||h.$h(t),h}Lc(t){const i=this.cn.layout;return i.background.type==="gradient"?t===0?i.background.topColor:i.background.bottomColor:i.background.color}}function xi(n){return!H(n)&&!Ct(n)}function ue(n){return H(n)}(function(n){n[n.Disabled=0]="Disabled",n[n.Continuous=1]="Continuous",n[n.OnDataUpdate=2]="OnDataUpdate"})(vs||(vs={})),function(n){n[n.LastBar=0]="LastBar",n[n.LastVisible=1]="LastVisible"}(ps||(ps={})),function(n){n.Solid="solid",n.VerticalGradient="gradient"}(gs||(gs={})),function(n){n[n.Year=0]="Year",n[n.Month=1]="Month",n[n.DayOfMonth=2]="DayOfMonth",n[n.Time=3]="Time",n[n.TimeWithSeconds=4]="TimeWithSeconds"}(bs||(bs={}));const ws=n=>n.getUTCFullYear();function hh(n,t,i){return t.replace(/yyyy/g,(s=>A(ws(s),4))(n)).replace(/yy/g,(s=>A(ws(s)%100,2))(n)).replace(/MMMM/g,((s,e)=>new Date(s.getUTCFullYear(),s.getUTCMonth(),1).toLocaleString(e,{month:"long"}))(n,i)).replace(/MMM/g,((s,e)=>new Date(s.getUTCFullYear(),s.getUTCMonth(),1).toLocaleString(e,{month:"short"}))(n,i)).replace(/MM/g,(s=>A((e=>e.getUTCMonth()+1)(s),2))(n)).replace(/dd/g,(s=>A((e=>e.getUTCDate())(s),2))(n))}class ce{constructor(t="yyyy-MM-dd",i="default"){this.gd=t,this.Md=i}Y_(t){return hh(t,this.gd,this.Md)}}class rh{constructor(t){this.xd=t||"%h:%m:%s"}Y_(t){return this.xd.replace("%h",A(t.getUTCHours(),2)).replace("%m",A(t.getUTCMinutes(),2)).replace("%s",A(t.getUTCSeconds(),2))}}const oh={Sd:"yyyy-MM-dd",kd:"%h:%m:%s",yd:" ",Cd:"default"};class lh{constructor(t={}){const i=Object.assign(Object.assign({},oh),t);this.Td=new ce(i.Sd,i.Cd),this.Pd=new rh(i.kd),this.Rd=i.yd}Y_(t){return`${this.Td.Y_(t)}${this.Rd}${this.Pd.Y_(t)}`}}function Nt(n){return 60*n*60*1e3}function hi(n){return 60*n*1e3}const Wt=[{Dd:(ys=1,1e3*ys),Vd:10},{Dd:hi(1),Vd:20},{Dd:hi(5),Vd:21},{Dd:hi(30),Vd:22},{Dd:Nt(1),Vd:30},{Dd:Nt(3),Vd:31},{Dd:Nt(6),Vd:32},{Dd:Nt(12),Vd:33}];var ys;function xs(n,t){if(n.getUTCFullYear()!==t.getUTCFullYear())return 70;if(n.getUTCMonth()!==t.getUTCMonth())return 60;if(n.getUTCDate()!==t.getUTCDate())return 50;for(let i=Wt.length-1;i>=0;--i)if(Math.floor(t.getTime()/Wt[i].Dd)!==Math.floor(n.getTime()/Wt[i].Dd))return Wt[i].Vd;return 0}function ri(n){let t=n;if(Ct(n)&&(t=Ti(n)),!xi(t))throw new Error("time must be of type BusinessDay");const i=new Date(Date.UTC(t.year,t.month-1,t.day,0,0,0,0));return{Od:Math.round(i.getTime()/1e3),Bd:t}}function Ss(n){if(!ue(n))throw new Error("time must be of type isUTCTimestamp");return{Od:n}}function Ti(n){const t=new Date(n);if(isNaN(t.getTime()))throw new Error(`Invalid date string=${n}, expected format=yyyy-mm-dd`);return{day:t.getUTCDate(),month:t.getUTCMonth()+1,year:t.getUTCFullYear()}}function Ms(n){Ct(n.time)&&(n.time=Ti(n.time))}class _s{options(){return this.cn}setOptions(t){this.cn=t,this.updateFormatter(t.localization)}preprocessData(t){Array.isArray(t)?function(i){i.forEach(Ms)}(t):Ms(t)}createConverterToInternalObj(t){return p(function(i){return i.length===0?null:xi(i[0].time)||Ct(i[0].time)?ri:Ss}(t))}key(t){return typeof t=="object"&&"Od"in t?t.Od:this.key(this.convertHorzItemToInternal(t))}cacheKey(t){const i=t;return i.Bd===void 0?new Date(1e3*i.Od).getTime():new Date(Date.UTC(i.Bd.year,i.Bd.month-1,i.Bd.day)).getTime()}convertHorzItemToInternal(t){return ue(i=t)?Ss(i):xi(i)?ri(i):ri(Ti(i));var i}updateFormatter(t){if(!this.cn)return;const i=t.dateFormat;this.cn.timeScale.timeVisible?this.Ad=new lh({Sd:i,kd:this.cn.timeScale.secondsVisible?"%h:%m:%s":"%h:%m",yd:"   ",Cd:t.locale}):this.Ad=new ce(i,t.locale)}formatHorzItem(t){const i=t;return this.Ad.Y_(new Date(1e3*i.Od))}formatTickmark(t,i){const s=function(h,r,o){switch(h){case 0:case 10:return r?o?4:3:2;case 20:case 21:case 22:case 30:case 31:case 32:case 33:return r?3:2;case 50:return 2;case 60:return 1;case 70:return 0}}(t.weight,this.cn.timeScale.timeVisible,this.cn.timeScale.secondsVisible),e=this.cn.timeScale;if(e.tickMarkFormatter!==void 0){const h=e.tickMarkFormatter(t.originalTime,s,i.locale);if(h!==null)return h}return function(h,r,o){const l={};switch(r){case 0:l.year="numeric";break;case 1:l.month="short";break;case 2:l.day="numeric";break;case 3:l.hour12=!1,l.hour="2-digit",l.minute="2-digit";break;case 4:l.hour12=!1,l.hour="2-digit",l.minute="2-digit",l.second="2-digit"}const a=h.Bd===void 0?new Date(1e3*h.Od):new Date(Date.UTC(h.Bd.year,h.Bd.month-1,h.Bd.day));return new Date(a.getUTCFullYear(),a.getUTCMonth(),a.getUTCDate(),a.getUTCHours(),a.getUTCMinutes(),a.getUTCSeconds(),a.getUTCMilliseconds()).toLocaleString(o,l)}(t.time,s,i.locale)}maxTickMarkWeight(t){let i=t.reduce(Yn,t[0]).weight;return i>30&&i<50&&(i=30),i}fillWeightsForPoints(t,i){(function(s,e=0){if(s.length===0)return;let h=e===0?null:s[e-1].time.Od,r=h!==null?new Date(1e3*h):null,o=0;for(let l=e;l<s.length;++l){const a=s[l],u=new Date(1e3*a.time.Od);r!==null&&(a.timeWeight=xs(u,r)),o+=a.time.Od-(h||a.time.Od),h=a.time.Od,r=u}if(e===0&&s.length>1){const l=Math.ceil(o/(s.length-1)),a=new Date(1e3*(s[0].time.Od-l));s[0].timeWeight=xs(new Date(1e3*s[0].time.Od),a)}})(t,i)}static Id(t){return F({localization:{dateFormat:"dd MMM 'yy"}},t??{})}}const ft=typeof window<"u";function zs(){return!!ft&&window.navigator.userAgent.toLowerCase().indexOf("firefox")>-1}function oi(){return!!ft&&/iPhone|iPad|iPod/.test(window.navigator.platform)}function Si(n){return n+n%2}function li(n,t){return n.zd-t.zd}function ai(n,t,i){const s=(n.zd-t.zd)/(n.ot-t.ot);return Math.sign(s)*Math.min(Math.abs(s),i)}class ah{constructor(t,i,s,e){this.Ld=null,this.Ed=null,this.Nd=null,this.Fd=null,this.Wd=null,this.jd=0,this.Hd=0,this.$d=t,this.Ud=i,this.qd=s,this.rs=e}Yd(t,i){if(this.Ld!==null){if(this.Ld.ot===i)return void(this.Ld.zd=t);if(Math.abs(this.Ld.zd-t)<this.rs)return}this.Fd=this.Nd,this.Nd=this.Ed,this.Ed=this.Ld,this.Ld={ot:i,zd:t}}Vr(t,i){if(this.Ld===null||this.Ed===null||i-this.Ld.ot>50)return;let s=0;const e=ai(this.Ld,this.Ed,this.Ud),h=li(this.Ld,this.Ed),r=[e],o=[h];if(s+=h,this.Nd!==null){const a=ai(this.Ed,this.Nd,this.Ud);if(Math.sign(a)===Math.sign(e)){const u=li(this.Ed,this.Nd);if(r.push(a),o.push(u),s+=u,this.Fd!==null){const c=ai(this.Nd,this.Fd,this.Ud);if(Math.sign(c)===Math.sign(e)){const d=li(this.Nd,this.Fd);r.push(c),o.push(d),s+=d}}}}let l=0;for(let a=0;a<r.length;++a)l+=o[a]/s*r[a];Math.abs(l)<this.$d||(this.Wd={zd:t,ot:i},this.Hd=l,this.jd=function(a,u){const c=Math.log(u);return Math.log(1*c/-a)/c}(Math.abs(l),this.qd))}tc(t){const i=p(this.Wd),s=t-i.ot;return i.zd+this.Hd*(Math.pow(this.qd,s)-1)/Math.log(this.qd)}Qu(t){return this.Wd===null||this.Zd(t)===this.jd}Zd(t){const i=t-p(this.Wd).ot;return Math.min(i,this.jd)}}class uh{constructor(t,i){this.Xd=void 0,this.Kd=void 0,this.Gd=void 0,this.en=!1,this.Jd=t,this.Qd=i,this.tf()}bt(){this.tf()}if(){this.Xd&&this.Jd.removeChild(this.Xd),this.Kd&&this.Jd.removeChild(this.Kd),this.Xd=void 0,this.Kd=void 0}nf(){return this.en!==this.sf()||this.Gd!==this.ef()}ef(){return Xs(yt(this.Qd.W().layout.textColor))>160?"dark":"light"}sf(){return this.Qd.W().layout.attributionLogo}rf(){const t=new URL(location.href);return t.hostname?"&utm_source="+t.hostname+t.pathname:""}tf(){this.nf()&&(this.if(),this.en=this.sf(),this.en&&(this.Gd=this.ef(),this.Kd=document.createElement("style"),this.Kd.innerText="a#tv-attr-logo{--fill:#131722;--stroke:#fff;position:absolute;left:10px;bottom:10px;height:19px;width:35px;margin:0;padding:0;border:0;z-index:3;}a#tv-attr-logo[data-dark]{--fill:#D1D4DC;--stroke:#131722;}",this.Xd=document.createElement("a"),this.Xd.href=`https://www.tradingview.com/?utm_medium=lwc-link&utm_campaign=lwc-chart${this.rf()}`,this.Xd.title="Charting by TradingView",this.Xd.id="tv-attr-logo",this.Xd.target="_blank",this.Xd.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 35 19" width="35" height="19" fill="none"><g fill-rule="evenodd" clip-path="url(#a)" clip-rule="evenodd"><path fill="var(--stroke)" d="M2 0H0v10h6v9h21.4l.5-1.3 6-15 1-2.7H23.7l-.5 1.3-.2.6a5 5 0 0 0-7-.9V0H2Zm20 17h4l5.2-13 .8-2h-7l-1 2.5-.2.5-1.5 3.8-.3.7V17Zm-.8-10a3 3 0 0 0 .7-2.7A3 3 0 1 0 16.8 7h4.4ZM14 7V2H2v6h6v9h4V7h2Z"/><path fill="var(--fill)" d="M14 2H2v6h6v9h6V2Zm12 15h-7l6-15h7l-6 15Zm-7-9a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></g><defs><clipPath id="a"><path fill="var(--stroke)" d="M0 0h35v19H0z"/></clipPath></defs></svg>',this.Xd.toggleAttribute("data-dark",this.Gd==="dark"),this.Jd.appendChild(this.Kd),this.Jd.appendChild(this.Xd)))}}function et(n,t){const i=p(n.ownerDocument).createElement("canvas");n.appendChild(i);const s=ye(i,{options:{allowResizeObserver:!1},transform:(e,h)=>({width:Math.max(e.width,h.width),height:Math.max(e.height,h.height)})});return s.resizeCanvasElement(t),s}function nt(n){var t;n.width=1,n.height=1,(t=n.getContext("2d"))===null||t===void 0||t.clearRect(0,0,1,1)}function Mi(n,t,i,s){n.gl&&n.gl(t,i,s)}function Ft(n,t,i,s){n.X(t,i,s)}function _i(n,t,i,s){const e=n(i,s);for(const h of e){const r=h.gt();r!==null&&t(r)}}function ch(n){ft&&window.chrome!==void 0&&n.addEventListener("mousedown",t=>{if(t.button===1)return t.preventDefault(),!1})}class Ni{constructor(t,i,s){this.hf=0,this.lf=null,this.af={nt:Number.NEGATIVE_INFINITY,st:Number.POSITIVE_INFINITY},this._f=0,this.uf=null,this.cf={nt:Number.NEGATIVE_INFINITY,st:Number.POSITIVE_INFINITY},this.df=null,this.ff=!1,this.vf=null,this.pf=null,this.mf=!1,this.bf=!1,this.wf=!1,this.gf=null,this.Mf=null,this.xf=null,this.Sf=null,this.kf=null,this.yf=null,this.Cf=null,this.Tf=0,this.Pf=!1,this.Rf=!1,this.Df=!1,this.Vf=0,this.Of=null,this.Bf=!oi(),this.Af=e=>{this.If(e)},this.zf=e=>{if(this.Lf(e)){const h=this.Ef(e);if(++this._f,this.uf&&this._f>1){const{Nf:r}=this.Ff(K(e),this.cf);r<30&&!this.wf&&this.Wf(h,this.Hf.jf),this.$f()}}else{const h=this.Ef(e);if(++this.hf,this.lf&&this.hf>1){const{Nf:r}=this.Ff(K(e),this.af);r<5&&!this.bf&&this.Uf(h,this.Hf.qf),this.Yf()}}},this.Zf=t,this.Hf=i,this.cn=s,this.Xf()}S(){this.gf!==null&&(this.gf(),this.gf=null),this.Mf!==null&&(this.Mf(),this.Mf=null),this.Sf!==null&&(this.Sf(),this.Sf=null),this.kf!==null&&(this.kf(),this.kf=null),this.yf!==null&&(this.yf(),this.yf=null),this.xf!==null&&(this.xf(),this.xf=null),this.Kf(),this.Yf()}Gf(t){this.Sf&&this.Sf();const i=this.Jf.bind(this);if(this.Sf=()=>{this.Zf.removeEventListener("mousemove",i)},this.Zf.addEventListener("mousemove",i),this.Lf(t))return;const s=this.Ef(t);this.Uf(s,this.Hf.Qf),this.Bf=!0}Yf(){this.lf!==null&&clearTimeout(this.lf),this.hf=0,this.lf=null,this.af={nt:Number.NEGATIVE_INFINITY,st:Number.POSITIVE_INFINITY}}$f(){this.uf!==null&&clearTimeout(this.uf),this._f=0,this.uf=null,this.cf={nt:Number.NEGATIVE_INFINITY,st:Number.POSITIVE_INFINITY}}Jf(t){if(this.Df||this.pf!==null||this.Lf(t))return;const i=this.Ef(t);this.Uf(i,this.Hf.tv),this.Bf=!0}iv(t){const i=ui(t.changedTouches,p(this.Of));if(i===null||(this.Vf=Dt(t),this.Cf!==null)||this.Rf)return;this.Pf=!0;const s=this.Ff(K(i),p(this.pf)),{nv:e,sv:h,Nf:r}=s;if(this.mf||!(r<5)){if(!this.mf){const o=.5*e,l=h>=o&&!this.cn.ev(),a=o>h&&!this.cn.rv();l||a||(this.Rf=!0),this.mf=!0,this.wf=!0,this.Kf(),this.$f()}if(!this.Rf){const o=this.Ef(t,i);this.Wf(o,this.Hf.hv),lt(t)}}}lv(t){if(t.button!==0)return;const i=this.Ff(K(t),p(this.vf)),{Nf:s}=i;if(s>=5&&(this.bf=!0,this.Yf()),this.bf){const e=this.Ef(t);this.Uf(e,this.Hf.av)}}Ff(t,i){const s=Math.abs(i.nt-t.nt),e=Math.abs(i.st-t.st);return{nv:s,sv:e,Nf:s+e}}ov(t){let i=ui(t.changedTouches,p(this.Of));if(i===null&&t.touches.length===0&&(i=t.changedTouches[0]),i===null)return;this.Of=null,this.Vf=Dt(t),this.Kf(),this.pf=null,this.yf&&(this.yf(),this.yf=null);const s=this.Ef(t,i);if(this.Wf(s,this.Hf._v),++this._f,this.uf&&this._f>1){const{Nf:e}=this.Ff(K(i),this.cf);e<30&&!this.wf&&this.Wf(s,this.Hf.jf),this.$f()}else this.wf||(this.Wf(s,this.Hf.uv),this.Hf.uv&&lt(t));this._f===0&&lt(t),t.touches.length===0&&this.ff&&(this.ff=!1,lt(t))}If(t){if(t.button!==0)return;const i=this.Ef(t);if(this.vf=null,this.Df=!1,this.kf&&(this.kf(),this.kf=null),zs()&&this.Zf.ownerDocument.documentElement.removeEventListener("mouseleave",this.Af),!this.Lf(t))if(this.Uf(i,this.Hf.cv),++this.hf,this.lf&&this.hf>1){const{Nf:s}=this.Ff(K(t),this.af);s<5&&!this.bf&&this.Uf(i,this.Hf.qf),this.Yf()}else this.bf||this.Uf(i,this.Hf.dv)}Kf(){this.df!==null&&(clearTimeout(this.df),this.df=null)}fv(t){if(this.Of!==null)return;const i=t.changedTouches[0];this.Of=i.identifier,this.Vf=Dt(t);const s=this.Zf.ownerDocument.documentElement;this.wf=!1,this.mf=!1,this.Rf=!1,this.pf=K(i),this.yf&&(this.yf(),this.yf=null);{const h=this.iv.bind(this),r=this.ov.bind(this);this.yf=()=>{s.removeEventListener("touchmove",h),s.removeEventListener("touchend",r)},s.addEventListener("touchmove",h,{passive:!1}),s.addEventListener("touchend",r,{passive:!1}),this.Kf(),this.df=setTimeout(this.vv.bind(this,t),240)}const e=this.Ef(t,i);this.Wf(e,this.Hf.pv),this.uf||(this._f=0,this.uf=setTimeout(this.$f.bind(this),500),this.cf=K(i))}mv(t){if(t.button!==0)return;const i=this.Zf.ownerDocument.documentElement;zs()&&i.addEventListener("mouseleave",this.Af),this.bf=!1,this.vf=K(t),this.kf&&(this.kf(),this.kf=null);{const e=this.lv.bind(this),h=this.If.bind(this);this.kf=()=>{i.removeEventListener("mousemove",e),i.removeEventListener("mouseup",h)},i.addEventListener("mousemove",e),i.addEventListener("mouseup",h)}if(this.Df=!0,this.Lf(t))return;const s=this.Ef(t);this.Uf(s,this.Hf.bv),this.lf||(this.hf=0,this.lf=setTimeout(this.Yf.bind(this),500),this.af=K(t))}Xf(){this.Zf.addEventListener("mouseenter",this.Gf.bind(this)),this.Zf.addEventListener("touchcancel",this.Kf.bind(this));{const t=this.Zf.ownerDocument,i=s=>{this.Hf.wv&&(s.composed&&this.Zf.contains(s.composedPath()[0])||s.target&&this.Zf.contains(s.target)||this.Hf.wv())};this.Mf=()=>{t.removeEventListener("touchstart",i)},this.gf=()=>{t.removeEventListener("mousedown",i)},t.addEventListener("mousedown",i),t.addEventListener("touchstart",i,{passive:!0})}oi()&&(this.xf=()=>{this.Zf.removeEventListener("dblclick",this.zf)},this.Zf.addEventListener("dblclick",this.zf)),this.Zf.addEventListener("mouseleave",this.gv.bind(this)),this.Zf.addEventListener("touchstart",this.fv.bind(this),{passive:!0}),ch(this.Zf),this.Zf.addEventListener("mousedown",this.mv.bind(this)),this.Mv(),this.Zf.addEventListener("touchmove",()=>{},{passive:!1})}Mv(){this.Hf.xv===void 0&&this.Hf.Sv===void 0&&this.Hf.kv===void 0||(this.Zf.addEventListener("touchstart",t=>this.yv(t.touches),{passive:!0}),this.Zf.addEventListener("touchmove",t=>{if(t.touches.length===2&&this.Cf!==null&&this.Hf.Sv!==void 0){const i=Cs(t.touches[0],t.touches[1])/this.Tf;this.Hf.Sv(this.Cf,i),lt(t)}},{passive:!1}),this.Zf.addEventListener("touchend",t=>{this.yv(t.touches)}))}yv(t){t.length===1&&(this.Pf=!1),t.length!==2||this.Pf||this.ff?this.Cv():this.Tv(t)}Tv(t){const i=this.Zf.getBoundingClientRect()||{left:0,top:0};this.Cf={nt:(t[0].clientX-i.left+(t[1].clientX-i.left))/2,st:(t[0].clientY-i.top+(t[1].clientY-i.top))/2},this.Tf=Cs(t[0],t[1]),this.Hf.xv!==void 0&&this.Hf.xv(),this.Kf()}Cv(){this.Cf!==null&&(this.Cf=null,this.Hf.kv!==void 0&&this.Hf.kv())}gv(t){if(this.Sf&&this.Sf(),this.Lf(t)||!this.Bf)return;const i=this.Ef(t);this.Uf(i,this.Hf.Pv),this.Bf=!oi()}vv(t){const i=ui(t.touches,p(this.Of));if(i===null)return;const s=this.Ef(t,i);this.Wf(s,this.Hf.Rv),this.wf=!0,this.ff=!0}Lf(t){return t.sourceCapabilities&&t.sourceCapabilities.firesTouchEvents!==void 0?t.sourceCapabilities.firesTouchEvents:Dt(t)<this.Vf+500}Wf(t,i){i&&i.call(this.Hf,t)}Uf(t,i){i&&i.call(this.Hf,t)}Ef(t,i){const s=i||t,e=this.Zf.getBoundingClientRect()||{left:0,top:0};return{clientX:s.clientX,clientY:s.clientY,pageX:s.pageX,pageY:s.pageY,screenX:s.screenX,screenY:s.screenY,localX:s.clientX-e.left,localY:s.clientY-e.top,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey,metaKey:t.metaKey,Dv:!t.type.startsWith("mouse")&&t.type!=="contextmenu"&&t.type!=="click",Vv:t.type,Ov:s.target,Bv:t.view,Av:()=>{t.type!=="touchstart"&&lt(t)}}}}function Cs(n,t){const i=n.clientX-t.clientX,s=n.clientY-t.clientY;return Math.sqrt(i*i+s*s)}function lt(n){n.cancelable&&n.preventDefault()}function K(n){return{nt:n.pageX,st:n.pageY}}function Dt(n){return n.timeStamp||performance.now()}function ui(n,t){for(let i=0;i<n.length;++i)if(n[i].identifier===t)return n[i];return null}function Pt(n){return{Hc:n.Hc,Iv:{gr:n.zv.externalId},Lv:n.zv.cursorStyle}}function dh(n,t,i){for(const s of n){const e=s.gt();if(e!==null&&e.wr){const h=e.wr(t,i);if(h!==null)return{Bv:s,Iv:h}}}return null}function ci(n,t){return i=>{var s,e,h,r;return((e=(s=i.Dt())===null||s===void 0?void 0:s.Pa())!==null&&e!==void 0?e:"")!==t?[]:(r=(h=i.da)===null||h===void 0?void 0:h.call(i,n))!==null&&r!==void 0?r:[]}}function Es(n,t,i,s){if(!n.length)return;let e=0;const h=i/2,r=n[0].At(s,!0);let o=t===1?h-(n[0].Vi()-r/2):n[0].Vi()-r/2-h;o=Math.max(0,o);for(let l=1;l<n.length;l++){const a=n[l],u=n[l-1],c=u.At(s,!1),d=a.Vi(),f=u.Vi();if(t===1?d>f-c:d<f+c){const v=f-c*t;a.Oi(v);const m=v-t*c/2;if((t===1?m<0:m>i)&&o>0){const g=t===1?-1-m:m-i,b=Math.min(g,o);for(let w=e;w<n.length;w++)n[w].Oi(n[w].Vi()+t*b);o-=b}}else e=l,o=t===1?f-c-d:d-(f+c)}}class Rs{constructor(t,i,s,e){this.Li=null,this.Ev=null,this.Nv=!1,this.Fv=new Mt(200),this.Qr=null,this.Wv=0,this.jv=!1,this.Hv=()=>{this.jv||this.tn.$v().$t().Uh()},this.Uv=()=>{this.jv||this.tn.$v().$t().Uh()},this.tn=t,this.cn=i,this.ko=i.layout,this.Oc=s,this.qv=e==="left",this.Yv=ci("normal",e),this.Zv=ci("top",e),this.Xv=ci("bottom",e),this.Kv=document.createElement("div"),this.Kv.style.height="100%",this.Kv.style.overflow="hidden",this.Kv.style.width="25px",this.Kv.style.left="0",this.Kv.style.position="relative",this.Gv=et(this.Kv,k({width:16,height:16})),this.Gv.subscribeSuggestedBitmapSizeChanged(this.Hv);const h=this.Gv.canvasElement;h.style.position="absolute",h.style.zIndex="1",h.style.left="0",h.style.top="0",this.Jv=et(this.Kv,k({width:16,height:16})),this.Jv.subscribeSuggestedBitmapSizeChanged(this.Uv);const r=this.Jv.canvasElement;r.style.position="absolute",r.style.zIndex="2",r.style.left="0",r.style.top="0";const o={bv:this.Qv.bind(this),pv:this.Qv.bind(this),av:this.tp.bind(this),hv:this.tp.bind(this),wv:this.ip.bind(this),cv:this.np.bind(this),_v:this.np.bind(this),qf:this.sp.bind(this),jf:this.sp.bind(this),Qf:this.ep.bind(this),Pv:this.rp.bind(this)};this.hp=new Ni(this.Jv.canvasElement,o,{ev:()=>!this.cn.handleScroll.vertTouchDrag,rv:()=>!0})}S(){this.hp.S(),this.Jv.unsubscribeSuggestedBitmapSizeChanged(this.Uv),nt(this.Jv.canvasElement),this.Jv.dispose(),this.Gv.unsubscribeSuggestedBitmapSizeChanged(this.Hv),nt(this.Gv.canvasElement),this.Gv.dispose(),this.Li!==null&&this.Li.Ko().p(this),this.Li=null}lp(){return this.Kv}P(){return this.ko.fontSize}ap(){const t=this.Oc.W();return this.Qr!==t.R&&(this.Fv.nr(),this.Qr=t.R),t}op(){if(this.Li===null)return 0;let t=0;const i=this.ap(),s=p(this.Gv.canvasElement.getContext("2d"));s.save();const e=this.Li.Ha();s.font=this._p(),e.length>0&&(t=Math.max(this.Fv.xi(s,e[0].so),this.Fv.xi(s,e[e.length-1].so)));const h=this.up();for(let a=h.length;a--;){const u=this.Fv.xi(s,h[a].Kt());u>t&&(t=u)}const r=this.Li.Ct();if(r!==null&&this.Ev!==null&&(o=this.cn.crosshair).mode!==2&&o.horzLine.visible&&o.horzLine.labelVisible){const a=this.Li.pn(1,r),u=this.Li.pn(this.Ev.height-2,r);t=Math.max(t,this.Fv.xi(s,this.Li.Fi(Math.floor(Math.min(a,u))+.11111111111111,r)),this.Fv.xi(s,this.Li.Fi(Math.ceil(Math.max(a,u))-.11111111111111,r)))}var o;s.restore();const l=t||34;return Si(Math.ceil(i.C+i.T+i.A+i.I+5+l))}cp(t){this.Ev!==null&&tt(this.Ev,t)||(this.Ev=t,this.jv=!0,this.Gv.resizeCanvasElement(t),this.Jv.resizeCanvasElement(t),this.jv=!1,this.Kv.style.width=`${t.width}px`,this.Kv.style.height=`${t.height}px`)}dp(){return p(this.Ev).width}Gi(t){this.Li!==t&&(this.Li!==null&&this.Li.Ko().p(this),this.Li=t,t.Ko().l(this.fo.bind(this),this))}Dt(){return this.Li}nr(){const t=this.tn.fp();this.tn.$v().$t().E_(t,p(this.Dt()))}vp(t){if(this.Ev===null)return;if(t!==1){this.pp(),this.Gv.applySuggestedBitmapSize();const s=it(this.Gv);s!==null&&(s.useBitmapCoordinateSpace(e=>{this.mp(e),this.Ie(e)}),this.tn.bp(s,this.Xv),this.wp(s),this.tn.bp(s,this.Yv),this.gp(s))}this.Jv.applySuggestedBitmapSize();const i=it(this.Jv);i!==null&&(i.useBitmapCoordinateSpace(({context:s,bitmapSize:e})=>{s.clearRect(0,0,e.width,e.height)}),this.Mp(i),this.tn.bp(i,this.Zv))}xp(){return this.Gv.bitmapSize}Sp(t,i,s){const e=this.xp();e.width>0&&e.height>0&&t.drawImage(this.Gv.canvasElement,i,s)}bt(){var t;(t=this.Li)===null||t===void 0||t.Ha()}Qv(t){if(this.Li===null||this.Li.Ni()||!this.cn.handleScale.axisPressedMouseMove.price)return;const i=this.tn.$v().$t(),s=this.tn.fp();this.Nv=!0,i.V_(s,this.Li,t.localY)}tp(t){if(this.Li===null||!this.cn.handleScale.axisPressedMouseMove.price)return;const i=this.tn.$v().$t(),s=this.tn.fp(),e=this.Li;i.O_(s,e,t.localY)}ip(){if(this.Li===null||!this.cn.handleScale.axisPressedMouseMove.price)return;const t=this.tn.$v().$t(),i=this.tn.fp(),s=this.Li;this.Nv&&(this.Nv=!1,t.B_(i,s))}np(t){if(this.Li===null||!this.cn.handleScale.axisPressedMouseMove.price)return;const i=this.tn.$v().$t(),s=this.tn.fp();this.Nv=!1,i.B_(s,this.Li)}sp(t){this.cn.handleScale.axisDoubleClickReset.price&&this.nr()}ep(t){this.Li!==null&&(!this.tn.$v().$t().W().handleScale.axisPressedMouseMove.price||this.Li.Mh()||this.Li.Oo()||this.kp(1))}rp(t){this.kp(0)}up(){const t=[],i=this.Li===null?void 0:this.Li;return(s=>{for(let e=0;e<s.length;++e){const h=s[e].Rn(this.tn.fp(),i);for(let r=0;r<h.length;r++)t.push(h[r])}})(this.tn.fp().Uo()),t}mp({context:t,bitmapSize:i}){const{width:s,height:e}=i,h=this.tn.fp().$t(),r=h.q(),o=h.bd();r===o?Ht(t,0,0,s,e,r):As(t,0,0,s,e,r,o)}Ie({context:t,bitmapSize:i,horizontalPixelRatio:s}){if(this.Ev===null||this.Li===null||!this.Li.W().borderVisible)return;t.fillStyle=this.Li.W().borderColor;const e=Math.max(1,Math.floor(this.ap().C*s));let h;h=this.qv?i.width-e:0,t.fillRect(h,0,e,i.height)}wp(t){if(this.Ev===null||this.Li===null)return;const i=this.Li.Ha(),s=this.Li.W(),e=this.ap(),h=this.qv?this.Ev.width-e.T:0;s.borderVisible&&s.ticksVisible&&t.useBitmapCoordinateSpace(({context:r,horizontalPixelRatio:o,verticalPixelRatio:l})=>{r.fillStyle=s.borderColor;const a=Math.max(1,Math.floor(l)),u=Math.floor(.5*l),c=Math.round(e.T*o);r.beginPath();for(const d of i)r.rect(Math.floor(h*o),Math.round(d.Ea*l)-u,c,a);r.fill()}),t.useMediaCoordinateSpace(({context:r})=>{var o;r.font=this._p(),r.fillStyle=(o=s.textColor)!==null&&o!==void 0?o:this.ko.textColor,r.textAlign=this.qv?"right":"left",r.textBaseline="middle";const l=this.qv?Math.round(h-e.A):Math.round(h+e.T+e.A),a=i.map(u=>this.Fv.Mi(r,u.so));for(let u=i.length;u--;){const c=i[u];r.fillText(c.so,l,c.Ea+a[u])}})}pp(){if(this.Ev===null||this.Li===null)return;const t=[],i=this.Li.Uo().slice(),s=this.tn.fp(),e=this.ap();this.Li===s.pr()&&this.tn.fp().Uo().forEach(r=>{s.vr(r)&&i.push(r)});const h=this.Li;i.forEach(r=>{r.Rn(s,h).forEach(o=>{o.Oi(null),o.Bi()&&t.push(o)})}),t.forEach(r=>r.Oi(r.ki())),this.Li.W().alignLabels&&this.yp(t,e)}yp(t,i){if(this.Ev===null)return;const s=this.Ev.height/2,e=t.filter(r=>r.ki()<=s),h=t.filter(r=>r.ki()>s);e.sort((r,o)=>o.ki()-r.ki()),h.sort((r,o)=>r.ki()-o.ki());for(const r of t){const o=Math.floor(r.At(i)/2),l=r.ki();l>-o&&l<o&&r.Oi(o),l>this.Ev.height-o&&l<this.Ev.height+o&&r.Oi(this.Ev.height-o)}Es(e,1,this.Ev.height,i),Es(h,-1,this.Ev.height,i)}gp(t){if(this.Ev===null)return;const i=this.up(),s=this.ap(),e=this.qv?"right":"left";i.forEach(h=>{h.Ai()&&h.gt(p(this.Li)).X(t,s,this.Fv,e)})}Mp(t){if(this.Ev===null||this.Li===null)return;const i=this.tn.$v().$t(),s=[],e=this.tn.fp(),h=i.Zc().Rn(e,this.Li);h.length&&s.push(h);const r=this.ap(),o=this.qv?"right":"left";s.forEach(l=>{l.forEach(a=>{a.gt(p(this.Li)).X(t,r,this.Fv,o)})})}kp(t){this.Kv.style.cursor=t===1?"ns-resize":"default"}fo(){const t=this.op();this.Wv<t&&this.tn.$v().$t().Kl(),this.Wv=t}_p(){return dt(this.ko.fontSize,this.ko.fontFamily)}}function fh(n,t){var i,s;return(s=(i=n.ua)===null||i===void 0?void 0:i.call(n,t))!==null&&s!==void 0?s:[]}function Ot(n,t){var i,s;return(s=(i=n.Pn)===null||i===void 0?void 0:i.call(n,t))!==null&&s!==void 0?s:[]}function mh(n,t){var i,s;return(s=(i=n.Ji)===null||i===void 0?void 0:i.call(n,t))!==null&&s!==void 0?s:[]}function vh(n,t){var i,s;return(s=(i=n.aa)===null||i===void 0?void 0:i.call(n,t))!==null&&s!==void 0?s:[]}class Wi{constructor(t,i){this.Ev=k({width:0,height:0}),this.Cp=null,this.Tp=null,this.Pp=null,this.Rp=null,this.Dp=!1,this.Vp=new V,this.Op=new V,this.Bp=0,this.Ap=!1,this.Ip=null,this.zp=!1,this.Lp=null,this.Ep=null,this.jv=!1,this.Hv=()=>{this.jv||this.Np===null||this.$i().Uh()},this.Uv=()=>{this.jv||this.Np===null||this.$i().Uh()},this.Qd=t,this.Np=i,this.Np.W_().l(this.Fp.bind(this),this,!0),this.Wp=document.createElement("td"),this.Wp.style.padding="0",this.Wp.style.position="relative";const s=document.createElement("div");s.style.width="100%",s.style.height="100%",s.style.position="relative",s.style.overflow="hidden",this.jp=document.createElement("td"),this.jp.style.padding="0",this.Hp=document.createElement("td"),this.Hp.style.padding="0",this.Wp.appendChild(s),this.Gv=et(s,k({width:16,height:16})),this.Gv.subscribeSuggestedBitmapSizeChanged(this.Hv);const e=this.Gv.canvasElement;e.style.position="absolute",e.style.zIndex="1",e.style.left="0",e.style.top="0",this.Jv=et(s,k({width:16,height:16})),this.Jv.subscribeSuggestedBitmapSizeChanged(this.Uv);const h=this.Jv.canvasElement;h.style.position="absolute",h.style.zIndex="2",h.style.left="0",h.style.top="0",this.$p=document.createElement("tr"),this.$p.appendChild(this.jp),this.$p.appendChild(this.Wp),this.$p.appendChild(this.Hp),this.Up(),this.hp=new Ni(this.Jv.canvasElement,this,{ev:()=>this.Ip===null&&!this.Qd.W().handleScroll.vertTouchDrag,rv:()=>this.Ip===null&&!this.Qd.W().handleScroll.horzTouchDrag})}S(){this.Cp!==null&&this.Cp.S(),this.Tp!==null&&this.Tp.S(),this.Pp=null,this.Jv.unsubscribeSuggestedBitmapSizeChanged(this.Uv),nt(this.Jv.canvasElement),this.Jv.dispose(),this.Gv.unsubscribeSuggestedBitmapSizeChanged(this.Hv),nt(this.Gv.canvasElement),this.Gv.dispose(),this.Np!==null&&this.Np.W_().p(this),this.hp.S()}fp(){return p(this.Np)}qp(t){var i,s;this.Np!==null&&this.Np.W_().p(this),this.Np=t,this.Np!==null&&this.Np.W_().l(Wi.prototype.Fp.bind(this),this,!0),this.Up(),this.Qd.Yp().indexOf(this)===this.Qd.Yp().length-1?(this.Pp=(i=this.Pp)!==null&&i!==void 0?i:new uh(this.Wp,this.Qd),this.Pp.bt()):((s=this.Pp)===null||s===void 0||s.if(),this.Pp=null)}$v(){return this.Qd}lp(){return this.$p}Up(){if(this.Np!==null&&(this.Zp(),this.$i().wt().length!==0)){if(this.Cp!==null){const t=this.Np.R_();this.Cp.Gi(p(t))}if(this.Tp!==null){const t=this.Np.D_();this.Tp.Gi(p(t))}}}Xp(){this.Cp!==null&&this.Cp.bt(),this.Tp!==null&&this.Tp.bt()}M_(){return this.Np!==null?this.Np.M_():0}x_(t){this.Np&&this.Np.x_(t)}Qf(t){if(!this.Np)return;this.Kp();const i=t.localX,s=t.localY;this.Gp(i,s,t)}bv(t){this.Kp(),this.Jp(),this.Gp(t.localX,t.localY,t)}tv(t){var i;if(!this.Np)return;this.Kp();const s=t.localX,e=t.localY;this.Gp(s,e,t);const h=this.wr(s,e);this.Qd.Qp((i=h?.Lv)!==null&&i!==void 0?i:null),this.$i().jc(h&&{Hc:h.Hc,Iv:h.Iv})}dv(t){this.Np!==null&&(this.Kp(),this.tm(t))}qf(t){this.Np!==null&&this.im(this.Op,t)}jf(t){this.qf(t)}av(t){this.Kp(),this.nm(t),this.Gp(t.localX,t.localY,t)}cv(t){this.Np!==null&&(this.Kp(),this.Ap=!1,this.sm(t))}uv(t){this.Np!==null&&this.tm(t)}Rv(t){if(this.Ap=!0,this.Ip===null){const i={x:t.localX,y:t.localY};this.rm(i,i,t)}}Pv(t){this.Np!==null&&(this.Kp(),this.Np.$t().jc(null),this.hm())}lm(){return this.Vp}am(){return this.Op}xv(){this.Bp=1,this.$i().Un()}Sv(t,i){if(!this.Qd.W().handleScale.pinch)return;const s=5*(i-this.Bp);this.Bp=i,this.$i().Qc(t.nt,s)}pv(t){this.Ap=!1,this.zp=this.Ip!==null,this.Jp();const i=this.$i().Zc();this.Ip!==null&&i.yt()&&(this.Lp={x:i.Yt(),y:i.Zt()},this.Ip={x:t.localX,y:t.localY})}hv(t){if(this.Np===null)return;const i=t.localX,s=t.localY;if(this.Ip===null)this.nm(t);else{this.zp=!1;const e=p(this.Lp),h=e.x+(i-this.Ip.x),r=e.y+(s-this.Ip.y);this.Gp(h,r,t)}}_v(t){this.$v().W().trackingMode.exitMode===0&&(this.zp=!0),this.om(),this.sm(t)}wr(t,i){const s=this.Np;return s===null?null:function(e,h,r){const o=e.Uo(),l=function(a,u,c){var d,f;let v,m;for(const w of a){const x=(f=(d=w.va)===null||d===void 0?void 0:d.call(w,u,c))!==null&&f!==void 0?f:[];for(const y of x)g=y.zOrder,(!(b=v?.zOrder)||g==="top"&&b!=="top"||g==="normal"&&b==="bottom")&&(v=y,m=w)}var g,b;return v&&m?{zv:v,Hc:m}:null}(o,h,r);if(l?.zv.zOrder==="top")return Pt(l);for(const a of o){if(l&&l.Hc===a&&l.zv.zOrder!=="bottom"&&!l.zv.isBackground)return Pt(l);const u=dh(a.Pn(e),h,r);if(u!==null)return{Hc:a,Bv:u.Bv,Iv:u.Iv};if(l&&l.Hc===a&&l.zv.zOrder!=="bottom"&&l.zv.isBackground)return Pt(l)}return l?.zv?Pt(l):null}(s,t,i)}_m(t,i){p(i==="left"?this.Cp:this.Tp).cp(k({width:t,height:this.Ev.height}))}um(){return this.Ev}cp(t){tt(this.Ev,t)||(this.Ev=t,this.jv=!0,this.Gv.resizeCanvasElement(t),this.Jv.resizeCanvasElement(t),this.jv=!1,this.Wp.style.width=t.width+"px",this.Wp.style.height=t.height+"px")}dm(){const t=p(this.Np);t.P_(t.R_()),t.P_(t.D_());for(const i of t.Ba())if(t.vr(i)){const s=i.Dt();s!==null&&t.P_(s),i.Vn()}}xp(){return this.Gv.bitmapSize}Sp(t,i,s){const e=this.xp();e.width>0&&e.height>0&&t.drawImage(this.Gv.canvasElement,i,s)}vp(t){if(t===0||this.Np===null)return;if(t>1&&this.dm(),this.Cp!==null&&this.Cp.vp(t),this.Tp!==null&&this.Tp.vp(t),t!==1){this.Gv.applySuggestedBitmapSize();const s=it(this.Gv);s!==null&&(s.useBitmapCoordinateSpace(e=>{this.mp(e)}),this.Np&&(this.fm(s,fh),this.vm(s),this.pm(s),this.fm(s,Ot),this.fm(s,mh)))}this.Jv.applySuggestedBitmapSize();const i=it(this.Jv);i!==null&&(i.useBitmapCoordinateSpace(({context:s,bitmapSize:e})=>{s.clearRect(0,0,e.width,e.height)}),this.bm(i),this.fm(i,vh))}wm(){return this.Cp}gm(){return this.Tp}bp(t,i){this.fm(t,i)}Fp(){this.Np!==null&&this.Np.W_().p(this),this.Np=null}tm(t){this.im(this.Vp,t)}im(t,i){const s=i.localX,e=i.localY;t.M()&&t.m(this.$i().St().Nu(s),{x:s,y:e},i)}mp({context:t,bitmapSize:i}){const{width:s,height:e}=i,h=this.$i(),r=h.q(),o=h.bd();r===o?Ht(t,0,0,s,e,o):As(t,0,0,s,e,r,o)}vm(t){const i=p(this.Np).j_().qh().gt();i!==null&&i.X(t,!1)}pm(t){const i=this.$i().Yc();this.Mm(t,Ot,Mi,i),this.Mm(t,Ot,Ft,i)}bm(t){this.Mm(t,Ot,Ft,this.$i().Zc())}fm(t,i){const s=p(this.Np).Uo();for(const e of s)this.Mm(t,i,Mi,e);for(const e of s)this.Mm(t,i,Ft,e)}Mm(t,i,s,e){const h=p(this.Np),r=h.$t().Wc(),o=r!==null&&r.Hc===e,l=r!==null&&o&&r.Iv!==void 0?r.Iv.Mr:void 0;_i(i,a=>s(a,t,o,l),e,h)}Zp(){if(this.Np===null)return;const t=this.Qd,i=this.Np.R_().W().visible,s=this.Np.D_().W().visible;i||this.Cp===null||(this.jp.removeChild(this.Cp.lp()),this.Cp.S(),this.Cp=null),s||this.Tp===null||(this.Hp.removeChild(this.Tp.lp()),this.Tp.S(),this.Tp=null);const e=t.$t().ud();i&&this.Cp===null&&(this.Cp=new Rs(this,t.W(),e,"left"),this.jp.appendChild(this.Cp.lp())),s&&this.Tp===null&&(this.Tp=new Rs(this,t.W(),e,"right"),this.Hp.appendChild(this.Tp.lp()))}xm(t){return t.Dv&&this.Ap||this.Ip!==null}Sm(t){return Math.max(0,Math.min(t,this.Ev.width-1))}km(t){return Math.max(0,Math.min(t,this.Ev.height-1))}Gp(t,i,s){this.$i().ld(this.Sm(t),this.km(i),s,p(this.Np))}hm(){this.$i().od()}om(){this.zp&&(this.Ip=null,this.hm())}rm(t,i,s){this.Ip=t,this.zp=!1,this.Gp(i.x,i.y,s);const e=this.$i().Zc();this.Lp={x:e.Yt(),y:e.Zt()}}$i(){return this.Qd.$t()}sm(t){if(!this.Dp)return;const i=this.$i(),s=this.fp();if(i.z_(s,s.vn()),this.Rp=null,this.Dp=!1,i.ed(),this.Ep!==null){const e=performance.now(),h=i.St();this.Ep.Vr(h.Hu(),e),this.Ep.Qu(e)||i.Zn(this.Ep)}}Kp(){this.Ip=null}Jp(){if(this.Np){if(this.$i().Un(),document.activeElement!==document.body&&document.activeElement!==document.documentElement)p(document.activeElement).blur();else{const t=document.getSelection();t!==null&&t.removeAllRanges()}!this.Np.vn().Ni()&&this.$i().St().Ni()}}nm(t){if(this.Np===null)return;const i=this.$i(),s=i.St();if(s.Ni())return;const e=this.Qd.W(),h=e.handleScroll,r=e.kineticScroll;if((!h.pressedMouseMove||t.Dv)&&(!h.horzTouchDrag&&!h.vertTouchDrag||!t.Dv))return;const o=this.Np.vn(),l=performance.now();if(this.Rp!==null||this.xm(t)||(this.Rp={x:t.clientX,y:t.clientY,Od:l,ym:t.localX,Cm:t.localY}),this.Rp!==null&&!this.Dp&&(this.Rp.x!==t.clientX||this.Rp.y!==t.clientY)){if(t.Dv&&r.touch||!t.Dv&&r.mouse){const a=s.le();this.Ep=new ah(.2/a,7/a,.997,15/a),this.Ep.Yd(s.Hu(),this.Rp.Od)}else this.Ep=null;o.Ni()||i.A_(this.Np,o,t.localY),i.nd(t.localX),this.Dp=!0}this.Dp&&(o.Ni()||i.I_(this.Np,o,t.localY),i.sd(t.localX),this.Ep!==null&&this.Ep.Yd(s.Hu(),l))}}class ks{constructor(t,i,s,e,h){this.ft=!0,this.Ev=k({width:0,height:0}),this.Hv=()=>this.vp(3),this.qv=t==="left",this.Oc=s.ud,this.cn=i,this.Tm=e,this.Pm=h,this.Kv=document.createElement("div"),this.Kv.style.width="25px",this.Kv.style.height="100%",this.Kv.style.overflow="hidden",this.Gv=et(this.Kv,k({width:16,height:16})),this.Gv.subscribeSuggestedBitmapSizeChanged(this.Hv)}S(){this.Gv.unsubscribeSuggestedBitmapSizeChanged(this.Hv),nt(this.Gv.canvasElement),this.Gv.dispose()}lp(){return this.Kv}um(){return this.Ev}cp(t){tt(this.Ev,t)||(this.Ev=t,this.Gv.resizeCanvasElement(t),this.Kv.style.width=`${t.width}px`,this.Kv.style.height=`${t.height}px`,this.ft=!0)}vp(t){if(t<3&&!this.ft||this.Ev.width===0||this.Ev.height===0)return;this.ft=!1,this.Gv.applySuggestedBitmapSize();const i=it(this.Gv);i!==null&&i.useBitmapCoordinateSpace(s=>{this.mp(s),this.Ie(s)})}xp(){return this.Gv.bitmapSize}Sp(t,i,s){const e=this.xp();e.width>0&&e.height>0&&t.drawImage(this.Gv.canvasElement,i,s)}Ie({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:e}){if(!this.Tm())return;t.fillStyle=this.cn.timeScale.borderColor;const h=Math.floor(this.Oc.W().C*s),r=Math.floor(this.Oc.W().C*e),o=this.qv?i.width-h:0;t.fillRect(o,0,h,r)}mp({context:t,bitmapSize:i}){Ht(t,0,0,i.width,i.height,this.Pm())}}function Di(n){return t=>{var i,s;return(s=(i=t.fa)===null||i===void 0?void 0:i.call(t,n))!==null&&s!==void 0?s:[]}}const ph=Di("normal"),gh=Di("top"),bh=Di("bottom");class wh{constructor(t,i){this.Rm=null,this.Dm=null,this.k=null,this.Vm=!1,this.Ev=k({width:0,height:0}),this.Om=new V,this.Fv=new Mt(5),this.jv=!1,this.Hv=()=>{this.jv||this.Qd.$t().Uh()},this.Uv=()=>{this.jv||this.Qd.$t().Uh()},this.Qd=t,this.q_=i,this.cn=t.W().layout,this.Xd=document.createElement("tr"),this.Bm=document.createElement("td"),this.Bm.style.padding="0",this.Am=document.createElement("td"),this.Am.style.padding="0",this.Kv=document.createElement("td"),this.Kv.style.height="25px",this.Kv.style.padding="0",this.Im=document.createElement("div"),this.Im.style.width="100%",this.Im.style.height="100%",this.Im.style.position="relative",this.Im.style.overflow="hidden",this.Kv.appendChild(this.Im),this.Gv=et(this.Im,k({width:16,height:16})),this.Gv.subscribeSuggestedBitmapSizeChanged(this.Hv);const s=this.Gv.canvasElement;s.style.position="absolute",s.style.zIndex="1",s.style.left="0",s.style.top="0",this.Jv=et(this.Im,k({width:16,height:16})),this.Jv.subscribeSuggestedBitmapSizeChanged(this.Uv);const e=this.Jv.canvasElement;e.style.position="absolute",e.style.zIndex="2",e.style.left="0",e.style.top="0",this.Xd.appendChild(this.Bm),this.Xd.appendChild(this.Kv),this.Xd.appendChild(this.Am),this.zm(),this.Qd.$t().g_().l(this.zm.bind(this),this),this.hp=new Ni(this.Jv.canvasElement,this,{ev:()=>!0,rv:()=>!this.Qd.W().handleScroll.horzTouchDrag})}S(){this.hp.S(),this.Rm!==null&&this.Rm.S(),this.Dm!==null&&this.Dm.S(),this.Jv.unsubscribeSuggestedBitmapSizeChanged(this.Uv),nt(this.Jv.canvasElement),this.Jv.dispose(),this.Gv.unsubscribeSuggestedBitmapSizeChanged(this.Hv),nt(this.Gv.canvasElement),this.Gv.dispose()}lp(){return this.Xd}Lm(){return this.Rm}Em(){return this.Dm}bv(t){if(this.Vm)return;this.Vm=!0;const i=this.Qd.$t();!i.St().Ni()&&this.Qd.W().handleScale.axisPressedMouseMove.time&&i.Jc(t.localX)}pv(t){this.bv(t)}wv(){const t=this.Qd.$t();!t.St().Ni()&&this.Vm&&(this.Vm=!1,this.Qd.W().handleScale.axisPressedMouseMove.time&&t.hd())}av(t){const i=this.Qd.$t();!i.St().Ni()&&this.Qd.W().handleScale.axisPressedMouseMove.time&&i.rd(t.localX)}hv(t){this.av(t)}cv(){this.Vm=!1;const t=this.Qd.$t();t.St().Ni()&&!this.Qd.W().handleScale.axisPressedMouseMove.time||t.hd()}_v(){this.cv()}qf(){this.Qd.W().handleScale.axisDoubleClickReset.time&&this.Qd.$t().Kn()}jf(){this.qf()}Qf(){this.Qd.$t().W().handleScale.axisPressedMouseMove.time&&this.kp(1)}Pv(){this.kp(0)}um(){return this.Ev}Nm(){return this.Om}Fm(t,i,s){tt(this.Ev,t)||(this.Ev=t,this.jv=!0,this.Gv.resizeCanvasElement(t),this.Jv.resizeCanvasElement(t),this.jv=!1,this.Kv.style.width=`${t.width}px`,this.Kv.style.height=`${t.height}px`,this.Om.m(t)),this.Rm!==null&&this.Rm.cp(k({width:i,height:t.height})),this.Dm!==null&&this.Dm.cp(k({width:s,height:t.height}))}Wm(){const t=this.jm();return Math.ceil(t.C+t.T+t.P+t.L+t.B+t.Hm)}bt(){this.Qd.$t().St().Ha()}xp(){return this.Gv.bitmapSize}Sp(t,i,s){const e=this.xp();e.width>0&&e.height>0&&t.drawImage(this.Gv.canvasElement,i,s)}vp(t){if(t===0)return;if(t!==1){this.Gv.applySuggestedBitmapSize();const s=it(this.Gv);s!==null&&(s.useBitmapCoordinateSpace(e=>{this.mp(e),this.Ie(e),this.$m(s,bh)}),this.wp(s),this.$m(s,ph)),this.Rm!==null&&this.Rm.vp(t),this.Dm!==null&&this.Dm.vp(t)}this.Jv.applySuggestedBitmapSize();const i=it(this.Jv);i!==null&&(i.useBitmapCoordinateSpace(({context:s,bitmapSize:e})=>{s.clearRect(0,0,e.width,e.height)}),this.Um([...this.Qd.$t().wt(),this.Qd.$t().Zc()],i),this.$m(i,gh))}$m(t,i){const s=this.Qd.$t().wt();for(const e of s)_i(i,h=>Mi(h,t,!1,void 0),e,void 0);for(const e of s)_i(i,h=>Ft(h,t,!1,void 0),e,void 0)}mp({context:t,bitmapSize:i}){Ht(t,0,0,i.width,i.height,this.Qd.$t().bd())}Ie({context:t,bitmapSize:i,verticalPixelRatio:s}){if(this.Qd.W().timeScale.borderVisible){t.fillStyle=this.qm();const e=Math.max(1,Math.floor(this.jm().C*s));t.fillRect(0,0,i.width,e)}}wp(t){const i=this.Qd.$t().St(),s=i.Ha();if(!s||s.length===0)return;const e=this.q_.maxTickMarkWeight(s),h=this.jm(),r=i.W();r.borderVisible&&r.ticksVisible&&t.useBitmapCoordinateSpace(({context:o,horizontalPixelRatio:l,verticalPixelRatio:a})=>{o.strokeStyle=this.qm(),o.fillStyle=this.qm();const u=Math.max(1,Math.floor(l)),c=Math.floor(.5*l);o.beginPath();const d=Math.round(h.T*a);for(let f=s.length;f--;){const v=Math.round(s[f].coord*l);o.rect(v-c,0,u,d)}o.fill()}),t.useMediaCoordinateSpace(({context:o})=>{const l=h.C+h.T+h.L+h.P/2;o.textAlign="center",o.textBaseline="middle",o.fillStyle=this.$(),o.font=this._p();for(const a of s)if(a.weight<e){const u=a.needAlignCoordinate?this.Ym(o,a.coord,a.label):a.coord;o.fillText(a.label,u,l)}this.Qd.W().timeScale.allowBoldLabels&&(o.font=this.Zm());for(const a of s)if(a.weight>=e){const u=a.needAlignCoordinate?this.Ym(o,a.coord,a.label):a.coord;o.fillText(a.label,u,l)}})}Ym(t,i,s){const e=this.Fv.xi(t,s),h=e/2,r=Math.floor(i-h)+.5;return r<0?i+=Math.abs(0-r):r+e>this.Ev.width&&(i-=Math.abs(this.Ev.width-(r+e))),i}Um(t,i){const s=this.jm();for(const e of t)for(const h of e.Qi())h.gt().X(i,s)}qm(){return this.Qd.W().timeScale.borderColor}$(){return this.cn.textColor}j(){return this.cn.fontSize}_p(){return dt(this.j(),this.cn.fontFamily)}Zm(){return dt(this.j(),this.cn.fontFamily,"bold")}jm(){this.k===null&&(this.k={C:1,N:NaN,L:NaN,B:NaN,ji:NaN,T:5,P:NaN,R:"",Wi:new Mt,Hm:0});const t=this.k,i=this._p();if(t.R!==i){const s=this.j();t.P=s,t.R=i,t.L=3*s/12,t.B=3*s/12,t.ji=9*s/12,t.N=0,t.Hm=4*s/12,t.Wi.nr()}return this.k}kp(t){this.Kv.style.cursor=t===1?"ew-resize":"default"}zm(){const t=this.Qd.$t(),i=t.W();i.leftPriceScale.visible||this.Rm===null||(this.Bm.removeChild(this.Rm.lp()),this.Rm.S(),this.Rm=null),i.rightPriceScale.visible||this.Dm===null||(this.Am.removeChild(this.Dm.lp()),this.Dm.S(),this.Dm=null);const s={ud:this.Qd.$t().ud()},e=()=>i.leftPriceScale.borderVisible&&t.St().W().borderVisible,h=()=>t.bd();i.leftPriceScale.visible&&this.Rm===null&&(this.Rm=new ks("left",i,s,e,h),this.Bm.appendChild(this.Rm.lp())),i.rightPriceScale.visible&&this.Dm===null&&(this.Dm=new ks("right",i,s,e,h),this.Am.appendChild(this.Dm.lp()))}}const yh=!!ft&&!!navigator.userAgentData&&navigator.userAgentData.brands.some(n=>n.brand.includes("Chromium"))&&!!ft&&(!((di=navigator?.userAgentData)===null||di===void 0)&&di.platform?navigator.userAgentData.platform==="Windows":navigator.userAgent.toLowerCase().indexOf("win")>=0);var di;class xh{constructor(t,i,s){var e;this.Xm=[],this.Km=0,this.ho=0,this.__=0,this.Gm=0,this.Jm=0,this.Qm=null,this.tb=!1,this.Vp=new V,this.Op=new V,this.Rc=new V,this.ib=null,this.nb=null,this.Jd=t,this.cn=i,this.q_=s,this.Xd=document.createElement("div"),this.Xd.classList.add("tv-lightweight-charts"),this.Xd.style.overflow="hidden",this.Xd.style.direction="ltr",this.Xd.style.width="100%",this.Xd.style.height="100%",(e=this.Xd).style.userSelect="none",e.style.webkitUserSelect="none",e.style.msUserSelect="none",e.style.MozUserSelect="none",e.style.webkitTapHighlightColor="transparent",this.sb=document.createElement("table"),this.sb.setAttribute("cellspacing","0"),this.Xd.appendChild(this.sb),this.eb=this.rb.bind(this),fi(this.cn)&&this.hb(!0),this.$i=new nh(this.Vc.bind(this),this.cn,s),this.$t().Xc().l(this.lb.bind(this),this),this.ab=new wh(this,this.q_),this.sb.appendChild(this.ab.lp());const h=i.autoSize&&this.ob();let r=this.cn.width,o=this.cn.height;if(h||r===0||o===0){const l=t.getBoundingClientRect();r=r||l.width,o=o||l.height}this._b(r,o),this.ub(),t.appendChild(this.Xd),this.cb(),this.$i.St().ec().l(this.$i.Kl.bind(this.$i),this),this.$i.g_().l(this.$i.Kl.bind(this.$i),this)}$t(){return this.$i}W(){return this.cn}Yp(){return this.Xm}fb(){return this.ab}S(){this.hb(!1),this.Km!==0&&window.cancelAnimationFrame(this.Km),this.$i.Xc().p(this),this.$i.St().ec().p(this),this.$i.g_().p(this),this.$i.S();for(const t of this.Xm)this.sb.removeChild(t.lp()),t.lm().p(this),t.am().p(this),t.S();this.Xm=[],p(this.ab).S(),this.Xd.parentElement!==null&&this.Xd.parentElement.removeChild(this.Xd),this.Rc.S(),this.Vp.S(),this.Op.S(),this.pb()}_b(t,i,s=!1){if(this.ho===i&&this.__===t)return;const e=function(o){const l=Math.floor(o.width),a=Math.floor(o.height);return k({width:l-l%2,height:a-a%2})}(k({width:t,height:i}));this.ho=e.height,this.__=e.width;const h=this.ho+"px",r=this.__+"px";p(this.Xd).style.height=h,p(this.Xd).style.width=r,this.sb.style.height=h,this.sb.style.width=r,s?this.mb(W.es(),performance.now()):this.$i.Kl()}vp(t){t===void 0&&(t=W.es());for(let i=0;i<this.Xm.length;i++)this.Xm[i].vp(t.Hn(i).Fn);this.cn.timeScale.visible&&this.ab.vp(t.jn())}$h(t){const i=fi(this.cn);this.$i.$h(t);const s=fi(this.cn);s!==i&&this.hb(s),this.cb(),this.bb(t)}lm(){return this.Vp}am(){return this.Op}Xc(){return this.Rc}wb(){this.Qm!==null&&(this.mb(this.Qm,performance.now()),this.Qm=null);const t=this.gb(null),i=document.createElement("canvas");i.width=t.width,i.height=t.height;const s=p(i.getContext("2d"));return this.gb(s),i}Mb(t){return t==="left"&&!this.xb()||t==="right"&&!this.Sb()||this.Xm.length===0?0:p(t==="left"?this.Xm[0].wm():this.Xm[0].gm()).dp()}kb(){return this.cn.autoSize&&this.ib!==null}yb(){return this.Xd}Qp(t){this.nb=t,this.nb?this.yb().style.setProperty("cursor",t):this.yb().style.removeProperty("cursor")}Cb(){return this.nb}Tb(){return D(this.Xm[0]).um()}bb(t){(t.autoSize!==void 0||!this.ib||t.width===void 0&&t.height===void 0)&&(t.autoSize&&!this.ib&&this.ob(),t.autoSize===!1&&this.ib!==null&&this.pb(),t.autoSize||t.width===void 0&&t.height===void 0||this._b(t.width||this.__,t.height||this.ho))}gb(t){let i=0,s=0;const e=this.Xm[0],h=(o,l)=>{let a=0;for(let u=0;u<this.Xm.length;u++){const c=this.Xm[u],d=p(o==="left"?c.wm():c.gm()),f=d.xp();t!==null&&d.Sp(t,l,a),a+=f.height}};this.xb()&&(h("left",0),i+=p(e.wm()).xp().width);for(let o=0;o<this.Xm.length;o++){const l=this.Xm[o],a=l.xp();t!==null&&l.Sp(t,i,s),s+=a.height}i+=e.xp().width,this.Sb()&&(h("right",i),i+=p(e.gm()).xp().width);const r=(o,l,a)=>{p(o==="left"?this.ab.Lm():this.ab.Em()).Sp(p(t),l,a)};if(this.cn.timeScale.visible){const o=this.ab.xp();if(t!==null){let l=0;this.xb()&&(r("left",l,s),l=p(e.wm()).xp().width),this.ab.Sp(t,l,s),l+=o.width,this.Sb()&&r("right",l,s)}s+=o.height}return k({width:i,height:s})}Pb(){let t=0,i=0,s=0;for(const v of this.Xm)this.xb()&&(i=Math.max(i,p(v.wm()).op(),this.cn.leftPriceScale.minimumWidth)),this.Sb()&&(s=Math.max(s,p(v.gm()).op(),this.cn.rightPriceScale.minimumWidth)),t+=v.M_();i=Si(i),s=Si(s);const e=this.__,h=this.ho,r=Math.max(e-i-s,0),o=this.cn.timeScale.visible;let l=o?Math.max(this.ab.Wm(),this.cn.timeScale.minimumHeight):0;var a;l=(a=l)+a%2;const u=0+l,c=h<u?0:h-u,d=c/t;let f=0;for(let v=0;v<this.Xm.length;++v){const m=this.Xm[v];m.qp(this.$i.qc()[v]);let g=0,b=0;b=v===this.Xm.length-1?c-f:Math.round(m.M_()*d),g=Math.max(b,2),f+=g,m.cp(k({width:r,height:g})),this.xb()&&m._m(i,"left"),this.Sb()&&m._m(s,"right"),m.fp()&&this.$i.Kc(m.fp(),g)}this.ab.Fm(k({width:o?r:0,height:l}),o?i:0,o?s:0),this.$i.S_(r),this.Gm!==i&&(this.Gm=i),this.Jm!==s&&(this.Jm=s)}hb(t){t?this.Xd.addEventListener("wheel",this.eb,{passive:!1}):this.Xd.removeEventListener("wheel",this.eb)}Rb(t){switch(t.deltaMode){case t.DOM_DELTA_PAGE:return 120;case t.DOM_DELTA_LINE:return 32}return yh?1/window.devicePixelRatio:1}rb(t){if(!(t.deltaX!==0&&this.cn.handleScroll.mouseWheel||t.deltaY!==0&&this.cn.handleScale.mouseWheel))return;const i=this.Rb(t),s=i*t.deltaX/100,e=-i*t.deltaY/100;if(t.cancelable&&t.preventDefault(),e!==0&&this.cn.handleScale.mouseWheel){const h=Math.sign(e)*Math.min(1,Math.abs(e)),r=t.clientX-this.Xd.getBoundingClientRect().left;this.$t().Qc(r,h)}s!==0&&this.cn.handleScroll.mouseWheel&&this.$t().td(-80*s)}mb(t,i){var s;const e=t.jn();e===3&&this.Db(),e!==3&&e!==2||(this.Vb(t),this.Ob(t,i),this.ab.bt(),this.Xm.forEach(h=>{h.Xp()}),((s=this.Qm)===null||s===void 0?void 0:s.jn())===3&&(this.Qm.ts(t),this.Db(),this.Vb(this.Qm),this.Ob(this.Qm,i),t=this.Qm,this.Qm=null)),this.vp(t)}Ob(t,i){for(const s of t.Qn())this.ns(s,i)}Vb(t){const i=this.$i.qc();for(let s=0;s<i.length;s++)t.Hn(s).Wn&&i[s].N_()}ns(t,i){const s=this.$i.St();switch(t.qn){case 0:s.hc();break;case 1:s.lc(t.Vt);break;case 2:s.Gn(t.Vt);break;case 3:s.Jn(t.Vt);break;case 4:s.qu();break;case 5:t.Vt.Qu(i)||s.Jn(t.Vt.tc(i))}}Vc(t){this.Qm!==null?this.Qm.ts(t):this.Qm=t,this.tb||(this.tb=!0,this.Km=window.requestAnimationFrame(i=>{if(this.tb=!1,this.Km=0,this.Qm!==null){const s=this.Qm;this.Qm=null,this.mb(s,i);for(const e of s.Qn())if(e.qn===5&&!e.Vt.Qu(i)){this.$t().Zn(e.Vt);break}}}))}Db(){this.ub()}ub(){const t=this.$i.qc(),i=t.length,s=this.Xm.length;for(let e=i;e<s;e++){const h=D(this.Xm.pop());this.sb.removeChild(h.lp()),h.lm().p(this),h.am().p(this),h.S()}for(let e=s;e<i;e++){const h=new Wi(this,t[e]);h.lm().l(this.Bb.bind(this),this),h.am().l(this.Ab.bind(this),this),this.Xm.push(h),this.sb.insertBefore(h.lp(),this.ab.lp())}for(let e=0;e<i;e++){const h=t[e],r=this.Xm[e];r.fp()!==h?r.qp(h):r.Up()}this.cb(),this.Pb()}Ib(t,i,s){var e;const h=new Map;t!==null&&this.$i.wt().forEach(u=>{const c=u.In().ll(t);c!==null&&h.set(u,c)});let r;if(t!==null){const u=(e=this.$i.St().Ui(t))===null||e===void 0?void 0:e.originalTime;u!==void 0&&(r=u)}const o=this.$t().Wc(),l=o!==null&&o.Hc instanceof Vi?o.Hc:void 0,a=o!==null&&o.Iv!==void 0?o.Iv.gr:void 0;return{zb:r,ee:t??void 0,Lb:i??void 0,Eb:l,Nb:h,Fb:a,Wb:s??void 0}}Bb(t,i,s){this.Vp.m(()=>this.Ib(t,i,s))}Ab(t,i,s){this.Op.m(()=>this.Ib(t,i,s))}lb(t,i,s){this.Rc.m(()=>this.Ib(t,i,s))}cb(){const t=this.cn.timeScale.visible?"":"none";this.ab.lp().style.display=t}xb(){return this.Xm[0].fp().R_().W().visible}Sb(){return this.Xm[0].fp().D_().W().visible}ob(){return"ResizeObserver"in window&&(this.ib=new ResizeObserver(t=>{const i=t.find(s=>s.target===this.Jd);i&&this._b(i.contentRect.width,i.contentRect.height)}),this.ib.observe(this.Jd,{box:"border-box"}),!0)}pb(){this.ib!==null&&this.ib.disconnect(),this.ib=null}}function fi(n){return!!(n.handleScroll.mouseWheel||n.handleScale.mouseWheel)}function Sh(n){return function(t){return t.open!==void 0}(n)||function(t){return t.value!==void 0}(n)}function de(n,t){var i={};for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&t.indexOf(s)<0&&(i[s]=n[s]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function"){var e=0;for(s=Object.getOwnPropertySymbols(n);e<s.length;e++)t.indexOf(s[e])<0&&Object.prototype.propertyIsEnumerable.call(n,s[e])&&(i[s[e]]=n[s[e]])}return i}function Ls(n,t,i,s){const e=i.value,h={ee:t,ot:n,Vt:[e,e,e,e],zb:s};return i.color!==void 0&&(h.V=i.color),h}function Mh(n,t,i,s){const e=i.value,h={ee:t,ot:n,Vt:[e,e,e,e],zb:s};return i.lineColor!==void 0&&(h.lt=i.lineColor),i.topColor!==void 0&&(h.Ps=i.topColor),i.bottomColor!==void 0&&(h.Rs=i.bottomColor),h}function _h(n,t,i,s){const e=i.value,h={ee:t,ot:n,Vt:[e,e,e,e],zb:s};return i.topLineColor!==void 0&&(h.Re=i.topLineColor),i.bottomLineColor!==void 0&&(h.De=i.bottomLineColor),i.topFillColor1!==void 0&&(h.ke=i.topFillColor1),i.topFillColor2!==void 0&&(h.ye=i.topFillColor2),i.bottomFillColor1!==void 0&&(h.Ce=i.bottomFillColor1),i.bottomFillColor2!==void 0&&(h.Te=i.bottomFillColor2),h}function zh(n,t,i,s){const e={ee:t,ot:n,Vt:[i.open,i.high,i.low,i.close],zb:s};return i.color!==void 0&&(e.V=i.color),e}function Ch(n,t,i,s){const e={ee:t,ot:n,Vt:[i.open,i.high,i.low,i.close],zb:s};return i.color!==void 0&&(e.V=i.color),i.borderColor!==void 0&&(e.Ot=i.borderColor),i.wickColor!==void 0&&(e.Xh=i.wickColor),e}function Eh(n,t,i,s,e){const h=D(e)(i),r=Math.max(...h),o=Math.min(...h),l=h[h.length-1],a=[l,r,o,l],u=i,{time:c,color:d}=u;return{ee:t,ot:n,Vt:a,zb:s,$e:de(u,["time","color"]),V:d}}function Bt(n){return n.Vt!==void 0}function $s(n,t){return t.customValues!==void 0&&(n.jb=t.customValues),n}function Z(n){return(t,i,s,e,h,r)=>function(o,l){return l?l(o):(a=o).open===void 0&&a.value===void 0;var a}(s,r)?$s({ot:t,ee:i,zb:e},s):$s(n(t,i,s,e,h),s)}function Vs(n){return{Candlestick:Z(Ch),Bar:Z(zh),Area:Z(Mh),Baseline:Z(_h),Histogram:Z(Ls),Line:Z(Ls),Custom:Z(Eh)}[n]}function Ts(n){return{ee:0,Hb:new Map,la:n}}function Ns(n,t){if(n!==void 0&&n.length!==0)return{$b:t.key(n[0].ot),Ub:t.key(n[n.length-1].ot)}}function Ws(n){let t;return n.forEach(i=>{t===void 0&&(t=i.zb)}),D(t)}class Rh{constructor(t){this.qb=new Map,this.Yb=new Map,this.Zb=new Map,this.Xb=[],this.q_=t}S(){this.qb.clear(),this.Yb.clear(),this.Zb.clear(),this.Xb=[]}Kb(t,i){let s=this.qb.size!==0,e=!1;const h=this.Yb.get(t);if(h!==void 0)if(this.Yb.size===1)s=!1,e=!0,this.qb.clear();else for(const l of this.Xb)l.pointData.Hb.delete(t)&&(e=!0);let r=[];if(i.length!==0){const l=i.map(f=>f.time),a=this.q_.createConverterToInternalObj(i),u=Vs(t.Qh()),c=t.Ca(),d=t.Ta();r=i.map((f,v)=>{const m=a(f.time),g=this.q_.key(m);let b=this.qb.get(g);b===void 0&&(b=Ts(m),this.qb.set(g,b),e=!0);const w=u(m,b.ee,f,l[v],c,d);return b.Hb.set(t,w),w})}s&&this.Gb(),this.Jb(t,r);let o=-1;if(e){const l=[];this.qb.forEach(a=>{l.push({timeWeight:0,time:a.la,pointData:a,originalTime:Ws(a.Hb)})}),l.sort((a,u)=>this.q_.key(a.time)-this.q_.key(u.time)),o=this.Qb(l)}return this.tw(t,o,function(l,a,u){const c=Ns(l,u),d=Ns(a,u);if(c!==void 0&&d!==void 0)return{ta:c.Ub>=d.Ub&&c.$b>=d.$b}}(this.Yb.get(t),h,this.q_))}vd(t){return this.Kb(t,[])}iw(t,i){const s=i;(function(m){m.zb===void 0&&(m.zb=m.time)})(s),this.q_.preprocessData(i);const e=this.q_.createConverterToInternalObj([i])(i.time),h=this.Zb.get(t);if(h!==void 0&&this.q_.key(e)<this.q_.key(h))throw new Error(`Cannot update oldest data, last time=${h}, new time=${e}`);let r=this.qb.get(this.q_.key(e));const o=r===void 0;r===void 0&&(r=Ts(e),this.qb.set(this.q_.key(e),r));const l=Vs(t.Qh()),a=t.Ca(),u=t.Ta(),c=l(e,r.ee,i,s.zb,a,u);r.Hb.set(t,c),this.nw(t,c);const d={ta:Bt(c)};if(!o)return this.tw(t,-1,d);const f={timeWeight:0,time:r.la,pointData:r,originalTime:Ws(r.Hb)},v=Et(this.Xb,this.q_.key(f.time),(m,g)=>this.q_.key(m.time)<g);this.Xb.splice(v,0,f);for(let m=v;m<this.Xb.length;++m)mi(this.Xb[m].pointData,m);return this.q_.fillWeightsForPoints(this.Xb,v),this.tw(t,v,d)}nw(t,i){let s=this.Yb.get(t);s===void 0&&(s=[],this.Yb.set(t,s));const e=s.length!==0?s[s.length-1]:null;e===null||this.q_.key(i.ot)>this.q_.key(e.ot)?Bt(i)&&s.push(i):Bt(i)?s[s.length-1]=i:s.splice(-1,1),this.Zb.set(t,i.ot)}Jb(t,i){i.length!==0?(this.Yb.set(t,i.filter(Bt)),this.Zb.set(t,i[i.length-1].ot)):(this.Yb.delete(t),this.Zb.delete(t))}Gb(){for(const t of this.Xb)t.pointData.Hb.size===0&&this.qb.delete(this.q_.key(t.time))}Qb(t){let i=-1;for(let s=0;s<this.Xb.length&&s<t.length;++s){const e=this.Xb[s],h=t[s];if(this.q_.key(e.time)!==this.q_.key(h.time)){i=s;break}h.timeWeight=e.timeWeight,mi(h.pointData,s)}if(i===-1&&this.Xb.length!==t.length&&(i=Math.min(this.Xb.length,t.length)),i===-1)return-1;for(let s=i;s<t.length;++s)mi(t[s].pointData,s);return this.q_.fillWeightsForPoints(t,i),this.Xb=t,i}sw(){if(this.Yb.size===0)return null;let t=0;return this.Yb.forEach(i=>{i.length!==0&&(t=Math.max(t,i[i.length-1].ee))}),t}tw(t,i,s){const e={ew:new Map,St:{Eu:this.sw()}};if(i!==-1)this.Yb.forEach((h,r)=>{e.ew.set(r,{$e:h,rw:r===t?s:void 0})}),this.Yb.has(t)||e.ew.set(t,{$e:[],rw:s}),e.St.hw=this.Xb,e.St.lw=i;else{const h=this.Yb.get(t);e.ew.set(t,{$e:h||[],rw:s})}return e}}function mi(n,t){n.ee=t,n.Hb.forEach(i=>{i.ee=t})}function Pi(n){const t={value:n.Vt[3],time:n.zb};return n.jb!==void 0&&(t.customValues=n.jb),t}function Ds(n){const t=Pi(n);return n.V!==void 0&&(t.color=n.V),t}function kh(n){const t=Pi(n);return n.lt!==void 0&&(t.lineColor=n.lt),n.Ps!==void 0&&(t.topColor=n.Ps),n.Rs!==void 0&&(t.bottomColor=n.Rs),t}function Lh(n){const t=Pi(n);return n.Re!==void 0&&(t.topLineColor=n.Re),n.De!==void 0&&(t.bottomLineColor=n.De),n.ke!==void 0&&(t.topFillColor1=n.ke),n.ye!==void 0&&(t.topFillColor2=n.ye),n.Ce!==void 0&&(t.bottomFillColor1=n.Ce),n.Te!==void 0&&(t.bottomFillColor2=n.Te),t}function fe(n){const t={open:n.Vt[0],high:n.Vt[1],low:n.Vt[2],close:n.Vt[3],time:n.zb};return n.jb!==void 0&&(t.customValues=n.jb),t}function $h(n){const t=fe(n);return n.V!==void 0&&(t.color=n.V),t}function Vh(n){const t=fe(n),{V:i,Ot:s,Xh:e}=n;return i!==void 0&&(t.color=i),s!==void 0&&(t.borderColor=s),e!==void 0&&(t.wickColor=e),t}function zi(n){return{Area:kh,Line:Ds,Baseline:Lh,Histogram:Ds,Bar:$h,Candlestick:Vh,Custom:Th}[n]}function Th(n){const t=n.zb;return Object.assign(Object.assign({},n.$e),{time:t})}const Nh={vertLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},horzLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},mode:1},Wh={vertLines:{color:"#D6DCDE",style:0,visible:!0},horzLines:{color:"#D6DCDE",style:0,visible:!0}},Dh={background:{type:"solid",color:"#FFFFFF"},textColor:"#191919",fontSize:12,fontFamily:Ci,attributionLogo:!0},vi={autoScale:!0,mode:0,invertScale:!1,alignLabels:!0,borderVisible:!0,borderColor:"#2B2B43",entireTextOnly:!1,visible:!1,ticksVisible:!1,scaleMargins:{bottom:.1,top:.2},minimumWidth:0},Ph={rightOffset:0,barSpacing:6,minBarSpacing:.5,fixLeftEdge:!1,fixRightEdge:!1,lockVisibleTimeRangeOnResize:!1,rightBarStaysOnScroll:!1,borderVisible:!0,borderColor:"#2B2B43",visible:!0,timeVisible:!1,secondsVisible:!0,shiftVisibleRangeOnNewBar:!0,allowShiftVisibleRangeOnWhitespaceReplacement:!1,ticksVisible:!1,uniformDistribution:!1,minimumHeight:0,allowBoldLabels:!0},Oh={color:"rgba(0, 0, 0, 0)",visible:!1,fontSize:48,fontFamily:Ci,fontStyle:"",text:"",horzAlign:"center",vertAlign:"center"};function Ps(){return{width:0,height:0,autoSize:!1,layout:Dh,crosshair:Nh,grid:Wh,overlayPriceScales:Object.assign({},vi),leftPriceScale:Object.assign(Object.assign({},vi),{visible:!1}),rightPriceScale:Object.assign(Object.assign({},vi),{visible:!0}),timeScale:Ph,watermark:Oh,localization:{locale:ft?navigator.language:"",dateFormat:"dd MMM 'yy"},handleScroll:{mouseWheel:!0,pressedMouseMove:!0,horzTouchDrag:!0,vertTouchDrag:!0},handleScale:{axisPressedMouseMove:{time:!0,price:!0},axisDoubleClickReset:{time:!0,price:!0},mouseWheel:!0,pinch:!0},kineticScroll:{mouse:!1,touch:!0},trackingMode:{exitMode:1}}}class Bh{constructor(t,i){this.aw=t,this.ow=i}applyOptions(t){this.aw.$t().$c(this.ow,t)}options(){return this.Li().W()}width(){return At(this.ow)?this.aw.Mb(this.ow):0}Li(){return p(this.aw.$t().Uc(this.ow)).Dt}}function Os(n,t,i){const s=de(n,["time","originalTime"]),e=Object.assign({time:t},s);return i!==void 0&&(e.originalTime=i),e}const Ih={color:"#FF0000",price:0,lineStyle:2,lineWidth:1,lineVisible:!0,axisLabelVisible:!0,title:"",axisLabelColor:"",axisLabelTextColor:""};class Fh{constructor(t){this.Nh=t}applyOptions(t){this.Nh.$h(t)}options(){return this.Nh.W()}_w(){return this.Nh}}class jh{constructor(t,i,s,e,h){this.uw=new V,this.Es=t,this.cw=i,this.dw=s,this.q_=h,this.fw=e}S(){this.uw.S()}priceFormatter(){return this.Es.ba()}priceToCoordinate(t){const i=this.Es.Ct();return i===null?null:this.Es.Dt().Rt(t,i.Vt)}coordinateToPrice(t){const i=this.Es.Ct();return i===null?null:this.Es.Dt().pn(t,i.Vt)}barsInLogicalRange(t){if(t===null)return null;const i=new ct(new wt(t.from,t.to)).lu(),s=this.Es.In();if(s.Ni())return null;const e=s.ll(i.Os(),1),h=s.ll(i.ui(),-1),r=p(s.el()),o=p(s.An());if(e!==null&&h!==null&&e.ee>h.ee)return{barsBefore:t.from-r,barsAfter:o-t.to};const l={barsBefore:e===null||e.ee===r?t.from-r:e.ee-r,barsAfter:h===null||h.ee===o?o-t.to:o-h.ee};return e!==null&&h!==null&&(l.from=e.zb,l.to=h.zb),l}setData(t){this.q_,this.Es.Qh(),this.cw.pw(this.Es,t),this.mw("full")}update(t){this.Es.Qh(),this.cw.bw(this.Es,t),this.mw("update")}dataByIndex(t,i){const s=this.Es.In().ll(t,i);return s===null?null:zi(this.seriesType())(s)}data(){const t=zi(this.seriesType());return this.Es.In().ne().map(i=>t(i))}subscribeDataChanged(t){this.uw.l(t)}unsubscribeDataChanged(t){this.uw.v(t)}setMarkers(t){this.q_;const i=t.map(s=>Os(s,this.q_.convertHorzItemToInternal(s.time),s.time));this.Es.na(i)}markers(){return this.Es.sa().map(t=>Os(t,t.originalTime,void 0))}applyOptions(t){this.Es.$h(t)}options(){return X(this.Es.W())}priceScale(){return this.dw.priceScale(this.Es.Dt().Pa())}createPriceLine(t){const i=F(X(Ih),t),s=this.Es.ea(i);return new Fh(s)}removePriceLine(t){this.Es.ra(t._w())}seriesType(){return this.Es.Qh()}attachPrimitive(t){this.Es.ka(t),t.attached&&t.attached({chart:this.fw,series:this,requestUpdate:()=>this.Es.$t().Kl()})}detachPrimitive(t){this.Es.ya(t),t.detached&&t.detached()}mw(t){this.uw.M()&&this.uw.m(t)}}class qh{constructor(t,i,s){this.ww=new V,this.mu=new V,this.Om=new V,this.$i=t,this.yl=t.St(),this.ab=i,this.yl.nc().l(this.gw.bind(this)),this.yl.sc().l(this.Mw.bind(this)),this.ab.Nm().l(this.xw.bind(this)),this.q_=s}S(){this.yl.nc().p(this),this.yl.sc().p(this),this.ab.Nm().p(this),this.ww.S(),this.mu.S(),this.Om.S()}scrollPosition(){return this.yl.Hu()}scrollToPosition(t,i){i?this.yl.Ju(t,1e3):this.$i.Jn(t)}scrollToRealTime(){this.yl.Gu()}getVisibleRange(){const t=this.yl.Vu();return t===null?null:{from:t.from.originalTime,to:t.to.originalTime}}setVisibleRange(t){const i={from:this.q_.convertHorzItemToInternal(t.from),to:this.q_.convertHorzItemToInternal(t.to)},s=this.yl.Iu(i);this.$i.pd(s)}getVisibleLogicalRange(){const t=this.yl.Du();return t===null?null:{from:t.Os(),to:t.ui()}}setVisibleLogicalRange(t){Y(t.from<=t.to,"The from index cannot be after the to index."),this.$i.pd(t)}resetTimeScale(){this.$i.Kn()}fitContent(){this.$i.hc()}logicalToCoordinate(t){const i=this.$i.St();return i.Ni()?null:i.It(t)}coordinateToLogical(t){return this.yl.Ni()?null:this.yl.Nu(t)}timeToCoordinate(t){const i=this.q_.convertHorzItemToInternal(t),s=this.yl.Va(i,!1);return s===null?null:this.yl.It(s)}coordinateToTime(t){const i=this.$i.St(),s=i.Nu(t),e=i.Ui(s);return e===null?null:e.originalTime}width(){return this.ab.um().width}height(){return this.ab.um().height}subscribeVisibleTimeRangeChange(t){this.ww.l(t)}unsubscribeVisibleTimeRangeChange(t){this.ww.v(t)}subscribeVisibleLogicalRangeChange(t){this.mu.l(t)}unsubscribeVisibleLogicalRangeChange(t){this.mu.v(t)}subscribeSizeChange(t){this.Om.l(t)}unsubscribeSizeChange(t){this.Om.v(t)}applyOptions(t){this.yl.$h(t)}options(){return Object.assign(Object.assign({},X(this.yl.W())),{barSpacing:this.yl.le()})}gw(){this.ww.M()&&this.ww.m(this.getVisibleRange())}Mw(){this.mu.M()&&this.mu.m(this.getVisibleLogicalRange())}xw(t){this.Om.m(t.width,t.height)}}function Kh(n){if(n===void 0||n.type==="custom")return;const t=n;t.minMove!==void 0&&t.precision===void 0&&(t.precision=function(i){if(i>=1)return 0;let s=0;for(;s<8;s++){const e=Math.round(i);if(Math.abs(e-i)<1e-8)return s;i*=10}return s}(t.minMove))}function Bs(n){return function(t){if(Lt(t.handleScale)){const s=t.handleScale;t.handleScale={axisDoubleClickReset:{time:s,price:s},axisPressedMouseMove:{time:s,price:s},mouseWheel:s,pinch:s}}else if(t.handleScale!==void 0){const{axisPressedMouseMove:s,axisDoubleClickReset:e}=t.handleScale;Lt(s)&&(t.handleScale.axisPressedMouseMove={time:s,price:s}),Lt(e)&&(t.handleScale.axisDoubleClickReset={time:e,price:e})}const i=t.handleScroll;Lt(i)&&(t.handleScroll={horzTouchDrag:i,vertTouchDrag:i,mouseWheel:i,pressedMouseMove:i})}(n),n}class Hh{constructor(t,i,s){this.Sw=new Map,this.kw=new Map,this.yw=new V,this.Cw=new V,this.Tw=new V,this.Pw=new Rh(i);const e=s===void 0?X(Ps()):F(X(Ps()),Bs(s));this.q_=i,this.aw=new xh(t,e,i),this.aw.lm().l(r=>{this.yw.M()&&this.yw.m(this.Rw(r()))},this),this.aw.am().l(r=>{this.Cw.M()&&this.Cw.m(this.Rw(r()))},this),this.aw.Xc().l(r=>{this.Tw.M()&&this.Tw.m(this.Rw(r()))},this);const h=this.aw.$t();this.Dw=new qh(h,this.aw.fb(),this.q_)}remove(){this.aw.lm().p(this),this.aw.am().p(this),this.aw.Xc().p(this),this.Dw.S(),this.aw.S(),this.Sw.clear(),this.kw.clear(),this.yw.S(),this.Cw.S(),this.Tw.S(),this.Pw.S()}resize(t,i,s){this.autoSizeActive()||this.aw._b(t,i,s)}addCustomSeries(t,i){const s=at(t),e=Object.assign(Object.assign({},js),s.defaultOptions());return this.Vw("Custom",e,i,s)}addAreaSeries(t){return this.Vw("Area",Ee,t)}addBaselineSeries(t){return this.Vw("Baseline",Re,t)}addBarSeries(t){return this.Vw("Bar",ze,t)}addCandlestickSeries(t={}){return function(i){i.borderColor!==void 0&&(i.borderUpColor=i.borderColor,i.borderDownColor=i.borderColor),i.wickColor!==void 0&&(i.wickUpColor=i.wickColor,i.wickDownColor=i.wickColor)}(t),this.Vw("Candlestick",_e,t)}addHistogramSeries(t){return this.Vw("Histogram",ke,t)}addLineSeries(t){return this.Vw("Line",Ce,t)}removeSeries(t){const i=D(this.Sw.get(t)),s=this.Pw.vd(i);this.aw.$t().vd(i),this.Ow(s),this.Sw.delete(t),this.kw.delete(i)}pw(t,i){this.Ow(this.Pw.Kb(t,i))}bw(t,i){this.Ow(this.Pw.iw(t,i))}subscribeClick(t){this.yw.l(t)}unsubscribeClick(t){this.yw.v(t)}subscribeCrosshairMove(t){this.Tw.l(t)}unsubscribeCrosshairMove(t){this.Tw.v(t)}subscribeDblClick(t){this.Cw.l(t)}unsubscribeDblClick(t){this.Cw.v(t)}priceScale(t){return new Bh(this.aw,t)}timeScale(){return this.Dw}applyOptions(t){this.aw.$h(Bs(t))}options(){return this.aw.W()}takeScreenshot(){return this.aw.wb()}autoSizeActive(){return this.aw.kb()}chartElement(){return this.aw.yb()}paneSize(){const t=this.aw.Tb();return{height:t.height,width:t.width}}setCrosshairPosition(t,i,s){const e=this.Sw.get(s);if(e===void 0)return;const h=this.aw.$t().dr(e);h!==null&&this.aw.$t().ad(t,i,h)}clearCrosshairPosition(){this.aw.$t().od(!0)}Vw(t,i,s={},e){Kh(s.priceFormat);const h=F(X(qs),X(i),s),r=this.aw.$t().dd(t,h,e),o=new jh(r,this,this,this,this.q_);return this.Sw.set(o,r),this.kw.set(r,o),o}Ow(t){const i=this.aw.$t();i._d(t.St.Eu,t.St.hw,t.St.lw),t.ew.forEach((s,e)=>e.J(s.$e,s.rw)),i.Wu()}Bw(t){return D(this.kw.get(t))}Rw(t){const i=new Map;t.Nb.forEach((e,h)=>{const r=h.Qh(),o=zi(r)(e);if(r!=="Custom")Y(Sh(o));else{const l=h.Ta();Y(!l||l(o)===!1)}i.set(this.Bw(h),o)});const s=t.Eb!==void 0&&this.kw.has(t.Eb)?this.Bw(t.Eb):void 0;return{time:t.zb,logical:t.ee,point:t.Lb,hoveredSeries:s,hoveredObjectId:t.Fb,seriesData:i,sourceEvent:t.Wb}}}function Xh(n,t,i){let s;if(Ct(n)){const h=document.getElementById(n);Y(h!==null,`Cannot find element in DOM with id=${n}`),s=h}else s=n;const e=new Hh(s,t,i);return t.setOptions(e.options()),e}function Is(n,t){return Xh(n,new _s,_s.Id(t))}Object.assign(Object.assign({},qs),js);var Ah=zt("<div><div></div><div><div></div><div></div><div></div><div>"),Uh=zt("<button>");function Jh(n){let t,i,s,e;const[h,r]=It(null),[o,l]=It(null),[a,u]=It("equity"),c=()=>{if(!t)return;const m=Is(t,{width:t.clientWidth,height:n.height||400,layout:{background:{color:"#ffffff"},textColor:"#333"},grid:{vertLines:{color:"#f0f0f0"},horzLines:{color:"#f0f0f0"}},rightPriceScale:{borderColor:"#cccccc"},timeScale:{borderColor:"#cccccc",timeVisible:!0,secondsVisible:!1}}),g=m.addLineSeries({color:"#1890ff",lineWidth:2,title:"权益曲线"}),b=n.result.equity.map(x=>({time:new Date(x.timestamp).getTime()/1e3,value:x.equity}));g.setData(b),r(m);const w=new ResizeObserver(()=>{m.applyOptions({width:t.clientWidth})});w.observe(t),ji(()=>{w.disconnect(),m.remove()})},d=()=>{if(!i)return;const m=Is(i,{width:i.clientWidth,height:n.height||400,layout:{background:{color:"#ffffff"},textColor:"#333"},grid:{vertLines:{color:"#f0f0f0"},horzLines:{color:"#f0f0f0"}},rightPriceScale:{borderColor:"#cccccc"},timeScale:{borderColor:"#cccccc",timeVisible:!0,secondsVisible:!1}}),g=m.addAreaSeries({topColor:"rgba(255, 77, 79, 0.3)",bottomColor:"rgba(255, 77, 79, 0.1)",lineColor:"#ff4d4f",lineWidth:2,title:"回撤曲线"}),b=n.result.drawdownSeries?.map(x=>({time:new Date(x.timestamp).getTime()/1e3,value:x.drawdown}))||[];g.setData(b),l(m);const w=new ResizeObserver(()=>{m.applyOptions({width:i.clientWidth})});w.observe(i),ji(()=>{w.disconnect(),m.remove()})},f=()=>{if(!s)return;const m=[];for(let E=1;E<n.result.equity.length;E++){const M=n.result.equity[E-1].equity,B=(n.result.equity[E].equity-M)/M;m.push(B)}const g=20,b=Math.min(...m),x=(Math.max(...m)-b)/g,y=new Array(g).fill(0);m.forEach(E=>{const M=Math.min(Math.floor((E-b)/x),g-1);y[M]++});const S=document.createElement("canvas");S.width=s.clientWidth,S.height=n.height||400;const R=S.getContext("2d");s.innerHTML="",s.appendChild(S);const C=Math.max(...y),_=S.width/g;R.fillStyle="#1890ff",y.forEach((E,M)=>{const T=E/C*(S.height-40),B=M*_,I=S.height-T-20;R.fillRect(B,I,_-2,T)}),R.fillStyle="#666",R.font="12px Arial",R.textAlign="center";for(let E=0;E<=g;E+=5){const M=E*_,T=(b+E*x)*100;R.fillText(`${T.toFixed(1)}%`,M,S.height-5)}},v=()=>{if(!e||!n.result.monthlyReturns)return;const m=e;m.innerHTML="";const g=document.createElement("div");g.style.cssText=`
      display: grid;
      grid-template-columns: repeat(12, 1fr);
      gap: 2px;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    `;const b=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],w=n.result.monthlyReturns.map(S=>S.return),x=Math.min(...w),y=Math.max(...w);n.result.monthlyReturns.forEach((S,R)=>{const C=document.createElement("div");(S.return-x)/(y-x);let _;S.return<0?_=`rgba(255, 77, 79, ${Math.abs(S.return)/Math.abs(x)*.8})`:_=`rgba(82, 196, 26, ${S.return/y*.8})`,C.style.cssText=`
        background-color: ${_};
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        padding: 8px;
        text-align: center;
        font-size: 12px;
        min-height: 40px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;
        transition: transform 0.2s;
      `,C.innerHTML=`
        <div style="font-weight: bold; margin-bottom: 2px;">${b[R%12]}</div>
        <div style="color: ${S.return>=0?"#52c41a":"#ff4d4f"};">
          ${S.return>=0?"+":""}${(S.return*100).toFixed(1)}%
        </div>
      `,C.addEventListener("mouseenter",()=>{C.style.transform="scale(1.05)"}),C.addEventListener("mouseleave",()=>{C.style.transform="scale(1)"}),g.appendChild(C)}),m.appendChild(g)};return me(()=>{ve(()=>{const m=a();m==="equity"?setTimeout(c,100):m==="drawdown"?setTimeout(d,100):m==="returns"?setTimeout(f,100):m==="monthly"&&setTimeout(v,100)})}),(()=>{var m=Ah(),g=m.firstChild,b=g.nextSibling,w=b.firstChild,x=w.nextSibling,y=x.nextSibling,S=y.nextSibling;N(g,()=>[{key:"equity",label:"权益曲线"},{key:"drawdown",label:"回撤分析"},{key:"returns",label:"收益分布"},{key:"monthly",label:"月度热力图"}].map(M=>(()=>{var T=Uh();return T.$$click=()=>u(M.key),N(T,()=>M.label),vt(()=>L(T,$({px:"16px",py:"12px",border:"none",bg:"transparent",cursor:"pointer",fontSize:"14px",fontWeight:a()===M.key?"600":"400",color:a()===M.key?"#1890ff":"#666",borderBottom:a()===M.key?"2px solid #1890ff":"2px solid transparent",transition:"all 0.3s ease",_hover:{color:"#1890ff"}}))),T})()));var R=t;typeof R=="function"?kt(R,w):t=w;var C=i;typeof C=="function"?kt(C,x):i=x;var _=s;typeof _=="function"?kt(_,y):s=y;var E=e;return typeof E=="function"?kt(E,S):e=S,vt(M=>{var T=$({bg:"white",borderRadius:"12px",p:"24px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}),B=$({display:"flex",borderBottom:"1px solid #f0f0f0",mb:"24px"}),I=$({minHeight:`${n.height||400}px`}),z=a()==="equity"?"block":"none",U=a()==="drawdown"?"block":"none",q=a()==="returns"?"block":"none",J=a()==="monthly"?"block":"none";return T!==M.e&&L(m,M.e=T),B!==M.t&&L(g,M.t=B),I!==M.a&&L(b,M.a=I),z!==M.o&&Rt(w,"display",M.o=z),U!==M.i&&Rt(x,"display",M.i=U),q!==M.n&&Rt(y,"display",M.n=q),J!==M.s&&Rt(S,"display",M.s=J),M},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),m})()}Fs(["click"]);function Gh(n,t,i,s=.03){if(n.length===0)throw new Error("权益数据不能为空");const e=Qh(n),h=Yh(n),r=Zh(e),o=h,l=tr(e),{maxDrawdown:a,maxDrawdownDuration:u}=ir(n),c=sr(e,s),d=er(r,a),f=nr(e,s),v=i?hr(e,i):0,m=rr(t),g=or(e),b=i?lr(e,i):0,w=i?ar(r,b,i,s):0,x=i?ur(e,i):0,y=b!==0?(r-s)/b:0;return{totalReturn:h,annualizedReturn:r,cumulativeReturn:o,volatility:l,maxDrawdown:a,maxDrawdownDuration:u,sharpeRatio:c,calmarRatio:d,sortinoRatio:f,informationRatio:v,...m,...g,beta:b,alpha:w,trackingError:x,treynorRatio:y}}function Qh(n){const t=[];for(let i=1;i<n.length;i++){const s=n[i-1].equity,h=(n[i].equity-s)/s;t.push(h)}return t}function Yh(n){if(n.length===0)return 0;const t=n[0].equity;return(n[n.length-1].equity-t)/t}function Zh(n){if(n.length===0)return 0;const t=n.reduce((s,e)=>s*(1+e),1)-1,i=n.length/252;return Math.pow(1+t,1/i)-1}function tr(n){if(n.length===0)return 0;const t=n.reduce((s,e)=>s+e,0)/n.length,i=n.reduce((s,e)=>s+Math.pow(e-t,2),0)/n.length;return Math.sqrt(i*252)}function ir(n){let t=0,i=0,s=n[0]?.equity||0,e=0,h=0;for(let r=0;r<n.length;r++){const o=n[r].equity;if(o>s)s=o,h>0&&(i=Math.max(i,h),h=0);else{const l=(s-o)/s;t=Math.max(t,l),h===0&&(e=r),h=r-e+1}}return h>0&&(i=Math.max(i,h)),{maxDrawdown:t,maxDrawdownDuration:i}}function sr(n,t){if(n.length===0)return 0;const i=n.map(h=>h-t/252),s=i.reduce((h,r)=>h+r,0)/i.length,e=Math.sqrt(i.reduce((h,r)=>h+Math.pow(r-s,2),0)/i.length);return e>0?s*Math.sqrt(252)/(e*Math.sqrt(252)):0}function er(n,t){return t>0?n/t:0}function nr(n,t){if(n.length===0)return 0;const i=n.map(r=>r-t/252),s=i.reduce((r,o)=>r+o,0)/i.length,e=i.filter(r=>r<0);if(e.length===0)return 1/0;const h=Math.sqrt(e.reduce((r,o)=>r+Math.pow(o,2),0)/n.length);return h>0?s*Math.sqrt(252)/(h*Math.sqrt(252)):0}function hr(n,t){if(n.length!==t.length||n.length===0)return 0;const i=n.map((h,r)=>h-t[r]),s=i.reduce((h,r)=>h+r,0)/i.length,e=Math.sqrt(i.reduce((h,r)=>h+Math.pow(r-s,2),0)/i.length);return e>0?s/e:0}function rr(n){const t=n.filter(c=>(c.pnl||0)>0),i=n.filter(c=>(c.pnl||0)<0),s=n.length>0?t.length/n.length:0,e=t.reduce((c,d)=>c+(d.pnl||0),0),h=Math.abs(i.reduce((c,d)=>c+(d.pnl||0),0)),r=h>0?e/h:0,o=t.length>0?e/t.length:0,l=i.length>0?h/i.length:0,a=t.length>0?Math.max(...t.map(c=>c.pnl||0)):0,u=i.length>0?Math.min(...i.map(c=>c.pnl||0)):0;return{winRate:s,profitFactor:r,averageWin:o,averageLoss:l,largestWin:a,largestLoss:u}}function or(n){if(n.length===0)return{var95:0,var99:0,cvar95:0,cvar99:0};const t=[...n].sort((u,c)=>u-c),i=Math.floor(n.length*.05),s=Math.floor(n.length*.01),e=t[i]||0,h=t[s]||0,r=t.slice(0,i+1),o=t.slice(0,s+1),l=r.length>0?r.reduce((u,c)=>u+c,0)/r.length:0,a=o.length>0?o.reduce((u,c)=>u+c,0)/o.length:0;return{var95:e,var99:h,cvar95:l,cvar99:a}}function lr(n,t){if(n.length!==t.length||n.length===0)return 0;const i=n.reduce((r,o)=>r+o,0)/n.length,s=t.reduce((r,o)=>r+o,0)/t.length;let e=0,h=0;for(let r=0;r<n.length;r++){const o=n[r]-i,l=t[r]-s;e+=o*l,h+=l*l}return e/=n.length,h/=n.length,h>0?e/h:0}function ar(n,t,i,s){const e=i.reduce((h,r)=>h+r,0)/i.length*252;return n-(s+t*(e-s))}function ur(n,t){if(n.length!==t.length||n.length===0)return 0;const i=n.map((h,r)=>h-t[r]),s=i.reduce((h,r)=>h+r,0)/i.length,e=i.reduce((h,r)=>h+Math.pow(r-s,2),0)/i.length;return Math.sqrt(e*252)}var cr=zt("<div><div></div><div><h3> - 详细分析</h3><div>"),dr=zt("<div><div><h3></h3><div></div></div><div> · <!> · </div><div><div><div>总收益率</div><div>%</div></div><div><div>夏普比率</div><div></div></div></div><div>创建时间: "),fr=zt("<div><div></div><div>");function pi(n,t,i){const s=[];let e=n;const h=Math.pow(1+i/100,1/t)-1,r=new Date("2023-01-01");for(let o=0;o<t;o++){const l=new Date(r);l.setDate(r.getDate()+o);const a=1+(Math.random()-.5)*.04,u=h*a;e*=1+u;const c=Math.max(...s.map(f=>f.equity),e),d=c>0?(e-c)/c:0;s.push({timestamp:l.toISOString(),equity:e,drawdown:d})}return s}function gi(n){const t=[],i=["000001","000002","000858","600036","600519"],s=new Date("2023-01-01");for(let e=0;e<n;e++){const h=new Date(s);h.setDate(s.getDate()+Math.floor(Math.random()*250));const r=Math.random()>.5?"buy":"sell",o=10+Math.random()*90,l=Math.floor(Math.random()*1e3)+100,a=(Math.random()-.4)*1e3;t.push({timestamp:h.toISOString(),symbol:i[Math.floor(Math.random()*i.length)],side:r,quantity:l,price:o,pnl:a})}return t.sort((e,h)=>new Date(e.timestamp).getTime()-new Date(h.timestamp).getTime())}function bi(n){const t=[],i=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"];for(let s=0;s<n;s++)t.push({month:i[s],return:(Math.random()-.4)*.12});return t}function wi(n){const t=[],i=new Date("2023-01-01");let s=1e5,e=1e5;for(let h=0;h<n;h++){const r=new Date(i);r.setDate(i.getDate()+h);const o=(Math.random()-.48)*.02;e*=1+o,e>s&&(s=e);const l=(e-s)/s;t.push({timestamp:r.toISOString(),drawdown:l})}return t}function vr(){const[n,t]=It("bt_001"),i=[{id:"bt_001",name:"动量策略回测",strategy:"动量策略",symbol:"000001",period:"2023-01-01 至 2024-01-01",status:"completed",totalReturn:15.6,sharpeRatio:1.85,maxDrawdown:-3.2,winRate:68.5,trades:156,createdAt:"2024-01-15",equity:pi(1e5,252,15.6),tradeHistory:gi(156),monthlyReturns:bi(12),drawdownSeries:wi(252)},{id:"bt_002",name:"均值回归策略回测",strategy:"均值回归策略",symbol:"000002",period:"2023-06-01 至 2024-06-01",status:"completed",totalReturn:8.3,sharpeRatio:1.42,maxDrawdown:-2.1,winRate:72.3,trades:89,createdAt:"2024-01-10",equity:pi(1e5,252,8.3),tradeHistory:gi(89),monthlyReturns:bi(12),drawdownSeries:wi(252)},{id:"bt_003",name:"网格交易策略回测",strategy:"网格交易策略",symbol:"000858",period:"2023-03-01 至 2024-03-01",status:"running",totalReturn:5.7,sharpeRatio:1.23,maxDrawdown:-1.8,winRate:65.4,trades:234,createdAt:"2024-01-08",equity:pi(1e5,252,5.7),tradeHistory:gi(234),monthlyReturns:bi(12),drawdownSeries:wi(252)}],s=()=>i.find(o=>o.id===n()),e=qi(()=>{const o=s();return o?Gh(o.equity,o.tradeHistory,void 0,.03):null}),h=()=>{const o=s();return o?{totalReturn:o.totalReturn,annualizedReturn:o.totalReturn*1.2,sharpeRatio:o.sharpeRatio,maxDrawdown:o.maxDrawdown,winRate:o.winRate,trades:o.tradeHistory,equity:o.equity,monthlyReturns:o.monthlyReturns,drawdownSeries:o.drawdownSeries}:{}},r=qi(()=>{const o=e(),l=s();return!o||!l?[]:[{label:"总收益率",value:`${o.totalReturn>=0?"+":""}${(o.totalReturn*100).toFixed(2)}%`,trend:"up"},{label:"年化收益率",value:`${o.annualizedReturn>=0?"+":""}${(o.annualizedReturn*100).toFixed(2)}%`,trend:"up"},{label:"最大回撤",value:`${(o.maxDrawdown*100).toFixed(2)}%`,trend:"down"},{label:"夏普比率",value:o.sharpeRatio.toFixed(3),trend:"up"},{label:"卡尔马比率",value:o.calmarRatio.toFixed(3),trend:"up"},{label:"索提诺比率",value:o.sortinoRatio.toFixed(3),trend:"up"},{label:"胜率",value:`${(o.winRate*100).toFixed(1)}%`,trend:"up"},{label:"盈亏比",value:o.profitFactor.toFixed(2),trend:"neutral"},{label:"波动率",value:`${(o.volatility*100).toFixed(2)}%`,trend:"neutral"},{label:"VaR(95%)",value:`${(o.var95*100).toFixed(2)}%`,trend:"down"},{label:"CVaR(95%)",value:`${(o.cvar95*100).toFixed(2)}%`,trend:"down"},{label:"交易次数",value:l.trades.toString(),trend:"neutral"}]});return(()=>{var o=cr(),l=o.firstChild,a=l.nextSibling,u=a.firstChild,c=u.firstChild,d=u.nextSibling;return N(l,Gt(Ki,{each:i,children:f=>(()=>{var v=dr(),m=v.firstChild,g=m.firstChild,b=g.nextSibling,w=m.nextSibling,x=w.firstChild,y=x.nextSibling;y.nextSibling;var S=w.nextSibling,R=S.firstChild,C=R.firstChild,_=C.nextSibling,E=_.firstChild,M=R.nextSibling,T=M.firstChild,B=T.nextSibling,I=S.nextSibling;return I.firstChild,v.$$click=()=>t(f.id),N(g,()=>f.name),N(b,(()=>{var z=pe(()=>f.status==="completed");return()=>z()?"已完成":f.status==="running"?"运行中":"失败"})()),N(w,()=>f.strategy,x),N(w,()=>f.symbol,y),N(w,()=>f.period,null),N(_,()=>f.totalReturn>=0?"+":"",E),N(_,()=>f.totalReturn,E),N(B,()=>f.sharpeRatio),N(I,()=>f.createdAt,null),vt(z=>{var U=$({bg:"white",borderRadius:"12px",p:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",border:n()===f.id?"2px solid #1890ff":"1px solid #f0f0f0",cursor:"pointer",transition:"all 0.3s ease",_hover:{boxShadow:"0 4px 16px rgba(0,0,0,0.15)",transform:"translateY(-2px)"}}),q=$({display:"flex",alignItems:"center",justifyContent:"space-between",mb:"12px"}),J=$({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),G=$({px:"8px",py:"4px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",bg:f.status==="completed"?"#f6ffed":f.status==="running"?"#e6f7ff":"#fff2f0",color:f.status==="completed"?"#52c41a":f.status==="running"?"#1890ff":"#ff4d4f"}),ht=$({fontSize:"14px",color:"#8c8c8c",mb:"12px"}),rt=$({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"12px",mb:"12px"}),ot=$({fontSize:"12px",color:"#8c8c8c",mb:"4px"}),Oi=$({fontSize:"16px",fontWeight:"700",color:f.totalReturn>=0?"#52c41a":"#ff4d4f"}),Bi=$({fontSize:"12px",color:"#8c8c8c",mb:"4px"}),Ii=$({fontSize:"16px",fontWeight:"700",color:"#262626"}),Fi=$({fontSize:"12px",color:"#8c8c8c"});return U!==z.e&&L(v,z.e=U),q!==z.t&&L(m,z.t=q),J!==z.a&&L(g,z.a=J),G!==z.o&&L(b,z.o=G),ht!==z.i&&L(w,z.i=ht),rt!==z.n&&L(S,z.n=rt),ot!==z.s&&L(C,z.s=ot),Oi!==z.h&&L(_,z.h=Oi),Bi!==z.r&&L(T,z.r=Bi),Ii!==z.d&&L(B,z.d=Ii),Fi!==z.l&&L(I,z.l=Fi),z},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0}),v})()})),N(u,()=>s()?.name,c),N(d,Gt(Ki,{get each(){return r()},children:f=>(()=>{var v=fr(),m=v.firstChild,g=m.nextSibling;return N(m,()=>f.label),N(g,()=>f.value),vt(b=>{var w=$({textAlign:"center",p:"12px",borderRadius:"8px",bg:"#fafafa",border:"1px solid #f0f0f0",transition:"all 0.3s ease",_hover:{bg:"#f0f0f0",transform:"translateY(-2px)",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"}}),x=$({fontSize:"11px",color:"#8c8c8c",mb:"6px",fontWeight:"500"}),y=$({fontSize:"16px",fontWeight:"700",color:f.trend==="up"?"#52c41a":f.trend==="down"?"#ff4d4f":"#262626"});return w!==b.e&&L(v,b.e=w),x!==b.t&&L(m,b.t=x),y!==b.a&&L(g,b.a=y),b},{e:void 0,t:void 0,a:void 0}),v})()})),N(o,Gt(Jh,{get result(){return h()},height:450}),null),vt(f=>{var v=$({display:"flex",flexDirection:"column",gap:"24px"}),m=$({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(350px, 1fr))",gap:"16px"}),g=$({bg:"white",borderRadius:"16px",p:"24px",boxShadow:"0 4px 20px rgba(0,0,0,0.08)",border:"1px solid #f0f0f0"}),b=$({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0,mb:"20px"}),w=$({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(140px, 1fr))",gap:"12px"});return v!==f.e&&L(o,f.e=v),m!==f.t&&L(l,f.t=m),g!==f.a&&L(a,f.a=g),b!==f.o&&L(u,f.o=b),w!==f.i&&L(d,f.i=w),f},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),o})()}Fs(["click"]);export{vr as default};
