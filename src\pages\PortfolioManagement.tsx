import { createSignal, For, Show } from 'solid-js'
import { css } from '../../styled-system/css'
// import { EnhancedChart } from '../components/EnhancedChart'

interface Position {
  symbol: string
  name: string
  quantity: number
  avgCost: number
  currentPrice: number
  marketValue: number
  unrealizedPnL: number
  unrealizedPnLPercent: number
  weight: number
  sector: string
  beta: number
  lastUpdate: string
}

interface PortfolioSummary {
  totalValue: number
  totalCost: number
  totalPnL: number
  totalPnLPercent: number
  dayChange: number
  dayChangePercent: number
  availableCash: number
  totalAssets: number
}

interface Transaction {
  id: string
  timestamp: string
  symbol: string
  type: 'buy' | 'sell'
  quantity: number
  price: number
  amount: number
  commission: number
  status: 'completed' | 'pending' | 'failed'
}

export default function PortfolioManagement() {
  const [selectedTab, setSelectedTab] = createSignal<'overview' | 'positions' | 'transactions' | 'analysis'>('overview')
  const [sortBy, setSortBy] = createSignal('weight')
  const [sortDirection, setSortDirection] = createSignal<'asc' | 'desc'>('desc')
  const [filterSector, setFilterSector] = createSignal('all')

  // 投资组合概览数据
  const [portfolioSummary,] = createSignal<PortfolioSummary>({
    totalValue: 1256800,
    totalCost: 1189200,
    totalPnL: 67600,
    totalPnLPercent: 5.68,
    dayChange: -8500,
    dayChangePercent: -0.67,
    availableCash: 156700,
    totalAssets: 1413500
  })

  // 持仓数据
  const [positions,] = createSignal<Position[]>([
    {
      symbol: '000001.SZ',
      name: '平安银行',
      quantity: 10000,
      avgCost: 12.56,
      currentPrice: 13.24,
      marketValue: 132400,
      unrealizedPnL: 6800,
      unrealizedPnLPercent: 5.41,
      weight: 10.5,
      sector: '金融',
      beta: 1.15,
      lastUpdate: '2024-01-15 15:00:00'
    },
    {
      symbol: '000858.SZ',
      name: '五粮液',
      quantity: 1500,
      avgCost: 185.60,
      currentPrice: 189.50,
      marketValue: 284250,
      unrealizedPnL: 5850,
      unrealizedPnLPercent: 2.10,
      weight: 22.6,
      sector: '消费',
      beta: 0.95,
      lastUpdate: '2024-01-15 15:00:00'
    },
    {
      symbol: '300059.SZ',
      name: '东方财富',
      quantity: 8000,
      avgCost: 22.30,
      currentPrice: 21.85,
      marketValue: 174800,
      unrealizedPnL: -3600,
      unrealizedPnLPercent: -2.02,
      weight: 13.9,
      sector: '科技',
      beta: 1.45,
      lastUpdate: '2024-01-15 15:00:00'
    },
    {
      symbol: '600519.SH',
      name: '贵州茅台',
      quantity: 200,
      avgCost: 1680.00,
      currentPrice: 1720.50,
      marketValue: 344100,
      unrealizedPnL: 8100,
      unrealizedPnLPercent: 2.41,
      weight: 27.4,
      sector: '消费',
      beta: 0.85,
      lastUpdate: '2024-01-15 15:00:00'
    },
    {
      symbol: '002415.SZ',
      name: '海康威视',
      quantity: 5000,
      avgCost: 31.20,
      currentPrice: 32.15,
      marketValue: 160750,
      unrealizedPnL: 4750,
      unrealizedPnLPercent: 3.04,
      weight: 12.8,
      sector: '科技',
      beta: 1.25,
      lastUpdate: '2024-01-15 15:00:00'
    },
    {
      symbol: '600036.SH',
      name: '招商银行',
      quantity: 4000,
      avgCost: 38.50,
      currentPrice: 39.80,
      marketValue: 159200,
      unrealizedPnL: 5200,
      unrealizedPnLPercent: 3.38,
      weight: 12.7,
      sector: '金融',
      beta: 1.05,
      lastUpdate: '2024-01-15 15:00:00'
    }
  ])

  // 交易记录
  const [transactions,] = createSignal<Transaction[]>([
    {
      id: 'tx_001',
      timestamp: '2024-01-15 14:32:15',
      symbol: '000001.SZ',
      type: 'buy',
      quantity: 1000,
      price: 13.25,
      amount: 13250,
      commission: 5.3,
      status: 'completed'
    },
    {
      id: 'tx_002',
      timestamp: '2024-01-15 13:45:20',
      symbol: '600519.SH',
      type: 'sell',
      quantity: 50,
      price: 1718.00,
      amount: 85900,
      commission: 34.36,
      status: 'completed'
    },
    {
      id: 'tx_003',
      timestamp: '2024-01-15 11:20:08',
      symbol: '300059.SZ',
      type: 'buy',
      quantity: 2000,
      price: 21.80,
      amount: 43600,
      commission: 17.44,
      status: 'completed'
    },
    {
      id: 'tx_004',
      timestamp: '2024-01-15 10:15:30',
      symbol: '002415.SZ',
      type: 'buy',
      quantity: 1000,
      price: 32.00,
      amount: 32000,
      commission: 12.8,
      status: 'pending'
    }
  ])

  const sectors = ['all', '金融', '消费', '科技', '医药', '能源']
  
  const filteredPositions = () => {
    let filtered = positions()
    
    if (filterSector() !== 'all') {
      filtered = filtered.filter(p => p.sector === filterSector())
    }
    
    return filtered.sort((a, b) => {
      const aVal = a[sortBy() as keyof Position] as number
      const bVal = b[sortBy() as keyof Position] as number
      return sortDirection() === 'desc' ? bVal - aVal : aVal - bVal
    })
  }

  const sectorDistribution = () => {
    const distribution = positions().reduce((acc, position) => {
      acc[position.sector] = (acc[position.sector] || 0) + position.weight
      return acc
    }, {} as Record<string, number>)
    
    return Object.entries(distribution).map(([sector, weight]) => ({
      name: sector,
      value: weight,
      color: getSectorColor(sector)
    }))
  }

  const getSectorColor = (sector: string) => {
    const colors: Record<string, string> = {
      '金融': '#1890ff',
      '消费': '#52c41a',
      '科技': '#722ed1',
      '医药': '#eb2f96',
      '能源': '#fa8c16'
    }
    return colors[sector] || '#8c8c8c'
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString()}`
  }

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`
  }

  return (
    <div class={css({
      display: 'flex',
      flexDirection: 'column',
      gap: '24px',
      padding: '24px'
    })}>
      {/* 标题栏 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      })}>
        <h1 class={css({
          fontSize: '28px',
          fontWeight: '600',
          margin: 0,
          color: '#262626'
        })}>
          投资组合管理
        </h1>
        <div class={css({
          display: 'flex',
          gap: '12px'
        })}>
          <button class={css({
            px: '16px',
            py: '8px',
            bg: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            _hover: {
              bg: '#40a9ff'
            }
          })}>
            导出报告
          </button>
          <button class={css({
            px: '16px',
            py: '8px',
            bg: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            _hover: {
              bg: '#73d13d'
            }
          })}>
            智能调仓
          </button>
        </div>
      </div>

      {/* 投资组合概览 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '16px'
      })}>
        <div class={css({
          bg: 'white',
          p: '20px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        })}>
          <div class={css({
            fontSize: '14px',
            color: '#8c8c8c',
            mb: '8px'
          })}>
            总资产
          </div>
          <div class={css({
            fontSize: '24px',
            fontWeight: '700',
            color: '#262626'
          })}>
            {formatCurrency(portfolioSummary().totalAssets)}
          </div>
        </div>

        <div class={css({
          bg: 'white',
          p: '20px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        })}>
          <div class={css({
            fontSize: '14px',
            color: '#8c8c8c',
            mb: '8px'
          })}>
            持仓市值
          </div>
          <div class={css({
            fontSize: '24px',
            fontWeight: '700',
            color: '#262626'
          })}>
            {formatCurrency(portfolioSummary().totalValue)}
          </div>
        </div>

        <div class={css({
          bg: 'white',
          p: '20px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        })}>
          <div class={css({
            fontSize: '14px',
            color: '#8c8c8c',
            mb: '8px'
          })}>
            总盈亏
          </div>
          <div class={css({
            fontSize: '24px',
            fontWeight: '700',
            color: portfolioSummary().totalPnL >= 0 ? '#52c41a ' : '#ff4d4f'
          })}>
            {formatCurrency(portfolioSummary().totalPnL)}
          </div>
          <div class={css({
            fontSize: '12px',
            color: portfolioSummary().totalPnL >= 0 ? '#52c41a ' : '#ff4d4f'
          })}>
            {formatPercent(portfolioSummary().totalPnLPercent)}
          </div>
        </div>

        <div class={css({
          bg: 'white',
          p: '20px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        })}>
          <div class={css({
            fontSize: '14px',
            color: '#8c8c8c',
            mb: '8px'
          })}>
            今日盈亏
          </div>
          <div class={css({
            fontSize: '24px',
            fontWeight: '700',
            color: portfolioSummary().dayChange >= 0 ? '#52c41a ' : '#ff4d4f'
          })}>
            {formatCurrency(portfolioSummary().dayChange)}
          </div>
          <div class={css({
            fontSize: '12px',
            color: portfolioSummary().dayChange >= 0 ? '#52c41a ' : '#ff4d4f'
          })}>
            {formatPercent(portfolioSummary().dayChangePercent)}
          </div>
        </div>

        <div class={css({
          bg: 'white',
          p: '20px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        })}>
          <div class={css({
            fontSize: '14px',
            color: '#8c8c8c',
            mb: '8px'
          })}>
            可用资金
          </div>
          <div class={css({
            fontSize: '24px',
            fontWeight: '700',
            color: '#262626'
          })}>
            {formatCurrency(portfolioSummary().availableCash)}
          </div>
        </div>

        <div class={css({
          bg: 'white',
          p: '20px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        })}>
          <div class={css({
            fontSize: '14px',
            color: '#8c8c8c',
            mb: '8px'
          })}>
            持仓数量
          </div>
          <div class={css({
            fontSize: '24px',
            fontWeight: '700',
            color: '#262626'
          })}>
            {positions().length}
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div class={css({
        bg: 'white',
        borderRadius: '12px',
        p: '16px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      })}>
        <div class={css({
          display: 'flex',
          borderBottom: '1px solid #f0f0f0'
        })}>
          {[
            { key: 'overview', label: '概览分析' },
            { key: 'positions', label: '持仓详情' },
            { key: 'transactions', label: '交易记录' },
            { key: 'analysis', label: '组合分析' }
          ].map(tab => (
            <button
              class={css({
                px: '20px',
                py: '12px',
                border: 'none',
                bg: 'transparent',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: selectedTab() === tab.key ? '600' : '400',
                color: selectedTab() === tab.key ? '#1890ff ' : '#666',
                borderBottom: selectedTab() === tab.key ? '2px solid #1890ff ' : '2px solid transparent',
                transition: 'all 0.3s ease'
              })}
              onClick={() => setSelectedTab(tab.key as any)}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* 标签页内容 */}
        <div class={css({ mt: '24px' })}>
          <Show when={selectedTab() === 'overview'}>
            <div class={css({
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '24px',
              '@media (max-width: 768px)': {
                gridTemplateColumns: '1fr'
              }
            })}>
              {/* 行业分布饼图占位符 */}
              <div class={css({
                bg: '#fafafa',
                borderRadius: '8px',
                p: '24px',
                textAlign: 'center',
                minHeight: '300px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              })}>
                <div>
                  <div class={css({
                    fontSize: '48px',
                    mb: '16px'
                  })}>
                    📊
                  </div>
                  <h3 class={css({
                    fontSize: '16px',
                    fontWeight: '600',
                    color: '#262626',
                    mb: '8px'
                  })}>
                    行业分布图
                  </h3>
                  <div class={css({
                    display: 'flex',
                    flexWrap: 'wrap',
                    justifyContent: 'center',
                    gap: '12px',
                    mt: '16px'
                  })}>
                    <For each={sectorDistribution()}>
                      {(sector) => (
                        <div class={css({
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          fontSize: '12px'
                        })}>
                          <div class={css({
                            w: '12px',
                            h: '12px',
                            borderRadius: '50%',
                            bg: sector.color
                          })}></div>
                          <span>{sector.name}: {sector.value.toFixed(1)}%</span>
                        </div>
                      )}
                    </For>
                  </div>
                </div>
              </div>

              {/* 持仓权重Top5 */}
              <div class={css({
                bg: '#fafafa',
                borderRadius: '8px',
                p: '24px'
              })}>
                <h3 class={css({
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#262626',
                  mb: '16px'
                })}>
                  持仓权重前5名
                </h3>
                <div class={css({
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '12px'
                })}>
                  <For each={positions().slice(0, 5).sort((a, b) => b.weight - a.weight)}>
                    {(position, index) => (
                      <div class={css({
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: '12px',
                        bg: 'white',
                        borderRadius: '6px'
                      })}>
                        <div class={css({
                          display: 'flex',
                          alignItems: 'center',
                          gap: '12px'
                        })}>
                          <div class={css({
                            w: '24px',
                            h: '24px',
                            borderRadius: '50%',
                            bg: '#1890ff',
                            color: 'white',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '12px',
                            fontWeight: '600'
                          })}>
                            {index() + 1}
                          </div>
                          <div>
                            <div class={css({
                              fontSize: '14px',
                              fontWeight: '600',
                              color: '#262626'
                            })}>
                              {position.name}
                            </div>
                            <div class={css({
                              fontSize: '12px',
                              color: '#8c8c8c'
                            })}>
                              {position.symbol}
                            </div>
                          </div>
                        </div>
                        <div class={css({
                          textAlign: 'right'
                        })}>
                          <div class={css({
                            fontSize: '14px',
                            fontWeight: '600',
                            color: '#262626'
                          })}>
                            {position.weight.toFixed(1)}%
                          </div>
                          <div class={css({
                            fontSize: '12px',
                            color: position.unrealizedPnL >= 0 ? '#52c41a ' : '#ff4d4f'
                          })}>
                            {formatPercent(position.unrealizedPnLPercent)}
                          </div>
                        </div>
                      </div>
                    )}
                  </For>
                </div>
              </div>
            </div>
          </Show>

          <Show when={selectedTab() === 'positions'}>
            {/* 筛选和排序 */}
            <div class={css({
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              mb: '20px'
            })}>
              <select
                value={filterSector()}
                onChange={(e) => setFilterSector(e.target.value)}
                class={css({
                  px: '12px',
                  py: '8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '6px',
                  fontSize: '14px'
                })}
              >
                <For each={sectors}>
                  {(sector) => (
                    <option value={sector}>{sector === 'all' ? '全部行业' : sector}</option>
                  )}
                </For>
              </select>

              <select
                value={sortBy()}
                onChange={(e) => setSortBy(e.target.value)}
                class={css({
                  px: '12px',
                  py: '8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '6px',
                  fontSize: '14px'
                })}
              >
                <option value="weight">按权重排序</option>
                <option value="unrealizedPnL">按盈亏排序</option>
                <option value="marketValue">按市值排序</option>
              </select>

              <button
                onClick={() => setSortDirection(prev => prev === 'desc' ? 'asc' : 'desc')}
                class={css({
                  px: '12px',
                  py: '8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '6px',
                  fontSize: '14px',
                  bg: 'white',
                  cursor: 'pointer'
                })}
              >
                {sortDirection() === 'desc' ? '↓ 降序' : '↑ 升序'}
              </button>
            </div>

            {/* 持仓列表 */}
            <div class={css({
              display: 'grid',
              gap: '12px'
            })}>
              <For each={filteredPositions()}>
                {(position) => (
                  <div class={css({
                    display: 'grid',
                    gridTemplateColumns: '160px 1fr 120px 120px 120px 100px',
                    alignItems: 'center',
                    p: '16px',
                    bg: '#fafafa',
                    borderRadius: '8px',
                    fontSize: '14px',
                    '@media (max-width: 768px)': {
                      gridTemplateColumns: '1fr',
                      gap: '8px'
                    }
                  })}>
                    <div>
                      <div class={css({
                        fontWeight: '600',
                        color: '#262626',
                        mb: '4px'
                      })}>
                        {position.name}
                      </div>
                      <div class={css({
                        fontSize: '12px',
                        color: '#8c8c8c'
                      })}>
                        {position.symbol}
                      </div>
                      <div class={css({
                        fontSize: '10px',
                        px: '6px',
                        py: '2px',
                        bg: getSectorColor(position.sector) + '20',
                        color: getSectorColor(position.sector),
                        borderRadius: '4px',
                        display: 'inline-block',
                        mt: '4px'
                      })}>
                        {position.sector}
                      </div>
                    </div>

                    <div>
                      <div class={css({
                        fontSize: '12px',
                        color: '#8c8c8c',
                        mb: '2px'
                      })}>
                        {position.quantity.toLocaleString()}股 × ¥{position.currentPrice.toFixed(2)}
                      </div>
                      <div class={css({
                        fontSize: '12px',
                        color: '#8c8c8c'
                      })}>
                        成本价: ¥{position.avgCost.toFixed(2)}
                      </div>
                    </div>

                    <div class={css({
                      textAlign: 'right',
                      fontWeight: '600',
                      color: '#262626'
                    })}>
                      {formatCurrency(position.marketValue)}
                    </div>

                    <div class={css({
                      textAlign: 'right',
                      color: position.unrealizedPnL >= 0 ? '#52c41a ' : '#ff4d4f',
                      fontWeight: '600'
                    })}>
                      {formatCurrency(position.unrealizedPnL)}
                      <div class={css({
                        fontSize: '12px'
                      })}>
                        {formatPercent(position.unrealizedPnLPercent)}
                      </div>
                    </div>

                    <div class={css({
                      textAlign: 'right',
                      fontWeight: '600'
                    })}>
                      {position.weight.toFixed(1)}%
                    </div>

                    <div class={css({
                      textAlign: 'center'
                    })}>
                      <button class={css({
                        px: '8px',
                        py: '4px',
                        bg: '#1890ff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '12px',
                        cursor: 'pointer',
                        mb: '4px',
                        display: 'block',
                        width: '100%'
                      })}>
                        交易
                      </button>
                      <button class={css({
                        px: '8px',
                        py: '4px',
                        bg: '#52c41a',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '12px',
                        cursor: 'pointer',
                        display: 'block',
                        width: '100%'
                      })}>
                        详情
                      </button>
                    </div>
                  </div>
                )}
              </For>
            </div>
          </Show>

          <Show when={selectedTab() === 'transactions'}>
            <div class={css({
              display: 'grid',
              gap: '12px'
            })}>
              <For each={transactions()}>
                {(transaction) => (
                  <div class={css({
                    display: 'grid',
                    gridTemplateColumns: '160px 80px 120px 120px 120px 80px',
                    alignItems: 'center',
                    p: '16px',
                    bg: '#fafafa',
                    borderRadius: '8px',
                    fontSize: '14px',
                    '@media (max-width: 768px)': {
                      gridTemplateColumns: '1fr',
                      gap: '8px'
                    }
                  })}>
                    <div>
                      <div class={css({
                        fontSize: '12px',
                        color: '#8c8c8c'
                      })}>
                        {transaction.timestamp}
                      </div>
                      <div class={css({
                        fontWeight: '600',
                        color: '#262626'
                      })}>
                        {transaction.symbol}
                      </div>
                    </div>

                    <div class={css({
                      textAlign: 'center'
                    })}>
                      <span class={css({
                        px: '8px',
                        py: '4px',
                        borderRadius: '4px',
                        fontSize: '12px',
                        fontWeight: '600',
                        bg: transaction.type === 'buy' ? '#f6ffed ' : '#fff2f0',
                        color: transaction.type === 'buy' ? '#52c41a ' : '#ff4d4f'
                      })}>
                        {transaction.type === 'buy' ? '买入' : '卖出'}
                      </span>
                    </div>

                    <div class={css({
                      textAlign: 'right'
                    })}>
                      {transaction.quantity.toLocaleString()}股
                    </div>

                    <div class={css({
                      textAlign: 'right'
                    })}>
                      ¥{transaction.price.toFixed(2)}
                    </div>

                    <div class={css({
                      textAlign: 'right',
                      fontWeight: '600'
                    })}>
                      {formatCurrency(transaction.amount)}
                    </div>

                    <div class={css({
                      textAlign: 'center'
                    })}>
                      <span class={css({
                        px: '6px',
                        py: '2px',
                        borderRadius: '3px',
                        fontSize: '10px',
                        fontWeight: '500',
                        bg: transaction.status === 'completed' ? '#f6ffed:
                            transaction.status === 'pending' ? '#e6f7ff: '#fff2f0',
                        color: transaction.status === 'completed' ? '#52c41a ' :
                               transaction.status === 'pending' ? '#1890ff ' : '#ff4d4f'
                      })}>
                        {transaction.status === 'completed' ? '已完成' :
                         transaction.status === 'pending' ? '待成交' : '失败'}
                      </span>
                    </div>
                  </div>
                )}
              </For>
            </div>
          </Show>

          <Show when={selectedTab() === 'analysis'}>
            <div class={css({
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '20px'
            })}>
              <div class={css({
                bg: '#fafafa',
                p: '20px',
                borderRadius: '8px'
              })}>
                <h3 class={css({
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#262626',
                  mb: '16px'
                })}>
                  风险评估
                </h3>
                <div class={css({
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '12px'
                })}>
                  <div class={css({
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '14px'
                  })}>
                    <span>组合Beta:</span>
                    <span class={css({ fontWeight: '600' })}>
                      {(positions().reduce((sum, p) => sum + p.beta * p.weight, 0) / 100).toFixed(2)}
                    </span>
                  </div>
                  <div class={css({
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '14px'
                  })}>
                    <span>集中度风险:</span>
                    <span class={css({ 
                      fontWeight: '600',
                      color: Math.max(...positions().map(p => p.weight)) > 30 ? '#ff4d4f ' : '#52c41a'
                    })}>
                      {Math.max(...positions().map(p => p.weight)).toFixed(1)}%
                    </span>
                  </div>
                  <div class={css({
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '14px'
                  })}>
                    <span>行业分散度:</span>
                    <span class={css({ fontWeight: '600' })}>
                      {new Set(positions().map(p => p.sector)).size}个行业
                    </span>
                  </div>
                </div>
              </div>

              <div class={css({
                bg: '#fafafa',
                p: '20px',
                borderRadius: '8px'
              })}>
                <h3 class={css({
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#262626',
                  mb: '16px'
                })}>
                  绩效分析
                </h3>
                <div class={css({
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '12px'
                })}>
                  <div class={css({
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '14px'
                  })}>
                    <span>胜率:</span>
                    <span class={css({ fontWeight: '600' })}>
                      {(positions().filter(p => p.unrealizedPnL > 0).length / positions().length * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div class={css({
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '14px'
                  })}>
                    <span>平均收益率:</span>
                    <span class={css({ 
                      fontWeight: '600',
                      color: portfolioSummary().totalPnLPercent >= 0 ? '#52c41a ' : '#ff4d4f'
                    })}>
                      {formatPercent(portfolioSummary().totalPnLPercent)}
                    </span>
                  </div>
                  <div class={css({
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '14px'
                  })}>
                    <span>最大单笔盈利:</span>
                    <span class={css({ 
                      fontWeight: '600',
                      color: '#52c41a'
                    })}>
                      {formatCurrency(Math.max(...positions().map(p => p.unrealizedPnL)))}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Show>
        </div>
      </div>
    </div>
  )
}