import { createSignal, createEffect, JSX, Show, For } from 'solid-js';

// 移动端底部导航
export interface MobileTabBarProps {
  tabs: {
    id: string;
    icon: JSX.Element | string;
    label: string;
    badge?: number;
  }[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  theme?: 'light' | 'dark';
}

export function MobileTabBar(props: MobileTabBarProps) {
  return (
    <div style={{
      position: 'fixed',
      bottom: '0',
      left: '0',
      right: '0',
      background: props.theme === 'dark' ? '#1f2937' : '#ffffff',
      borderTop: `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
      display: 'flex',
      zIndex: '1000',
      paddingBottom: 'env(safe-area-inset-bottom)',
    }}>
      <For each={props.tabs}>
        {(tab) => (
          <button
            onClick={() => props.onTabChange(tab.id)}
            style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '12px 8px',
              border: 'none',
              background: 'transparent',
              cursor: 'pointer',
              transition: 'all 0.2s',
              position: 'relative',
            }}
          >
            {/* 图标 */}
            <div style={{
              fontSize: '20px',
              marginBottom: '4px',
              color: props.activeTab === tab.id 
                ? '#3b82f6' 
                : (props.theme === 'dark' ? '#9ca3af' : '#6b7280'),
              transition: 'color 0.2s',
            }}>
              {tab.icon}
            </div>

            {/* 标签 */}
            <div style={{
              fontSize: '12px',
              color: props.activeTab === tab.id 
                ? '#3b82f6' 
                : (props.theme === 'dark' ? '#9ca3af' : '#6b7280'),
              transition: 'color 0.2s',
            }}>
              {tab.label}
            </div>

            {/* 活动指示器 */}
            <Show when={props.activeTab === tab.id}>
              <div style={{
                position: 'absolute',
                top: '0',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '32px',
                height: '2px',
                background: '#3b82f6',
                borderRadius: '1px',
              }} />
            </Show>

            {/* 徽章 */}
            <Show when={tab.badge && tab.badge > 0}>
              <div style={{
                position: 'absolute',
                top: '8px',
                right: '50%',
                transform: 'translateX(10px)',
                minWidth: '16px',
                height: '16px',
                background: '#ef4444',
                color: 'white',
                borderRadius: '8px',
                fontSize: '10px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '0 4px',
              }}>
                {tab.badge > 99 ? '99+' : tab.badge}
              </div>
            </Show>
          </button>
        )}
      </For>
    </div>
  );
}

// 移动端顶部导航栏
export interface MobileHeaderProps {
  title: string;
  leftAction?: {
    icon: JSX.Element | string;
    onClick: () => void;
  };
  rightActions?: {
    icon: JSX.Element | string;
    onClick: () => void;
    badge?: number;
  }[];
  theme?: 'light' | 'dark';
  transparent?: boolean;
}

export function MobileHeader(props: MobileHeaderProps) {
  return (
    <div style={{
      position: 'sticky',
      top: '0',
      zIndex: '1000',
      background: props.transparent 
        ? 'transparent' 
        : (props.theme === 'dark' ? '#1f2937' : '#ffffff'),
      borderBottom: props.transparent 
        ? 'none' 
        : `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
      padding: '16px',
      paddingTop: 'max(16px, env(safe-area-inset-top))',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      ...(props.transparent && {
        'backdrop-filter': 'blur(10px)',
        background: props.theme === 'dark' 
          ? 'rgba(31, 41, 55, 0.8)' 
          : 'rgba(255, 255, 255, 0.8)',
      }),
    }}>
      {/* 左侧操作 */}
      <div style={{ width: '44px', height: '44px' }}>
        <Show when={props.leftAction}>
          <button
            onClick={props.leftAction!.onClick}
            style={{
              width: '44px',
              height: '44px',
              borderRadius: '8px',
              border: 'none',
              background: 'transparent',
              color: props.theme === 'dark' ? '#f9fafb' : '#111827',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.2s',
            }}
            onMouseDown={(e) => {
              e.currentTarget.style.background = props.theme === 'dark' ? '#374151' : '#f3f4f6';
            }}
            onMouseUp={(e) => {
              e.currentTarget.style.background = 'transparent';
            }}
          >
            {props.leftAction!.icon}
          </button>
        </Show>
      </div>

      {/* 标题 */}
      <h1 style={{
        flex: '1',
        textAlign: 'center',
        margin: '0',
        fontSize: '18px',
        fontWeight: 'bold',
        color: props.theme === 'dark' ? '#f9fafb' : '#111827',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
      }}>
        {props.title}
      </h1>

      {/* 右侧操作 */}
      <div style={{
        display: 'flex',
        gap: '8px',
        minWidth: '44px',
        justifyContent: 'flex-end',
      }}>
        <Show when={props.rightActions}>
          <For each={props.rightActions}>
            {(action) => (
              <button
                onClick={action.onClick}
                style={{
                  width: '44px',
                  height: '44px',
                  borderRadius: '8px',
                  border: 'none',
                  background: 'transparent',
                  color: props.theme === 'dark' ? '#f9fafb' : '#111827',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transition: 'all 0.2s',
                  position: 'relative',
                }}
                onMouseDown={(e) => {
                  e.currentTarget.style.background = props.theme === 'dark' ? '#374151' : '#f3f4f6';
                }}
                onMouseUp={(e) => {
                  e.currentTarget.style.background = 'transparent';
                }}
              >
                {action.icon}
                <Show when={action.badge && action.badge > 0}>
                  <div style={{
                    position: 'absolute',
                    top: '6px',
                    right: '6px',
                    minWidth: '16px',
                    height: '16px',
                    background: '#ef4444',
                    color: 'white',
                    borderRadius: '8px',
                    fontSize: '10px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '0 4px',
                  }}>
                    {action.badge > 99 ? '99+' : action.badge}
                  </div>
                </Show>
              </button>
            )}
          </For>
        </Show>
      </div>
    </div>
  );
}

// 移动端抽屉组件
export interface MobileDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  children: JSX.Element;
  position?: 'left' | 'right' | 'bottom';
  theme?: 'light' | 'dark';
  title?: string;
  height?: string;
}

export function MobileDrawer(props: MobileDrawerProps) {
  const [startY, setStartY] = createSignal<number | null>(null);
  const position = props.position || 'bottom';

  const handleTouchStart = (e: TouchEvent) => {
    if (position === 'bottom') {
      setStartY(e.touches[0].clientY);
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (position === 'bottom' && startY() !== null) {
      const currentY = e.touches[0].clientY;
      const deltaY = currentY - startY()!;
      
      // 向下滑动超过50px时关闭抽屉
      if (deltaY > 50) {
        props.onClose();
        setStartY(null);
      }
    }
  };

  const handleTouchEnd = () => {
    setStartY(null);
  };

  const getTransform = () => {
    if (!props.isOpen) {
      switch (position) {
        case 'left': return 'translateX(-100%)';
        case 'right': return 'translateX(100%)';
        case 'bottom': return 'translateY(100%)';
        default: return 'translateY(100%)';
      }
    }
    return 'translate(0, 0)';
  };

  const getPosition = () => {
    switch (position) {
      case 'left':
        return { top: '0', left: '0', bottom: '0', width: '280px' };
      case 'right':
        return { top: '0', right: '0', bottom: '0', width: '280px' };
      case 'bottom':
      default:
        return {
          bottom: '0',
          left: '0',
          right: '0',
          height: props.height || 'auto',
          maxHeight: '80vh',
        };
    }
  };

  return (
    <>
      {/* 遮罩层 */}
      <Show when={props.isOpen}>
        <div
          onClick={props.onClose}
          style={{
            position: 'fixed',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            background: 'rgba(0, 0, 0, 0.5)',
            zIndex: '9998',
            opacity: props.isOpen ? '1' : '0',
            transition: 'opacity 0.3s',
          }}
        />
      </Show>

      {/* 抽屉内容 */}
      <div
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        style={{
          position: 'fixed',
          ...getPosition(),
          background: props.theme === 'dark' ? '#1f2937' : '#ffffff',
          boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.3)',
          zIndex: '9999',
          transform: getTransform(),
          transition: 'transform 0.3s ease-in-out',
          ...(position === 'bottom' && {
            borderTopLeftRadius: '16px',
            borderTopRightRadius: '16px',
          }),
          overflowY: 'auto',
        }}
      >
        {/* 底部抽屉的拖拽指示器 */}
        <Show when={position === 'bottom'}>
          <div style={{
            width: '36px',
            height: '4px',
            background: props.theme === 'dark' ? '#4b5563' : '#d1d5db',
            borderRadius: '2px',
            margin: '12px auto 8px',
          }} />
        </Show>

        {/* 标题 */}
        <Show when={props.title}>
          <div style={{
            padding: '16px 24px 0',
            borderBottom: `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
          }}>
            <h3 style={{
              margin: '0 0 16px 0',
              fontSize: '18px',
              fontWeight: 'bold',
              color: props.theme === 'dark' ? '#f9fafb' : '#111827',
            }}>
              {props.title}
            </h3>
          </div>
        </Show>

        {/* 内容 */}
        <div style={{
          padding: props.title ? '24px' : '16px 24px 24px',
        }}>
          {props.children}
        </div>
      </div>
    </>
  );
}

// 移动端卡片组件
export interface MobileCardProps {
  children: JSX.Element;
  title?: string;
  subtitle?: string;
  action?: JSX.Element;
  onClick?: () => void;
  theme?: 'light' | 'dark';
  padding?: string;
}

export function MobileCard(props: MobileCardProps) {
  return (
    <div
      onClick={props.onClick}
      style={{
        background: props.theme === 'dark' ? '#1f2937' : '#ffffff',
        borderRadius: '12px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        overflow: 'hidden',
        cursor: props.onClick ? 'pointer' : 'default',
        transition: 'all 0.2s',
        ...(props.onClick && {
          'active:scale': '0.98',
          'active:opacity': '0.8',
        }),
      }}
      onMouseDown={(e) => {
        if (props.onClick) {
          e.currentTarget.style.transform = 'scale(0.98)';
        }
      }}
      onMouseUp={(e) => {
        if (props.onClick) {
          e.currentTarget.style.transform = 'scale(1)';
        }
      }}
    >
      {/* 标题栏 */}
      <Show when={props.title || props.subtitle || props.action}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '16px 20px',
          borderBottom: `1px solid ${props.theme === 'dark' ? '#374151' : '#f3f4f6'}`,
        }}>
          <div style={{ flex: '1' }}>
            <Show when={props.title}>
              <h3 style={{
                margin: '0 0 4px 0',
                fontSize: '16px',
                fontWeight: '600',
                color: props.theme === 'dark' ? '#f9fafb' : '#111827',
              }}>
                {props.title}
              </h3>
            </Show>
            <Show when={props.subtitle}>
              <p style={{
                margin: '0',
                fontSize: '14px',
                color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
              }}>
                {props.subtitle}
              </p>
            </Show>
          </div>
          <Show when={props.action}>
            <div style={{ marginLeft: '16px' }}>
              {props.action}
            </div>
          </Show>
        </div>
      </Show>

      {/* 内容 */}
      <div style={{
        padding: props.padding || '20px',
      }}>
        {props.children}
      </div>
    </div>
  );
}

// 移动端列表组件
export interface MobileListItem {
  id: string;
  title: string;
  subtitle?: string;
  icon?: JSX.Element;
  rightIcon?: JSX.Element;
  badge?: number;
  onClick?: () => void;
}

export interface MobileListProps {
  items: MobileListItem[];
  theme?: 'light' | 'dark';
  dividers?: boolean;
}

export function MobileList(props: MobileListProps) {
  return (
    <div style={{
      background: props.theme === 'dark' ? '#1f2937' : '#ffffff',
      borderRadius: '12px',
      overflow: 'hidden',
    }}>
      <For each={props.items}>
        {(item, index) => (
          <div
            onClick={item.onClick}
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '16px 20px',
              cursor: item.onClick ? 'pointer' : 'default',
              transition: 'background-color 0.2s',
              ...(props.dividers && index() < props.items.length - 1 && {
                borderBottom: `1px solid ${props.theme === 'dark' ? '#374151' : '#f3f4f6'}`,
              }),
            }}
            onMouseDown={(e) => {
              if (item.onClick) {
                e.currentTarget.style.background = props.theme === 'dark' ? '#374151' : '#f3f4f6';
              }
            }}
            onMouseUp={(e) => {
              if (item.onClick) {
                e.currentTarget.style.background = 'transparent';
              }
            }}
          >
            {/* 图标 */}
            <Show when={item.icon}>
              <div style={{
                marginRight: '16px',
                color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
              }}>
                {item.icon}
              </div>
            </Show>

            {/* 内容 */}
            <div style={{ flex: '1' }}>
              <div style={{
                fontSize: '16px',
                fontWeight: '500',
                color: props.theme === 'dark' ? '#f9fafb' : '#111827',
                marginBottom: item.subtitle ? '4px' : '0',
              }}>
                {item.title}
              </div>
              <Show when={item.subtitle}>
                <div style={{
                  fontSize: '14px',
                  color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
                }}>
                  {item.subtitle}
                </div>
              </Show>
            </div>

            {/* 徽章 */}
            <Show when={item.badge && item.badge > 0}>
              <div style={{
                minWidth: '20px',
                height: '20px',
                background: '#ef4444',
                color: 'white',
                borderRadius: '10px',
                fontSize: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '0 6px',
                marginRight: '12px',
              }}>
                {item.badge > 99 ? '99+' : item.badge}
              </div>
            </Show>

            {/* 右侧图标 */}
            <Show when={item.rightIcon}>
              <div style={{
                color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
              }}>
                {item.rightIcon}
              </div>
            </Show>
          </div>
        )}
      </For>
    </div>
  );
}

// 移动端安全区域处理
export function MobileSafeArea(props: { children: JSX.Element }) {
  return (
    <div style={{
      paddingTop: 'env(safe-area-inset-top)',
      paddingBottom: 'env(safe-area-inset-bottom)',
      paddingLeft: 'env(safe-area-inset-left)',
      paddingRight: 'env(safe-area-inset-right)',
    }}>
      {props.children}
    </div>
  );
}