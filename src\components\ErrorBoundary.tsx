import { createSignal, JSX, onMount, Show } from 'solid-js';

export interface ErrorInfo {
  message: string;
  stack?: string;
  timestamp: number;
  componentStack?: string;
  userAgent: string;
  url: string;
}

interface ErrorBoundaryProps {
  children: JSX.Element;
  fallback?: (error: ErrorInfo, reset: () => void) => JSX.Element;
  onError?: (error: ErrorInfo) => void;
  resetOnPropsChange?: boolean;
  theme?: 'light' | 'dark';
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: ErrorInfo | null;
}

export default function ErrorBoundary(props: ErrorBoundaryProps) {
  const [state, setState] = createSignal<ErrorBoundaryState>({
    hasError: false,
    error: null,
  });

  // 重置错误状态
  const resetError = () => {
    setState({ hasError: false, error: null });
  };

  // 处理错误
  const handleError = (error: Error, errorInfo?: any) => {
    const errorData: ErrorInfo = {
      message: error.message || '未知错误',
      stack: error.stack,
      timestamp: Date.now(),
      componentStack: errorInfo?.componentStack,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    setState({ hasError: true, error: errorData });

    // 调用错误回调
    if (props.onError) {
      props.onError(errorData);
    }

    // 在开发环境下记录错误
    if (import.meta.env?.DEV) {
      console.error('ErrorBoundary 捕获到错误:', error);
      console.error('错误信息:', errorData);
    }

    // 发送错误到监控服务
    reportError(errorData);
  };

  // 报告错误到监控服务
  const reportError = (errorInfo: ErrorInfo) => {
    try {
      // 这里可以集成 Sentry、LogRocket 等监控服务
      fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorInfo),
      }).catch(console.warn);
    } catch (e) {
      // 静默处理错误报告失败
    }
  };

  // 全局错误处理
  onMount(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason));
      handleError(error);
      event.preventDefault();
    };

    const handleGlobalError = (event: ErrorEvent) => {
      const error = event.error || new Error(event.message);
      handleError(error);
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleGlobalError);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleGlobalError);
    };
  });

  // 默认错误回退组件
  const DefaultFallback = (error: ErrorInfo, reset: () => void) => (
    <div style={{
      padding: '32px',
      textAlign: 'center',
      background: props.theme === 'dark' ? '#1f2937' : '#ffffff',
      color: props.theme === 'dark' ? '#f9fafb' : '#111827',
      borderRadius: '8px',
      boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
      margin: '16px',
    }}>
      {/* 错误图标 */}
      <div style={{
        width: '64px',
        height: '64px',
        margin: '0 auto 24px',
        borderRadius: '50%',
        background: '#fee2e2',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '24px',
      }}>
        ⚠️
      </div>

      {/* 错误标题 */}
      <h2 style={{
        margin: '0 0 16px 0',
        fontSize: '24px',
        fontWeight: 'bold',
        color: props.theme === 'dark' ? '#f87171' : '#dc2626',
      }}>
        哎呀，出现了一些问题
      </h2>

      {/* 错误描述 */}
      <p style={{
        margin: '0 0 24px 0',
        color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
        lineHeight: '1.6',
        maxWidth: '500px',
        marginLeft: 'auto',
        marginRight: 'auto',
      }}>
        应用遇到了一个意外错误。我们已经记录了这个问题，请稍后重试。
      </p>

      {/* 错误详情 (开发模式) */}
      <Show when={import.meta.env?.DEV && error.message}>
        <div style={{
          background: props.theme === 'dark' ? '#111827' : '#f9fafb',
          border: `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
          borderRadius: '6px',
          padding: '16px',
          margin: '0 0 24px 0',
          textAlign: 'left',
          maxWidth: '600px',
          marginLeft: 'auto',
          marginRight: 'auto',
        }}>
          <div style={{
            fontWeight: '600',
            fontSize: '14px',
            color: props.theme === 'dark' ? '#f9fafb' : '#111827',
            marginBottom: '8px',
          }}>
            错误详情:
          </div>
          <div style={{
            fontFamily: 'monospace',
            fontSize: '12px',
            color: props.theme === 'dark' ? '#ef4444' : '#dc2626',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-all',
          }}>
            {error.message}
          </div>
          <Show when={error.stack}>
            <details style={{ marginTop: '12px' }}>
              <summary style={{
                cursor: 'pointer',
                fontSize: '12px',
                color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
              }}>
                显示堆栈跟踪
              </summary>
              <pre style={{
                marginTop: '8px',
                fontSize: '10px',
                color: props.theme === 'dark' ? '#6b7280' : '#9ca3af',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-all',
                maxHeight: '200px',
                overflowY: 'auto',
              }}>
                {error.stack}
              </pre>
            </details>
          </Show>
        </div>
      </Show>

      {/* 操作按钮 */}
      <div style={{
        display: 'flex',
        gap: '12px',
        justifyContent: 'center',
        flexWrap: 'wrap',
      }}>
        <button
          onClick={reset}
          style={{
            padding: '12px 24px',
            borderRadius: '6px',
            border: 'none',
            background: '#3b82f6',
            color: 'white',
            cursor: 'pointer',
            fontWeight: '500',
            'transition': 'background-color 0.2s',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#2563eb';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = '#3b82f6';
          }}
        >
          重试
        </button>

        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '12px 24px',
            borderRadius: '6px',
            border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
            background: 'transparent',
            color: props.theme === 'dark' ? '#f9fafb' : '#374151',
            cursor: 'pointer',
            fontWeight: '500',
            'transition': 'all 0.2s',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = props.theme === 'dark' ? '#374151' : '#f3f4f6';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent';
          }}
        >
          刷新页面
        </button>

        <Show when={import.meta.env?.DEV}>
          <button
            onClick={() => {
              const errorText = `错误信息: ${error.message}\n\n堆栈跟踪:\n${error.stack}`;
              navigator.clipboard?.writeText(errorText);
              alert('错误信息已复制到剪贴板');
            }}
            style={{
              padding: '12px 24px',
              borderRadius: '6px',
              border: `1px solid ${props.theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
              background: 'transparent',
              color: props.theme === 'dark' ? '#f9fafb' : '#374151',
              cursor: 'pointer',
              fontWeight: '500',
              'transition': 'all 0.2s',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = props.theme === 'dark' ? '#374151' : '#f3f4f6';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'transparent';
            }}
          >
            复制错误
          </button>
        </Show>
      </div>

      {/* 时间戳 */}
      <div style={{
        marginTop: '24px',
        fontSize: '12px',
        color: props.theme === 'dark' ? '#6b7280' : '#9ca3af',
      }}>
        错误时间: {new Date(error.timestamp).toLocaleString()}
      </div>
    </div>
  );

  const currentState = state();

  if (currentState.hasError && currentState.error) {
    const fallback = props.fallback || DefaultFallback;
    return fallback(currentState.error, resetError);
  }

  try {
    return props.children;
  } catch (error) {
    if (error instanceof Error) {
      handleError(error);
    }
    if (currentState.hasError && currentState.error) {
      const fallback = props.fallback || DefaultFallback;
      return fallback(currentState.error, resetError);
    }
    throw error;
  }
}

// 导出错误信息接口
export type { ErrorInfo };

// 错误边界 Hook
export function createErrorHandler() {
  const [error, setError] = createSignal<ErrorInfo | null>(null);

  const handleError = (err: Error | string) => {
    const errorInfo: ErrorInfo = {
      message: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    setError(errorInfo);
    console.error('捕获到错误:', errorInfo);
  };

  const clearError = () => {
    setError(null);
  };

  return {
    error,
    hasError: () => error() !== null,
    handleError,
    clearError,
  };
}