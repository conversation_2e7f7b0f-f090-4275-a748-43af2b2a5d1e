import { createSignal, For } from 'solid-js'
import { css } from '../../styled-system/css'

export default function StrategyEditor() {
  const [selectedStrategy, setSelectedStrategy] = createSignal('momentum')
  const [,] = createSignal('editor')

  // 策略模板
  const strategies = [
    {
      id: 'momentum',
      name: '动量策略',
      description: '基于价格动量的交易策略',
      status: 'active',
      profit: '+12.5%',
      code: `# 动量策略示例
import pandas as pd
import numpy as np

def momentum_strategy(data, period=20):
    """
    动量策略：当价格突破N日高点时买入，跌破N日低点时卖出
    """
    data['high_n'] = data['high'].rolling(period).max()
    data['low_n'] = data['low'].rolling(period).min()

    # 生成交易信号
    data['signal'] = 0
    data.loc[data['close'] > data['high_n'].shift(1), 'signal'] = 1  # 买入
    data.loc[data['close'] < data['low_n'].shift(1), 'signal'] = -1  # 卖出

    return data

# 策略参数
PERIOD = 20
STOP_LOSS = 0.05  # 5% 止损
TAKE_PROFIT = 0.15  # 15% 止盈`
    },
    {
      id: 'mean_reversion',
      name: '均值回归策略',
      description: '基于价格均值回归的交易策略',
      status: 'testing',
      profit: '+8.3%',
      code: `# 均值回归策略示例
import pandas as pd
import numpy as np

def mean_reversion_strategy(data, period=20, std_dev=2):
    """
    均值回归策略：当价格偏离均线超过N个标准差时进行反向交易
    """
    data['ma'] = data['close'].rolling(period).mean()
    data['std'] = data['close'].rolling(period).std()
    data['upper_band'] = data['ma'] + std_dev * data['std']
    data['lower_band'] = data['ma'] - std_dev * data['std']

    # 生成交易信号
    data['signal'] = 0
    data.loc[data['close'] < data['lower_band'], 'signal'] = 1  # 买入
    data.loc[data['close'] > data['upper_band'], 'signal'] = -1  # 卖出

    return data`
    },
    {
      id: 'grid_trading',
      name: '网格交易策略',
      description: '在震荡市场中的网格交易策略',
      status: 'paused',
      profit: '+5.7%',
      code: `# 网格交易策略示例
import pandas as pd
import numpy as np

def grid_trading_strategy(data, grid_size=0.02, max_positions=5):
    """
    网格交易策略：在价格网格中进行买卖操作
    """
    base_price = data['close'].iloc[0]

    # 计算网格价位
    grid_levels = []
    for i in range(-max_positions, max_positions + 1):
        grid_levels.append(base_price * (1 + i * grid_size))

    data['signal'] = 0
    positions = 0

    for i in range(1, len(data)):
        current_price = data['close'].iloc[i]

        # 检查是否触及网格线
        for level in grid_levels:
            if abs(current_price - level) < base_price * 0.001:  # 容差
                if current_price < base_price and positions < max_positions:
                    data.iloc[i, data.columns.get_loc('signal')] = 1  # 买入
                    positions += 1
                elif current_price > base_price and positions > -max_positions:
                    data.iloc[i, data.columns.get_loc('signal')] = -1  # 卖出
                    positions -= 1

    return data`
    }
  ]

  const currentStrategy = () => strategies.find(s => s.id === selectedStrategy())

  // 策略性能数据
  const performanceData = [
    { label: '总收益率', value: '+12.5%', trend: 'up' },
    { label: '年化收益率', value: '+18.7%', trend: 'up' },
    { label: '最大回撤', value: '-3.2%', trend: 'down' },
    { label: '夏普比率', value: '1.85', trend: 'up' },
    { label: '胜率', value: '68.5%', trend: 'up' },
    { label: '交易次数', value: '156', trend: 'neutral' }
  ]

  return (
    <div class={css({
      display: 'flex',
      flexDirection: 'column',
      gap: '24px'
    })}>
      {/* 策略选择和状态 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '16px'
      })}>
        <For each={strategies}>
          {(strategy) => (
            <div
              class={css({
                bg: 'white',
                borderRadius: '12px',
                p: '20px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                border: selectedStrategy() === strategy.id ? '2px solid #1890ff ' : '1px solid #f0f0f0',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                _hover: {
                  boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
                  transform: 'translateY(-2px)'
                }
              })}
              onClick={() => setSelectedStrategy(strategy.id)}
            >
              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mb: '12px'
              })}>
                <h3 class={css({
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#262626',
                  margin: 0
                })}>
                  {strategy.name}
                </h3>
                <div class={css({
                  px: '8px',
                  py: '4px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: '500',
                  bg: strategy.status === 'active' ? '#f6ffed :
                      strategy.status === 'testing' ? '#fff7e6' : '#f5f5f5',
                  color: strategy.status === 'active' ? '#52c41a :
                         strategy.status === 'testing' ? '#fa8c16' : '#8c8c8c'
                })}>
                  {strategy.status === 'active' ? '运行中' :
                   strategy.status === 'testing' ? '测试中' : '已暂停'}
                </div>
              </div>

              <p class={css({
                fontSize: '14px',
                color: '#8c8c8c',
                mb: '12px',
                lineHeight: '1.4'
              })}>
                {strategy.description}
              </p>

              <div class={css({
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              })}>
                <span class={css({
                  fontSize: '12px',
                  color: '#8c8c8c'
                })}>
                  累计收益
                </span>
                <span class={css({
                  fontSize: '16px',
                  fontWeight: '700',
                  color: '#52c41a'
                })}>
                  {strategy.profit}
                </span>
              </div>
            </div>
          )}
        </For>
      </div>

      {/* 策略性能数据 */}
      <div class={css({
        bg: 'white',
        borderRadius: '16px',
        p: '24px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: '1px solid #f0f0f0'
      })}>
        <h3 class={css({
          fontSize: '18px',
          fontWeight: '600',
          color: '#262626',
          margin: 0,
          mb: '20px'
        })}>
          {currentStrategy()?.name} - 策略性能
        </h3>

        <div class={css({
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
          gap: '16px'
        })}>
          <For each={performanceData}>
            {(metric) => (
              <div class={css({
                textAlign: 'center',
                p: '16px',
                borderRadius: '8px',
                bg: '#fafafa',
                border: '1px solid #f0f0f0'
              })}>
                <div class={css({
                  fontSize: '12px',
                  color: '#8c8c8c',
                  mb: '8px'
                })}>
                  {metric.label}
                </div>
                <div class={css({
                  fontSize: '20px',
                  fontWeight: '700',
                  color: metric.trend === 'up' ? '#52c41a :
                         metric.trend === 'down' ? '#ff4d4f' : '#262626'
                })}>
                  {metric.value}
                </div>
              </div>
            )}
          </For>
        </div>
      </div>

      {/* 代码编辑器 */}
      <div class={css({
        bg: 'white',
        borderRadius: '16px',
        p: '24px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: '1px solid #f0f0f0'
      })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: '20px'
        })}>
          <h3 class={css({
            fontSize: '18px',
            fontWeight: '600',
            color: '#262626',
            margin: 0
          })}>
            策略代码
          </h3>

          <div class={css({
            display: 'flex',
            gap: '8px'
          })}>
            <button type="button" class={css({
              px: '16px',
              py: '8px',
              bg: '#52c41a',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '14px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              _hover: { bg: '#73d13d' }
            })}>
              保存策略
            </button>
            <button type="button" class={css({
              px: '16px',
              py: '8px',
              bg: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '14px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              _hover: { bg: '#40a9ff' }
            })}>
              运行回测
            </button>
          </div>
        </div>

        <div class={css({
          bg: '#1e1e1e',
          borderRadius: '8px',
          p: '16px',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '14px',
          lineHeight: '1.5',
          color: '#d4d4d4',
          overflow: 'auto',
          maxHeight: '500px'
        })}>
          <pre class={css({ margin: 0, whiteSpace: 'pre-wrap' })}>
            {currentStrategy()?.code}
          </pre>
        </div>
      </div>
    </div>
  )
}


