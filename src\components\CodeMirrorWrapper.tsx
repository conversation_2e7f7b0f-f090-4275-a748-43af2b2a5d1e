/**
 * CodeMirror 懒加载包装器组件
 * 按照方案文档实现懒加载和性能优化
 */

import { createSignal, createEffect, onMount, onCleanup, Show, Suspense, lazy } from 'solid-js';
import { EditorView, keymap } from '@codemirror/view';
import { EditorState, Extension } from '@codemirror/state';
import { basicSetup } from '@codemirror/basic-setup';
import { python } from '@codemirror/lang-python';
import { javascript } from '@codemirror/lang-javascript';
import { oneDark } from '@codemirror/theme-one-dark';
import { autocompletion, completionKeymap } from '@codemirror/autocomplete';
import { searchKeymap } from '@codemirror/search';
import { indentWithTab } from '@codemirror/commands';

interface CodeMirrorWrapperProps {
  value?: string;
  onChange?: (value: string) => void;
  language?: 'python' | 'javascript' | 'typescript';
  theme?: 'light' | 'dark';
  placeholder?: string;
  readOnly?: boolean;
  height?: string;
  width?: string;
  fontSize?: number;
  lineNumbers?: boolean;
  foldGutter?: boolean;
  autoFocus?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  onCursorChange?: (line: number, col: number) => void;
}

// 懒加载的CodeMirror核心组件
const LazyCodeMirror = lazy(() => Promise.resolve({
  default: (props: CodeMirrorWrapperProps) => {
    const [editor, setEditor] = createSignal<EditorView | null>(null);
    const [container, setContainer] = createSignal<HTMLDivElement | null>(null);
    const [isReady, setIsReady] = createSignal(false);
    const [error, setError] = createSignal<string | null>(null);

    // 获取语言扩展
    const getLanguageExtension = (): Extension => {
      switch (props.language) {
        case 'python :
          return python();
        case 'javascript :
        case 'typescript :
          return javascript();
        default:
          return javascript();
      }
    };

    // 获取主题扩展
    const getThemeExtension = (): Extension[] => {
      const extensions: Extension[] = [];
      
      if (props.theme === 'dark') {
        extensions.push(oneDark);
      }
      
      return extensions;
    };

    // 创建编辑器配置
    const createEditorConfig = (): Extension[] => {
      const extensions: Extension[] = [
        basicSetup,
        getLanguageExtension(),
        ...getThemeExtension(),
        autocompletion(),
        keymap.of([
          ...completionKeymap,
          ...searchKeymap,
          indentWithTab
        ]),
        EditorView.updateListener.of((update) => {
          if (update.docChanged && props.onChange) {
            props.onChange(update.state.doc.toString());
          }
          
          if (update.selectionSet && props.onCursorChange) {
            const selection = update.state.selection.main;
            const line = update.state.doc.lineAt(selection.head);
            props.onCursorChange(line.number, selection.head - line.from);
          }
        }),
        EditorView.focusChangeEffect.of((state, focusing) => {
          if (focusing && props.onFocus) {
            props.onFocus();
          } else if (!focusing && props.onBlur) {
            props.onBlur();
          }
          return null;
        })
      ];

      // 只读模式
      if (props.readOnly) {
        extensions.push(EditorState.readOnly.of(true));
      }

      // 自定义样式
      if (props.fontSize || props.height) {
        extensions.push(
          EditorView.theme({
            '&': {
              fontSize: props.fontSize ? `${props.fontSize}px` : '14px',
              height: props.height || 'auto'
            },
            '.cm-content : {
              minHeight: props.height || '200px'
            },
            '.cm-focused : {
              outline: 'none'
            }
          })
        );
      }

      return extensions;
    };

    // 初始化编辑器
    const initializeEditor = () => {
      const containerEl = container();
      if (!containerEl) return;

      try {
        const state = EditorState.create({
          doc: props.value || '',
          extensions: createEditorConfig()
        });

        const view = new EditorView({
          state,
          parent: containerEl
        });

        setEditor(view);
        setIsReady(true);

        if (props.autoFocus) {
          view.focus();
        }

        onCleanup(() => {
          view.destroy();
        });

      } catch (err) {
        console.error('CodeMirror初始化失败:', err);
        setError('编辑器初始化失败');
      }
    };

    // 更新编辑器内容
    const updateContent = (newValue: string) => {
      const editorInstance = editor();
      if (!editorInstance) return;

      const currentValue = editorInstance.state.doc.toString();
      if (currentValue !== newValue) {
        editorInstance.dispatch({
          changes: {
            from: 0,
            to: editorInstance.state.doc.length,
            insert: newValue
          }
        });
      }
    };

    // 重新配置编辑器
    const reconfigureEditor = () => {
      const editorInstance = editor();
      if (!editorInstance) return;

      editorInstance.dispatch({
        effects: EditorState.reconfigure.of(createEditorConfig())
      });
    };

    // 组件挂载时初始化
    onMount(() => {
      initializeEditor();
    });

    // 响应值变化
    createEffect(() => {
      if (props.value !== undefined) {
        updateContent(props.value);
      }
    });

    // 响应配置变化
    createEffect(() => {
      if (isReady()) {
        reconfigureEditor();
      }
    });

    return (
      <div style={{
        width: props.width || '100%',
        height: props.height || 'auto',
        border: `1px solid ${props.theme === 'dark' ? '#374151' : '#d1d5db'}`,
        'border-radius' : '8px',
        overflow: 'hidden',
        position: 'relative'
      }}>
        <Show when={error()}>
          <div style={{
            padding: '20px',
            'text-align' : 'center',
            color: '#ef4444',
            'background-color : props.theme === 'dark' ? '#1f2937' : '#f9fafb'
          }}>
            <div style={{ 'font-size' : '24px', 'margin-bottom' : '8px' }}>⚠️</div>
            {error()}
          </div>
        </Show>

        <Show when={!error()}>
          <div 
            ref={setContainer}
            style={{
              width: '100%',
              height: '100%',
              'min-height : props.height || '200px'
            }}
          />
        </Show>

        {/* 占位符 */}
        <Show when={!isReady() && !error() && props.placeholder}>
          <div style={{
            position: 'absolute',
            top: '12px',
            left: '12px',
            color: props.theme === 'dark' ? '#6b7280' : '#9ca3af',
            'pointer-events' : 'none',
            fontFamily: 'monospace',
            'font-size : `${props.fontSize || 14}px`
          }}>
            {props.placeholder}
          </div>
        </Show>
      </div>
    );
  }
}));

export default function CodeMirrorWrapper(props: CodeMirrorWrapperProps) {
  const [isLoading, setIsLoading] = createSignal(true);

  // 模拟加载延迟以展示懒加载效果
  onMount(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 100);

    onCleanup(() => {
      clearTimeout(timer);
    });
  });

  return (
    <div style={{
      width: props.width || '100%',
      height: props.height || 'auto',
      position: 'relative'
    }}>
      <Show when={isLoading()}>
        <div style={{
          width: '100%',
          height: props.height || '200px',
          'background-color : props.theme === 'dark' ? '#1f2937' : '#f9fafb',
          border: `1px solid ${props.theme === 'dark' ? '#374151' : '#d1d5db'}`,
          'border-radius' : '8px',
          display: 'flex',
          'align-items' : 'center',
          'justify-content' : 'center',
          color: props.theme === 'dark' ? '#9ca3af' : '#6b7280'
        }}>
          <div style={{
            display: 'flex',
            'flex-direction' : 'column',
            'align-items' : 'center',
            gap: '12px'
          }}>
            <div style={{
              width: '24px',
              height: '24px',
              border: '2px solid #e5e7eb',
              'border-top' : '2px solid #3b82f6',
              'border-radius' : '50%',
              animation: 'spin 1s linear infinite'
            }} />
            <span style={{ 'font-size' : '14px' }}>加载编辑器...</span>
          </div>
        </div>
      </Show>

      <Show when={!isLoading()}>
        <Suspense fallback={
          <div style={{
            width: '100%',
            height: props.height || '200px',
            'background-color : props.theme === 'dark' ? '#1f2937' : '#f9fafb',
            border: `1px solid ${props.theme === 'dark' ? '#374151' : '#d1d5db'}`,
            'border-radius' : '8px',
            display: 'flex',
            'align-items' : 'center',
            'justify-content' : 'center'
          }}>
            <div style={{
              width: '32px',
              height: '32px',
              border: '3px solid #e5e7eb',
              'border-top' : '3px solid #3b82f6',
              'border-radius' : '50%',
              animation: 'spin 1s linear infinite'
            }} />
          </div>
        }>
          <LazyCodeMirror {...props} />
        </Suspense>
      </Show>
    </div>
  );
}
