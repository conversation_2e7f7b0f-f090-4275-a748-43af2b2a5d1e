/**
 * 专业金融图表组件 - 基于 Lightweight Charts
 * 完全按照方案文档实现
 */

import { createSignal, createEffect, onCleanup, onMount, Show } from 'solid-js';
import { 
  createChart, 
  ColorType, 
  LineStyle,
  IChartApi, 
  ISeriesApi, 
  CandlestickData, 
  HistogramData,
  LineData,
  CrosshairMode,
  PriceScaleMode
} from'lightweight-charts';
import { marketEventBus, getSelectedSymbol } from'../stores/market';

export interface ChartData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

interface FinancialChartProps {
  symbol?: string;
  data?: ChartData[];
  height?: number;
  width?: string;
  theme?:'light' |'dark';
  showVolume?: boolean;
  showIndicators?: boolean;
  indicators?: string[];
  onPriceUpdate?: (price: number) => void;
  onCrosshairMove?: (price: number, time: number) => void;
}

export default function FinancialChart(props: FinancialChartProps) {
  const [chart, setChart] = createSignal<IChartApi | null>(null);
  const [candlestickSeries, setCandlestickSeries] = createSignal<ISeriesApi<'Candlestick'> | null>(null);
  const [volumeSeries, setVolumeSeries] = createSignal<ISeriesApi<'Histogram'> | null>(null);
  const [container, setContainer] = createSignal<HTMLDivElement | null>(null);
  const [isLoading, setIsLoading] = createSignal(false);
  const [error, setError] = createSignal<string | null>(null);

  // 主题配置
  const getChartOptions = () => ({
    layout: {
      background: { 
        type: ColorType.Solid, 
        color: props.theme ==='dark' ?'#111827' :'#ffffff' 
      },
      textColor: props.theme ==='dark' ?'#d1d5db':'#374151',
    },
    grid: {
      vertLines: { color: props.theme ==='dark' ?'#1f2937':'#e5e7eb' },
      horzLines: { color: props.theme ==='dark' ?'#1f2937':'#e5e7eb' },
    },
    crosshair: {
      mode: CrosshairMode.Normal,
    },
    rightPriceScale: {
      borderColor: props.theme ==='dark' ?'#374151' :'#d1d5db',
    },
    timeScale: {
      borderColor: props.theme ==='dark' ?'#374151' :'#d1d5db',
      timeVisible: true,
      secondsVisible: false,
    },
    width: container()?.clientWidth || 800,
    height: props.height || 500,
  });

  // 初始化图表
  const initializeChart = () => {
    const containerEl = container();
    if (!containerEl) return;

    try {
      // 清理现有图表
      const existingChart = chart();
      if (existingChart) {
        existingChart.remove();
      }

      const newChart = createChart(containerEl, getChartOptions());
      setChart(newChart);

      // 创建K线图系列
      const candlestick = newChart.addCandlestickSeries({
        upColor:'#26a69a',
        downColor:'#ef5350',
        borderVisible: false,
        wickUpColor:'#26a69a',
        wickDownColor:'#ef5350',
      });
      setCandlestickSeries(candlestick);

      // 创建成交量系列
      if (props.showVolume) {
        const volume = newChart.addHistogramSeries({
          color: props.theme ==='dark' ?'#4b5563':'#9ca3af',
          priceFormat: { type:'volume' },
          priceScaleId:'',
          scaleMargins: { top: 0.8, bottom: 0 },
        });
        setVolumeSeries(volume);
      }

      // 十字光标移动事件
      newChart.subscribeCrosshairMove((param) => {
        if (param.time && param.point) {
          const price = candlestick.coordinateToPrice(param.point.y);
          if (price !== null && props.onCrosshairMove) {
            props.onCrosshairMove(price, param.time as number);
          }
        }
      });

      // 窗口大小调整
      const resizeHandler = () => {
        if (containerEl) {
          newChart.applyOptions({ 
            width: containerEl.clientWidth,
            height: props.height || 500
          });
        }
      };

      window.addEventListener('resize', resizeHandler);
      
      onCleanup(() => {
        window.removeEventListener('resize', resizeHandler);
        newChart.remove();
      });

    } catch (err) {
      console.error('图表初始化失败:', err);
      setError('图表初始化失败');
    }
  };

  // 更新图表数据
  const updateChartData = () => {
    const candlestick = candlestickSeries();
    const volume = volumeSeries();
    
    if (!candlestick || !props.data) return;

    try {
      setIsLoading(true);
      setError(null);

      // 转换数据格式
      const candleData: CandlestickData[] = props.data.map(item => ({
        time: item.time,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
      }));

      const volumeData: HistogramData[] = props.data
        .filter(item => item.volume !== undefined)
        .map(item => ({
          time: item.time,
          value: item.volume!,
          color: item.close >= item.open ?'#26a69a':'#ef5350',
        }));

      // 设置数据
      candlestick.setData(candleData);
      if (volume && volumeData.length > 0) {
        volume.setData(volumeData);
      }

      // 自动缩放到数据范围
      const chartInstance = chart();
      if (chartInstance) {
        chartInstance.timeScale().fitContent();
      }

    } catch (err) {
      console.error('更新图表数据失败:', err);
      setError('更新图表数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 更新主题
  const updateTheme = () => {
    const chartInstance = chart();
    if (!chartInstance) return;

    chartInstance.applyOptions(getChartOptions());
  };

  // 监听实时价格更新
  createEffect(() => {
    const unsubscribe = marketEventBus.on('price-update', (data) => {
      if (data.symbol === props.symbol && props.onPriceUpdate) {
        props.onPriceUpdate(data.price);
      }
    });

    onCleanup(unsubscribe);
  });

  // 组件挂载时初始化
  onMount(() => {
    initializeChart();
  });

  // 响应数据变化
  createEffect(() => {
    if (props.data) {
      updateChartData();
    }
  });

  // 响应主题变化
  createEffect(() => {
    updateTheme();
  });

  // 响应容器大小变化
  createEffect(() => {
    const containerEl = container();
    if (containerEl) {
      const resizeObserver = new ResizeObserver(() => {
        const chartInstance = chart();
        if (chartInstance) {
          chartInstance.applyOptions({
            width: containerEl.clientWidth,
            height: props.height || 500
          });
        }
      });

      resizeObserver.observe(containerEl);
      
      onCleanup(() => {
        resizeObserver.disconnect();
      });
    }
  });

  return (
    <div style={{
      position:'relative',
      width: props.width ||''100%',
      height: `${props.height || 500}px`,
      borderRadius:'12px',
      overflow:'hidden',
      border: `1px solid ${props.theme ==='dark' ?'#374151' :'#e5e7eb'}`,
     'background-color': props.theme === dark' ?'#111827' :'#ffffff'
    }}>
      {/* 加载状态 */}
      <Show when={isLoading()}>
        <div style={{
          position:'absolute',
          top:''50%',
          left:''50%',
          transform:'translate(-50%, -50%)',
         'z-index : 10,
          color: props.theme === 'dark' ?'#d1d5db':'#374151'
        }}>
          <div style={{
            width:'32px',
            height:'32px',
            border:'3px solid #e5e7eb',
            borderTop:'3px solid #3b82f6',
            borderRadius:''50%',
            animation:'spin 1s linear infinite',
            margin:'0 auto 8px'
          }} />
          加载中...
        </div>
      </Show>

      {/* 错误状态 */}
      <Show when={error()}>
        <div style={{
          position:'absolute',
          top:''50%',
          left:''50%',
          transform:'translate(-50%, -50%)',
         'z-index': '10',
          textAlign: 'center',
          color:'#ef4444'
        }}>
          <div style={{ fontSize:'24px', marginBottom:'8px' }}>⚠️</div>
          {error()}
        </div>
      </Show>

      {/* 图表容器 */}
      <div 
        ref={setContainer} 
        style={{
          width:''100%',
          height:''100%',
          opacity: isLoading() || error() ?'0.3' :'1',
          transition:'opacity 0.3s ease'
        }}
      />

      {/* 图表工具栏 */}
      <div style={{
        position:'absolute',
        top:'12px',
        right:'12px',
        display:'flex',
        gap:'8px',
       'z-index : 5
      }}>
        <Show when={props.symbol}>
          <div style={{
            padding: '4px 8px',
           'background-color': props.theme === dark' ?'rgba(31, 41, 55, 0.8)' :'rgba(255, 255, 255, 0.8)',
            borderRadius:'6px',
            fontSize:'12px',
            fontWeight:'600',
            color: props.theme ==='dark' ?'#d1d5db':'#374151',
           'backdrop-filter' :'blur(4px)'
          }}>
            {props.symbol}
          </div>
        </Show>
      </div>
    </div>
  );
}
