!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@floating-ui/core")):"function"==typeof define&&define.amd?define(["exports","@floating-ui/core"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).FloatingUIDOM={},t.FloatingUICore)}(this,(function(t,e){"use strict";const n=Math.min,o=Math.max,i=Math.round,r=Math.floor,c=t=>({x:t,y:t});function l(t){return u(t)?(t.nodeName||"").toLowerCase():"#document"}function s(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function f(t){var e;return null==(e=(u(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function u(t){return t instanceof Node||t instanceof s(t).Node}function a(t){return t instanceof Element||t instanceof s(t).Element}function d(t){return t instanceof HTMLElement||t instanceof s(t).HTMLElement}function h(t){return"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof s(t).ShadowRoot)}function p(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=v(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(i)}function m(t){return["table","td","th"].includes(l(t))}function g(t){const e=w(),n=v(t);return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function y(t){let e=T(t);for(;d(e)&&!x(e);){if(g(e))return e;e=T(e)}return null}function w(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function x(t){return["html","body","#document"].includes(l(t))}function v(t){return s(t).getComputedStyle(t)}function b(t){return a(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function T(t){if("html"===l(t))return t;const e=t.assignedSlot||t.parentNode||h(t)&&t.host||f(t);return h(e)?e.host:e}function L(t){const e=T(t);return x(e)?t.ownerDocument?t.ownerDocument.body:t.body:d(e)&&p(e)?e:L(e)}function R(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const i=L(t),r=i===(null==(o=t.ownerDocument)?void 0:o.body),c=s(i);return r?e.concat(c,c.visualViewport||[],p(i)?i:[],c.frameElement&&n?R(c.frameElement):[]):e.concat(i,R(i,[],n))}function E(t){const e=v(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const r=d(t),c=r?t.offsetWidth:n,l=r?t.offsetHeight:o,s=i(n)!==c||i(o)!==l;return s&&(n=c,o=l),{width:n,height:o,$:s}}function C(t){return a(t)?t:t.contextElement}function O(t){const e=C(t);if(!d(e))return c(1);const n=e.getBoundingClientRect(),{width:o,height:r,$:l}=E(e);let s=(l?i(n.width):n.width)/o,f=(l?i(n.height):n.height)/r;return s&&Number.isFinite(s)||(s=1),f&&Number.isFinite(f)||(f=1),{x:s,y:f}}const S=c(0);function F(t){const e=s(t);return w()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:S}function D(t,n,o,i){void 0===n&&(n=!1),void 0===o&&(o=!1);const r=t.getBoundingClientRect(),l=C(t);let f=c(1);n&&(i?a(i)&&(f=O(i)):f=O(t));const u=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==s(t))&&e}(l,o,i)?F(l):c(0);let d=(r.left+u.x)/f.x,h=(r.top+u.y)/f.y,p=r.width/f.x,m=r.height/f.y;if(l){const t=s(l),e=i&&a(i)?s(i):i;let n=t.frameElement;for(;n&&i&&e!==t;){const t=O(n),e=n.getBoundingClientRect(),o=v(n),i=e.left+(n.clientLeft+parseFloat(o.paddingLeft))*t.x,r=e.top+(n.clientTop+parseFloat(o.paddingTop))*t.y;d*=t.x,h*=t.y,p*=t.x,m*=t.y,d+=i,h+=r,n=s(n).frameElement}}return e.rectToClientRect({width:p,height:m,x:d,y:h})}const P=[":popover-open",":modal"];function H(t){let e=!1,n=0,o=0;if(P.forEach((n=>{!function(n){try{e=e||t.matches(n)}catch(t){}}(n)})),e){const e=y(t);if(e){const t=e.getBoundingClientRect();n=t.x,o=t.y}}return[e,n,o]}function W(t){return D(f(t)).left+b(t).scrollLeft}function M(t,n,i){let r;if("viewport"===n)r=function(t,e){const n=s(t),o=f(t),i=n.visualViewport;let r=o.clientWidth,c=o.clientHeight,l=0,u=0;if(i){r=i.width,c=i.height;const t=w();(!t||t&&"fixed"===e)&&(l=i.offsetLeft,u=i.offsetTop)}return{width:r,height:c,x:l,y:u}}(t,i);else if("document"===n)r=function(t){const e=f(t),n=b(t),i=t.ownerDocument.body,r=o(e.scrollWidth,e.clientWidth,i.scrollWidth,i.clientWidth),c=o(e.scrollHeight,e.clientHeight,i.scrollHeight,i.clientHeight);let l=-n.scrollLeft+W(t);const s=-n.scrollTop;return"rtl"===v(i).direction&&(l+=o(e.clientWidth,i.clientWidth)-r),{width:r,height:c,x:l,y:s}}(f(t));else if(a(n))r=function(t,e){const n=D(t,!0,"fixed"===e),o=n.top+t.clientTop,i=n.left+t.clientLeft,r=d(t)?O(t):c(1);return{width:t.clientWidth*r.x,height:t.clientHeight*r.y,x:i*r.x,y:o*r.y}}(n,i);else{const e=F(t);r={...n,x:n.x-e.x,y:n.y-e.y}}return e.rectToClientRect(r)}function z(t,e){const n=T(t);return!(n===e||!a(n)||x(n))&&("fixed"===v(n).position||z(n,e))}function A(t,e,n,o){const i=d(e),r=f(e),s="fixed"===n,u=D(t,!0,s,e);let a={scrollLeft:0,scrollTop:0};const h=c(0);if(i||!i&&!s)if(("body"!==l(e)||p(r))&&(a=b(e)),i){const t=D(e,!0,s,e);h.x=t.x+e.clientLeft,h.y=t.y+e.clientTop}else r&&(h.x=W(r));let m=u.left+a.scrollLeft-h.x,g=u.top+a.scrollTop-h.y;const[y,w,x]=H(o);return y&&(m+=w,g+=x,i&&(m+=e.clientLeft,g+=e.clientTop)),{x:m,y:g,width:u.width,height:u.height}}function V(t,e){return d(t)&&"fixed"!==v(t).position?e?e(t):t.offsetParent:null}function B(t,e){const n=s(t);if(!d(t))return n;let o=V(t,e);for(;o&&m(o)&&"static"===v(o).position;)o=V(o,e);return o&&("html"===l(o)||"body"===l(o)&&"static"===v(o).position&&!g(o))?n:o||y(t)||n}const N={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const r=f(o),[s]=e?H(e.floating):[!1];if(o===r||s)return n;let u={scrollLeft:0,scrollTop:0},a=c(1);const h=c(0),m=d(o);if((m||!m&&"fixed"!==i)&&(("body"!==l(o)||p(r))&&(u=b(o)),d(o))){const t=D(o);a=O(o),h.x=t.x+o.clientLeft,h.y=t.y+o.clientTop}return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-u.scrollLeft*a.x+h.x,y:n.y*a.y-u.scrollTop*a.y+h.y}},getDocumentElement:f,getClippingRect:function(t){let{element:e,boundary:i,rootBoundary:r,strategy:c}=t;const s=[..."clippingAncestors"===i?function(t,e){const n=e.get(t);if(n)return n;let o=R(t,[],!1).filter((t=>a(t)&&"body"!==l(t))),i=null;const r="fixed"===v(t).position;let c=r?T(t):t;for(;a(c)&&!x(c);){const e=v(c),n=g(c);n||"fixed"!==e.position||(i=null),(r?!n&&!i:!n&&"static"===e.position&&i&&["absolute","fixed"].includes(i.position)||p(c)&&!n&&z(t,c))?o=o.filter((t=>t!==c)):i=e,c=T(c)}return e.set(t,o),o}(e,this._c):[].concat(i),r],f=s[0],u=s.reduce(((t,i)=>{const r=M(e,i,c);return t.top=o(r.top,t.top),t.right=n(r.right,t.right),t.bottom=n(r.bottom,t.bottom),t.left=o(r.left,t.left),t}),M(e,f,c));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:B,getElementRects:async function(t){const e=this.getOffsetParent||B,n=this.getDimensions;return{reference:A(t.reference,await e(t.floating),t.strategy,t.floating),floating:{x:0,y:0,...await n(t.floating)}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=E(t);return{width:e,height:n}},getScale:O,isElement:a,isRTL:function(t){return"rtl"===v(t).direction}};const I=e.autoPlacement,k=e.shift,j=e.flip,q=e.size,U=e.hide,X=e.arrow,Y=e.inline,$=e.limitShift;Object.defineProperty(t,"detectOverflow",{enumerable:!0,get:function(){return e.detectOverflow}}),Object.defineProperty(t,"offset",{enumerable:!0,get:function(){return e.offset}}),t.arrow=X,t.autoPlacement=I,t.autoUpdate=function(t,e,i,c){void 0===c&&(c={});const{ancestorScroll:l=!0,ancestorResize:s=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:a="function"==typeof IntersectionObserver,animationFrame:d=!1}=c,h=C(t),p=l||s?[...h?R(h):[],...R(e)]:[];p.forEach((t=>{l&&t.addEventListener("scroll",i,{passive:!0}),s&&t.addEventListener("resize",i)}));const m=h&&a?function(t,e){let i,c=null;const l=f(t);function s(){var t;clearTimeout(i),null==(t=c)||t.disconnect(),c=null}return function f(u,a){void 0===u&&(u=!1),void 0===a&&(a=1),s();const{left:d,top:h,width:p,height:m}=t.getBoundingClientRect();if(u||e(),!p||!m)return;const g={rootMargin:-r(h)+"px "+-r(l.clientWidth-(d+p))+"px "+-r(l.clientHeight-(h+m))+"px "+-r(d)+"px",threshold:o(0,n(1,a))||1};let y=!0;function w(t){const e=t[0].intersectionRatio;if(e!==a){if(!y)return f();e?f(!1,e):i=setTimeout((()=>{f(!1,1e-7)}),100)}y=!1}try{c=new IntersectionObserver(w,{...g,root:l.ownerDocument})}catch(t){c=new IntersectionObserver(w,g)}c.observe(t)}(!0),s}(h,i):null;let g,y=-1,w=null;u&&(w=new ResizeObserver((t=>{let[n]=t;n&&n.target===h&&w&&(w.unobserve(e),cancelAnimationFrame(y),y=requestAnimationFrame((()=>{var t;null==(t=w)||t.observe(e)}))),i()})),h&&!d&&w.observe(h),w.observe(e));let x=d?D(t):null;return d&&function e(){const n=D(t);!x||n.x===x.x&&n.y===x.y&&n.width===x.width&&n.height===x.height||i();x=n,g=requestAnimationFrame(e)}(),i(),()=>{var t;p.forEach((t=>{l&&t.removeEventListener("scroll",i),s&&t.removeEventListener("resize",i)})),null==m||m(),null==(t=w)||t.disconnect(),w=null,d&&cancelAnimationFrame(g)}},t.computePosition=(t,n,o)=>{const i=new Map,r={platform:N,...o},c={...r.platform,_c:i};return e.computePosition(t,n,{...r,platform:c})},t.flip=j,t.getOverflowAncestors=R,t.hide=U,t.inline=Y,t.limitShift=$,t.platform=N,t.shift=k,t.size=q}));
