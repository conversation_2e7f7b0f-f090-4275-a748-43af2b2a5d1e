import { A } from '@solidjs/router';
import { Show } from 'solid-js';
import { useTheme } from '../context/ThemeContext';
import { userStore } from '../stores';

export default function Header() {
  const { theme, toggleTheme } = useTheme();
  const isAuthenticated = () => userStore.state.isAuthenticated;
  const user = () => userStore.state.user;

  const navItems = [
    { path: '/', label: '仪表盘' },
    { path: '/market', label: '市场数据' },
    { path: '/strategy', label: '策略编辑' },
    { path: '/backtest', label: '回测分析' },
    { path: '/login', label: '登录演示' },
  ];

  return (
    <header style={{
      background: "white",
      'border-bottom: "1px solid #e5e7eb",
      'box-shadow : "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
      position: "sticky",
      top: 0,
      'z-index: 50
    }}>
      <div style={{
        'max-width : "1280px",
        margin: "0 auto",
        padding: "12px 16px",
        display: "flex",
        'align-items: "center",
        'justify-content : "space-between"
      }}>
        {/* Logo */}
        <A href="/" style={{
          display: "flex",
          'align-items : "center",
          gap: "12px",
          'font-size: "1.25rem",
          'font-weight : "bold",
          color: "#3b82f6",
          'text-decoration : "none"
        }}>
          <div style={{
            width: "32px",
            height: "32px",
            background: "linear-gradient(to bottom right, #3b82f6, #1d4ed8)",
            'border-radius : "8px",
            display: "flex",
            'align-items: "center",
            'justify-content : "center",
            color: "white",
            'font-size: "14px",
            'font-weight : "bold"
          }}>
            Q
          </div>
          量化交易平台
        </A>

        {/* 导航菜单 */}
        <nav style={{
          display: "flex",
          'align-items : "center",
          gap: "4px"
        }}>
          {navItems.map((item) => (
            <A
              href={item.path}
              style={{
                display: "flex",
                'align-items : "center",
                gap: "8px",
                padding: "8px 12px",
                'border-radius: "6px",
                'font-size: "14px",
                'font-weight : "500",
                color: "#6b7280",
                'text-decoration : "none",
                transition: "all 0.2s ease"
              }}
            >
              {item.label}
            </A>
          ))}
        </nav>

        {/* 右侧操作区 */}
        <div style={{
          display: "flex",
          'align-items : "center",
          gap: "12px"
        }}>
          {/* 连接状态指示器 */}
          <div style={{
            display: "flex",
            'align-items : "center",
            gap: "8px",
            padding: "4px 8px",
            'border-radius : "9999px",
            background: "#dcfce7",
            color: "#15803d",
            'font-size: "12px",
            'font-weight : "500"
          }}>
            <div style={{
              width: "8px",
              height: "8px",
              background: "#22c55e",
              'border-radius : "50%"
            }} />
            已连接
          </div>

          {/* 用户信息 */}
          <Show when={isAuthenticated()} fallback={
            <A href="/login" style={{
              padding: "8px 16px",
              'border-radius : "6px",
              background: "#3b82f6",
              color: "white",
              'text-decoration: "none",
              'font-size: "14px",
              'font-weight : "500",
              transition: "all 0.2s ease"
            }}>
              登录
            </A>
          }>
            <div style={{
              display: "flex",
              'align-items : "center",
              gap: "8px",
              padding: "4px 8px",
              'border-radius : "8px",
              background: "#f3f4f6",
              border: "1px solid #e5e7eb"
            }}>
              <img
                src={user()?.avatar}
                alt="用户头像"
                style={{
                  width: "24px",
                  height: "24px",
                  'border-radius: "50%"
                }}
              />
              <span style={{
                'font-size: "14px",
                'font-weight : "500",
                color: "#374151"
              }}>
                {user()?.nickname}
              </span>
              <button
                style={{
                  background: "none",
                  border: "none",
                  color: "#6b7280",
                  cursor: "pointer",
                  padding: "4px",
                  'border-radius: "4px",
                  'font-size: "12px"
                }}
                onClick={async () => {
                  if (confirm('确定要退出登录吗？')) {
                    await userStore.logout();
                    window.location.href = '/login';
                  }
                }}
                title="退出登录"
              >
                🚪
              </button>
            </div>
          </Show>

          {/* 主题切换按钮 */}
          <button
            type="button"
            onClick={toggleTheme}
            style={{
              padding: "8px",
              'border-radius : "6px",
              color: "#6b7280",
              background: "transparent",
              border: "none",
              cursor: "pointer",
              transition: "all 0.2s ease"
            }}
            title={theme() === 'light' ? '切换到深色模式' ' : '切换到浅色模式'}
          >
            {theme() === 'light' ? '🌙' ' : '☀️'}
          </button>
        </div>
      </div>
    </header>
  );
}
