import { createSignal } from 'solid-js';
import { css } from '../../styled-system/css';
import ParameterOptimizer from '../components/ParameterOptimizer';
import { OptimizationResult } from '../utils/parameter-optimization';

export default function ParameterOptimization() {
  const [, setOptimizationResults] = createSignal<OptimizationResult[]>([]);
  const [, setShowResults] = createSignal(false);

  const handleOptimizationComplete = (results: OptimizationResult[]) => {
    setOptimizationResults(results);
    setShowResults(true);
  };

  return (
    <div class={css({
      display: 'flex',
      flexDirection: 'column',
      gap: '24px',
      maxWidth: '1200px',
      margin: '0 auto',
      p: '24px'
    })}>
      {/* 页面标题 */}
      <div class={css({
        textAlign: 'center',
        mb: '20px'
      })}>
        <h1 class={css({
          fontSize: '28px',
          fontWeight: '700',
          color: '#262626',
          margin: 0,
          mb: '8px'
        })}>
          🔧 参数优化
        </h1>
        <p class={css({
          fontSize: '16px',
          color: '#8c8c8c',
          margin: 0
        })}>
          使用先进的优化算法找到策略的最佳参数组合
        </p>
      </div>

      {/* 优化方法介绍 */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '16px',
        mb: '24px'
      })}>
        <div class={css({
          bg: 'white',
          borderRadius: '12px',
          p: '20px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          border: '1px solid #f0f0f0'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            mb: '12px'
          })}>
            <div class={css({
              width: '40px',
              height: '40px',
              bg: '#1890ff',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: '12px'
            })}>
              <span class={css({
                fontSize: '20px',
                color: 'white'
              })}>
                🔍
              </span>
            </div>
            <h3 class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#262626',
              margin: 0
            })}>
              网格搜索
            </h3>
          </div>
          <p class={css({
            fontSize: '14px',
            color: '#8c8c8c',
            lineHeight: '1.5',
            margin: 0
          })}>
            系统性地搜索参数空间的每个组合，保证找到全局最优解，适合参数较少的情况。
          </p>
        </div>

        <div class={css({
          bg: 'white',
          borderRadius: '12px',
          p: '20px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          border: '1px solid #f0f0f0'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            mb: '12px'
          })}>
            <div class={css({
              width: '40px',
              height: '40px',
              bg: '#52c41a',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: '12px'
            })}>
              <span class={css({
                fontSize: '20px',
                color: 'white'
              })}>
                🧬
              </span>
            </div>
            <h3 class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#262626',
              margin: 0
            })}>
              遗传算法
            </h3>
          </div>
          <p class={css({
            fontSize: '14px',
            color: '#8c8c8c',
            lineHeight: '1.5',
            margin: 0
          })}>
            模拟生物进化过程，通过选择、交叉和变异操作寻找最优解，适合复杂的多参数优化。
          </p>
        </div>

        <div class={css({
          bg: 'white',
          borderRadius: '12px',
          p: '20px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          border: '1px solid #f0f0f0'
        })}>
          <div class={css({
            display: 'flex',
            alignItems: 'center',
            mb: '12px'
          })}>
            <div class={css({
              width: '40px',
              height: '40px',
              bg: '#722ed1',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: '12px'
            })}>
              <span class={css({
                fontSize: '20px',
                color: 'white'
              })}>
                🎯
              </span>
            </div>
            <h3 class={css({
              fontSize: '16px',
              fontWeight: '600',
              color: '#262626',
              margin: 0
            })}>
              贝叶斯优化
            </h3>
          </div>
          <p class={css({
            fontSize: '14px',
            color: '#8c8c8c',
            lineHeight: '1.5',
            margin: 0
          })}>
            基于概率模型的智能搜索，能够在较少的评估次数下找到最优解，适合计算成本高的场景。
          </p>
        </div>
      </div>

      {/* 参数优化器 */}
      <ParameterOptimizer onOptimizationComplete={handleOptimizationComplete} />

      {/* 优化建议 */}
      <div class={css({
        bg: 'white',
        borderRadius: '12px',
        p: '24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      })}>
        <h3 class={css({
          fontSize: '18px',
          fontWeight: '600',
          color: '#262626',
          margin: 0,
          mb: '16px'
        })}>
          💡 优化建议
        </h3>
        
        <div class={css({
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '16px'
        })}>
          <div class={css({
            p: '16px',
            bg: '#f6ffed',
            borderRadius: '8px',
            border: '1px solid #b7eb8f'
          })}>
            <h4 class={css({
              fontSize: '14px',
              fontWeight: '600',
              color: '#52c41a',
              margin: 0,
              mb: '8px'
            })}>
              参数设置
            </h4>
            <ul class={css({
              fontSize: '12px',
              color: '#8c8c8c',
              lineHeight: '1.5',
              margin: 0,
              paddingLeft: '16px'
            })}>
              <li>合理设置参数范围，避免过大或过小</li>
              <li>考虑参数之间的约束关系</li>
              <li>使用合适的步长，平衡精度和效率</li>
            </ul>
          </div>

          <div class={css({
            p: '16px',
            bg: '#fff7e6',
            borderRadius: '8px',
            border: '1px solid #ffd591'
          })}>
            <h4 class={css({
              fontSize: '14px',
              fontWeight: '600',
              color: '#fa8c16',
              margin: 0,
              mb: '8px'
            })}>
              目标选择
            </h4>
            <ul class={css({
              fontSize: '12px',
              color: '#8c8c8c',
              lineHeight: '1.5',
              margin: 0,
              paddingLeft: '16px'
            })}>
              <li>夏普比率：综合考虑收益和风险</li>
              <li>卡尔马比率：关注回撤控制</li>
              <li>总收益率：追求绝对收益</li>
            </ul>
          </div>

          <div class={css({
            p: '16px',
            bg: '#f0f5ff',
            borderRadius: '8px',
            border: '1px solid #adc6ff'
          })}>
            <h4 class={css({
              fontSize: '14px',
              fontWeight: '600',
              color: '#1890ff',
              margin: 0,
              mb: '8px'
            })}>
              方法选择
            </h4>
            <ul class={css({
              fontSize: '12px',
              color: '#8c8c8c',
              lineHeight: '1.5',
              margin: 0,
              paddingLeft: '16px'
            })}>
              <li>参数少（≤4个）：使用网格搜索</li>
              <li>参数多（5-10个）：使用遗传算法</li>
              <li>计算成本高：使用贝叶斯优化</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 注意事项 */}
      <div class={css({
        bg: '#fff2f0',
        borderRadius: '8px',
        p: '16px',
        border: '1px solid #ffccc7'
      })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          mb: '8px'
        })}>
          <span class={css({
            fontSize: '16px',
            mr: '8px'
          })}>
            ⚠️
          </span>
          <h4 class={css({
            fontSize: '14px',
            fontWeight: '600',
            color: '#ff4d4f',
            margin: 0
          })}>
            注意事项
          </h4>
        </div>
        <p class={css({
          fontSize: '12px',
          color: '#8c8c8c',
          lineHeight: '1.5',
          margin: 0
        })}>
          参数优化存在过拟合风险，建议使用样本外数据验证优化结果。优化后的参数可能在历史数据上表现良好，但在未来市场中未必有效。
          建议结合多个时间段的数据进行验证，并定期重新优化参数。
        </p>
      </div>
    </div>
  );
}
