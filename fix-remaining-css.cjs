const fs = require('fs');
const path = require('path');

// 需要额外检查的文件
const file = 'src/pages/StrategyEditor.tsx';
const filePath = path.join(__dirname, file);

try {
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复 whiteSpace': 'pre-wrap' 为 whiteSpace: 'pre-wrap'
  content = content.replace(/whiteSpace':\s*'/g, "whiteSpace: '");
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 StrategyEditor.tsx 中的 whiteSpace 属性');
} catch (error) {
  console.error('❌ 修复失败:', error.message);
}
