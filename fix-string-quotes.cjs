const fs = require('fs');
const path = require('path');

// 修复字符串引号问题的模式
const stringQuotePatterns = [
  // 修复缺少闭合引号的字符串 (在三元运算符中)
  { 
    from: /(\?\s*')([^']*)\s*:\s*'([^']*)',/g, 
    to: "? '$2' : '$3'," 
  },
  
  // 修复对象键缺少闭合引号
  { 
    from: /(\s+)(['"\/][^'"]*)\s*:\s*'([^']*)',/g, 
    to: "$1'$2': '$3'," 
  },
  
  // 修复特定的字符串模式
  { from: /'2px solid #1890ff\s*:\s*'1px solid #f0f0f0'/g, to: "'2px solid #1890ff' : '1px solid #f0f0f0'" },
  { from: /'#52c41a\s*:\s*'#ff4d4f'/g, to: "'#52c41a' : '#ff4d4f'" },
  { from: /'64px\s*:\s*'240px'/g, to: "'64px' : '240px'" },
  
  // 修复对象键名缺少引号的问题
  { from: /(\s+)\/([^'"\s:]+)\s*:\s*'([^']*)',/g, to: "$1'/$2': '$3'," },
  
  // 修复三元运算符中的字符串
  { from: /\?\s*'([^']*)\s*:\s*'([^']*)',/g, to: "? '$1' : '$2'," },
  
  // 修复更复杂的三元运算符模式
  { from: /\?\s*'([^']*)\s*:\s*'([^']*)',/g, to: "? '$1' : '$2'," }
];

function fixStringQuotes(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let fixCount = 0;
    let originalContent = content;
    
    // 应用所有修复模式
    stringQuotePatterns.forEach(pattern => {
      const beforeContent = content;
      content = content.replace(pattern.from, pattern.to);
      if (content !== beforeContent) {
        fixCount++;
      }
    });
    
    // 手动修复一些特定的模式
    const manualFixes = [
      // 修复三元运算符中缺少引号的问题
      {
        from: /\?\s*'([^']*)\s*:\s*'([^']*)',/g,
        to: "? '$1' : '$2',"
      },
      // 修复对象键缺少引号
      {
        from: /(\s+)([a-zA-Z\/][^'"\s:]*)\s*:\s*'([^']*)',/g,
        to: "$1'$2': '$3',"
      }
    ];
    
    manualFixes.forEach(fix => {
      const beforeContent = content;
      content = content.replace(fix.from, fix.to);
      if (content !== beforeContent) {
        fixCount++;
      }
    });
    
    if (fixCount > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${path.basename(filePath)}: ${fixCount} string quote errors`);
      return true;
    } else {
      console.log(`✓  ${path.basename(filePath)}: no string quote errors found`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function processFiles(filePaths) {
  let totalFixed = 0;
  
  for (const filePath of filePaths) {
    if (fs.existsSync(filePath)) {
      if (fixStringQuotes(filePath)) {
        totalFixed++;
      }
    } else {
      console.warn(`⚠️  File not found: ${filePath}`);
    }
  }
  
  return totalFixed;
}

// 主函数
function main() {
  console.log('🔧 Fixing string quote syntax errors...\n');
  
  // 需要修复的文件列表
  const filesToFix = [
    'src/components/AdvancedLayout.tsx',
    'src/pages/Dashboard.tsx',
    'src/pages/StrategyEditor.tsx',
    'src/pages/BacktestAnalysis.tsx',
    'src/pages/ParameterOptimization.tsx',
    'src/pages/MarketData.tsx',
    'src/components/BacktestCharts.tsx',
    'src/components/ParameterOptimizer.tsx',
    'src/components/MarketNews.tsx',
    'src/components/PortfolioOverview.tsx'
  ];
  
  const totalFixed = processFiles(filesToFix);
  
  console.log('\n' + '='.repeat(50));
  console.log(`✨ Complete! Fixed ${totalFixed} files with string quote errors.`);
  
  if (totalFixed > 0) {
    console.log('\n📋 Next steps:');
    console.log('1. Run "npm run build" to test production build');
    console.log('2. Run "npm run dev" to test development server');
  }
}

main();
