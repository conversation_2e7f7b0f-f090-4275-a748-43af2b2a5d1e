# @lezer/lr

[ [**WEBSITE**](http://lezer.codemirror.net) | [**ISSUES**](https://github.com/lezer-parser/lezer/issues) | [**FORUM**](https://discuss.codemirror.net/c/lezer) | [**CHANGELOG**](https://github.com/lezer-parser/lr/blob/master/CHANGELOG.md) ]

<PERSON><PERSON> ("reader" in Dutch, pronounced pretty much as laser) is an
incremental GLR parser intended for use in an editor or similar
system, which needs to keep a representation of the program current
during changes and in the face of syntax errors.

It prioritizes speed and compactness (both of parser table files and
of syntax tree) over having a highly usable parse tree—trees nodes are
just blobs with a start, end, tag, and set of child nodes, with no
further labeling of child nodes or extra metadata.

This package contains the run-time LR parser library. It consumes
parsers generated by
[@lezer/generator](https://github.com/lezer-parser/generator).

The parser programming interface is documented on [the
website](https://lezer.codemirror.net/docs/ref/#lr).

The code is licensed under an MIT license.

This project was hugely inspired by
[tree-sitter](http://tree-sitter.github.io/tree-sitter/).
