import {
	FormControlDescription as Description,
	FormControlErrorMessage as ErrorMessage,
	type FormControlDescriptionCommonProps as SelectDescriptionCommonProps,
	type FormControlDescriptionOptions as SelectDescriptionOptions,
	type FormControlDescriptionProps as SelectDescriptionProps,
	type FormControlDescriptionRenderProps as SelectDescriptionRenderProps,
	type FormControlErrorMessageCommonProps as SelectErrorMessageCommonProps,
	type FormControlErrorMessageOptions as SelectErrorMessageOptions,
	type FormControlErrorMessageProps as SelectErrorMessageProps,
	type FormControlErrorMessageRenderProps as SelectErrorMessageRenderProps,
} from "../form-control";
import {
	Item,
	ItemDescription,
	ItemIndicator,
	ItemLabel,
	Section,
	type ListboxItemCommonProps as SelectItemCommonProps,
	type ListboxItemDescriptionCommonProps as SelectItemDescriptionCommonProps,
	type ListboxItemDescriptionOptions as SelectItemDescriptionOptions,
	type ListboxItemDescriptionProps as SelectItemDescriptionProps,
	type ListboxItemDescriptionRenderProps as SelectItemDescriptionRenderProps,
	type ListboxItemIndicatorCommonProps as SelectItemIndicatorCommonProps,
	type ListboxItemIndicatorOptions as SelectItemIndicatorOptions,
	type ListboxItemIndicatorProps as SelectItemIndicatorProps,
	type ListboxItemIndicatorRenderProps as SelectItemIndicatorRenderProps,
	type ListboxItemLabelCommonProps as SelectItemLabelCommonProps,
	type ListboxItemLabelOptions as SelectItemLabelOptions,
	type ListboxItemLabelProps as SelectItemLabelProps,
	type ListboxItemLabelRenderProps as SelectItemLabelRenderProps,
	type ListboxItemOptions as SelectItemOptions,
	type ListboxItemProps as SelectItemProps,
	type ListboxItemRenderProps as SelectItemRenderProps,
	type ListboxSectionProps as SelectSectionProps,
} from "../listbox";
import {
	Arrow,
	type PopperArrowCommonProps as SelectArrowCommonProps,
	type PopperArrowOptions as SelectArrowOptions,
	type PopperArrowProps as SelectArrowProps,
	type PopperArrowRenderProps as SelectArrowRenderProps,
} from "../popper";
import type {
	SelectBaseItemComponentProps as SelectRootItemComponentProps,
	SelectBaseSectionComponentProps as SelectRootSectionComponentProps,
} from "./select-base";
import {
	SelectContent as Content,
	type SelectContentCommonProps,
	type SelectContentOptions,
	type SelectContentProps,
	type SelectContentRenderProps,
} from "./select-content";
import {
	SelectHiddenSelect as HiddenSelect,
	type SelectHiddenSelectProps,
} from "./select-hidden-select";
import {
	SelectIcon as Icon,
	type SelectIconCommonProps,
	type SelectIconOptions,
	type SelectIconProps,
	type SelectIconRenderProps,
} from "./select-icon";
import {
	SelectLabel as Label,
	type SelectLabelCommonProps,
	type SelectLabelOptions,
	type SelectLabelProps,
	type SelectLabelRenderProps,
} from "./select-label";
import {
	SelectListbox as Listbox,
	type SelectListboxCommonProps,
	type SelectListboxOptions,
	type SelectListboxProps,
	type SelectListboxRenderProps,
} from "./select-listbox";
import {
	SelectPortal as Portal,
	type SelectPortalProps,
} from "./select-portal";
import {
	SelectRoot as Root,
	type SelectMultipleSelectionOptions,
	type SelectRootCommonProps,
	type SelectRootOptions,
	type SelectRootProps,
	type SelectRootRenderProps,
	type SelectSingleSelectionOptions,
} from "./select-root";
import {
	type SelectTriggerCommonProps,
	type SelectTriggerOptions,
	type SelectTriggerProps,
	type SelectTriggerRenderProps,
	SelectTrigger as Trigger,
} from "./select-trigger";
import {
	type SelectValueCommonProps,
	type SelectValueOptions,
	type SelectValueProps,
	type SelectValueRenderProps,
	SelectValue as Value,
} from "./select-value";

export type {
	SelectArrowOptions,
	SelectArrowCommonProps,
	SelectArrowRenderProps,
	SelectArrowProps,
	SelectContentOptions,
	SelectContentCommonProps,
	SelectContentRenderProps,
	SelectContentProps,
	SelectDescriptionProps,
	SelectErrorMessageOptions,
	SelectErrorMessageCommonProps,
	SelectErrorMessageRenderProps,
	SelectErrorMessageProps,
	SelectHiddenSelectProps,
	SelectIconOptions,
	SelectIconCommonProps,
	SelectIconRenderProps,
	SelectIconProps,
	SelectDescriptionOptions,
	SelectDescriptionCommonProps,
	SelectDescriptionRenderProps,
	SelectItemDescriptionOptions,
	SelectItemDescriptionCommonProps,
	SelectItemDescriptionRenderProps,
	SelectItemDescriptionProps,
	SelectItemIndicatorOptions,
	SelectItemIndicatorCommonProps,
	SelectItemIndicatorRenderProps,
	SelectItemIndicatorProps,
	SelectItemLabelOptions,
	SelectItemLabelCommonProps,
	SelectItemLabelRenderProps,
	SelectLabelOptions,
	SelectLabelCommonProps,
	SelectLabelRenderProps,
	SelectItemLabelProps,
	SelectItemOptions,
	SelectItemCommonProps,
	SelectItemRenderProps,
	SelectItemProps,
	SelectLabelProps,
	SelectListboxOptions,
	SelectListboxCommonProps,
	SelectListboxRenderProps,
	SelectListboxProps,
	SelectMultipleSelectionOptions,
	SelectPortalProps,
	SelectRootItemComponentProps,
	SelectRootOptions,
	SelectRootCommonProps,
	SelectRootRenderProps,
	SelectRootProps,
	SelectRootSectionComponentProps,
	SelectSectionProps,
	SelectSingleSelectionOptions,
	SelectTriggerOptions,
	SelectTriggerCommonProps,
	SelectTriggerRenderProps,
	SelectTriggerProps,
	SelectValueOptions,
	SelectValueCommonProps,
	SelectValueRenderProps,
	SelectValueProps,
};

export {
	Arrow,
	Content,
	Description,
	ErrorMessage,
	HiddenSelect,
	Icon,
	Item,
	ItemDescription,
	ItemIndicator,
	ItemLabel,
	Label,
	Listbox,
	Portal,
	Root,
	Section,
	Trigger,
	Value,
};

export const Select = Object.assign(Root, {
	Arrow,
	Content,
	Description,
	ErrorMessage,
	HiddenSelect,
	Icon,
	Item,
	ItemDescription,
	ItemIndicator,
	ItemLabel,
	Label,
	Listbox,
	Portal,
	Section,
	Trigger,
	Value,
});

/**
 * API will most probably change
 */
export { useSelectContext, type SelectContextValue } from "./select-context";
