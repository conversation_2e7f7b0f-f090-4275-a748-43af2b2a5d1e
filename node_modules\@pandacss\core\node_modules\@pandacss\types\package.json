{"name": "@pandacss/types", "version": "0.39.2", "description": "The types for css panda", "main": "dist/index.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "exports": {".": {"source": "./src/index.ts", "types": "./dist/index.d.ts", "default": "./dist/index.d.ts"}, "./package.json": "./package.json"}, "sideEffects": false, "homepage": "https://panda-css.com", "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/panda.git", "directory": "packages/types"}, "publishConfig": {"access": "public"}, "files": ["dist"], "devDependencies": {"csstype": "3.1.3", "microdiff": "1.3.2", "ncp": "2.0.0", "pkg-types": "1.0.3", "ts-morph": "21.0.1", "@pandacss/extractor": "0.39.2"}, "scripts": {"dev": "tsx scripts/watch.ts", "build": "cross-env PANDA_BUILD=1 tsx scripts/postbuild.ts && tsx scripts/build.ts"}}