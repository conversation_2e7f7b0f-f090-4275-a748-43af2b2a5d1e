import {
  SEP,
  formatPath,
  getReferences,
  isTokenReference,
  isValidToken,
  mergeConfigs,
  serializeTokenValue
} from "./chunk-F5ESIWB7.mjs";
import {
  diffConfigs
} from "./chunk-D3CL3VYD.mjs";
import {
  resolveTsPathPattern
} from "./chunk-RPIVZP2I.mjs";

// src/bundle-config.ts
import { logger } from "@pandacss/logger";
import { PandaError as PandaError2 } from "@pandacss/shared";
import { bundleNRequire } from "bundle-n-require";

// src/find-config.ts
import { PandaError } from "@pandacss/shared";
import findUp from "escalade/sync";
import { resolve } from "path";

// src/is-panda-config.ts
var configName = "panda";
var pandaConfigFiles = /* @__PURE__ */ new Set([
  `${configName}.config.ts`,
  `${configName}.config.js`,
  `${configName}.config.mts`,
  `${configName}.config.mjs`,
  `${configName}.config.cts`,
  `${configName}.config.cjs`
]);
var isPandaConfig = (file) => pandaConfigFiles.has(file);

// src/find-config.ts
function findConfig(options) {
  const { cwd = process.cwd(), file } = options;
  if (file) {
    return resolve(cwd, file);
  }
  const configPath = findUp(cwd, (_dir, paths) => paths.find(isPandaConfig));
  if (!configPath) {
    throw new PandaError(
      "CONFIG_NOT_FOUND",
      `Cannot find config file \`panda.config.{ts,js,mjs,mts}\`. Did you forget to run \`panda init\`?`
    );
  }
  return configPath;
}

// src/bundle-config.ts
async function bundle(filepath, cwd) {
  const { mod, dependencies } = await bundleNRequire(filepath, {
    cwd,
    interopDefault: true
  });
  const config = mod?.default ?? mod;
  return {
    config,
    dependencies
  };
}
async function bundleConfig(options) {
  const { cwd, file } = options;
  const filePath = findConfig({ cwd, file });
  logger.debug("config:path", filePath);
  const result = await bundle(filePath, cwd);
  if (typeof result.config !== "object") {
    throw new PandaError2("CONFIG_ERROR", `\u{1F4A5} Config must export or return an object.`);
  }
  result.config.outdir ??= "styled-system";
  result.config.validation ??= "warn";
  return {
    ...result,
    config: result.config,
    path: filePath
  };
}

// src/get-mod-deps.ts
import fs from "fs";
import path from "path";

// src/ts-config-paths.ts
import { resolve as resolve2 } from "path";
function convertTsPathsToRegexes(paths, baseUrl) {
  const sortedPatterns = Object.keys(paths).sort((a, b) => getPrefixLength(b) - getPrefixLength(a));
  const resolved = [];
  for (let pattern of sortedPatterns) {
    const relativePaths = paths[pattern];
    pattern = escapeStringRegexp(pattern).replace(/\*/g, "(.+)");
    resolved.push({
      pattern: new RegExp("^" + pattern + "$"),
      paths: relativePaths.map((relativePath) => resolve2(baseUrl, relativePath))
    });
  }
  return resolved;
}
function getPrefixLength(pattern) {
  const prefixLength = pattern.indexOf("*");
  return pattern.substr(0, prefixLength).length;
}
function escapeStringRegexp(string) {
  return string.replace(/[|\\{}()[\]^$+?.]/g, "\\$&").replace(/-/g, "\\x2d");
}

// src/get-mod-deps.ts
import ts from "typescript";
var jsExtensions = [".js", ".cjs", ".mjs"];
var jsResolutionOrder = ["", ".js", ".cjs", ".mjs", ".ts", ".cts", ".mts", ".jsx", ".tsx"];
var tsResolutionOrder = ["", ".ts", ".cts", ".mts", ".tsx", ".js", ".cjs", ".mjs", ".jsx"];
function resolveWithExtension(file, extensions) {
  for (const ext of extensions) {
    const full = `${file}${ext}`;
    if (fs.existsSync(full) && fs.statSync(full).isFile()) {
      return full;
    }
  }
  for (const ext of extensions) {
    const full = `${file}/index${ext}`;
    if (fs.existsSync(full)) {
      return full;
    }
  }
  return null;
}
var importRegex = /import[\s\S]*?['"](.{3,}?)['"]/gi;
var importFromRegex = /import[\s\S]*from[\s\S]*?['"](.{3,}?)['"]/gi;
var requireRegex = /require\(['"`](.+)['"`]\)/gi;
var exportRegex = /export[\s\S]*from[\s\S]*?['"](.{3,}?)['"]/gi;
function getDeps(opts, fromAlias) {
  const { filename, seen } = opts;
  const { moduleResolution: _, ...compilerOptions } = opts.compilerOptions ?? {};
  const absoluteFile = resolveWithExtension(
    path.resolve(opts.cwd, filename),
    jsExtensions.includes(opts.ext) ? jsResolutionOrder : tsResolutionOrder
  );
  if (absoluteFile === null)
    return;
  if (fromAlias) {
    opts.foundModuleAliases.set(fromAlias, absoluteFile);
  }
  if (seen.size > 1 && seen.has(absoluteFile))
    return;
  seen.add(absoluteFile);
  const contents = fs.readFileSync(absoluteFile, "utf-8");
  const fileDeps = [
    ...contents.matchAll(importRegex),
    ...contents.matchAll(importFromRegex),
    ...contents.matchAll(requireRegex),
    ...contents.matchAll(exportRegex)
  ];
  if (!fileDeps.length)
    return;
  const nextOpts = {
    // Resolve new base for new imports/requires
    cwd: path.dirname(absoluteFile),
    ext: path.extname(absoluteFile),
    seen,
    baseUrl: opts.baseUrl,
    pathMappings: opts.pathMappings,
    foundModuleAliases: opts.foundModuleAliases
  };
  fileDeps.forEach((match) => {
    const mod = match[1];
    if (mod[0] === ".") {
      getDeps(Object.assign({}, nextOpts, { filename: mod }));
      return;
    }
    try {
      const found = ts.resolveModuleName(mod, absoluteFile, compilerOptions, ts.sys).resolvedModule;
      if (found && found.extension.endsWith("ts")) {
        getDeps(Object.assign({}, nextOpts, { filename: found.resolvedFileName }));
        return;
      }
      if (!opts.pathMappings)
        return;
      const filename2 = resolveTsPathPattern(opts.pathMappings, mod);
      if (!filename2)
        return;
      getDeps(Object.assign({}, nextOpts, { filename: filename2 }), mod);
    } catch (err) {
    }
  });
}
function getConfigDependencies(filePath, tsOptions = { pathMappings: [] }, compilerOptions) {
  if (filePath === null)
    return { deps: /* @__PURE__ */ new Set(), aliases: /* @__PURE__ */ new Map() };
  const foundModuleAliases = /* @__PURE__ */ new Map();
  const deps = /* @__PURE__ */ new Set();
  deps.add(filePath);
  getDeps({
    filename: filePath,
    ext: path.extname(filePath),
    cwd: path.dirname(filePath),
    seen: deps,
    baseUrl: tsOptions.baseUrl,
    pathMappings: tsOptions.pathMappings ?? [],
    foundModuleAliases,
    compilerOptions
  });
  return { deps, aliases: foundModuleAliases };
}

// src/get-resolved-config.ts
async function getResolvedConfig(config, cwd) {
  const presets = config.presets ?? [];
  const configs = [];
  while (presets.length > 0) {
    const preset = await presets.shift();
    if (typeof preset === "string") {
      const presetModule = await bundle(preset, cwd);
      configs.unshift(await presetModule.config);
      presets.unshift(...await presetModule.config.presets ?? []);
    } else {
      configs.unshift(preset);
      presets.unshift(...preset.presets ?? []);
    }
  }
  configs.unshift(config);
  return mergeConfigs(configs);
}

// src/resolve-config.ts
import { logger as logger3 } from "@pandacss/logger";
import { omit, parseJson, stringifyJson, traverse } from "@pandacss/shared";

// src/bundled-preset.ts
import { preset as presetBase } from "@pandacss/preset-base";
import { preset as presetPanda } from "@pandacss/preset-panda";
var bundledPresets = {
  "@pandacss/preset-base": presetBase,
  "@pandacss/preset-panda": presetPanda,
  "@pandacss/dev/presets": presetPanda
};
var bundledPresetsNames = Object.keys(bundledPresets);
var isBundledPreset = (preset) => bundledPresetsNames.includes(preset);
var getBundledPreset = (preset) => {
  return typeof preset === "string" && isBundledPreset(preset) ? bundledPresets[preset] : void 0;
};

// src/validate-config.ts
import { logger as logger2 } from "@pandacss/logger";
import { PandaError as PandaError3 } from "@pandacss/shared";

// src/validation/validate-artifact.ts
var validateArtifactNames = (names, addError) => {
  names.recipes.forEach((recipeName) => {
    if (names.slotRecipes.has(recipeName)) {
      addError("recipes", `This recipe name is already used in \`theme.slotRecipes\`: ${recipeName}`);
    }
    if (names.patterns.has(recipeName)) {
      addError("recipes", `This recipe name is already used in \`patterns\`: \`${recipeName}\``);
    }
  });
  names.slotRecipes.forEach((recipeName) => {
    if (names.patterns.has(recipeName)) {
      addError("recipes", `This recipe name is already used in \`patterns\`: ${recipeName}`);
    }
  });
};

// src/validation/validate-breakpoints.ts
import { getUnit } from "@pandacss/shared";
var validateBreakpoints = (breakpoints, addError) => {
  if (!breakpoints)
    return;
  const units = /* @__PURE__ */ new Set();
  const values = Object.values(breakpoints);
  for (const value of values) {
    const unit = getUnit(value) ?? "px";
    units.add(unit);
  }
  if (units.size > 1) {
    addError("breakpoints", `All breakpoints must use the same unit: \`${values.join(", ")}\``);
  }
};

// src/validation/validate-condition.ts
import { isString } from "@pandacss/shared";
var validateConditions = (conditions, addError) => {
  if (!conditions)
    return;
  Object.values(conditions).forEach((condition) => {
    if (isString(condition)) {
      if (!condition.startsWith("@") && !condition.includes("&")) {
        addError("conditions", `Selectors should contain the \`&\` character: \`${condition}\``);
      }
      return;
    }
    condition.forEach((c) => {
      if (!c.startsWith("@") && !c.includes("&")) {
        addError("conditions", `Selectors should contain the \`&\` character: \`${c}\``);
      }
    });
  });
};

// src/validation/validate-patterns.ts
var validatePatterns = (patterns, names) => {
  if (!patterns)
    return;
  Object.keys(patterns).forEach((patternName) => {
    names.patterns.add(patternName);
  });
};

// src/validation/validate-recipes.ts
var validateRecipes = (options) => {
  const {
    config: { theme },
    artifacts
  } = options;
  if (!theme)
    return;
  if (theme.recipes) {
    Object.keys(theme.recipes).forEach((recipeName) => {
      artifacts.recipes.add(recipeName);
    });
  }
  if (theme.slotRecipes) {
    Object.keys(theme.slotRecipes).forEach((recipeName) => {
      artifacts.slotRecipes.add(recipeName);
    });
  }
  return artifacts;
};

// src/validation/validate-tokens.ts
import { isObject, walkObject } from "@pandacss/shared";

// src/validation/validate-token-references.ts
var validateTokenReferences = (props) => {
  const { valueAtPath, refsByPath, addError, typeByPath } = props;
  refsByPath.forEach((refs, path2) => {
    if (refs.has(path2)) {
      addError("tokens", `Self token reference: \`${path2}\``);
    }
    const stack = [path2];
    while (stack.length > 0) {
      let currentPath = stack.pop();
      if (currentPath.includes("/")) {
        const [tokenPath] = currentPath.split("/");
        currentPath = tokenPath;
      }
      const value = valueAtPath.get(currentPath);
      if (!value) {
        const configKey = typeByPath.get(path2);
        addError("tokens", `Missing token: \`${currentPath}\` used in \`theme.${configKey}.${path2}\``);
      }
      if (isTokenReference(value) && !refsByPath.has(value)) {
        addError("tokens", `Unknown token reference: \`${currentPath}\` used in \`${value}\``);
      }
      const deps = refsByPath.get(currentPath);
      if (!deps)
        continue;
      for (const transitiveDep of deps) {
        if (path2 === transitiveDep) {
          addError(
            "tokens",
            `Circular token reference: \`${transitiveDep}\` -> \`${currentPath}\` -> ... -> \`${path2}\``
          );
          break;
        }
        stack.push(transitiveDep);
      }
    }
  });
};

// src/validation/validate-tokens.ts
var validateTokens = (options) => {
  const {
    config: { theme },
    tokens,
    addError
  } = options;
  if (!theme)
    return;
  const { tokenNames, semanticTokenNames, valueAtPath, refsByPath, typeByPath } = tokens;
  if (theme.tokens) {
    const tokenPaths = /* @__PURE__ */ new Set();
    walkObject(
      theme.tokens,
      (value, paths) => {
        const path2 = paths.join(SEP);
        tokenNames.add(path2);
        tokenPaths.add(path2);
        valueAtPath.set(path2, value);
        if (path2.includes("DEFAULT")) {
          valueAtPath.set(path2.replace(SEP + "DEFAULT", ""), value);
        }
      },
      {
        stop: isValidToken
      }
    );
    tokenPaths.forEach((path2) => {
      const itemValue = valueAtPath.get(path2);
      const formattedPath = formatPath(path2);
      typeByPath.set(formattedPath, "tokens");
      if (!isValidToken(itemValue)) {
        addError("tokens", `Token must contain 'value': \`theme.tokens.${formattedPath}\``);
        return;
      }
      if (path2.includes(" ")) {
        addError("tokens", `Token key must not contain spaces: \`theme.tokens.${formattedPath}\``);
        return;
      }
      const valueStr = serializeTokenValue(itemValue.value || itemValue);
      if (isTokenReference(valueStr)) {
        refsByPath.set(formattedPath, /* @__PURE__ */ new Set([]));
      }
      const references = refsByPath.get(formattedPath);
      if (!references)
        return;
      getReferences(valueStr).forEach((reference) => {
        references.add(reference);
      });
    });
  }
  if (theme.semanticTokens) {
    const tokenPaths = /* @__PURE__ */ new Set();
    walkObject(
      theme.semanticTokens,
      (value, paths) => {
        const path2 = paths.join(SEP);
        semanticTokenNames.add(path2);
        valueAtPath.set(path2, value);
        tokenPaths.add(path2);
        if (path2.includes("DEFAULT")) {
          valueAtPath.set(path2.replace(SEP + "DEFAULT", ""), value);
        }
        if (!isValidToken(value))
          return;
        walkObject(value, (itemValue, paths2) => {
          const valuePath = paths2.join(SEP);
          const formattedPath = formatPath(path2);
          typeByPath.set(formattedPath, "semanticTokens");
          const fullPath = formattedPath + "." + paths2.join(SEP);
          if (valuePath.includes("value" + SEP + "value")) {
            addError("tokens", `You used \`value\` twice resulting in an invalid token \`theme.tokens.${fullPath}\``);
          }
          const valueStr = serializeTokenValue(itemValue.value || itemValue);
          if (isTokenReference(valueStr)) {
            if (!refsByPath.has(formattedPath)) {
              refsByPath.set(formattedPath, /* @__PURE__ */ new Set());
            }
            const references = refsByPath.get(formattedPath);
            if (!references)
              return;
            getReferences(valueStr).forEach((reference) => {
              references.add(reference);
            });
          }
        });
      },
      {
        stop: isValidToken
      }
    );
    tokenPaths.forEach((path2) => {
      const formattedPath = formatPath(path2);
      const value = valueAtPath.get(path2);
      if (path2.includes(" ")) {
        addError("tokens", `Token key must not contain spaces: \`theme.tokens.${formattedPath}\``);
        return;
      }
      if (!isObject(value) && !path2.includes("value")) {
        addError("tokens", `Token must contain 'value': \`theme.semanticTokens.${formattedPath}\``);
      }
    });
  }
  validateTokenReferences({ valueAtPath, refsByPath, addError, typeByPath });
};

// src/validate-config.ts
var validateConfig = (config) => {
  if (config.validation === "none")
    return;
  const warnings = /* @__PURE__ */ new Set();
  const addError = (scope, message) => {
    warnings.add(`[${scope}] ` + message);
  };
  validateBreakpoints(config.theme?.breakpoints, addError);
  validateConditions(config.conditions, addError);
  const artifacts = {
    recipes: /* @__PURE__ */ new Set(),
    slotRecipes: /* @__PURE__ */ new Set(),
    patterns: /* @__PURE__ */ new Set()
  };
  const tokens = {
    tokenNames: /* @__PURE__ */ new Set(),
    semanticTokenNames: /* @__PURE__ */ new Set(),
    valueAtPath: /* @__PURE__ */ new Map(),
    refsByPath: /* @__PURE__ */ new Map(),
    typeByPath: /* @__PURE__ */ new Map()
  };
  if (config.theme) {
    validateTokens({ config, tokens, addError });
    validateRecipes({ config, tokens, artifacts, addError });
  }
  validatePatterns(config.patterns, artifacts);
  validateArtifactNames(artifacts, addError);
  if (warnings.size) {
    const errors = `\u26A0\uFE0F Invalid config:
${Array.from(warnings).map((err) => "- " + err).join("\n")}
`;
    if (config.validation === "error") {
      throw new PandaError3("CONFIG_ERROR", errors);
    }
    logger2.warn("config", errors);
    return warnings;
  }
};

// src/resolve-config.ts
var hookUtils = {
  omit,
  traverse
};
async function resolveConfig(result, cwd) {
  const presets = /* @__PURE__ */ new Set();
  if (!result.config.eject) {
    presets.add(presetBase);
  }
  if (result.config.presets) {
    result.config.presets.forEach((preset) => {
      presets.add(getBundledPreset(preset) ?? preset);
    });
  } else if (!result.config.eject) {
    presets.add(presetPanda);
  }
  result.config.presets = Array.from(presets);
  const mergedConfig = await getResolvedConfig(result.config, cwd);
  const hooks = mergedConfig.hooks ?? {};
  if (mergedConfig.logLevel) {
    logger3.level = mergedConfig.logLevel;
  }
  validateConfig(mergedConfig);
  const loadConfigResult = {
    ...result,
    config: mergedConfig
  };
  if (hooks["config:resolved"]) {
    const result2 = await hooks["config:resolved"]({
      config: loadConfigResult.config,
      path: loadConfigResult.path,
      dependencies: loadConfigResult.dependencies,
      utils: hookUtils
    });
    if (result2) {
      loadConfigResult.config = result2;
    }
  }
  const serialized = stringifyJson(loadConfigResult.config);
  const deserialize = () => parseJson(serialized);
  return { ...loadConfigResult, serialized, deserialize, hooks };
}

// src/load-config.ts
async function loadConfig(options) {
  const result = await bundleConfig(options);
  return resolveConfig(result, options.cwd);
}
export {
  bundleConfig,
  convertTsPathsToRegexes,
  diffConfigs,
  findConfig,
  getConfigDependencies,
  getResolvedConfig,
  loadConfig,
  mergeConfigs,
  resolveConfig
};
