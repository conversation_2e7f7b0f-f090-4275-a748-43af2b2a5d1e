/**
 * 用户状态管理
 */
import { createSignal, createEffect } from 'solid-js'
import { userApi, apiManager, type UserData, type UserPreferences } from '../api'

// 用户状态
export interface UserState {
  user: UserData | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  loginAttempts: number
  lastLoginTime: number | null
}

// 创建响应式状态
const [userState, setUserState] = createSignal<UserState>({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  loginAttempts: 0,
  lastLoginTime: null
})

/**
 * 用户Store
 */
export class UserStore {
  // 获取状态
  get state() {
    return userState()
  }

  // 设置加载状态
  setLoading(loading: boolean) {
    setUserState(prev => ({ ...prev, isLoading: loading }))
  }

  // 设置错误
  setError(error: string | null) {
    setUserState(prev => ({ ...prev, error }))
  }

  // 设置用户信息
  setUser(user: UserData | null) {
    setUserState(prev => ({
      ...prev,
      user,
      isAuthenticated: !!user,
      error: null
    }))
  }

  // 增加登录尝试次数
  incrementLoginAttempts() {
    setUserState(prev => ({
      ...prev,
      loginAttempts: prev.loginAttempts + 1
    }))
  }

  // 重置登录尝试次数
  resetLoginAttempts() {
    setUserState(prev => ({
      ...prev,
      loginAttempts: 0
    }))
  }

  // 设置最后登录时间
  setLastLoginTime(time: number | null) {
    setUserState(prev => ({
      ...prev,
      lastLoginTime: time
    }))
  }

  // 用户登录
  async login(credentials: { username: string; password: string; rememberMe?: boolean }) {
    try {
      this.setLoading(true)
      this.setError(null)

      const response = await userApi.login(credentials)
      
      this.setUser(response.user)
      this.resetLoginAttempts()
      this.setLastLoginTime(Date.now())

      // 初始化API管理器
      await apiManager.onUserLogin()

      return response
    } catch (error) {
      console.error('登录失败:', error)
      this.incrementLoginAttempts()
      
      const errorMessage = error instanceof Error ? error.message : '登录失败'
      this.setError(errorMessage)
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 用户注册
  async register(data: {
    username: string
    email: string
    password: string
    confirmPassword: string
    captchaToken?: string
    inviteCode?: string
  }) {
    try {
      this.setLoading(true)
      this.setError(null)

      const user = await userApi.register(data)
      
      // 注册成功后不自动登录，需要用户手动登录
      return user
    } catch (error) {
      console.error('注册失败:', error)
      const errorMessage = error instanceof Error ? error.message : '注册失败'
      this.setError(errorMessage)
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 用户登出
  async logout() {
    try {
      this.setLoading(true)
      
      await userApi.logout()
      await apiManager.onUserLogout()
      
      this.setUser(null)
      this.resetLoginAttempts()
      this.setLastLoginTime(null)
    } catch (error) {
      console.error('登出失败:', error)
      // 即使API调用失败，也要清除本地状态
      this.setUser(null)
      this.resetLoginAttempts()
      this.setLastLoginTime(null)
    } finally {
      this.setLoading(false)
    }
  }

  // 获取用户信息
  async fetchUserInfo() {
    try {
      this.setLoading(true)
      const user = await userApi.getUserInfo()
      this.setUser(user)
      return user
    } catch (error) {
      console.error('获取用户信息失败:', error)
      
      // 如果是认证错误，清除用户状态
      if (error instanceof Error && error.message.includes('401')) {
        this.setUser(null)
        await apiManager.onUserLogout()
      }
      
      this.setError(error instanceof Error ? error.message : '获取用户信息失败')
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 更新用户资料
  async updateProfile(data: {
    nickname?: string
    email?: string
    phone?: string
    avatar?: string
    profile?: any
  }) {
    try {
      this.setLoading(true)
      const updatedUser = await userApi.updateProfile(data)
      this.setUser(updatedUser)
      return updatedUser
    } catch (error) {
      console.error('更新用户资料失败:', error)
      this.setError(error instanceof Error ? error.message : '更新用户资料失败')
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 修改密码
  async changePassword(data: {
    oldPassword: string
    newPassword: string
    confirmPassword: string
  }) {
    try {
      this.setLoading(true)
      await userApi.changePassword(data)
      this.setError(null)
    } catch (error) {
      console.error('修改密码失败:', error)
      this.setError(error instanceof Error ? error.message : '修改密码失败')
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 重置密码
  async resetPassword(email: string) {
    try {
      this.setLoading(true)
      await userApi.resetPassword({ email })
      this.setError(null)
    } catch (error) {
      console.error('重置密码失败:', error)
      this.setError(error instanceof Error ? error.message : '重置密码失败')
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 上传头像
  async uploadAvatar(file: File) {
    try {
      this.setLoading(true)
      const result = await userApi.uploadAvatar(file)
      
      // 更新用户头像
      if (this.state.user) {
        const updatedUser = { ...this.state.user, avatar: result.url }
        this.setUser(updatedUser)
      }
      
      return result
    } catch (error) {
      console.error('上传头像失败:', error)
      this.setError(error instanceof Error ? error.message : '上传头像失败')
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 更新用户偏好设置
  async updatePreferences(preferences: Partial<UserPreferences>) {
    try {
      this.setLoading(true)
      const updatedPreferences = await userApi.updatePreferences(preferences)
      
      // 更新用户偏好
      if (this.state.user) {
        const updatedUser = { 
          ...this.state.user, 
          preferences: updatedPreferences 
        }
        this.setUser(updatedUser)
      }
      
      return updatedPreferences
    } catch (error) {
      console.error('更新用户偏好失败:', error)
      this.setError(error instanceof Error ? error.message : '更新用户偏好失败')
      throw error
    } finally {
      this.setLoading(false)
    }
  }

  // 检查认证状态
  async checkAuthStatus() {
    const token = typeof window !== 'undefined' ? localStorage.getItem('access_token') : null
    
    if (!token) {
      this.setUser(null)
      return false
    }

    try {
      await this.fetchUserInfo()
      return true
    } catch (error) {
      // token无效，清除本地存储
      if (typeof window !== 'undefined') {
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user_info')
      }
      this.setUser(null)
      return false
    }
  }

  // 刷新token
  async refreshToken() {
    try {
      const response = await userApi.refreshToken()
      return response
    } catch (error) {
      console.error('刷新token失败:', error)
      // token刷新失败，需要重新登录
      await this.logout()
      throw error
    }
  }

  // 获取用户权限
  getUserPermissions(): string[] {
    const user = this.state.user
    if (!user) return []

    const permissions: string[] = []
    
    // 基础权限
    permissions.push('read', 'profile', 'update': 'profile)
    
    // 根据角色添加权限
    switch (user.role) {
      case 'admin':
        permissions.push('admin', *)
        break
      case 'vip':
        permissions.push('vip', *, 'create': 'strategy', 'run': 'backtest)
        break
      case 'user':
        permissions.push('create', 'strategy', 'run': backtest)
        break
    }
    
    return permissions
  }

  // 检查用户权限
  hasPermission(permission: string): boolean {
    const permissions = this.getUserPermissions()
    return permissions.includes(permission) || permissions.includes('admin': *)
  }
}

// 创建store实例
export const userStore = new UserStore()

// 导出响应式状态访问器
export const useUserState = () => userState()
export const useUser = () => userState().user
export const useIsAuthenticated = () => userState().isAuthenticated
export const useUserLoading = () => userState().isLoading
export const useUserError = () => userState().error
export const useLoginAttempts = () => userState().loginAttempts

// 导出便捷函数
export const isLoggedIn = () => userState().isAuthenticated
export const getCurrentUser = () => userState().user
export const getUserRole = () => userState().user?.role
export const getUserPreferences = () => userState().user?.preferences
export const hasPermission = (permission: string) => userStore.hasPermission(permission)

// 初始化效果
createEffect(() => {
  // 检查认证状态
  userStore.checkAuthStatus()
})

// 导出默认实例
export default userStore
