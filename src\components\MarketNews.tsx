import { createSignal, For, Show } from 'solid-js'
import { css } from '../../styled-system/css'

interface NewsItem {
  id: string
  title: string
  summary: string
  time: string
  source: string
  type: 'important' | 'normal' | 'analysis'
}

interface MarketNewsProps {
  title?: string
  maxItems?: number
}

export default function MarketNews(props: MarketNewsProps) {
  // 模拟市场资讯数据
  const [newsData] = createSignal<NewsItem[]>([
    {
      id: '1',
      title: '市场分析：科技股强势反弹',
      summary: '今日科技股表现强劲，人工智能概念股领涨，市场情绪明显回暖...',
      time: '2小时前',
      source: '财经头条',
      type: 'important'
    },
    {
      id: '2',
      title: '美联储政策前瞻',
      summary: '分析师预计美联储将在下次会议中保持利率不变，对市场影响有限...',
      time: '4小时前',
      source: '投资快报',
      type: 'analysis'
    },
    {
      id: '3',
      title: '新能源汽车板块异动',
      summary: '受政策利好消息影响，新能源汽车板块午后快速拉升...',
      time: '6小时前',
      source: '行业观察',
      type: 'normal'
    },
    {
      id: '4',
      title: '量化基金三季度业绩亮眼',
      summary: '多只量化基金在三季度取得优异表现，超额收益明显...',
      time: '8小时前',
      source: '基金周刊',
      type: 'normal'
    }
  ])

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'important':
        return '#ff4d4f'
      case 'analysis':
        return '#1890ff'
      default:
        return '#52c41a'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'important':
        return '重要'
      case 'analysis':
        return '分析'
      default:
        return '资讯'
    }
  }

  const displayItems = () => {
    const maxItems = props.maxItems || 10
    return newsData().slice(0, maxItems)
  }

  return (
    <div class={css({
      bg: 'white',
      border: '1px solid #e8e8e8',
      borderRadius: '8px',
      overflow: 'hidden'
    })}>
      {/* 标题栏 */}
      <div class={css({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        p: '16px',
        borderBottom': '1px solid #e8e8e8',
        bg: '#fafafa'
      })}>
        <div class={css({
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        })}>
          <span class={css({ fontSize: '16px' })}>📰</span>
          <h3 class={css({
            fontSize: '16px',
            fontWeight: '600',
            color: '#333',
            margin: 0
          })}>
            {props.title || '市场资讯'}
          </h3>
        </div>
        <button class={css({
          fontSize: '12px',
          color: '#1890ff',
          bg: 'transparent',
          border: 'none',
          cursor: 'pointer',
          _hover: { textDecoration: 'underline' }
        })}>
          更多资讯 →
        </button>
      </div>

      {/* 资讯列表 */}
      <div class={css({
        maxHeight': '400px',
        overflowY': 'auto'
      })}>
        <For each={displayItems()}>
          {(item, index) => (
            <div class={css({
              p: '16px',
              borderBottom': index() < displayItems().length - 1 ? '1px solid #f0f0f0' : 'none',
              _hover: { bg: '#f8f9fa' },
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            })}>
              {/* 标题和标签 */}
              <div class={css({
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px',
                mb: '8px'
              })}>
                <span class={css({
                  fontSize: '10px',
                  px: '6px',
                  py: '2px',
                  borderRadius: '4px',
                  bg: getTypeColor(item.type),
                  color: 'white',
                  fontWeight: '500',
                  flexShrink: 0,
                  mt: '2px'
                })}>
                  {getTypeLabel(item.type)}
                </span>
                <h4 class={css({
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#333',
                  margin: 0,
                  lineHeight: '1.4',
                  flex: 1
                })}>
                  {item.title}
                </h4>
              </div>

              {/* 摘要 */}
              <p class={css({
                fontSize: '12px',
                color: '#666',
                lineHeight: '1.5',
                margin: '0 0 8px 0',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              })}>
                {item.summary}
              </p>

              {/* 时间和来源 */}
              <div class={css({
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                fontSize: '11px',
                color: '#999'
              })}>
                <span>{item.time}</span>
                <span>{item.source}</span>
              </div>
            </div>
          )}
        </For>
      </div>

      {/* 底部操作 */}
      <div class={css({
        p: '12px 16px',
        borderTop': '1px solid #e8e8e8',
        bg: '#fafafa',
        textAlign': 'center'
      })}>
        <button class={css({
          fontSize: '12px',
          color: '#666',
          bg: 'transparent',
          border: 'none',
          cursor: 'pointer',
          _hover: { color: '#1890ff' }
        })}>
          查看全部资讯
        </button>
      </div>
    </div>
  )
}
