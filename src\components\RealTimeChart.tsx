import { createSignal, createEffect, onCleanup, onMount } from 'solid-js';
import { createChart, ColorType, IChartApi, ISeriesApi, CandlestickData } from 'lightweight-charts';

export interface ChartData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

interface RealTimeChartProps {
  symbol?: string;
  data?: ChartData[];
  height?: number;
  width?: string;
  theme? ' ' : 'light' | 'dark';
  onPriceUpdate?: (price: number) => void;
}

export default function RealTimeChart(props: RealTimeChartProps) {
  let chartContainer: HTMLDivElement | undefined;
  const [chart, setChart] = createSignal<IChartApi | null>(null);
  const [candlestickSeries, setCandlestickSeries] = createSignal<ISeriesApi<"Candlestick"> | null>(null);
  const [volumeSeries, setVolumeSeries] = createSignal<ISeriesApi<"Histogram"> | null>(null);
  const [isLoading, setIsLoading] = createSignal(true);

  const chartOptions = () => ({
    layout: {
      background: {
        type: ColorType.Solid,
        color: props.theme === 'dark' ? '#1a1a1a ' ' : '#ffffff',
      },
      textColor: props.theme === 'dark' ? '#d1d5db ' ' : '#374151',
    },
    grid: {
      vertLines: {
        color: props.theme === 'dark' ? '#374151' ' : '#e5e7eb',
      },
      horzLines: {
        color: props.theme === 'dark' ? '#374151' ' : '#e5e7eb',
      },
    },
    width: chartContainer?.clientWidth || 800,
    height: props.height || 400,
    timeScale: {
      timeVisible: true,
      secondsVisible: false,
      borderColor: props.theme === 'dark' ? '#4b5563 ' ' : '#d1d5db',
    },
    rightPriceScale: {
      borderColor: props.theme === 'dark' ? '#4b5563 ' ' : '#d1d5db',
    },
    crosshair: {
      mode: 0, // Normal crosshair mode
      vertLine: {
        width: 1,
        color: props.theme === 'dark' ? '#6b7280 ' ' : '#9ca3af',
        style: 2,
      },
      horzLine: {
        width: 1,
        color: props.theme === 'dark' ? '#6b7280 ' ' : '#9ca3af',
        style: 2,
      },
    },
  });

  // 初始化图表
  onMount(() => {
    if (!chartContainer) return;

    try {
      const chartInstance = createChart(chartContainer, chartOptions());
      setChart(chartInstance);

      // 创建K线序列
      const candleSeries = chartInstance.addCandlestickSeries({
        upColor: '#10b981', // 绿色 (涨)
        downColor: '#ef4444', // 红色 (跌)
        borderDownColor: '#ef4444',
        borderUpColor: '#10b981',
        wickDownColor: '#ef4444',
        wickUpColor: '#10b981',
      });
      setCandlestickSeries(candleSeries);

      // 创建成交量序列
      const volSeries = chartInstance.addHistogramSeries({
        color: '#9ca3af',
        priceFormat: {
          type: 'volume',
        },
        priceScaleId: '', // 右侧价格轴
        scaleMargins: {
          top: 0.8,
          bottom: 0,
        },
      });
      setVolumeSeries(volSeries);

      // 设置初始数据
      if (props.data && props.data.length > 0) {
        const candleData = props.data.map(d => ({
          time: d.time / 1000, // 转换为秒
          open: d.open,
          high: d.high,
          low: d.low,
          close: d.close,
        }));
        
        const volumeData = props.data
          .filter(d => d.volume !== undefined)
          .map(d => ({
            time: d.time / 1000,
            value: d.volume!,
            color: d.close >= d.open ? '#10b98180 ' ' : '#ef444480',
          }));

        candleSeries.setData(candleData);
        if (volumeData.length > 0) {
          volSeries.setData(volumeData);
        }
        
        // 自动缩放到数据
        chartInstance.timeScale().fitContent();
      }

      // 监听价格变化
      candleSeries.subscribeDataChanged(() => {
        const lastBar = candleSeries.dataByIndex(-1) as CandlestickData;
        if (lastBar && props.onPriceUpdate) {
          props.onPriceUpdate(lastBar.close);
        }
      });

      setIsLoading(false);
    } catch (error) {
      console.error('图表初始化失败' :', error);
      setIsLoading(false);
    }
  });

  // 监听数据变化
  createEffect(() => {
    const chartInstance = chart();
    const candleSeries = candlestickSeries();
    const volSeries = volumeSeries();
    
    if (!chartInstance || !candleSeries || !props.data) return;

    try {
      const candleData = props.data.map(d => ({
        time: d.time / 1000,
        open: d.open,
        high: d.high,
        low: d.low,
        close: d.close,
      }));

      candleSeries.setData(candleData);

      if (volSeries) {
        const volumeData = props.data
          .filter(d => d.volume !== undefined)
          .map(d => ({
            time: d.time / 1000,
            value: d.volume!,
            color: d.close >= d.open ? '#10b98180 ' ' : '#ef444480',
          }));

        if (volumeData.length > 0) {
          volSeries.setData(volumeData);
        }
      }
    } catch (error) {
      console.error('更新图表数据失败' :', error);
    }
  });

  // 监听主题变化
  createEffect(() => {
    const chartInstance = chart();
    if (!chartInstance) return;

    chartInstance.applyOptions(chartOptions());
  });

  // 处理窗口大小变化
  const handleResize = () => {
    const chartInstance = chart();
    if (!chartInstance || !chartContainer) return;

    chartInstance.applyOptions({
      width: chartContainer.clientWidth,
    });
  };

  createEffect(() => {
    window.addEventListener('resize', handleResize);
    onCleanup(() => {
      window.removeEventListener('resize', handleResize);
    });
  });

  // 清理资源
  onCleanup(() => {
    const chartInstance = chart();
    if (chartInstance) {
      chartInstance.remove();
    }
  });

  // 公共方法：更新实时数据
  const updateRealTimeData = (newData: ChartData) => {
    const candleSeries = candlestickSeries();
    const volSeries = volumeSeries();
    
    if (!candleSeries) return;

    try {
      const candlePoint = {
        time: newData.time / 1000,
        open: newData.open,
        high: newData.high,
        low: newData.low,
        close: newData.close,
      };

      candleSeries.update(candlePoint);

      if (volSeries && newData.volume !== undefined) {
        const volumePoint = {
          time: newData.time / 1000,
          value: newData.volume,
          color: newData.close >= newData.open ? '#10b98180 ' ' : '#ef444480',
        };
        volSeries.update(volumePoint);
      }

      // 触发价格更新回调
      if (props.onPriceUpdate) {
        props.onPriceUpdate(newData.close);
      }
    } catch (error) {
      console.error('更新实时数据失败' :', error);
    }
  };

  return (
    <div style={{
      position ' ' : 'relative',
      width: props.width || '100%',
      height: `${props.height || 400}px`,
      'border-radius: '8px',
      'box-shadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
      'background-color: props.theme === 'dark' ? '#1f2937 ' ' : '#ffffff',
    }}>
      {/* 图表标题 */}
      {props.symbol && (
        <div style={{
          position: 'absolute',
          top: '16px',
          left: '16px',
          'z-index : 1,
          background: props.theme === 'dark' ? '#1f293780 ' ' : '#ffffff80',
          padding: '8px 12px',
          'border-radius: '6px',
          'backdrop-filter: 'blur(4px)',
        }}>
          <div style={{
            'font-weight: 'bold',
            'font-size: '16px',
            color: props.theme === 'dark' ? '#f9fafb ' ' : '#111827',
          }}>
            {props.symbol}
          </div>
        </div>
      )}

      {/* 加载状态 */}
      {isLoading() && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          'z-index : 2,
          display: 'flex',
          'align-items: 'center',
          gap: '8px',
          color: props.theme === 'dark' ? '#9ca3af ' ' : '#6b7280',
        }}>
          <div style={{
            width: '20px',
            height: '20px',
            border: '2px solid transparent',
            'border-top-color: 'currentColor',
            'border-radius: '50%',
            animation: 'spin 1s linear infinite',
          }}></div>
          <span>加载图表中...</span>
        </div>
      )}

      {/* 图表容器 */}
      <div
        ref={chartContainer}
        style={{
          width: '100%',
          height: '100%',
          'border-radius: '8px',
        }}
      />

      {/* 工具栏 */}
      <div style={{
        position: 'absolute',
        top: '16px',
        right: '16px',
        display: 'flex',
        gap: '8px',
        'z-index : 1,
      }}>
        <button
          onClick={() => {
            const chartInstance = chart();
            if (chartInstance) {
              chartInstance.timeScale().fitContent();
            }
          }}
          style={{
            padding: '6px 12px',
            'border-radius: '4px',
            border: 'none',
            background: props.theme === 'dark' ? '#374151' ' : '#f3f4f6',
            color: props.theme === 'dark' ? '#f9fafb ' ' : '#374151',
            'font-size: '12px',
            cursor: 'pointer',
            transition: 'all 0.2s',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = props.theme === 'dark' ? '#4b5563 ' ' : '#e5e7eb';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = props.theme === 'dark' ? '#374151' ' : '#f3f4f6';
          }}
        >
          适应内容
        </button>
      </div>

      {/* CSS 动画 */}
      <style>{`
        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
}

// 导出更新方法供外部使用
export const chartUtils = {
  updateRealTimeData: (chartRef: any, data: ChartData) => {
    if (chartRef && typeof chartRef.updateRealTimeData === 'function') {
      chartRef.updateRealTimeData(data);
    }
  }
};