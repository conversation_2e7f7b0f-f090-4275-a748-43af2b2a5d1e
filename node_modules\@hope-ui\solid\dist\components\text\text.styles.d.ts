import { VariantProps } from "@stitches/core";
export declare const textStyles: import("@stitches/core/types/styled-component").CssComponent<never, {
    size?: "xs" | "sm" | "base" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl" | "6xl" | "7xl" | "8xl" | "9xl" | undefined;
}, {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    "2xl": string;
    "reduce-motion": string;
    light: string;
    dark: string;
}, import("@stitches/core/types/css-util").CSS<{
    sm: string;
    md: string;
    lg: string;
    xl: string;
    "2xl": string;
    "reduce-motion": string;
    light: string;
    dark: string;
}, {
    colors: {
        loContrast: string;
        background: string;
        focusRing: string;
        closeButtonHoverBackground: string;
        closeButtonActiveBackground: string;
        progressStripe: string;
        danger1: string;
        danger2: string;
        danger3: string;
        danger4: string;
        danger5: string;
        danger6: string;
        danger7: string;
        danger8: string;
        danger9: string;
        danger10: string;
        danger11: string;
        danger12: string;
        warning1: string;
        warning2: string;
        warning3: string;
        warning4: string;
        warning5: string;
        warning6: string;
        warning7: string;
        warning8: string;
        warning9: string;
        warning10: string;
        warning11: string;
        warning12: string;
        info1: string;
        info2: string;
        info3: string;
        info4: string;
        info5: string;
        info6: string;
        info7: string;
        info8: string;
        info9: string;
        info10: string;
        info11: string;
        info12: string;
        success1: string;
        success2: string;
        success3: string;
        success4: string;
        success5: string;
        success6: string;
        success7: string;
        success8: string;
        success9: string;
        success10: string;
        success11: string;
        success12: string;
        neutral1: string;
        neutral2: string;
        neutral3: string;
        neutral4: string;
        neutral5: string;
        neutral6: string;
        neutral7: string;
        neutral8: string;
        neutral9: string;
        neutral10: string;
        neutral11: string;
        neutral12: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
        accent5: string;
        accent6: string;
        accent7: string;
        accent8: string;
        accent9: string;
        accent10: string;
        accent11: string;
        accent12: string;
        primary1: string;
        primary2: string;
        primary3: string;
        primary4: string;
        primary5: string;
        primary6: string;
        primary7: string;
        primary8: string;
        primary9: string;
        primary10: string;
        primary11: string;
        primary12: string;
        whiteAlpha1: string;
        whiteAlpha2: string;
        whiteAlpha3: string;
        whiteAlpha4: string;
        whiteAlpha5: string;
        whiteAlpha6: string;
        whiteAlpha7: string;
        whiteAlpha8: string;
        whiteAlpha9: string;
        whiteAlpha10: string;
        whiteAlpha11: string;
        whiteAlpha12: string;
        blackAlpha1: string;
        blackAlpha2: string;
        blackAlpha3: string;
        blackAlpha4: string;
        blackAlpha5: string;
        blackAlpha6: string;
        blackAlpha7: string;
        blackAlpha8: string;
        blackAlpha9: string;
        blackAlpha10: string;
        blackAlpha11: string;
        blackAlpha12: string;
    };
    space: {
        px: string;
        "0_5": string;
        "1": string;
        "1_5": string;
        "2": string;
        "2_5": string;
        "3": string;
        "3_5": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
        "12": string;
        "14": string;
        "16": string;
        "20": string;
        "24": string;
        "28": string;
        "32": string;
        "36": string;
        "40": string;
        "44": string;
        "48": string;
        "52": string;
        "56": string;
        "60": string;
        "64": string;
        "72": string;
        "80": string;
        "96": string;
    };
    sizes: {
        prose: string;
        max: string;
        min: string;
        full: string;
        screenW: string;
        screenH: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        "4xl": string;
        "5xl": string;
        "6xl": string;
        "7xl": string;
        "8xl": string;
        containerSm: string;
        containerMd: string;
        containerLg: string;
        containerXl: string;
        container2xl: string;
        px: string;
        "0_5": string;
        "1": string;
        "1_5": string;
        "2": string;
        "2_5": string;
        "3": string;
        "3_5": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
        "12": string;
        "14": string;
        "16": string;
        "20": string;
        "24": string;
        "28": string;
        "32": string;
        "36": string;
        "40": string;
        "44": string;
        "48": string;
        "52": string;
        "56": string;
        "60": string;
        "64": string;
        "72": string;
        "80": string;
        "96": string;
    };
    fonts: {
        sans: string;
        serif: string;
        mono: string;
    };
    fontSizes: {
        "2xs": string;
        xs: string;
        sm: string;
        base: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        "4xl": string;
        "5xl": string;
        "6xl": string;
        "7xl": string;
        "8xl": string;
        "9xl": string;
    };
    fontWeights: {
        hairline: number;
        thin: number;
        light: number;
        normal: number;
        medium: number;
        semibold: number;
        bold: number;
        extrabold: number;
        black: number;
    };
    letterSpacings: {
        tighter: string;
        tight: string;
        normal: string;
        wide: string;
        wider: string;
        widest: string;
    };
    lineHeights: {
        normal: string;
        none: number;
        shorter: number;
        short: number;
        base: number;
        tall: number;
        taller: number;
        "3": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
    };
    radii: {
        none: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        full: string;
    };
    shadows: {
        none: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        inner: string;
        outline: string;
    };
    zIndices: {
        hide: number;
        auto: string;
        base: number;
        docked: number;
        sticky: number;
        banner: number;
        overlay: number;
        modal: number;
        dropdown: number;
        popover: number;
        tooltip: number;
        skipLink: number;
        notification: number;
    };
}, {
    borderWidth: "sizes";
    borderTopWidth: "sizes";
    borderRightWidth: "sizes";
    borderBottomWidth: "sizes";
    borderLeftWidth: "sizes";
    strokeWidth: "sizes";
    gap: "space";
    gridGap: "space";
    columnGap: "space";
    gridColumnGap: "space";
    rowGap: "space";
    gridRowGap: "space";
    inset: "space";
    insetBlock: "space";
    insetBlockEnd: "space";
    insetBlockStart: "space";
    insetInline: "space";
    insetInlineEnd: "space";
    insetInlineStart: "space";
    margin: "space";
    marginTop: "space";
    marginRight: "space";
    marginBottom: "space";
    marginLeft: "space";
    marginBlock: "space";
    marginBlockEnd: "space";
    marginBlockStart: "space";
    marginInline: "space";
    marginInlineEnd: "space";
    marginInlineStart: "space";
    padding: "space";
    paddingTop: "space";
    paddingRight: "space";
    paddingBottom: "space";
    paddingLeft: "space";
    paddingBlock: "space";
    paddingBlockEnd: "space";
    paddingBlockStart: "space";
    paddingInline: "space";
    paddingInlineEnd: "space";
    paddingInlineStart: "space";
    scrollMargin: "space";
    scrollMarginTop: "space";
    scrollMarginRight: "space";
    scrollMarginBottom: "space";
    scrollMarginLeft: "space";
    scrollMarginBlock: "space";
    scrollMarginBlockEnd: "space";
    scrollMarginBlockStart: "space";
    scrollMarginInline: "space";
    scrollMarginInlineEnd: "space";
    scrollMarginInlineStart: "space";
    scrollPadding: "space";
    scrollPaddingTop: "space";
    scrollPaddingRight: "space";
    scrollPaddingBottom: "space";
    scrollPaddingLeft: "space";
    scrollPaddingBlock: "space";
    scrollPaddingBlockEnd: "space";
    scrollPaddingBlockStart: "space";
    scrollPaddingInline: "space";
    scrollPaddingInlineEnd: "space";
    scrollPaddingInlineStart: "space";
    top: "space";
    right: "space";
    bottom: "space";
    left: "space";
    fontSize: "fontSizes";
    background: "colors";
    backgroundColor: "colors";
    backgroundImage: "colors";
    borderImage: "colors";
    border: "colors";
    borderBlock: "colors";
    borderBlockEnd: "colors";
    borderBlockStart: "colors";
    borderBottom: "colors";
    borderBottomColor: "colors";
    borderColor: "colors";
    borderInline: "colors";
    borderInlineEnd: "colors";
    borderInlineStart: "colors";
    borderLeft: "colors";
    borderLeftColor: "colors";
    borderRight: "colors";
    borderRightColor: "colors";
    borderTop: "colors";
    borderTopColor: "colors";
    caretColor: "colors";
    color: "colors";
    columnRuleColor: "colors";
    outline: "colors";
    outlineColor: "colors";
    fill: "colors";
    stroke: "colors";
    textDecorationColor: "colors";
    fontFamily: "fonts";
    fontWeight: "fontWeights";
    lineHeight: "lineHeights";
    letterSpacing: "letterSpacings";
    blockSize: "sizes";
    minBlockSize: "sizes";
    maxBlockSize: "sizes";
    inlineSize: "sizes";
    minInlineSize: "sizes";
    maxInlineSize: "sizes";
    width: "sizes";
    minWidth: "sizes";
    maxWidth: "sizes";
    height: "sizes";
    minHeight: "sizes";
    maxHeight: "sizes";
    flexBasis: "sizes";
    gridTemplateColumns: "sizes";
    gridTemplateRows: "sizes";
    borderStyle: "borderStyles";
    borderTopStyle: "borderStyles";
    borderLeftStyle: "borderStyles";
    borderRightStyle: "borderStyles";
    borderBottomStyle: "borderStyles";
    borderRadius: "radii";
    borderTopLeftRadius: "radii";
    borderTopRightRadius: "radii";
    borderBottomRightRadius: "radii";
    borderBottomLeftRadius: "radii";
    boxShadow: "shadows";
    textShadow: "shadows";
    transition: "transitions";
    zIndex: "zIndices";
}, {
    noOfLines: (value: string | number) => {
        overflow: string;
        display: string;
        "-webkit-box-orient": string;
        "-webkit-line-clamp": string | number;
    };
    w: (value: {
        readonly [$$PropertyValue]: "width";
    }) => {
        width: {
            readonly [$$PropertyValue]: "width";
        };
    };
    minW: (value: {
        readonly [$$PropertyValue]: "minWidth";
    }) => {
        minWidth: {
            readonly [$$PropertyValue]: "minWidth";
        };
    };
    maxW: (value: {
        readonly [$$PropertyValue]: "maxWidth";
    }) => {
        maxWidth: {
            readonly [$$PropertyValue]: "maxWidth";
        };
    };
    h: (value: {
        readonly [$$PropertyValue]: "height";
    }) => {
        height: {
            readonly [$$PropertyValue]: "height";
        };
    };
    minH: (value: {
        readonly [$$PropertyValue]: "minHeight";
    }) => {
        minHeight: {
            readonly [$$PropertyValue]: "minHeight";
        };
    };
    maxH: (value: {
        readonly [$$PropertyValue]: "maxHeight";
    }) => {
        maxHeight: {
            readonly [$$PropertyValue]: "maxHeight";
        };
    };
    boxSize: (value: {
        readonly [$$PropertyValue]: "width";
    }) => {
        width: {
            readonly [$$PropertyValue]: "width";
        };
        height: {
            readonly [$$PropertyValue]: "width";
        };
    };
    shadow: (value: {
        readonly [$$PropertyValue]: "boxShadow";
    }) => {
        boxShadow: {
            readonly [$$PropertyValue]: "boxShadow";
        };
    };
    p: (value: {
        readonly [$$PropertyValue]: "padding";
    }) => {
        padding: {
            readonly [$$PropertyValue]: "padding";
        };
    };
    pt: (value: {
        readonly [$$PropertyValue]: "paddingTop";
    }) => {
        paddingTop: {
            readonly [$$PropertyValue]: "paddingTop";
        };
    };
    pr: (value: {
        readonly [$$PropertyValue]: "paddingRight";
    }) => {
        paddingRight: {
            readonly [$$PropertyValue]: "paddingRight";
        };
    };
    paddingStart: (value: {
        readonly [$$PropertyValue]: "paddingInlineStart";
    }) => {
        paddingInlineStart: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
    };
    ps: (value: {
        readonly [$$PropertyValue]: "paddingInlineStart";
    }) => {
        paddingInlineStart: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
    };
    pb: (value: {
        readonly [$$PropertyValue]: "paddingBottom";
    }) => {
        paddingBottom: {
            readonly [$$PropertyValue]: "paddingBottom";
        };
    };
    pl: (value: {
        readonly [$$PropertyValue]: "paddingLeft";
    }) => {
        paddingLeft: {
            readonly [$$PropertyValue]: "paddingLeft";
        };
    };
    pe: (value: {
        readonly [$$PropertyValue]: "paddingInlineEnd";
    }) => {
        paddingInlineEnd: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        };
    };
    paddingEnd: (value: {
        readonly [$$PropertyValue]: "paddingInlineEnd";
    }) => {
        paddingInlineEnd: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        };
    };
    px: (value: {
        readonly [$$PropertyValue]: "paddingInlineStart";
    }) => {
        paddingInlineStart: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
        paddingInlineEnd: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
    };
    py: (value: {
        readonly [$$PropertyValue]: "paddingTop";
    }) => {
        paddingTop: {
            readonly [$$PropertyValue]: "paddingTop";
        };
        paddingBottom: {
            readonly [$$PropertyValue]: "paddingTop";
        };
    };
    m: (value: {
        readonly [$$PropertyValue]: "margin";
    }) => {
        margin: {
            readonly [$$PropertyValue]: "margin";
        };
    };
    mt: (value: {
        readonly [$$PropertyValue]: "marginTop";
    }) => {
        marginTop: {
            readonly [$$PropertyValue]: "marginTop";
        };
    };
    mr: (value: {
        readonly [$$PropertyValue]: "marginRight";
    }) => {
        marginRight: {
            readonly [$$PropertyValue]: "marginRight";
        };
    };
    marginStart: (value: {
        readonly [$$PropertyValue]: "marginInlineStart";
    }) => {
        marginInlineStart: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
    };
    ms: (value: {
        readonly [$$PropertyValue]: "marginInlineStart";
    }) => {
        marginInlineStart: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
    };
    mb: (value: {
        readonly [$$PropertyValue]: "marginBottom";
    }) => {
        marginBottom: {
            readonly [$$PropertyValue]: "marginBottom";
        };
    };
    ml: (value: {
        readonly [$$PropertyValue]: "marginLeft";
    }) => {
        marginLeft: {
            readonly [$$PropertyValue]: "marginLeft";
        };
    };
    marginEnd: (value: {
        readonly [$$PropertyValue]: "marginInlineEnd";
    }) => {
        marginInlineEnd: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        };
    };
    me: (value: {
        readonly [$$PropertyValue]: "marginInlineEnd";
    }) => {
        marginInlineEnd: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        };
    };
    mx: (value: {
        readonly [$$PropertyValue]: "marginInlineStart";
    }) => {
        marginInlineStart: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
        marginInlineEnd: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
    };
    my: (value: {
        readonly [$$PropertyValue]: "marginTop";
    }) => {
        marginTop: {
            readonly [$$PropertyValue]: "marginTop";
        };
        marginBottom: {
            readonly [$$PropertyValue]: "marginTop";
        };
    };
    spaceX: (value: {
        readonly [$$PropertyValue]: "marginLeft";
    }) => {
        "& > * + *": {
            marginLeft: {
                readonly [$$PropertyValue]: "marginLeft";
            };
        };
    };
    spaceY: (value: {
        readonly [$$PropertyValue]: "marginTop";
    }) => {
        "& > * + *": {
            marginTop: {
                readonly [$$PropertyValue]: "marginTop";
            };
        };
    };
    borderTopRadius: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    borderRightRadius: (value: {
        readonly [$$PropertyValue]: "borderTopRightRadius";
    }) => {
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
    };
    borderStartRadius: (value: {
        readonly [$$PropertyValue]: "borderStartStartRadius";
    }) => {
        borderStartStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
        borderEndStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
    };
    borderBottomRadius: (value: {
        readonly [$$PropertyValue]: "borderBottomLeftRadius";
    }) => {
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
    };
    borderLeftRadius: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    borderEndRadius: (value: {
        readonly [$$PropertyValue]: "borderStartEndRadius";
    }) => {
        borderStartEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
        borderEndEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
    };
    rounded: (value: {
        readonly [$$PropertyValue]: "borderRadius";
    }) => {
        borderRadius: {
            readonly [$$PropertyValue]: "borderRadius";
        };
    };
    roundedTop: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    roundedRight: (value: {
        readonly [$$PropertyValue]: "borderTopRightRadius";
    }) => {
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
    };
    roundedStart: (value: {
        readonly [$$PropertyValue]: "borderStartStartRadius";
    }) => {
        borderStartStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
        borderEndStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
    };
    roundedBottom: (value: {
        readonly [$$PropertyValue]: "borderBottomLeftRadius";
    }) => {
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
    };
    roundedLeft: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    roundedEnd: (value: {
        readonly [$$PropertyValue]: "borderStartEndRadius";
    }) => {
        borderStartEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
        borderEndEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
    };
    _hover: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:hover, &[data-hover]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _active: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:active, &[data-active]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _focus: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:focus, &[data-focus]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _highlighted: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[data-highlighted]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _focusWithin: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:focus-within": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _focusVisible: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:focus-visible": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _disabled: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[disabled], &[aria-disabled=true], &[data-disabled]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _readOnly: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-readonly=true], &[readonly], &[data-readonly]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _before: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::before": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _after: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::after": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _empty: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:empty": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _expanded: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-expanded=true], &[data-expanded]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _checked: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-checked=true], &[data-checked]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _grabbed: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-grabbed=true], &[data-grabbed]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _pressed: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-pressed=true], &[data-pressed]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _invalid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-invalid=true], &[data-invalid]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _valid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[data-valid], &[data-state=valid]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _loading: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[data-loading], &[aria-busy=true]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _selected: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-selected=true], &[data-selected]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _hidden: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[hidden], &[data-hidden]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _even: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:nth-of-type(even)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _odd: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:nth-of-type(odd)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _first: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:first-of-type": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _last: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:last-of-type": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _notFirst: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:not(:first-of-type)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _notLast: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:not(:last-of-type)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _visited: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:visited": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _activeLink: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-current=page]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _activeStep: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-current=step]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _indeterminate: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:indeterminate, &[aria-checked=mixed], &[data-indeterminate]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupHover: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerHover: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupFocus: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerFocus: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupFocusVisible: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerFocusVisible: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupActive: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerActive: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupSelected: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerSelected: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupDisabled: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerDisabled: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupInvalid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerInvalid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupChecked: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerChecked: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupFocusWithin: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerFocusWithin: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerPlaceholderShown: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _placeholder: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::placeholder": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _placeholderShown: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:placeholder-shown": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _fullScreen: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:fullscreen": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _selection: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::selection": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _mediaDark: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "@media (prefers-color-scheme: dark)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _mediaReduceMotion: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "@media (prefers-reduced-motion: reduce)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _dark: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        ".hope-ui-dark &": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _light: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        ".hope-ui-light &": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    pos: (value: {
        readonly [$$PropertyValue]: "position";
    }) => {
        position: {
            readonly [$$PropertyValue]: "position";
        };
    };
    d: (value: {
        readonly [$$PropertyValue]: "display";
    }) => {
        display: {
            readonly [$$PropertyValue]: "display";
        };
    };
    borderX: (value: {
        readonly [$$PropertyValue]: "borderLeft";
    }) => {
        borderLeft: {
            readonly [$$PropertyValue]: "borderLeft";
        };
        borderRight: {
            readonly [$$PropertyValue]: "borderLeft";
        };
    };
    borderY: (value: {
        readonly [$$PropertyValue]: "borderTop";
    }) => {
        borderTop: {
            readonly [$$PropertyValue]: "borderTop";
        };
        borderBottom: {
            readonly [$$PropertyValue]: "borderTop";
        };
    };
    bg: (value: {
        readonly [$$PropertyValue]: "background";
    }) => {
        background: {
            readonly [$$PropertyValue]: "background";
        };
    };
    bgColor: (value: {
        readonly [$$PropertyValue]: "backgroundColor";
    }) => {
        backgroundColor: {
            readonly [$$PropertyValue]: "backgroundColor";
        };
    };
}>>;
export declare type TextVariants = VariantProps<typeof textStyles>;
//# sourceMappingURL=text.styles.d.ts.map