/**
 * HTTP客户端工具
 * 提供统一的API请求接口
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  code?: number
  timestamp?: string | number
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 列表响应类型
export interface ListResponse<T = any> {
  data: T[]
  total: number
  page?: number
  pageSize?: number
}

// 请求配置
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
  withCredentials?: boolean
  retries?: number
  retryDelay?: number
}

// 错误类型
export interface ApiError {
  code: number
  message: string
  details?: any
}

// HTTP客户端类
export class HttpClient {
  private baseURL: string
  private timeout: number
  private defaultHeaders: Record<string, string>

  constructor(config: {
    baseURL?: string
    timeout?: number
    headers?: Record<string, string>
  } = {}) {
    // 在开发环境下使用代理，生产环境使用完整URL
    this.baseURL = config.baseURL || import.meta.env?.VITE_API_BASE_URL || 
      (import.meta.env.DEV ? '/api/v1' : 'http://localhost:8000/api/v1)
    this.timeout = config.timeout || 10000
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...config.headers
    }
    
    if (import.meta.env.DEV) {
      console.log('[HttpClient] Initialized with baseURL:', this.baseURL)
    }
  }

  // 构建完整URL
  private buildURL(url: string, params?: Record<string, any>): string {
    const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`
    
    if (!params) return fullURL
    
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })
    
    const queryString = searchParams.toString()
    return queryString ? `${fullURL}?${queryString}` : fullURL
  }

  // 处理响应
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type')
    const isJson = contentType?.includes('application/json')
    
    let data: any
    try {
      data = isJson ? await response.json() : await response.text()
    } catch (error) {
      throw new Error('响应解析失败')
    }

    if (!response.ok) {
      throw {
        code: response.status,
        message: data.message || data.error || `HTTP ${response.status}`,
        details: data
      } as ApiError
    }

    // 统一响应格式
    if (typeof data === 'object' && data !== null) {
      return {
        success: true,
        data: data.data || data,
        message: data.message,
        code: response.status,
        timestamp: Date.now()
      }
    }

    return {
      success: true,
      data: data as T,
      code: response.status,
      timestamp: Date.now()
    }
  }

  // 通用请求方法
  async request<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    const {
      url,
      method = 'GET',
      params,
      data,
      headers = {},
      timeout = this.timeout,
      retries = 3,
      retryDelay = 1000
    } = config

    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers
    }

    // 添加认证token
    const token = typeof window !== 'undefined' ? localStorage.getItem('access_token') : null
    if (token) {
      requestHeaders.Authorization = `Bearer ${token}`
    }

    const requestConfig: RequestInit = {
      method,
      headers: requestHeaders,
      signal: AbortSignal.timeout(timeout)
    }

    // 处理请求体
    if (data && method !== 'GET') {
      if (data instanceof FormData) {
        delete requestHeaders['Content-Type'] // 让浏览器自动设置
        requestConfig.body = data
      } else {
        requestConfig.body = JSON.stringify(data)
      }
    }

    const fullURL = this.buildURL(url, method === 'GET' ? params : undefined)

    // 重试逻辑
    let lastError: any
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await fetch(fullURL, requestConfig)
        return await this.handleResponse<T>(response)
      } catch (error) {
        lastError = error
        
        if (attempt === retries) {
          break
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt))
      }
    }

    throw lastError
  }

  // GET请求
  async get<T>(url: string, params?: Record<string, any>, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'GET', params, headers })
  }

  // POST请求
  async post<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'POST', data, headers })
  }

  // PUT请求
  async put<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'PUT', data, headers })
  }

  // DELETE请求
  async delete<T>(url: string, params?: Record<string, any>, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'DELETE', params, headers })
  }

  // PATCH请求
  async patch<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'PATCH', data, headers })
  }
}

// 创建默认实例
export const httpClient = new HttpClient()

// 导出默认实例
export default httpClient
