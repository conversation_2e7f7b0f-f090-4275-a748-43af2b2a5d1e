declare const fonts: {
    sans: string;
    serif: string;
    mono: string;
};
declare const fontSizes: {
    "2xs": string;
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    "2xl": string;
    "3xl": string;
    "4xl": string;
    "5xl": string;
    "6xl": string;
    "7xl": string;
    "8xl": string;
    "9xl": string;
};
declare const fontWeights: {
    hairline: number;
    thin: number;
    light: number;
    normal: number;
    medium: number;
    semibold: number;
    bold: number;
    extrabold: number;
    black: number;
};
declare const lineHeights: {
    normal: string;
    none: number;
    shorter: number;
    short: number;
    base: number;
    tall: number;
    taller: number;
    "3": string;
    "4": string;
    "5": string;
    "6": string;
    "7": string;
    "8": string;
    "9": string;
    "10": string;
};
declare const letterSpacings: {
    tighter: string;
    tight: string;
    normal: string;
    wide: string;
    wider: string;
    widest: string;
};
export { fonts, fontSizes, fontWeights, letterSpacings, lineHeights };
//# sourceMappingURL=typography.d.ts.map