{"version": 3, "sources": ["optional-peer-dep:__vite-optional-peer-dep:react:jotai", "../../jotai/esm/vanilla/internals.mjs", "../../jotai/esm/vanilla.mjs", "../../jotai/esm/react.mjs"], "sourcesContent": ["throw new Error(`Could not resolve \"react\" imported by \"jotai\". Is it installed?`)", "const isSelfAtom = (atom, a) => atom.unstable_is ? atom.unstable_is(a) : a === atom;\nconst hasInitialValue = (atom) => \"init\" in atom;\nconst isActuallyWritableAtom = (atom) => !!atom.write;\nconst isAtomStateInitialized = (atomState) => \"v\" in atomState || \"e\" in atomState;\nconst returnAtomValue = (atomState) => {\n  if (\"e\" in atomState) {\n    throw atomState.e;\n  }\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !(\"v\" in atomState)) {\n    throw new Error(\"[Bug] atom state is not initialized\");\n  }\n  return atomState.v;\n};\nconst promiseStateMap = /* @__PURE__ */ new WeakMap();\nconst isPendingPromise = (value) => {\n  var _a;\n  return isPromiseLike(value) && !!((_a = promiseStateMap.get(value)) == null ? void 0 : _a[0]);\n};\nconst abortPromise = (promise) => {\n  const promiseState = promiseStateMap.get(promise);\n  if (promiseState == null ? void 0 : promiseState[0]) {\n    promiseState[0] = false;\n    promiseState[1].forEach((fn) => fn());\n  }\n};\nconst registerAbortHandler = (promise, abortHandler) => {\n  let promiseState = promiseStateMap.get(promise);\n  if (!promiseState) {\n    promiseState = [true, /* @__PURE__ */ new Set()];\n    promiseStateMap.set(promise, promiseState);\n    const settle = () => {\n      promiseState[0] = false;\n    };\n    promise.then(settle, settle);\n  }\n  promiseState[1].add(abortHandler);\n};\nconst isPromiseLike = (p) => typeof (p == null ? void 0 : p.then) === \"function\";\nconst addPendingPromiseToDependency = (atom, promise, dependencyAtomState) => {\n  if (!dependencyAtomState.p.has(atom)) {\n    dependencyAtomState.p.add(atom);\n    promise.then(\n      () => {\n        dependencyAtomState.p.delete(atom);\n      },\n      () => {\n        dependencyAtomState.p.delete(atom);\n      }\n    );\n  }\n};\nconst setAtomStateValueOrPromise = (atom, valueOrPromise, ensureAtomState) => {\n  const atomState = ensureAtomState(atom);\n  const hasPrevValue = \"v\" in atomState;\n  const prevValue = atomState.v;\n  if (isPromiseLike(valueOrPromise)) {\n    for (const a of atomState.d.keys()) {\n      addPendingPromiseToDependency(atom, valueOrPromise, ensureAtomState(a));\n    }\n  }\n  atomState.v = valueOrPromise;\n  delete atomState.e;\n  if (!hasPrevValue || !Object.is(prevValue, atomState.v)) {\n    ++atomState.n;\n    if (isPromiseLike(prevValue)) {\n      abortPromise(prevValue);\n    }\n  }\n};\nconst getMountedOrPendingDependents = (atom, atomState, mountedMap) => {\n  var _a;\n  const dependents = /* @__PURE__ */ new Set();\n  for (const a of ((_a = mountedMap.get(atom)) == null ? void 0 : _a.t) || []) {\n    if (mountedMap.has(a)) {\n      dependents.add(a);\n    }\n  }\n  for (const atomWithPendingPromise of atomState.p) {\n    dependents.add(atomWithPendingPromise);\n  }\n  return dependents;\n};\nconst createStoreHook = () => {\n  const callbacks = /* @__PURE__ */ new Set();\n  const notify = () => {\n    callbacks.forEach((fn) => fn());\n  };\n  notify.add = (fn) => {\n    callbacks.add(fn);\n    return () => {\n      callbacks.delete(fn);\n    };\n  };\n  return notify;\n};\nconst createStoreHookForAtoms = () => {\n  const all = {};\n  const callbacks = /* @__PURE__ */ new WeakMap();\n  const notify = (atom) => {\n    var _a, _b;\n    (_a = callbacks.get(all)) == null ? void 0 : _a.forEach((fn) => fn(atom));\n    (_b = callbacks.get(atom)) == null ? void 0 : _b.forEach((fn) => fn());\n  };\n  notify.add = (atom, fn) => {\n    const key = atom || all;\n    const fns = (callbacks.has(key) ? callbacks : callbacks.set(key, /* @__PURE__ */ new Set())).get(key);\n    fns.add(fn);\n    return () => {\n      fns == null ? void 0 : fns.delete(fn);\n      if (!fns.size) {\n        callbacks.delete(key);\n      }\n    };\n  };\n  return notify;\n};\nconst initializeStoreHooks = (storeHooks) => {\n  storeHooks.c || (storeHooks.c = createStoreHookForAtoms());\n  storeHooks.m || (storeHooks.m = createStoreHookForAtoms());\n  storeHooks.u || (storeHooks.u = createStoreHookForAtoms());\n  storeHooks.f || (storeHooks.f = createStoreHook());\n  return storeHooks;\n};\nconst BUILDING_BLOCKS = Symbol();\nconst getBuildingBlocks = (store) => store[BUILDING_BLOCKS];\nconst buildStore = (atomStateMap = /* @__PURE__ */ new WeakMap(), mountedMap = /* @__PURE__ */ new WeakMap(), invalidatedAtoms = /* @__PURE__ */ new WeakMap(), changedAtoms = /* @__PURE__ */ new Set(), mountCallbacks = /* @__PURE__ */ new Set(), unmountCallbacks = /* @__PURE__ */ new Set(), storeHooks = {}, atomRead = (atom, ...params) => atom.read(...params), atomWrite = (atom, ...params) => atom.write(...params), atomOnInit = (atom, store) => {\n  var _a;\n  return (_a = atom.unstable_onInit) == null ? void 0 : _a.call(atom, store);\n}, atomOnMount = (atom, setAtom) => {\n  var _a;\n  return (_a = atom.onMount) == null ? void 0 : _a.call(atom, setAtom);\n}, ...buildingBlockFunctions) => {\n  const ensureAtomState = buildingBlockFunctions[0] || ((atom) => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !atom) {\n      throw new Error(\"Atom is undefined or null\");\n    }\n    let atomState = atomStateMap.get(atom);\n    if (!atomState) {\n      atomState = { d: /* @__PURE__ */ new Map(), p: /* @__PURE__ */ new Set(), n: 0 };\n      atomStateMap.set(atom, atomState);\n      atomOnInit == null ? void 0 : atomOnInit(atom, store);\n    }\n    return atomState;\n  });\n  const flushCallbacks = buildingBlockFunctions[1] || (() => {\n    const errors = [];\n    const call = (fn) => {\n      try {\n        fn();\n      } catch (e) {\n        errors.push(e);\n      }\n    };\n    do {\n      if (storeHooks.f) {\n        call(storeHooks.f);\n      }\n      const callbacks = /* @__PURE__ */ new Set();\n      const add = callbacks.add.bind(callbacks);\n      changedAtoms.forEach((atom) => {\n        var _a;\n        return (_a = mountedMap.get(atom)) == null ? void 0 : _a.l.forEach(add);\n      });\n      changedAtoms.clear();\n      unmountCallbacks.forEach(add);\n      unmountCallbacks.clear();\n      mountCallbacks.forEach(add);\n      mountCallbacks.clear();\n      callbacks.forEach(call);\n      if (changedAtoms.size) {\n        recomputeInvalidatedAtoms();\n      }\n    } while (changedAtoms.size || unmountCallbacks.size || mountCallbacks.size);\n    if (errors.length) {\n      throw new AggregateError(errors);\n    }\n  });\n  const recomputeInvalidatedAtoms = buildingBlockFunctions[2] || (() => {\n    const topSortedReversed = [];\n    const visiting = /* @__PURE__ */ new WeakSet();\n    const visited = /* @__PURE__ */ new WeakSet();\n    const stack = Array.from(changedAtoms);\n    while (stack.length) {\n      const a = stack[stack.length - 1];\n      const aState = ensureAtomState(a);\n      if (visited.has(a)) {\n        stack.pop();\n        continue;\n      }\n      if (visiting.has(a)) {\n        if (invalidatedAtoms.get(a) === aState.n) {\n          topSortedReversed.push([a, aState]);\n        } else if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && invalidatedAtoms.has(a)) {\n          throw new Error(\"[Bug] invalidated atom exists\");\n        }\n        visited.add(a);\n        stack.pop();\n        continue;\n      }\n      visiting.add(a);\n      for (const d of getMountedOrPendingDependents(a, aState, mountedMap)) {\n        if (!visiting.has(d)) {\n          stack.push(d);\n        }\n      }\n    }\n    for (let i = topSortedReversed.length - 1; i >= 0; --i) {\n      const [a, aState] = topSortedReversed[i];\n      let hasChangedDeps = false;\n      for (const dep of aState.d.keys()) {\n        if (dep !== a && changedAtoms.has(dep)) {\n          hasChangedDeps = true;\n          break;\n        }\n      }\n      if (hasChangedDeps) {\n        readAtomState(a);\n        mountDependencies(a);\n      }\n      invalidatedAtoms.delete(a);\n    }\n  });\n  const readAtomState = buildingBlockFunctions[3] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    if (isAtomStateInitialized(atomState)) {\n      if (mountedMap.has(atom) && invalidatedAtoms.get(atom) !== atomState.n) {\n        return atomState;\n      }\n      if (Array.from(atomState.d).every(\n        ([a, n]) => (\n          // Recursively, read the atom state of the dependency, and\n          // check if the atom epoch number is unchanged\n          readAtomState(a).n === n\n        )\n      )) {\n        return atomState;\n      }\n    }\n    atomState.d.clear();\n    let isSync = true;\n    const mountDependenciesIfAsync = () => {\n      if (mountedMap.has(atom)) {\n        mountDependencies(atom);\n        recomputeInvalidatedAtoms();\n        flushCallbacks();\n      }\n    };\n    const getter = (a) => {\n      var _a2;\n      if (isSelfAtom(atom, a)) {\n        const aState2 = ensureAtomState(a);\n        if (!isAtomStateInitialized(aState2)) {\n          if (hasInitialValue(a)) {\n            setAtomStateValueOrPromise(a, a.init, ensureAtomState);\n          } else {\n            throw new Error(\"no atom init\");\n          }\n        }\n        return returnAtomValue(aState2);\n      }\n      const aState = readAtomState(a);\n      try {\n        return returnAtomValue(aState);\n      } finally {\n        atomState.d.set(a, aState.n);\n        if (isPendingPromise(atomState.v)) {\n          addPendingPromiseToDependency(atom, atomState.v, aState);\n        }\n        (_a2 = mountedMap.get(a)) == null ? void 0 : _a2.t.add(atom);\n        if (!isSync) {\n          mountDependenciesIfAsync();\n        }\n      }\n    };\n    let controller;\n    let setSelf;\n    const options = {\n      get signal() {\n        if (!controller) {\n          controller = new AbortController();\n        }\n        return controller.signal;\n      },\n      get setSelf() {\n        if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !isActuallyWritableAtom(atom)) {\n          console.warn(\"setSelf function cannot be used with read-only atom\");\n        }\n        if (!setSelf && isActuallyWritableAtom(atom)) {\n          setSelf = (...args) => {\n            if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && isSync) {\n              console.warn(\"setSelf function cannot be called in sync\");\n            }\n            if (!isSync) {\n              try {\n                return writeAtomState(atom, ...args);\n              } finally {\n                recomputeInvalidatedAtoms();\n                flushCallbacks();\n              }\n            }\n          };\n        }\n        return setSelf;\n      }\n    };\n    const prevEpochNumber = atomState.n;\n    try {\n      const valueOrPromise = atomRead(atom, getter, options);\n      setAtomStateValueOrPromise(atom, valueOrPromise, ensureAtomState);\n      if (isPromiseLike(valueOrPromise)) {\n        registerAbortHandler(valueOrPromise, () => controller == null ? void 0 : controller.abort());\n        valueOrPromise.then(\n          mountDependenciesIfAsync,\n          mountDependenciesIfAsync\n        );\n      }\n      return atomState;\n    } catch (error) {\n      delete atomState.v;\n      atomState.e = error;\n      ++atomState.n;\n      return atomState;\n    } finally {\n      isSync = false;\n      if (prevEpochNumber !== atomState.n && invalidatedAtoms.get(atom) === prevEpochNumber) {\n        invalidatedAtoms.set(atom, atomState.n);\n        changedAtoms.add(atom);\n        (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, atom);\n      }\n    }\n  });\n  const invalidateDependents = buildingBlockFunctions[4] || ((atom) => {\n    const stack = [atom];\n    while (stack.length) {\n      const a = stack.pop();\n      const aState = ensureAtomState(a);\n      for (const d of getMountedOrPendingDependents(a, aState, mountedMap)) {\n        const dState = ensureAtomState(d);\n        invalidatedAtoms.set(d, dState.n);\n        stack.push(d);\n      }\n    }\n  });\n  const writeAtomState = buildingBlockFunctions[5] || ((atom, ...args) => {\n    let isSync = true;\n    const getter = (a) => returnAtomValue(readAtomState(a));\n    const setter = (a, ...args2) => {\n      var _a;\n      const aState = ensureAtomState(a);\n      try {\n        if (isSelfAtom(atom, a)) {\n          if (!hasInitialValue(a)) {\n            throw new Error(\"atom not writable\");\n          }\n          const prevEpochNumber = aState.n;\n          const v = args2[0];\n          setAtomStateValueOrPromise(a, v, ensureAtomState);\n          mountDependencies(a);\n          if (prevEpochNumber !== aState.n) {\n            changedAtoms.add(a);\n            (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, a);\n            invalidateDependents(a);\n          }\n          return void 0;\n        } else {\n          return writeAtomState(a, ...args2);\n        }\n      } finally {\n        if (!isSync) {\n          recomputeInvalidatedAtoms();\n          flushCallbacks();\n        }\n      }\n    };\n    try {\n      return atomWrite(atom, getter, setter, ...args);\n    } finally {\n      isSync = false;\n    }\n  });\n  const mountDependencies = buildingBlockFunctions[6] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    const mounted = mountedMap.get(atom);\n    if (mounted && !isPendingPromise(atomState.v)) {\n      for (const [a, n] of atomState.d) {\n        if (!mounted.d.has(a)) {\n          const aState = ensureAtomState(a);\n          const aMounted = mountAtom(a);\n          aMounted.t.add(atom);\n          mounted.d.add(a);\n          if (n !== aState.n) {\n            changedAtoms.add(a);\n            (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, a);\n            invalidateDependents(a);\n          }\n        }\n      }\n      for (const a of mounted.d || []) {\n        if (!atomState.d.has(a)) {\n          mounted.d.delete(a);\n          const aMounted = unmountAtom(a);\n          aMounted == null ? void 0 : aMounted.t.delete(atom);\n        }\n      }\n    }\n  });\n  const mountAtom = buildingBlockFunctions[7] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    let mounted = mountedMap.get(atom);\n    if (!mounted) {\n      readAtomState(atom);\n      for (const a of atomState.d.keys()) {\n        const aMounted = mountAtom(a);\n        aMounted.t.add(atom);\n      }\n      mounted = {\n        l: /* @__PURE__ */ new Set(),\n        d: new Set(atomState.d.keys()),\n        t: /* @__PURE__ */ new Set()\n      };\n      mountedMap.set(atom, mounted);\n      (_a = storeHooks.m) == null ? void 0 : _a.call(storeHooks, atom);\n      if (isActuallyWritableAtom(atom)) {\n        const processOnMount = () => {\n          let isSync = true;\n          const setAtom = (...args) => {\n            try {\n              return writeAtomState(atom, ...args);\n            } finally {\n              if (!isSync) {\n                recomputeInvalidatedAtoms();\n                flushCallbacks();\n              }\n            }\n          };\n          try {\n            const onUnmount = atomOnMount(atom, setAtom);\n            if (onUnmount) {\n              mounted.u = () => {\n                isSync = true;\n                try {\n                  onUnmount();\n                } finally {\n                  isSync = false;\n                }\n              };\n            }\n          } finally {\n            isSync = false;\n          }\n        };\n        mountCallbacks.add(processOnMount);\n      }\n    }\n    return mounted;\n  });\n  const unmountAtom = buildingBlockFunctions[8] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    let mounted = mountedMap.get(atom);\n    if (mounted && !mounted.l.size && !Array.from(mounted.t).some((a) => {\n      var _a2;\n      return (_a2 = mountedMap.get(a)) == null ? void 0 : _a2.d.has(atom);\n    })) {\n      if (mounted.u) {\n        unmountCallbacks.add(mounted.u);\n      }\n      mounted = void 0;\n      mountedMap.delete(atom);\n      (_a = storeHooks.u) == null ? void 0 : _a.call(storeHooks, atom);\n      for (const a of atomState.d.keys()) {\n        const aMounted = unmountAtom(a);\n        aMounted == null ? void 0 : aMounted.t.delete(atom);\n      }\n      return void 0;\n    }\n    return mounted;\n  });\n  const buildingBlocks = [\n    // store state\n    atomStateMap,\n    mountedMap,\n    invalidatedAtoms,\n    changedAtoms,\n    mountCallbacks,\n    unmountCallbacks,\n    storeHooks,\n    // atom interceptors\n    atomRead,\n    atomWrite,\n    atomOnInit,\n    atomOnMount,\n    // building-block functions\n    ensureAtomState,\n    flushCallbacks,\n    recomputeInvalidatedAtoms,\n    readAtomState,\n    invalidateDependents,\n    writeAtomState,\n    mountDependencies,\n    mountAtom,\n    unmountAtom\n  ];\n  const store = {\n    get: (atom) => returnAtomValue(readAtomState(atom)),\n    set: (atom, ...args) => {\n      try {\n        return writeAtomState(atom, ...args);\n      } finally {\n        recomputeInvalidatedAtoms();\n        flushCallbacks();\n      }\n    },\n    sub: (atom, listener) => {\n      const mounted = mountAtom(atom);\n      const listeners = mounted.l;\n      listeners.add(listener);\n      flushCallbacks();\n      return () => {\n        listeners.delete(listener);\n        unmountAtom(atom);\n        flushCallbacks();\n      };\n    }\n  };\n  Object.defineProperty(store, BUILDING_BLOCKS, { value: buildingBlocks });\n  return store;\n};\nconst INTERNAL_buildStoreRev1 = buildStore;\nconst INTERNAL_getBuildingBlocksRev1 = getBuildingBlocks;\nconst INTERNAL_initializeStoreHooks = initializeStoreHooks;\nconst INTERNAL_isSelfAtom = isSelfAtom;\nconst INTERNAL_hasInitialValue = hasInitialValue;\nconst INTERNAL_isActuallyWritableAtom = isActuallyWritableAtom;\nconst INTERNAL_isAtomStateInitialized = isAtomStateInitialized;\nconst INTERNAL_returnAtomValue = returnAtomValue;\nconst INTERNAL_promiseStateMap = promiseStateMap;\nconst INTERNAL_isPendingPromise = isPendingPromise;\nconst INTERNAL_abortPromise = abortPromise;\nconst INTERNAL_registerAbortHandler = registerAbortHandler;\nconst INTERNAL_isPromiseLike = isPromiseLike;\nconst INTERNAL_addPendingPromiseToDependency = addPendingPromiseToDependency;\nconst INTERNAL_setAtomStateValueOrPromise = setAtomStateValueOrPromise;\nconst INTERNAL_getMountedOrPendingDependents = getMountedOrPendingDependents;\n\nexport { INTERNAL_abortPromise, INTERNAL_addPendingPromiseToDependency, INTERNAL_buildStoreRev1, INTERNAL_getBuildingBlocksRev1, INTERNAL_getMountedOrPendingDependents, INTERNAL_hasInitialValue, INTERNAL_initializeStoreHooks, INTERNAL_isActuallyWritableAtom, INTERNAL_isAtomStateInitialized, INTERNAL_isPendingPromise, INTERNAL_isPromiseLike, INTERNAL_isSelfAtom, INTERNAL_promiseStateMap, INTERNAL_registerAbortHandler, INTERNAL_returnAtomValue, INTERNAL_setAtomStateValueOrPromise };\n", "import { INTERNAL_buildStoreRev1 } from 'jotai/vanilla/internals';\n\nlet keyCount = 0;\nfunction atom(read, write) {\n  const key = `atom${++keyCount}`;\n  const config = {\n    toString() {\n      return (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && this.debugLabel ? key + \":\" + this.debugLabel : key;\n    }\n  };\n  if (typeof read === \"function\") {\n    config.read = read;\n  } else {\n    config.init = read;\n    config.read = defaultRead;\n    config.write = defaultWrite;\n  }\n  if (write) {\n    config.write = write;\n  }\n  return config;\n}\nfunction defaultRead(get) {\n  return get(this);\n}\nfunction defaultWrite(get, set, arg) {\n  return set(\n    this,\n    typeof arg === \"function\" ? arg(get(this)) : arg\n  );\n}\n\nlet overiddenCreateStore;\nfunction INTERNAL_overrideCreateStore(fn) {\n  overiddenCreateStore = fn(overiddenCreateStore);\n}\nfunction createStore() {\n  if (overiddenCreateStore) {\n    return overiddenCreateStore();\n  }\n  return INTERNAL_buildStoreRev1();\n}\nlet defaultStore;\nfunction getDefaultStore() {\n  if (!defaultStore) {\n    defaultStore = createStore();\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      globalThis.__JOTAI_DEFAULT_STORE__ || (globalThis.__JOTAI_DEFAULT_STORE__ = defaultStore);\n      if (globalThis.__JOTAI_DEFAULT_STORE__ !== defaultStore) {\n        console.warn(\n          \"Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044\"\n        );\n      }\n    }\n  }\n  return defaultStore;\n}\n\nexport { INTERNAL_overrideCreateStore, atom, createStore, getDefaultStore };\n", "'use client';\nimport React, { createContext, useContext, useRef, createElement, useReducer, useEffect, useDebugValue, useCallback } from 'react';\nimport { getDefaultStore, createStore } from 'jotai/vanilla';\nimport { INTERNAL_registerAbortHandler } from 'jotai/vanilla/internals';\n\nconst StoreContext = createContext(\n  void 0\n);\nfunction useStore(options) {\n  const store = useContext(StoreContext);\n  return (options == null ? void 0 : options.store) || store || getDefaultStore();\n}\nfunction Provider({\n  children,\n  store\n}) {\n  const storeRef = useRef(void 0);\n  if (!store && !storeRef.current) {\n    storeRef.current = createStore();\n  }\n  return createElement(\n    StoreContext.Provider,\n    {\n      value: store || storeRef.current\n    },\n    children\n  );\n}\n\nconst isPromiseLike = (x) => typeof (x == null ? void 0 : x.then) === \"function\";\nconst attachPromiseStatus = (promise) => {\n  if (!promise.status) {\n    promise.status = \"pending\";\n    promise.then(\n      (v) => {\n        promise.status = \"fulfilled\";\n        promise.value = v;\n      },\n      (e) => {\n        promise.status = \"rejected\";\n        promise.reason = e;\n      }\n    );\n  }\n};\nconst use = React.use || // A shim for older React versions\n((promise) => {\n  if (promise.status === \"pending\") {\n    throw promise;\n  } else if (promise.status === \"fulfilled\") {\n    return promise.value;\n  } else if (promise.status === \"rejected\") {\n    throw promise.reason;\n  } else {\n    attachPromiseStatus(promise);\n    throw promise;\n  }\n});\nconst continuablePromiseMap = /* @__PURE__ */ new WeakMap();\nconst createContinuablePromise = (promise, getValue) => {\n  let continuablePromise = continuablePromiseMap.get(promise);\n  if (!continuablePromise) {\n    continuablePromise = new Promise((resolve, reject) => {\n      let curr = promise;\n      const onFulfilled = (me) => (v) => {\n        if (curr === me) {\n          resolve(v);\n        }\n      };\n      const onRejected = (me) => (e) => {\n        if (curr === me) {\n          reject(e);\n        }\n      };\n      const onAbort = () => {\n        try {\n          const nextValue = getValue();\n          if (isPromiseLike(nextValue)) {\n            continuablePromiseMap.set(nextValue, continuablePromise);\n            curr = nextValue;\n            nextValue.then(onFulfilled(nextValue), onRejected(nextValue));\n            INTERNAL_registerAbortHandler(nextValue, onAbort);\n          } else {\n            resolve(nextValue);\n          }\n        } catch (e) {\n          reject(e);\n        }\n      };\n      promise.then(onFulfilled(promise), onRejected(promise));\n      INTERNAL_registerAbortHandler(promise, onAbort);\n    });\n    continuablePromiseMap.set(promise, continuablePromise);\n  }\n  return continuablePromise;\n};\nfunction useAtomValue(atom, options) {\n  const { delay, unstable_promiseStatus: promiseStatus = !React.use } = options || {};\n  const store = useStore(options);\n  const [[valueFromReducer, storeFromReducer, atomFromReducer], rerender] = useReducer(\n    (prev) => {\n      const nextValue = store.get(atom);\n      if (Object.is(prev[0], nextValue) && prev[1] === store && prev[2] === atom) {\n        return prev;\n      }\n      return [nextValue, store, atom];\n    },\n    void 0,\n    () => [store.get(atom), store, atom]\n  );\n  let value = valueFromReducer;\n  if (storeFromReducer !== store || atomFromReducer !== atom) {\n    rerender();\n    value = store.get(atom);\n  }\n  useEffect(() => {\n    const unsub = store.sub(atom, () => {\n      if (promiseStatus) {\n        try {\n          const value2 = store.get(atom);\n          if (isPromiseLike(value2)) {\n            attachPromiseStatus(\n              createContinuablePromise(value2, () => store.get(atom))\n            );\n          }\n        } catch (e) {\n        }\n      }\n      if (typeof delay === \"number\") {\n        setTimeout(rerender, delay);\n        return;\n      }\n      rerender();\n    });\n    rerender();\n    return unsub;\n  }, [store, atom, delay, promiseStatus]);\n  useDebugValue(value);\n  if (isPromiseLike(value)) {\n    const promise = createContinuablePromise(value, () => store.get(atom));\n    if (promiseStatus) {\n      attachPromiseStatus(promise);\n    }\n    return use(promise);\n  }\n  return value;\n}\n\nfunction useSetAtom(atom, options) {\n  const store = useStore(options);\n  const setAtom = useCallback(\n    (...args) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !(\"write\" in atom)) {\n        throw new Error(\"not writable atom\");\n      }\n      return store.set(atom, ...args);\n    },\n    [store, atom]\n  );\n  return setAtom;\n}\n\nfunction useAtom(atom, options) {\n  return [\n    useAtomValue(atom, options),\n    // We do wrong type assertion here, which results in throwing an error.\n    useSetAtom(atom, options)\n  ];\n}\n\nexport { Provider, useAtom, useAtomValue, useSetAtom, useStore };\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA,UAAM,IAAI,MAAM,iEAAiE;AAAA;AAAA;;;ACAjF,IAAM,aAAa,CAACA,OAAM,MAAMA,MAAK,cAAcA,MAAK,YAAY,CAAC,IAAI,MAAMA;AAC/E,IAAM,kBAAkB,CAACA,UAAS,UAAUA;AAC5C,IAAM,yBAAyB,CAACA,UAAS,CAAC,CAACA,MAAK;AAChD,IAAM,yBAAyB,CAAC,cAAc,OAAO,aAAa,OAAO;AACzE,IAAM,kBAAkB,CAAC,cAAc;AACrC,MAAI,OAAO,WAAW;AACpB,UAAM,UAAU;AAAA,EAClB;AACA,OAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,EAAE,OAAO,YAAY;AAC7F,UAAM,IAAI,MAAM,qCAAqC;AAAA,EACvD;AACA,SAAO,UAAU;AACnB;AACA,IAAM,kBAAkC,oBAAI,QAAQ;AACpD,IAAM,mBAAmB,CAAC,UAAU;AAClC,MAAI;AACJ,SAAO,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,gBAAgB,IAAI,KAAK,MAAM,OAAO,SAAS,GAAG,CAAC;AAC7F;AACA,IAAM,eAAe,CAAC,YAAY;AAChC,QAAM,eAAe,gBAAgB,IAAI,OAAO;AAChD,MAAI,gBAAgB,OAAO,SAAS,aAAa,CAAC,GAAG;AACnD,iBAAa,CAAC,IAAI;AAClB,iBAAa,CAAC,EAAE,QAAQ,CAAC,OAAO,GAAG,CAAC;AAAA,EACtC;AACF;AACA,IAAM,uBAAuB,CAAC,SAAS,iBAAiB;AACtD,MAAI,eAAe,gBAAgB,IAAI,OAAO;AAC9C,MAAI,CAAC,cAAc;AACjB,mBAAe,CAAC,MAAsB,oBAAI,IAAI,CAAC;AAC/C,oBAAgB,IAAI,SAAS,YAAY;AACzC,UAAM,SAAS,MAAM;AACnB,mBAAa,CAAC,IAAI;AAAA,IACpB;AACA,YAAQ,KAAK,QAAQ,MAAM;AAAA,EAC7B;AACA,eAAa,CAAC,EAAE,IAAI,YAAY;AAClC;AACA,IAAM,gBAAgB,CAAC,MAAM,QAAQ,KAAK,OAAO,SAAS,EAAE,UAAU;AACtE,IAAM,gCAAgC,CAACA,OAAM,SAAS,wBAAwB;AAC5E,MAAI,CAAC,oBAAoB,EAAE,IAAIA,KAAI,GAAG;AACpC,wBAAoB,EAAE,IAAIA,KAAI;AAC9B,YAAQ;AAAA,MACN,MAAM;AACJ,4BAAoB,EAAE,OAAOA,KAAI;AAAA,MACnC;AAAA,MACA,MAAM;AACJ,4BAAoB,EAAE,OAAOA,KAAI;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,6BAA6B,CAACA,OAAM,gBAAgB,oBAAoB;AAC5E,QAAM,YAAY,gBAAgBA,KAAI;AACtC,QAAM,eAAe,OAAO;AAC5B,QAAM,YAAY,UAAU;AAC5B,MAAI,cAAc,cAAc,GAAG;AACjC,eAAW,KAAK,UAAU,EAAE,KAAK,GAAG;AAClC,oCAA8BA,OAAM,gBAAgB,gBAAgB,CAAC,CAAC;AAAA,IACxE;AAAA,EACF;AACA,YAAU,IAAI;AACd,SAAO,UAAU;AACjB,MAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,WAAW,UAAU,CAAC,GAAG;AACvD,MAAE,UAAU;AACZ,QAAI,cAAc,SAAS,GAAG;AAC5B,mBAAa,SAAS;AAAA,IACxB;AAAA,EACF;AACF;AACA,IAAM,gCAAgC,CAACA,OAAM,WAAW,eAAe;AACrE,MAAI;AACJ,QAAM,aAA6B,oBAAI,IAAI;AAC3C,aAAW,OAAO,KAAK,WAAW,IAAIA,KAAI,MAAM,OAAO,SAAS,GAAG,MAAM,CAAC,GAAG;AAC3E,QAAI,WAAW,IAAI,CAAC,GAAG;AACrB,iBAAW,IAAI,CAAC;AAAA,IAClB;AAAA,EACF;AACA,aAAW,0BAA0B,UAAU,GAAG;AAChD,eAAW,IAAI,sBAAsB;AAAA,EACvC;AACA,SAAO;AACT;AA0CA,IAAM,kBAAkB,OAAO;AAE/B,IAAM,aAAa,CAAC,eAA+B,oBAAI,QAAQ,GAAG,aAA6B,oBAAI,QAAQ,GAAG,mBAAmC,oBAAI,QAAQ,GAAG,eAA+B,oBAAI,IAAI,GAAG,iBAAiC,oBAAI,IAAI,GAAG,mBAAmC,oBAAI,IAAI,GAAG,aAAa,CAAC,GAAG,WAAW,CAACC,UAAS,WAAWA,MAAK,KAAK,GAAG,MAAM,GAAG,YAAY,CAACA,UAAS,WAAWA,MAAK,MAAM,GAAG,MAAM,GAAG,aAAa,CAACA,OAAM,UAAU;AAC/b,MAAI;AACJ,UAAQ,KAAKA,MAAK,oBAAoB,OAAO,SAAS,GAAG,KAAKA,OAAM,KAAK;AAC3E,GAAG,cAAc,CAACA,OAAM,YAAY;AAClC,MAAI;AACJ,UAAQ,KAAKA,MAAK,YAAY,OAAO,SAAS,GAAG,KAAKA,OAAM,OAAO;AACrE,MAAM,2BAA2B;AAC/B,QAAM,kBAAkB,uBAAuB,CAAC,MAAM,CAACA,UAAS;AAC9D,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,CAACA,OAAM;AAC/E,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AACA,QAAI,YAAY,aAAa,IAAIA,KAAI;AACrC,QAAI,CAAC,WAAW;AACd,kBAAY,EAAE,GAAmB,oBAAI,IAAI,GAAG,GAAmB,oBAAI,IAAI,GAAG,GAAG,EAAE;AAC/E,mBAAa,IAAIA,OAAM,SAAS;AAChC,oBAAc,OAAO,SAAS,WAAWA,OAAM,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AACA,QAAM,iBAAiB,uBAAuB,CAAC,MAAM,MAAM;AACzD,UAAM,SAAS,CAAC;AAChB,UAAM,OAAO,CAAC,OAAO;AACnB,UAAI;AACF,WAAG;AAAA,MACL,SAAS,GAAG;AACV,eAAO,KAAK,CAAC;AAAA,MACf;AAAA,IACF;AACA,OAAG;AACD,UAAI,WAAW,GAAG;AAChB,aAAK,WAAW,CAAC;AAAA,MACnB;AACA,YAAM,YAA4B,oBAAI,IAAI;AAC1C,YAAM,MAAM,UAAU,IAAI,KAAK,SAAS;AACxC,mBAAa,QAAQ,CAACA,UAAS;AAC7B,YAAI;AACJ,gBAAQ,KAAK,WAAW,IAAIA,KAAI,MAAM,OAAO,SAAS,GAAG,EAAE,QAAQ,GAAG;AAAA,MACxE,CAAC;AACD,mBAAa,MAAM;AACnB,uBAAiB,QAAQ,GAAG;AAC5B,uBAAiB,MAAM;AACvB,qBAAe,QAAQ,GAAG;AAC1B,qBAAe,MAAM;AACrB,gBAAU,QAAQ,IAAI;AACtB,UAAI,aAAa,MAAM;AACrB,kCAA0B;AAAA,MAC5B;AAAA,IACF,SAAS,aAAa,QAAQ,iBAAiB,QAAQ,eAAe;AACtE,QAAI,OAAO,QAAQ;AACjB,YAAM,IAAI,eAAe,MAAM;AAAA,IACjC;AAAA,EACF;AACA,QAAM,4BAA4B,uBAAuB,CAAC,MAAM,MAAM;AACpE,UAAM,oBAAoB,CAAC;AAC3B,UAAM,WAA2B,oBAAI,QAAQ;AAC7C,UAAM,UAA0B,oBAAI,QAAQ;AAC5C,UAAM,QAAQ,MAAM,KAAK,YAAY;AACrC,WAAO,MAAM,QAAQ;AACnB,YAAM,IAAI,MAAM,MAAM,SAAS,CAAC;AAChC,YAAM,SAAS,gBAAgB,CAAC;AAChC,UAAI,QAAQ,IAAI,CAAC,GAAG;AAClB,cAAM,IAAI;AACV;AAAA,MACF;AACA,UAAI,SAAS,IAAI,CAAC,GAAG;AACnB,YAAI,iBAAiB,IAAI,CAAC,MAAM,OAAO,GAAG;AACxC,4BAAkB,KAAK,CAAC,GAAG,MAAM,CAAC;AAAA,QACpC,YAAY,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,iBAAiB,IAAI,CAAC,GAAG;AACxG,gBAAM,IAAI,MAAM,+BAA+B;AAAA,QACjD;AACA,gBAAQ,IAAI,CAAC;AACb,cAAM,IAAI;AACV;AAAA,MACF;AACA,eAAS,IAAI,CAAC;AACd,iBAAW,KAAK,8BAA8B,GAAG,QAAQ,UAAU,GAAG;AACpE,YAAI,CAAC,SAAS,IAAI,CAAC,GAAG;AACpB,gBAAM,KAAK,CAAC;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,aAAS,IAAI,kBAAkB,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACtD,YAAM,CAAC,GAAG,MAAM,IAAI,kBAAkB,CAAC;AACvC,UAAI,iBAAiB;AACrB,iBAAW,OAAO,OAAO,EAAE,KAAK,GAAG;AACjC,YAAI,QAAQ,KAAK,aAAa,IAAI,GAAG,GAAG;AACtC,2BAAiB;AACjB;AAAA,QACF;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,sBAAc,CAAC;AACf,0BAAkB,CAAC;AAAA,MACrB;AACA,uBAAiB,OAAO,CAAC;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,CAAC,MAAM,CAACA,UAAS;AAC5D,QAAI;AACJ,UAAM,YAAY,gBAAgBA,KAAI;AACtC,QAAI,uBAAuB,SAAS,GAAG;AACrC,UAAI,WAAW,IAAIA,KAAI,KAAK,iBAAiB,IAAIA,KAAI,MAAM,UAAU,GAAG;AACtE,eAAO;AAAA,MACT;AACA,UAAI,MAAM,KAAK,UAAU,CAAC,EAAE;AAAA,QAC1B,CAAC,CAAC,GAAG,CAAC;AAAA;AAAA;AAAA,UAGJ,cAAc,CAAC,EAAE,MAAM;AAAA;AAAA,MAE3B,GAAG;AACD,eAAO;AAAA,MACT;AAAA,IACF;AACA,cAAU,EAAE,MAAM;AAClB,QAAI,SAAS;AACb,UAAM,2BAA2B,MAAM;AACrC,UAAI,WAAW,IAAIA,KAAI,GAAG;AACxB,0BAAkBA,KAAI;AACtB,kCAA0B;AAC1B,uBAAe;AAAA,MACjB;AAAA,IACF;AACA,UAAM,SAAS,CAAC,MAAM;AACpB,UAAI;AACJ,UAAI,WAAWA,OAAM,CAAC,GAAG;AACvB,cAAM,UAAU,gBAAgB,CAAC;AACjC,YAAI,CAAC,uBAAuB,OAAO,GAAG;AACpC,cAAI,gBAAgB,CAAC,GAAG;AACtB,uCAA2B,GAAG,EAAE,MAAM,eAAe;AAAA,UACvD,OAAO;AACL,kBAAM,IAAI,MAAM,cAAc;AAAA,UAChC;AAAA,QACF;AACA,eAAO,gBAAgB,OAAO;AAAA,MAChC;AACA,YAAM,SAAS,cAAc,CAAC;AAC9B,UAAI;AACF,eAAO,gBAAgB,MAAM;AAAA,MAC/B,UAAE;AACA,kBAAU,EAAE,IAAI,GAAG,OAAO,CAAC;AAC3B,YAAI,iBAAiB,UAAU,CAAC,GAAG;AACjC,wCAA8BA,OAAM,UAAU,GAAG,MAAM;AAAA,QACzD;AACA,SAAC,MAAM,WAAW,IAAI,CAAC,MAAM,OAAO,SAAS,IAAI,EAAE,IAAIA,KAAI;AAC3D,YAAI,CAAC,QAAQ;AACX,mCAAyB;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACJ,UAAM,UAAU;AAAA,MACd,IAAI,SAAS;AACX,YAAI,CAAC,YAAY;AACf,uBAAa,IAAI,gBAAgB;AAAA,QACnC;AACA,eAAO,WAAW;AAAA,MACpB;AAAA,MACA,IAAI,UAAU;AACZ,aAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,CAAC,uBAAuBA,KAAI,GAAG;AACvG,kBAAQ,KAAK,qDAAqD;AAAA,QACpE;AACA,YAAI,CAAC,WAAW,uBAAuBA,KAAI,GAAG;AAC5C,oBAAU,IAAI,SAAS;AACrB,iBAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,QAAQ;AAChF,sBAAQ,KAAK,2CAA2C;AAAA,YAC1D;AACA,gBAAI,CAAC,QAAQ;AACX,kBAAI;AACF,uBAAO,eAAeA,OAAM,GAAG,IAAI;AAAA,cACrC,UAAE;AACA,0CAA0B;AAC1B,+BAAe;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,kBAAkB,UAAU;AAClC,QAAI;AACF,YAAM,iBAAiB,SAASA,OAAM,QAAQ,OAAO;AACrD,iCAA2BA,OAAM,gBAAgB,eAAe;AAChE,UAAI,cAAc,cAAc,GAAG;AACjC,6BAAqB,gBAAgB,MAAM,cAAc,OAAO,SAAS,WAAW,MAAM,CAAC;AAC3F,uBAAe;AAAA,UACb;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT,SAAS,OAAO;AACd,aAAO,UAAU;AACjB,gBAAU,IAAI;AACd,QAAE,UAAU;AACZ,aAAO;AAAA,IACT,UAAE;AACA,eAAS;AACT,UAAI,oBAAoB,UAAU,KAAK,iBAAiB,IAAIA,KAAI,MAAM,iBAAiB;AACrF,yBAAiB,IAAIA,OAAM,UAAU,CAAC;AACtC,qBAAa,IAAIA,KAAI;AACrB,SAAC,KAAK,WAAW,MAAM,OAAO,SAAS,GAAG,KAAK,YAAYA,KAAI;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACA,QAAM,uBAAuB,uBAAuB,CAAC,MAAM,CAACA,UAAS;AACnE,UAAM,QAAQ,CAACA,KAAI;AACnB,WAAO,MAAM,QAAQ;AACnB,YAAM,IAAI,MAAM,IAAI;AACpB,YAAM,SAAS,gBAAgB,CAAC;AAChC,iBAAW,KAAK,8BAA8B,GAAG,QAAQ,UAAU,GAAG;AACpE,cAAM,SAAS,gBAAgB,CAAC;AAChC,yBAAiB,IAAI,GAAG,OAAO,CAAC;AAChC,cAAM,KAAK,CAAC;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,uBAAuB,CAAC,MAAM,CAACA,UAAS,SAAS;AACtE,QAAI,SAAS;AACb,UAAM,SAAS,CAAC,MAAM,gBAAgB,cAAc,CAAC,CAAC;AACtD,UAAM,SAAS,CAAC,MAAM,UAAU;AAC9B,UAAI;AACJ,YAAM,SAAS,gBAAgB,CAAC;AAChC,UAAI;AACF,YAAI,WAAWA,OAAM,CAAC,GAAG;AACvB,cAAI,CAAC,gBAAgB,CAAC,GAAG;AACvB,kBAAM,IAAI,MAAM,mBAAmB;AAAA,UACrC;AACA,gBAAM,kBAAkB,OAAO;AAC/B,gBAAM,IAAI,MAAM,CAAC;AACjB,qCAA2B,GAAG,GAAG,eAAe;AAChD,4BAAkB,CAAC;AACnB,cAAI,oBAAoB,OAAO,GAAG;AAChC,yBAAa,IAAI,CAAC;AAClB,aAAC,KAAK,WAAW,MAAM,OAAO,SAAS,GAAG,KAAK,YAAY,CAAC;AAC5D,iCAAqB,CAAC;AAAA,UACxB;AACA,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,eAAe,GAAG,GAAG,KAAK;AAAA,QACnC;AAAA,MACF,UAAE;AACA,YAAI,CAAC,QAAQ;AACX,oCAA0B;AAC1B,yBAAe;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACF,aAAO,UAAUA,OAAM,QAAQ,QAAQ,GAAG,IAAI;AAAA,IAChD,UAAE;AACA,eAAS;AAAA,IACX;AAAA,EACF;AACA,QAAM,oBAAoB,uBAAuB,CAAC,MAAM,CAACA,UAAS;AAChE,QAAI;AACJ,UAAM,YAAY,gBAAgBA,KAAI;AACtC,UAAM,UAAU,WAAW,IAAIA,KAAI;AACnC,QAAI,WAAW,CAAC,iBAAiB,UAAU,CAAC,GAAG;AAC7C,iBAAW,CAAC,GAAG,CAAC,KAAK,UAAU,GAAG;AAChC,YAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG;AACrB,gBAAM,SAAS,gBAAgB,CAAC;AAChC,gBAAM,WAAW,UAAU,CAAC;AAC5B,mBAAS,EAAE,IAAIA,KAAI;AACnB,kBAAQ,EAAE,IAAI,CAAC;AACf,cAAI,MAAM,OAAO,GAAG;AAClB,yBAAa,IAAI,CAAC;AAClB,aAAC,KAAK,WAAW,MAAM,OAAO,SAAS,GAAG,KAAK,YAAY,CAAC;AAC5D,iCAAqB,CAAC;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AACA,iBAAW,KAAK,QAAQ,KAAK,CAAC,GAAG;AAC/B,YAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG;AACvB,kBAAQ,EAAE,OAAO,CAAC;AAClB,gBAAM,WAAW,YAAY,CAAC;AAC9B,sBAAY,OAAO,SAAS,SAAS,EAAE,OAAOA,KAAI;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,uBAAuB,CAAC,MAAM,CAACA,UAAS;AACxD,QAAI;AACJ,UAAM,YAAY,gBAAgBA,KAAI;AACtC,QAAI,UAAU,WAAW,IAAIA,KAAI;AACjC,QAAI,CAAC,SAAS;AACZ,oBAAcA,KAAI;AAClB,iBAAW,KAAK,UAAU,EAAE,KAAK,GAAG;AAClC,cAAM,WAAW,UAAU,CAAC;AAC5B,iBAAS,EAAE,IAAIA,KAAI;AAAA,MACrB;AACA,gBAAU;AAAA,QACR,GAAmB,oBAAI,IAAI;AAAA,QAC3B,GAAG,IAAI,IAAI,UAAU,EAAE,KAAK,CAAC;AAAA,QAC7B,GAAmB,oBAAI,IAAI;AAAA,MAC7B;AACA,iBAAW,IAAIA,OAAM,OAAO;AAC5B,OAAC,KAAK,WAAW,MAAM,OAAO,SAAS,GAAG,KAAK,YAAYA,KAAI;AAC/D,UAAI,uBAAuBA,KAAI,GAAG;AAChC,cAAM,iBAAiB,MAAM;AAC3B,cAAI,SAAS;AACb,gBAAM,UAAU,IAAI,SAAS;AAC3B,gBAAI;AACF,qBAAO,eAAeA,OAAM,GAAG,IAAI;AAAA,YACrC,UAAE;AACA,kBAAI,CAAC,QAAQ;AACX,0CAA0B;AAC1B,+BAAe;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AACA,cAAI;AACF,kBAAM,YAAY,YAAYA,OAAM,OAAO;AAC3C,gBAAI,WAAW;AACb,sBAAQ,IAAI,MAAM;AAChB,yBAAS;AACT,oBAAI;AACF,4BAAU;AAAA,gBACZ,UAAE;AACA,2BAAS;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAAA,UACF,UAAE;AACA,qBAAS;AAAA,UACX;AAAA,QACF;AACA,uBAAe,IAAI,cAAc;AAAA,MACnC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAc,uBAAuB,CAAC,MAAM,CAACA,UAAS;AAC1D,QAAI;AACJ,UAAM,YAAY,gBAAgBA,KAAI;AACtC,QAAI,UAAU,WAAW,IAAIA,KAAI;AACjC,QAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,EAAE,KAAK,CAAC,MAAM;AACnE,UAAI;AACJ,cAAQ,MAAM,WAAW,IAAI,CAAC,MAAM,OAAO,SAAS,IAAI,EAAE,IAAIA,KAAI;AAAA,IACpE,CAAC,GAAG;AACF,UAAI,QAAQ,GAAG;AACb,yBAAiB,IAAI,QAAQ,CAAC;AAAA,MAChC;AACA,gBAAU;AACV,iBAAW,OAAOA,KAAI;AACtB,OAAC,KAAK,WAAW,MAAM,OAAO,SAAS,GAAG,KAAK,YAAYA,KAAI;AAC/D,iBAAW,KAAK,UAAU,EAAE,KAAK,GAAG;AAClC,cAAM,WAAW,YAAY,CAAC;AAC9B,oBAAY,OAAO,SAAS,SAAS,EAAE,OAAOA,KAAI;AAAA,MACpD;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,iBAAiB;AAAA;AAAA,IAErB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,QAAQ;AAAA,IACZ,KAAK,CAACA,UAAS,gBAAgB,cAAcA,KAAI,CAAC;AAAA,IAClD,KAAK,CAACA,UAAS,SAAS;AACtB,UAAI;AACF,eAAO,eAAeA,OAAM,GAAG,IAAI;AAAA,MACrC,UAAE;AACA,kCAA0B;AAC1B,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,IACA,KAAK,CAACA,OAAM,aAAa;AACvB,YAAM,UAAU,UAAUA,KAAI;AAC9B,YAAM,YAAY,QAAQ;AAC1B,gBAAU,IAAI,QAAQ;AACtB,qBAAe;AACf,aAAO,MAAM;AACX,kBAAU,OAAO,QAAQ;AACzB,oBAAYA,KAAI;AAChB,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO,eAAe,OAAO,iBAAiB,EAAE,OAAO,eAAe,CAAC;AACvE,SAAO;AACT;AACA,IAAM,0BAA0B;AAWhC,IAAM,gCAAgC;;;AC5hBtC,IAAI,WAAW;AACf,SAAS,KAAK,MAAM,OAAO;AACzB,QAAM,MAAM,OAAO,EAAE,QAAQ;AAC7B,QAAM,SAAS;AAAA,IACb,WAAW;AACT,cAAQ,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,KAAK,aAAa,MAAM,MAAM,KAAK,aAAa;AAAA,IAC/H;AAAA,EACF;AACA,MAAI,OAAO,SAAS,YAAY;AAC9B,WAAO,OAAO;AAAA,EAChB,OAAO;AACL,WAAO,OAAO;AACd,WAAO,OAAO;AACd,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,OAAO;AACT,WAAO,QAAQ;AAAA,EACjB;AACA,SAAO;AACT;AACA,SAAS,YAAY,KAAK;AACxB,SAAO,IAAI,IAAI;AACjB;AACA,SAAS,aAAa,KAAK,KAAK,KAAK;AACnC,SAAO;AAAA,IACL;AAAA,IACA,OAAO,QAAQ,aAAa,IAAI,IAAI,IAAI,CAAC,IAAI;AAAA,EAC/C;AACF;AAEA,IAAI;AACJ,SAAS,6BAA6B,IAAI;AACxC,yBAAuB,GAAG,oBAAoB;AAChD;AACA,SAAS,cAAc;AACrB,MAAI,sBAAsB;AACxB,WAAO,qBAAqB;AAAA,EAC9B;AACA,SAAO,wBAAwB;AACjC;AACA,IAAI;AACJ,SAAS,kBAAkB;AACzB,MAAI,CAAC,cAAc;AACjB,mBAAe,YAAY;AAC3B,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,cAAc;AACtE,iBAAW,4BAA4B,WAAW,0BAA0B;AAC5E,UAAI,WAAW,4BAA4B,cAAc;AACvD,gBAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACvDA,mBAA2H;AAI3H,IAAM,eAAe;AAAA,EACnB;AACF;AACA,SAAS,SAAS,SAAS;AACzB,QAAM,QAAQ,SAAW,YAAY;AACrC,UAAQ,WAAW,OAAO,SAAS,QAAQ,UAAU,SAAS,gBAAgB;AAChF;AACA,SAAS,SAAS;AAAA,EAChB;AAAA,EACA;AACF,GAAG;AACD,QAAM,WAAW,SAAO,MAAM;AAC9B,MAAI,CAAC,SAAS,CAAC,SAAS,SAAS;AAC/B,aAAS,UAAU,YAAY;AAAA,EACjC;AACA,SAAO;AAAA,IACL,aAAa;AAAA,IACb;AAAA,MACE,OAAO,SAAS,SAAS;AAAA,IAC3B;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAMC,iBAAgB,CAAC,MAAM,QAAQ,KAAK,OAAO,SAAS,EAAE,UAAU;AACtE,IAAM,sBAAsB,CAAC,YAAY;AACvC,MAAI,CAAC,QAAQ,QAAQ;AACnB,YAAQ,SAAS;AACjB,YAAQ;AAAA,MACN,CAAC,MAAM;AACL,gBAAQ,SAAS;AACjB,gBAAQ,QAAQ;AAAA,MAClB;AAAA,MACA,CAAC,MAAM;AACL,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,MAAM,aAAAC,QAAM;AAAA,CACjB,CAAC,YAAY;AACZ,MAAI,QAAQ,WAAW,WAAW;AAChC,UAAM;AAAA,EACR,WAAW,QAAQ,WAAW,aAAa;AACzC,WAAO,QAAQ;AAAA,EACjB,WAAW,QAAQ,WAAW,YAAY;AACxC,UAAM,QAAQ;AAAA,EAChB,OAAO;AACL,wBAAoB,OAAO;AAC3B,UAAM;AAAA,EACR;AACF;AACA,IAAM,wBAAwC,oBAAI,QAAQ;AAC1D,IAAM,2BAA2B,CAAC,SAAS,aAAa;AACtD,MAAI,qBAAqB,sBAAsB,IAAI,OAAO;AAC1D,MAAI,CAAC,oBAAoB;AACvB,yBAAqB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpD,UAAI,OAAO;AACX,YAAM,cAAc,CAAC,OAAO,CAAC,MAAM;AACjC,YAAI,SAAS,IAAI;AACf,kBAAQ,CAAC;AAAA,QACX;AAAA,MACF;AACA,YAAM,aAAa,CAAC,OAAO,CAAC,MAAM;AAChC,YAAI,SAAS,IAAI;AACf,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AACA,YAAM,UAAU,MAAM;AACpB,YAAI;AACF,gBAAM,YAAY,SAAS;AAC3B,cAAID,eAAc,SAAS,GAAG;AAC5B,kCAAsB,IAAI,WAAW,kBAAkB;AACvD,mBAAO;AACP,sBAAU,KAAK,YAAY,SAAS,GAAG,WAAW,SAAS,CAAC;AAC5D,0CAA8B,WAAW,OAAO;AAAA,UAClD,OAAO;AACL,oBAAQ,SAAS;AAAA,UACnB;AAAA,QACF,SAAS,GAAG;AACV,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AACA,cAAQ,KAAK,YAAY,OAAO,GAAG,WAAW,OAAO,CAAC;AACtD,oCAA8B,SAAS,OAAO;AAAA,IAChD,CAAC;AACD,0BAAsB,IAAI,SAAS,kBAAkB;AAAA,EACvD;AACA,SAAO;AACT;AACA,SAAS,aAAaE,OAAM,SAAS;AACnC,QAAM,EAAE,OAAO,wBAAwB,gBAAgB,CAAC,aAAAD,QAAM,IAAI,IAAI,WAAW,CAAC;AAClF,QAAM,QAAQ,SAAS,OAAO;AAC9B,QAAM,CAAC,CAAC,kBAAkB,kBAAkB,eAAe,GAAG,QAAQ,IAAI;AAAA,IACxE,CAAC,SAAS;AACR,YAAM,YAAY,MAAM,IAAIC,KAAI;AAChC,UAAI,OAAO,GAAG,KAAK,CAAC,GAAG,SAAS,KAAK,KAAK,CAAC,MAAM,SAAS,KAAK,CAAC,MAAMA,OAAM;AAC1E,eAAO;AAAA,MACT;AACA,aAAO,CAAC,WAAW,OAAOA,KAAI;AAAA,IAChC;AAAA,IACA;AAAA,IACA,MAAM,CAAC,MAAM,IAAIA,KAAI,GAAG,OAAOA,KAAI;AAAA,EACrC;AACA,MAAI,QAAQ;AACZ,MAAI,qBAAqB,SAAS,oBAAoBA,OAAM;AAC1D,aAAS;AACT,YAAQ,MAAM,IAAIA,KAAI;AAAA,EACxB;AACA,WAAU,MAAM;AACd,UAAM,QAAQ,MAAM,IAAIA,OAAM,MAAM;AAClC,UAAI,eAAe;AACjB,YAAI;AACF,gBAAM,SAAS,MAAM,IAAIA,KAAI;AAC7B,cAAIF,eAAc,MAAM,GAAG;AACzB;AAAA,cACE,yBAAyB,QAAQ,MAAM,MAAM,IAAIE,KAAI,CAAC;AAAA,YACxD;AAAA,UACF;AAAA,QACF,SAAS,GAAG;AAAA,QACZ;AAAA,MACF;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,mBAAW,UAAU,KAAK;AAC1B;AAAA,MACF;AACA,eAAS;AAAA,IACX,CAAC;AACD,aAAS;AACT,WAAO;AAAA,EACT,GAAG,CAAC,OAAOA,OAAM,OAAO,aAAa,CAAC;AACtC,WAAc,KAAK;AACnB,MAAIF,eAAc,KAAK,GAAG;AACxB,UAAM,UAAU,yBAAyB,OAAO,MAAM,MAAM,IAAIE,KAAI,CAAC;AACrE,QAAI,eAAe;AACjB,0BAAoB,OAAO;AAAA,IAC7B;AACA,WAAO,IAAI,OAAO;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,WAAWA,OAAM,SAAS;AACjC,QAAM,QAAQ,SAAS,OAAO;AAC9B,QAAM,UAAU;AAAA,IACd,IAAI,SAAS;AACX,WAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,EAAE,WAAWA,QAAO;AAC5F,cAAM,IAAI,MAAM,mBAAmB;AAAA,MACrC;AACA,aAAO,MAAM,IAAIA,OAAM,GAAG,IAAI;AAAA,IAChC;AAAA,IACA,CAAC,OAAOA,KAAI;AAAA,EACd;AACA,SAAO;AACT;AAEA,SAAS,QAAQA,OAAM,SAAS;AAC9B,SAAO;AAAA,IACL,aAAaA,OAAM,OAAO;AAAA;AAAA,IAE1B,WAAWA,OAAM,OAAO;AAAA,EAC1B;AACF;", "names": ["atom", "atom", "isPromiseLike", "React", "atom"]}