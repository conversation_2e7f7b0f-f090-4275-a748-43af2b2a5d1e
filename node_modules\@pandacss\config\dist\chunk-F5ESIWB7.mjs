// src/merge-config.ts
import { assign, mergeWith, walkObject } from "@pandacss/shared";
import { mergeAndConcat } from "merge-anything";

// src/merge-hooks.ts
import { logger } from "@pandacss/logger";
var mergeHooks = (plugins) => {
  const hooksFns = {};
  plugins.forEach(({ name, hooks }) => {
    Object.entries(hooks ?? {}).forEach(([key, value]) => {
      if (!hooksFns[key]) {
        hooksFns[key] = [];
      }
      hooksFns[key].push([name, value]);
    });
  });
  const mergedHooks = Object.fromEntries(
    Object.entries(hooksFns).map(([key, entries]) => {
      const fns = entries.map(([name, fn]) => tryCatch(name, fn));
      const reducer = key in reducers ? reducers[key] : void 0;
      if (reducer) {
        return [key, reducer(fns)];
      }
      return [key, syncHooks.includes(key) ? callAll(...fns) : callAllAsync(...fns)];
    })
  );
  return mergedHooks;
};
var createReducer = (reducer) => reducer;
var reducers = {
  "config:resolved": createReducer((fns) => async (_args) => {
    const args = Object.assign({}, _args);
    const original = _args.config;
    let config = args.config;
    for (const hookFn of fns) {
      const result = await hookFn(Object.assign(args, { config, original }));
      if (result !== void 0) {
        config = result;
      }
    }
    return config;
  }),
  "parser:before": createReducer((fns) => (_args) => {
    const args = Object.assign({}, _args);
    const original = _args.content;
    let content = args.content;
    for (const hookFn of fns) {
      const result = hookFn(Object.assign(args, { content, original }));
      if (result !== void 0) {
        content = result;
      }
    }
    return content;
  }),
  "parser:preprocess": createReducer((fns) => (_args) => {
    const args = Object.assign({}, _args);
    const original = _args.data;
    let data = args.data;
    for (const hookFn of fns) {
      const result = hookFn(Object.assign(args, { data, original }));
      if (result !== void 0) {
        data = result;
      }
    }
    return data;
  }),
  "cssgen:done": createReducer((fns) => (_args) => {
    const args = Object.assign({}, _args);
    const original = _args.content;
    let content = args.content;
    for (const hookFn of fns) {
      const result = hookFn(Object.assign(args, { content, original }));
      if (result !== void 0) {
        content = result;
      }
    }
    return content;
  }),
  "codegen:prepare": createReducer((fns) => async (_args) => {
    const args = Object.assign({}, _args);
    const original = _args.artifacts;
    let artifacts = args.artifacts;
    for (const hookFn of fns) {
      const result = await hookFn(Object.assign(args, { artifacts, original }));
      if (result) {
        artifacts = result;
      }
    }
    return artifacts;
  })
};
var syncHooks = [
  "context:created",
  "parser:before",
  "parser:preprocess",
  "parser:after",
  "cssgen:done"
];
var callAllAsync = (...fns) => async (...a) => {
  for (const fn of fns) {
    await fn?.(...a);
  }
};
var callAll = (...fns) => (...a) => {
  fns.forEach((fn) => fn?.(...a));
};
var tryCatch = (name, fn) => {
  return (...args) => {
    try {
      return fn(...args);
    } catch (e) {
      logger.error("hooks", `The error below comes from the plugin ${name}`);
      console.error(e);
    }
  };
};

// src/validation/utils.ts
import { isObject, isString } from "@pandacss/shared";
var REFERENCE_REGEX = /({([^}]*)})/g;
var curlyBracketRegex = /[{}]/g;
var isValidToken = (token) => isObject(token) && Object.hasOwnProperty.call(token, "value");
var isTokenReference = (value) => typeof value === "string" && REFERENCE_REGEX.test(value);
var formatPath = (path) => path;
var SEP = ".";
function getReferences(value) {
  if (typeof value !== "string")
    return [];
  const matches = value.match(REFERENCE_REGEX);
  if (!matches)
    return [];
  return matches.map((match) => match.replace(curlyBracketRegex, "")).map((value2) => {
    return value2.trim().split("/")[0];
  });
}
var serializeTokenValue = (value) => {
  if (isString(value)) {
    return value;
  }
  if (isObject(value)) {
    return Object.values(value).map((v) => serializeTokenValue(v)).join(" ");
  }
  if (Array.isArray(value)) {
    return value.map((v) => serializeTokenValue(v)).join(" ");
  }
  return value.toString();
};

// src/merge-config.ts
function getExtends(items) {
  return items.reduce((merged, { extend }) => {
    if (!extend)
      return merged;
    return mergeWith(merged, extend, (originalValue, newValue) => {
      if (newValue === void 0) {
        return originalValue ?? [];
      }
      if (originalValue === void 0) {
        return [newValue];
      }
      if (Array.isArray(originalValue)) {
        return [newValue, ...originalValue];
      }
      return [newValue, originalValue];
    });
  }, {});
}
function mergeRecords(records) {
  return {
    ...records.reduce((acc, record) => assign(acc, record), {}),
    extend: getExtends(records)
  };
}
function mergeExtensions(records) {
  const { extend = [], ...restProps } = mergeRecords(records);
  return mergeWith(restProps, extend, (obj, extensions) => {
    return mergeAndConcat({}, obj, ...extensions);
  });
}
var isEmptyObject = (obj) => typeof obj === "object" && Object.keys(obj).length === 0;
var compact = (obj) => {
  return Object.keys(obj).reduce((acc, key) => {
    if (obj[key] !== void 0 && !isEmptyObject(obj[key])) {
      acc[key] = obj[key];
    }
    return acc;
  }, {});
};
var tokenKeys = ["description", "extensions", "type", "value", "deprecated"];
function mergeConfigs(configs) {
  const [userConfig] = configs;
  const pluginHooks = userConfig.plugins ?? [];
  if (userConfig.hooks) {
    pluginHooks.push({ name: "__panda.config__", hooks: userConfig.hooks });
  }
  const mergedResult = assign(
    {
      conditions: mergeExtensions(configs.map((config) => config.conditions ?? {})),
      theme: mergeExtensions(configs.map((config) => config.theme ?? {})),
      patterns: mergeExtensions(configs.map((config) => config.patterns ?? {})),
      utilities: mergeExtensions(configs.map((config) => config.utilities ?? {})),
      globalCss: mergeExtensions(configs.map((config) => config.globalCss ?? {})),
      globalVars: mergeExtensions(configs.map((config) => config.globalVars ?? {})),
      staticCss: mergeExtensions(configs.map((config) => config.staticCss ?? {})),
      themes: mergeExtensions(configs.map((config) => config.themes ?? {})),
      hooks: mergeHooks(pluginHooks)
    },
    ...configs
  );
  const withoutEmpty = compact(mergedResult);
  if (withoutEmpty.theme?.tokens) {
    walkObject(withoutEmpty.theme.tokens, (args) => args, {
      stop(token) {
        if (!isValidToken(token))
          return false;
        const keys = Object.keys(token);
        const nestedKeys = keys.filter((k) => !tokenKeys.includes(k));
        const nested = nestedKeys.length > 0;
        if (nested) {
          token.DEFAULT ||= {};
          tokenKeys.forEach((key) => {
            if (token[key] == null)
              return;
            token.DEFAULT[key] ||= token[key];
            delete token[key];
          });
        }
        return true;
      }
    });
  }
  return withoutEmpty;
}

export {
  isValidToken,
  isTokenReference,
  formatPath,
  SEP,
  getReferences,
  serializeTokenValue,
  mergeConfigs
};
