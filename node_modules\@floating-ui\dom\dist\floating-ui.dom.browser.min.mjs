import{rectToClientRect as t,autoPlacement as e,shift as n,flip as o,size as i,hide as r,arrow as c,inline as l,limitShift as s,computePosition as f}from"@floating-ui/core";export{detectOverflow,offset}from"@floating-ui/core";const u=Math.min,a=Math.max,d=Math.round,h=Math.floor,p=t=>({x:t,y:t});function m(t){return w(t)?(t.nodeName||"").toLowerCase():"#document"}function g(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function y(t){var e;return null==(e=(w(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function w(t){return t instanceof Node||t instanceof g(t).Node}function x(t){return t instanceof Element||t instanceof g(t).Element}function v(t){return t instanceof HTMLElement||t instanceof g(t).HTMLElement}function b(t){return"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof g(t).ShadowRoot)}function L(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=F(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(i)}function T(t){return["table","td","th"].includes(m(t))}function R(t){const e=S(),n=F(t);return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function E(t){let e=D(t);for(;v(e)&&!C(e);){if(R(e))return e;e=D(e)}return null}function S(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function C(t){return["html","body","#document"].includes(m(t))}function F(t){return g(t).getComputedStyle(t)}function O(t){return x(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function D(t){if("html"===m(t))return t;const e=t.assignedSlot||t.parentNode||b(t)&&t.host||y(t);return b(e)?e.host:e}function H(t){const e=D(t);return C(e)?t.ownerDocument?t.ownerDocument.body:t.body:v(e)&&L(e)?e:H(e)}function W(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const i=H(t),r=i===(null==(o=t.ownerDocument)?void 0:o.body),c=g(i);return r?e.concat(c,c.visualViewport||[],L(i)?i:[],c.frameElement&&n?W(c.frameElement):[]):e.concat(i,W(i,[],n))}function M(t){const e=F(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const i=v(t),r=i?t.offsetWidth:n,c=i?t.offsetHeight:o,l=d(n)!==r||d(o)!==c;return l&&(n=r,o=c),{width:n,height:o,$:l}}function z(t){return x(t)?t:t.contextElement}function P(t){const e=z(t);if(!v(e))return p(1);const n=e.getBoundingClientRect(),{width:o,height:i,$:r}=M(e);let c=(r?d(n.width):n.width)/o,l=(r?d(n.height):n.height)/i;return c&&Number.isFinite(c)||(c=1),l&&Number.isFinite(l)||(l=1),{x:c,y:l}}const V=p(0);function A(t){const e=g(t);return S()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:V}function B(e,n,o,i){void 0===n&&(n=!1),void 0===o&&(o=!1);const r=e.getBoundingClientRect(),c=z(e);let l=p(1);n&&(i?x(i)&&(l=P(i)):l=P(e));const s=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==g(t))&&e}(c,o,i)?A(c):p(0);let f=(r.left+s.x)/l.x,u=(r.top+s.y)/l.y,a=r.width/l.x,d=r.height/l.y;if(c){const t=g(c),e=i&&x(i)?g(i):i;let n=t.frameElement;for(;n&&i&&e!==t;){const t=P(n),e=n.getBoundingClientRect(),o=F(n),i=e.left+(n.clientLeft+parseFloat(o.paddingLeft))*t.x,r=e.top+(n.clientTop+parseFloat(o.paddingTop))*t.y;f*=t.x,u*=t.y,a*=t.x,d*=t.y,f+=i,u+=r,n=g(n).frameElement}}return t({width:a,height:d,x:f,y:u})}const N=[":popover-open",":modal"];function k(t){let e=!1,n=0,o=0;if(N.forEach((n=>{!function(n){try{e=e||t.matches(n)}catch(t){}}(n)})),e){const e=E(t);if(e){const t=e.getBoundingClientRect();n=t.x,o=t.y}}return[e,n,o]}function I(t){return B(y(t)).left+O(t).scrollLeft}function q(e,n,o){let i;if("viewport"===n)i=function(t,e){const n=g(t),o=y(t),i=n.visualViewport;let r=o.clientWidth,c=o.clientHeight,l=0,s=0;if(i){r=i.width,c=i.height;const t=S();(!t||t&&"fixed"===e)&&(l=i.offsetLeft,s=i.offsetTop)}return{width:r,height:c,x:l,y:s}}(e,o);else if("document"===n)i=function(t){const e=y(t),n=O(t),o=t.ownerDocument.body,i=a(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),r=a(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let c=-n.scrollLeft+I(t);const l=-n.scrollTop;return"rtl"===F(o).direction&&(c+=a(e.clientWidth,o.clientWidth)-i),{width:i,height:r,x:c,y:l}}(y(e));else if(x(n))i=function(t,e){const n=B(t,!0,"fixed"===e),o=n.top+t.clientTop,i=n.left+t.clientLeft,r=v(t)?P(t):p(1);return{width:t.clientWidth*r.x,height:t.clientHeight*r.y,x:i*r.x,y:o*r.y}}(n,o);else{const t=A(e);i={...n,x:n.x-t.x,y:n.y-t.y}}return t(i)}function X(t,e){const n=D(t);return!(n===e||!x(n)||C(n))&&("fixed"===F(n).position||X(n,e))}function Y(t,e,n,o){const i=v(e),r=y(e),c="fixed"===n,l=B(t,!0,c,e);let s={scrollLeft:0,scrollTop:0};const f=p(0);if(i||!i&&!c)if(("body"!==m(e)||L(r))&&(s=O(e)),i){const t=B(e,!0,c,e);f.x=t.x+e.clientLeft,f.y=t.y+e.clientTop}else r&&(f.x=I(r));let u=l.left+s.scrollLeft-f.x,a=l.top+s.scrollTop-f.y;const[d,h,g]=k(o);return d&&(u+=h,a+=g,i&&(u+=e.clientLeft,a+=e.clientTop)),{x:u,y:a,width:l.width,height:l.height}}function $(t,e){return v(t)&&"fixed"!==F(t).position?e?e(t):t.offsetParent:null}function _(t,e){const n=g(t);if(!v(t))return n;let o=$(t,e);for(;o&&T(o)&&"static"===F(o).position;)o=$(o,e);return o&&("html"===m(o)||"body"===m(o)&&"static"===F(o).position&&!R(o))?n:o||E(t)||n}const j={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const r=y(o),[c]=e?k(e.floating):[!1];if(o===r||c)return n;let l={scrollLeft:0,scrollTop:0},s=p(1);const f=p(0),u=v(o);if((u||!u&&"fixed"!==i)&&(("body"!==m(o)||L(r))&&(l=O(o)),v(o))){const t=B(o);s=P(o),f.x=t.x+o.clientLeft,f.y=t.y+o.clientTop}return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-l.scrollLeft*s.x+f.x,y:n.y*s.y-l.scrollTop*s.y+f.y}},getDocumentElement:y,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:o,strategy:i}=t;const r=[..."clippingAncestors"===n?function(t,e){const n=e.get(t);if(n)return n;let o=W(t,[],!1).filter((t=>x(t)&&"body"!==m(t))),i=null;const r="fixed"===F(t).position;let c=r?D(t):t;for(;x(c)&&!C(c);){const e=F(c),n=R(c);n||"fixed"!==e.position||(i=null),(r?!n&&!i:!n&&"static"===e.position&&i&&["absolute","fixed"].includes(i.position)||L(c)&&!n&&X(t,c))?o=o.filter((t=>t!==c)):i=e,c=D(c)}return e.set(t,o),o}(e,this._c):[].concat(n),o],c=r[0],l=r.reduce(((t,n)=>{const o=q(e,n,i);return t.top=a(o.top,t.top),t.right=u(o.right,t.right),t.bottom=u(o.bottom,t.bottom),t.left=a(o.left,t.left),t}),q(e,c,i));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:_,getElementRects:async function(t){const e=this.getOffsetParent||_,n=this.getDimensions;return{reference:Y(t.reference,await e(t.floating),t.strategy,t.floating),floating:{x:0,y:0,...await n(t.floating)}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=M(t);return{width:e,height:n}},getScale:P,isElement:x,isRTL:function(t){return"rtl"===F(t).direction}};function G(t,e,n,o){void 0===o&&(o={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:s=!1}=o,f=z(t),d=i||r?[...f?W(f):[],...W(e)]:[];d.forEach((t=>{i&&t.addEventListener("scroll",n,{passive:!0}),r&&t.addEventListener("resize",n)}));const p=f&&l?function(t,e){let n,o=null;const i=y(t);function r(){var t;clearTimeout(n),null==(t=o)||t.disconnect(),o=null}return function c(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),r();const{left:f,top:d,width:p,height:m}=t.getBoundingClientRect();if(l||e(),!p||!m)return;const g={rootMargin:-h(d)+"px "+-h(i.clientWidth-(f+p))+"px "+-h(i.clientHeight-(d+m))+"px "+-h(f)+"px",threshold:a(0,u(1,s))||1};let y=!0;function w(t){const e=t[0].intersectionRatio;if(e!==s){if(!y)return c();e?c(!1,e):n=setTimeout((()=>{c(!1,1e-7)}),100)}y=!1}try{o=new IntersectionObserver(w,{...g,root:i.ownerDocument})}catch(t){o=new IntersectionObserver(w,g)}o.observe(t)}(!0),r}(f,n):null;let m,g=-1,w=null;c&&(w=new ResizeObserver((t=>{let[o]=t;o&&o.target===f&&w&&(w.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame((()=>{var t;null==(t=w)||t.observe(e)}))),n()})),f&&!s&&w.observe(f),w.observe(e));let x=s?B(t):null;return s&&function e(){const o=B(t);!x||o.x===x.x&&o.y===x.y&&o.width===x.width&&o.height===x.height||n();x=o,m=requestAnimationFrame(e)}(),n(),()=>{var t;d.forEach((t=>{i&&t.removeEventListener("scroll",n),r&&t.removeEventListener("resize",n)})),null==p||p(),null==(t=w)||t.disconnect(),w=null,s&&cancelAnimationFrame(m)}}const J=e,K=n,Q=o,U=i,Z=r,tt=c,et=l,nt=s,ot=(t,e,n)=>{const o=new Map,i={platform:j,...n},r={...i.platform,_c:o};return f(t,e,{...i,platform:r})};export{tt as arrow,J as autoPlacement,G as autoUpdate,ot as computePosition,Q as flip,W as getOverflowAncestors,Z as hide,et as inline,nt as limitShift,j as platform,K as shift,U as size};
