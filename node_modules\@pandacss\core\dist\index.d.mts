import * as _pandacss_shared from '@pandacss/shared';
import { toHash } from '@pandacss/shared';
import { TokenDictionary } from '@pandacss/token-dictionary';
import * as _pandacss_types from '@pandacss/types';
import { AtRuleCondition, ConditionDetails, ConditionQuery, Conditions as Conditions$1, ThemeVariantsMap, GlobalVarsDefinition, Dict, ResultItem, StyleEntry, StyleResultObject, StyleProps, SlotRecipeDefinition, RecipeConfig, PartialBy, EncoderJson, AtomicStyleResult, RecipeBaseResult, GroupedResult, SystemStyleObject, AtomicRule, RecipeDefinition, AtomicRecipeRule, RecipeVariantsRule, ImportMapOutput, UserConfig, ImportMapInput, CascadeLayers, CascadeLayer, SlotRecipeConfig, ArtifactFilters, PatternHelpers, HashOptions, Config, TSConfig, LoadConfigResult, UtilityConfig, PropertyConfig, CssKeyframes, PatternConfig, StaticCssOptions, RequiredBy, StudioOptions, PandaHooks, PrefixOptions, Theme } from '@pandacss/types';
import postcss, { Root, AtRule } from 'postcss';
import { Stylesheet as Stylesheet$1, CssOptions as CssOptions$1 } from '@pandacss/core';

declare class Breakpoints {
    private breakpoints;
    sorted: ReturnType<typeof sortBreakpoints>;
    values: Record<string, BreakpointEntry>;
    keys: string[];
    ranges: Record<string, string>;
    conditions: Record<string, AtRuleCondition>;
    constructor(breakpoints: Record<string, string>);
    get: (name: string) => BreakpointEntry;
    build: ({ min, max }: {
        min?: string | null | undefined;
        max?: string | null | undefined;
    }) => string;
    only: (name: string) => string;
    private getRanges;
    private getConditions;
    getCondition: (key: string) => ConditionDetails | undefined;
    expandScreenAtRule: (root: Root) => void;
}
type BreakpointEntry = {
    name: string;
    min?: string | null;
    max?: string | null;
};
type Entries = [string, BreakpointEntry][];
declare function sortBreakpoints(breakpoints: Record<string, string>): Entries;

interface Options {
    conditions?: Conditions$1;
    breakpoints?: Record<string, string>;
    containerNames?: string[];
    containerSizes?: Record<string, string>;
    themes?: ThemeVariantsMap;
}
declare class Conditions {
    private options;
    values: Record<string, ConditionDetails>;
    breakpoints: Breakpoints;
    constructor(options: Options);
    private setupContainers;
    private setupThemes;
    getThemeSelector: (name: string) => string;
    getThemeName: (theme: string) => string;
    finalize: (paths: string[]) => string[];
    shift: (paths: string[]) => string[];
    segment: (paths: string[]) => {
        condition: string[];
        selector: string[];
    };
    has: (key: string) => boolean;
    isCondition: (key: string) => boolean;
    isEmpty: () => boolean;
    get: (key: string) => undefined | string | string[];
    getRaw: (condNameOrQuery: ConditionQuery) => ConditionDetails | undefined;
    sort: (conditions: string[]) => ConditionDetails[];
    normalize: (condition: ConditionQuery | ConditionDetails) => ConditionDetails | undefined;
    keys: () => string[];
    saveOne: (key: string, value: string) => void;
    remove(key: string): void;
    getSortedKeys: () => string[];
}

declare class FileEngine {
    private context;
    constructor(context: Pick<Context, 'config'>);
    private get forceConsistentTypeExtension();
    private get outExtension();
    ext(file: string): string;
    extDts(file: string): string;
    private __extDts;
    import(mod: string, file: string): string;
    importType(mod: string, file: string): string;
    exportType(mod: string, file: string): string;
    exportStar(file: string): string;
    exportTypeStar(file: string): string;
    isTypeFile(file: string): boolean;
    jsDocComment(comment: string | undefined, options?: {
        deprecated?: boolean | string;
        default?: string;
    }): string;
}

interface GlobalVarsOptions {
    globalVars?: GlobalVarsDefinition;
    cssVarRoot: string;
}
declare class GlobalVars {
    private options;
    keys: Set<string>;
    vars: string[];
    names: string[];
    constructor(options: GlobalVarsOptions);
    isEmpty(): boolean;
    toString(): string;
}

declare class StyleEncoder {
    private context;
    static separator: string;
    static conditionSeparator: string;
    atomic: Set<string>;
    compound_variants: Set<string>;
    recipes: Map<string, Set<string>>;
    recipes_base: Map<string, Set<string>>;
    constructor(context: Pick<Context, 'isTemplateLiteralSyntax' | 'isValidProperty' | 'recipes' | 'patterns' | 'conditions' | 'utility'>);
    filterStyleProps: (props: Dict) => Dict;
    clone: () => StyleEncoder;
    isEmpty: () => boolean;
    get results(): {
        atomic: Set<string>;
        recipes: Map<string, Set<string>>;
        recipes_base: Map<string, Set<string>>;
    };
    /**
     * Hashes a style object and adds the resulting hashes to a set.
     * @param set - The set to add the resulting hashes to.
     * @param obj - The style object to hash.
     * @param baseEntry - An optional base style entry to use when hashing the style object.
     */
    hashStyleObject: (set: Set<string>, obj: ResultItem['data'][number], baseEntry?: Partial<Omit<StyleEntry, 'prop' | 'value' | 'cond'>>) => void;
    processAtomic: (styles: StyleResultObject) => void;
    processStyleProps: (styleProps: StyleProps) => void;
    processConfigSlotRecipeBase: (recipeName: string, config: SlotRecipeDefinition) => void;
    processConfigSlotRecipe: (recipeName: string, variants: Record<string, any>) => void;
    processConfigRecipeBase: (recipeName: string, config: RecipeConfig) => void;
    processConfigRecipe: (recipeName: string, variants: Record<string, any>) => void;
    processRecipe: (recipeName: string, variants: Record<string, any>) => void;
    processRecipeBase(recipeName: string): void;
    processPattern: (name: string, patternProps: StyleResultObject, type?: 'pattern' | 'jsx-pattern', jsxName?: string | undefined) => void;
    processAtomicRecipe: (recipe: Pick<RecipeConfig, 'base' | 'variants' | 'compoundVariants'>) => void;
    processAtomicSlotRecipe: (recipe: PartialBy<SlotRecipeDefinition, 'slots'>) => void;
    getConfigRecipeHash: (recipeName: string) => {
        atomic: Set<string>;
        base: Set<string>;
        variants: Set<string>;
    };
    getConfigSlotRecipeHash: (recipeName: string) => {
        atomic: Set<string>;
        base: Dict;
        variants: Set<string>;
    };
    getRecipeHash: (recipeName: string) => {
        atomic: Set<string>;
        base: Set<string>;
        variants: Set<string>;
    } | {
        atomic: Set<string>;
        base: Dict;
        variants: Set<string>;
    };
    toJSON: () => {
        schemaVersion: string;
        styles: {
            atomic: string[];
            recipes: {
                [k: string]: string[];
            };
        };
    };
    fromJSON: (json: EncoderJson) => this;
}

declare class StyleDecoder {
    private context;
    constructor(context: Pick<Context, 'conditions' | 'utility' | 'recipes' | 'hash'>);
    classNames: Map<string, AtomicStyleResult | RecipeBaseResult>;
    atomic_cache: Map<string, AtomicStyleResult>;
    group_cache: Map<string, GroupedResult>;
    recipe_base_cache: Map<string, RecipeBaseResult>;
    atomic: Set<AtomicStyleResult>;
    recipes: Map<string, Set<AtomicStyleResult>>;
    recipes_base: Map<string, Set<RecipeBaseResult>>;
    clone: () => StyleDecoder;
    isEmpty: () => boolean;
    get results(): {
        atomic: Set<AtomicStyleResult>;
        recipes: Map<string, Set<AtomicStyleResult>>;
        recipes_base: Map<string, Set<RecipeBaseResult>>;
    };
    private formatSelector;
    private getRecipeName;
    private getTransformResult;
    resolveCondition: (condition: ConditionDetails) => string | string[];
    private getAtomic;
    getGroup: (hashSet: Set<string>, key: string) => GroupedResult;
    private getRecipeBase;
    collectAtomic: (encoder: StyleEncoder) => this;
    private processClassName;
    collectRecipe: (encoder: StyleEncoder) => void;
    collectRecipeBase: (encoder: StyleEncoder) => void;
    /**
     * Collect and re-create all styles and recipes objects from the style encoder
     * So that we can just iterate over them and transform resulting CSS objects into CSS strings
     */
    collect: (encoder: StyleEncoder) => this;
    getConfigRecipeResult: (recipeName: string) => {
        atomic: Set<AtomicStyleResult>;
        base: Set<RecipeBaseResult>;
        variants: Set<AtomicStyleResult>;
    };
    getConfigSlotRecipeResult: (recipeName: string) => {
        atomic: Set<AtomicStyleResult>;
        base: Dict;
        variants: Set<AtomicStyleResult>;
    };
    getRecipeResult: (recipeName: string) => {
        atomic: Set<AtomicStyleResult>;
        base: Set<RecipeBaseResult>;
        variants: Set<AtomicStyleResult>;
    } | {
        atomic: Set<AtomicStyleResult>;
        base: Dict;
        variants: Set<AtomicStyleResult>;
    };
}

declare class RuleProcessor {
    private context;
    encoder: StyleEncoder;
    decoder: StyleDecoder;
    sheet: Stylesheet$1;
    constructor(context: Context);
    getParamsOrThrow(): {
        encoder: StyleEncoder;
        decoder: StyleDecoder;
        sheet: Stylesheet$1;
    };
    clone(): this;
    toCss(options?: CssOptions$1): string;
    css(styles: SystemStyleObject): AtomicRule;
    cva(recipeConfig: RecipeDefinition<any>): AtomicRecipeRule;
    sva(recipeConfig: SlotRecipeDefinition<string, any>): AtomicRecipeRule;
    recipe(name: string, variants?: Record<string, any>): RecipeVariantsRule | undefined;
}

declare class HooksApi {
    private ctx;
    processor: RuleProcessor;
    constructor(ctx: Context);
    get config(): _pandacss_types.UserConfig;
    get configPath(): string;
    get configDependencies(): string[];
    get classNames(): Map<string, string>;
    get generatedClassNames(): Map<string, _pandacss_types.AtomicStyleResult | _pandacss_types.RecipeBaseResult>;
}

interface ImportResult {
    /** @example 'hstack' */
    name: string;
    /** @example 'pandaHStack' */
    alias: string;
    /**
     * @example '../../styled-system/patterns'
     * @example '@styles/patterns'
     */
    mod: string;
    importMapValue?: string;
    kind?: 'named' | 'namespace';
}
interface FileMatcherOptions {
    importMap: ImportMapOutput<string>;
    value: ImportResult[];
}
declare class FileMatcher {
    private context;
    imports: ImportResult[];
    namespaces: Map<string, ImportResult>;
    private importMap;
    private cssAliases;
    private cvaAliases;
    private svaAliases;
    private jsxFactoryAliases;
    private recipeAliases;
    private patternAliases;
    private propertiesMap;
    private functions;
    private components;
    constructor(context: Pick<Context, 'jsx' | 'patterns' | 'recipes' | 'isValidProperty'>, opts: FileMatcherOptions);
    private assignAliases;
    private assignProperties;
    isEmpty: () => boolean;
    toString: () => string;
    find: (id: string) => ImportResult | undefined;
    private createMatch;
    match: (id: string) => boolean;
    getName: (id: string) => string;
    getAliases: (id: string) => string[];
    private _patternsMatcher;
    isValidPattern: (id: string) => boolean;
    private _recipesMatcher;
    isValidRecipe: (id: string) => boolean;
    isRawFn: (fnName: string) => boolean;
    isNamespaced: (fnName: string) => boolean;
    normalizeFnName: (fnName: string) => string;
    isAliasFnName: (fnName: string) => boolean | undefined;
    matchFn: (fnName: string) => boolean;
    isJsxFactory: (tagName: string) => boolean | undefined;
    isPandaComponent: (tagName: string) => boolean;
    matchTag: (tagName: string) => boolean;
    matchTagProp: (tagName: string, propName: string) => boolean;
}

interface ImportMatcher {
    mods: string[];
    regex: RegExp;
    match(value: string): boolean;
}
declare class ImportMap {
    private context;
    value: ImportMapOutput<string>;
    matchers: Record<keyof ImportMapOutput<string>, ImportMatcher>;
    outdir: string;
    constructor(context: Pick<Context, 'jsx' | 'config' | 'conf' | 'isValidProperty' | 'recipes' | 'patterns'>);
    /**
     * Normalize one/many import map inputs to a single import map output with absolute paths.
     * @example
     * ```ts
     * importMap: '@acme/org'
     * ```
     *
     * will be normalized to
     * ```ts
     * {
     *   css: ['@acme/org/css'],
     *   recipe: ['@acme/org/recipes'],
     *   pattern: ['@acme/org/patterns'],
     *   jsx: ['@acme/org/jsx'],
     * }
     * ```
     *
     * @exammple
     * importMap: ['@acme/org', '@foo/org', '@bar/org']
     * ```
     *
     * will be normalized to
     * ```ts
     * {
     *   css: ['@acme/org/css', '@foo/org/css', '@bar/org/css'],
     *   recipe: ['@acme/org/recipes', '@foo/org/recipes', '@bar/org/recipes'],
     *   pattern: ['@acme/org/patterns', '@foo/org/patterns', '@bar/org/patterns'],
     *   jsx: ['@acme/org/jsx', '@foo/org/jsx', '@bar/org/jsx'],
     * }
     * ```
     */
    buildImportMap: (option: UserConfig['importMap']) => ImportMapOutput<string>;
    private fromString;
    private fromInput;
    private getOutdir;
    normalize: (map: string | ImportMapInput | undefined) => ImportMapOutput;
    private createMatcher;
    match: (result: ImportResult | undefined, resolveTsPath?: ((mod: string) => string | undefined) | undefined) => boolean;
    file: (results: ImportResult[]) => FileMatcher;
}

declare class Layers {
    private names;
    root: Root;
    reset: AtRule;
    base: AtRule;
    tokens: AtRule;
    recipes: AtRule;
    recipes_base: AtRule;
    recipes_slots: AtRule;
    recipes_slots_base: AtRule;
    utilities: AtRule;
    compositions: AtRule;
    private utilityRuleMap;
    constructor(names: CascadeLayers);
    getLayerRoot(layer: CascadeLayer): postcss.AtRule | postcss.Root;
    insert(): postcss.Root;
    get layerNames(): string[];
    get params(): string;
}

interface SerializeContext extends Omit<StylesheetContext, 'layers' | 'helpers' | 'hash' | 'cssVarRoot'> {
}

interface RecipeRecord {
    [key: string]: RecipeConfig | SlotRecipeConfig;
}
declare class Recipes {
    private recipes;
    slotSeparator: string;
    keys: string[];
    private deprecated;
    private context;
    constructor(recipes?: RecipeRecord);
    private getPropKey;
    private get separator();
    private getClassName;
    prune: () => void;
    save: (context: SerializeContext) => void;
    saveOne: (name: string, recipe: RecipeConfig | SlotRecipeConfig) => void;
    remove(name: string): void;
    private assignRecipe;
    getSlotKey: (name: string, slot: string) => string;
    isEmpty: () => boolean;
    isDeprecated: (name: string) => boolean;
    getNames: (name: string) => {
        baseName: string;
        upperName: string;
        dashName: string;
        jsxName: string;
    };
    getRecipe: (name: string) => RecipeNode | undefined;
    getConfig: (name: string) => RecipeConfig<_pandacss_types.RecipeVariantRecord> | SlotRecipeConfig;
    getConfigOrThrow: (name: string) => RecipeConfig<_pandacss_types.RecipeVariantRecord> | SlotRecipeConfig;
    find: (jsxName: string) => RecipeNode | undefined;
    filter: (jsxName: string) => RecipeNode[];
    get details(): RecipeNode[];
    splitProps: (recipeName: string, props: Dict) => Dict[];
    isSlotRecipe: (name: string) => boolean;
    static isSlotRecipeConfig: (config: RecipeConfig | SlotRecipeConfig) => config is SlotRecipeConfig;
    normalize: (name: string, config: RecipeConfig) => Required<RecipeConfig<_pandacss_types.RecipeVariantRecord>>;
    getTransform: (name: string, slot?: boolean) => (variant: string, value: string) => {
        layer: string;
        className: string;
        styles: SystemStyleObject;
    } | {
        className: string;
        styles: SystemStyleObject;
        layer?: undefined;
    };
    filterDetails: (filters?: ArtifactFilters) => RecipeNode[];
    static inferSlots: (recipe: PartialBy<SlotRecipeDefinition, 'slots'>) => string[];
    static isValidNode: (node: unknown) => node is RecipeNode;
}

interface TransformResult {
    layer?: string;
    className: string;
    styles: Dict;
}
interface StylesheetContext extends Pick<Context, 'utility' | 'conditions' | 'encoder' | 'decoder' | 'isValidProperty' | 'hooks' | 'globalVars'> {
    layers: Layers;
    helpers: PatternHelpers;
    hash?: boolean;
    lightningcss?: boolean;
    browserslist?: string[];
    polyfill?: boolean;
    cssVarRoot: string;
}
interface RecipeNode {
    /**
     * The name of the recipe
     */
    baseName: string;
    /**
     * Discriminant
     */
    type: 'recipe';
    /**
     * The keys of the variants
     */
    variantKeys: string[];
    /**
     * The map of the variant keys to their possible values
     */
    variantKeyMap: Record<string, string[]>;
    /**
     * The jsx keys or regex to match the recipe
     */
    jsx: (string | RegExp)[];
    /**
     * The name of the recipe in upper case
     */
    upperName: string;
    /**
     * The name of the recipe in dash case
     */
    dashName: string;
    /**
     * The name of the recipe in camel case
     */
    jsxName: string;
    /**
     * The regex to match the recipe
     */
    match: RegExp;
    /**
     * The transformed recipe config
     */
    config: RecipeConfig | SlotRecipeConfig;
    /**
     * The function to split the props
     */
    splitProps: (props: Dict) => [Dict, Dict];
    /**
     * The props of the recipe
     */
    props: string[];
}
interface CssOptions extends Pick<UserConfig, 'optimize' | 'minify'> {
}
interface ProcessOptions {
    styles: Dict;
    layer: LayerName;
}
type LayerName = 'base' | 'reset' | 'recipes_slots_base' | 'recipes_base' | 'tokens' | 'recipes' | 'utilities' | 'recipes_slots' | 'compositions';
interface ParserOptions {
    hash: HashOptions;
    imports: ImportMap;
    jsx: JsxEngine;
    syntax: Config['syntax'];
    recipes: Recipes;
    patterns: Patterns;
    encoder: StyleEncoder;
    join: (...paths: string[]) => string;
    compilerOptions: TSConfig['compilerOptions'];
    tsOptions: LoadConfigResult['tsOptions'];
}

interface UtilityOptions {
    config?: UtilityConfig;
    tokens: TokenDictionary;
    separator?: string;
    prefix?: string;
    shorthands?: boolean;
    strictTokens?: boolean;
    keyframes?: CssKeyframes;
}
declare class Utility {
    private options;
    /**
     * The token map or dictionary of tokens
     */
    tokens: TokenDictionary;
    /**
     * [cache] The map of property names to their resolved class names
     */
    classNames: Map<string, string>;
    /**
     * [cache] The map of the property to their resolved styless
     */
    styles: Map<string, Dict>;
    /**
     * Map of shorthand properties to their longhand properties
     */
    shorthands: Map<string, string>;
    /**
     * The map of possible values for each property
     */
    types: Map<string, Set<string>>;
    /**
     * The map of the property keys
     */
    propertyTypeKeys: Map<string, Set<string>>;
    /**
     * The utility config
     */
    config: UtilityConfig;
    /**
     * The map of property names to their transform functions
     */
    private transforms;
    /**
     * The map of property names to their config
     */
    private configs;
    /**
     * The map of deprecated properties
     */
    private deprecated;
    separator: string;
    prefix: string;
    strictTokens: boolean;
    constructor(options: UtilityOptions);
    defaultHashFn: typeof toHash;
    toHash: (path: string[], hashFn: (str: string) => string) => string;
    private normalizeConfig;
    private assignDeprecated;
    register: (property: string, config: PropertyConfig) => void;
    private assignShorthands;
    private assignColorPaletteProperty;
    resolveShorthand: (prop: string) => string;
    get hasShorthand(): boolean;
    get isEmpty(): boolean;
    entries: () => [string, string][];
    private getPropKey;
    private hash;
    /**
     * Get all the possible values for the defined property
     */
    getPropertyValues: (config: PropertyConfig, resolveFn?: ((key: string) => string) | undefined) => Record<string, string> | {
        type: string;
    } | {
        [x: string]: Record<string, string>;
    } | undefined;
    getPropertyRawValue(config: PropertyConfig, value: string): string;
    getToken: (path: string) => string;
    getTokenCategoryValues: (category: string) => Record<string, string> | undefined;
    /**
     * Normalize the property config
     */
    normalize: (propertyConfig: PropertyConfig | undefined) => PropertyConfig | undefined;
    private assignProperty;
    private assignProperties;
    assignPropertiesValues: () => this;
    private assignPropertyValues;
    getPropertyKeys: (prop: string) => string[];
    getPropertyTypeKeys: (property: string) => string[];
    private assignPropertyType;
    private assignPropertyTypes;
    /**
     * Returns the Typescript type for the define properties
     */
    getTypes: () => Map<string, string[]>;
    defaultTransform: (value: string, prop: string) => {
        [x: string]: string;
    };
    private setTransform;
    private getTransformArgs;
    private setStyles;
    formatClassName: (className: string) => string;
    /**
     * Returns the resolved className for a given property and value
     */
    getClassName: (property: string, raw: string) => string;
    getOrCreateClassName: (property: string, raw: string) => string;
    /**
     * Whether a given property exists in the config
     */
    has: (prop: string) => boolean;
    /**
     * Get or create the resolved styles for a given property and value
     */
    private getOrCreateStyle;
    /**
     * Returns the resolved className and styles for a given property and value
     */
    transform: (prop: string, value: string | undefined) => TransformResult;
    /**
     * All keys including shorthand keys
     */
    keys: () => string[];
    /**
     * Returns a map of the property keys and their shorthands
     */
    getPropShorthandsMap: () => Map<string, string[]>;
    /**
     * Returns the shorthands for a given property
     */
    getPropShorthands: (prop: string) => string[];
    /**
     * Whether a given property is deprecated
     */
    isDeprecated: (prop: string) => boolean;
}

interface PatternOptions {
    config: UserConfig;
    tokens: TokenDictionary;
    utility: Utility;
    helpers: PatternHelpers;
}
declare class Patterns {
    private options;
    patterns: Record<string, PatternConfig>;
    details: PatternNode[];
    keys: string[];
    private utility;
    private tokens;
    private deprecated;
    constructor(options: PatternOptions);
    private createDetail;
    getConfig(name: string): PatternConfig;
    transform(name: string, styles: Dict): Dict;
    getNames(name: string): PatternNames;
    find: (jsxName: string) => string;
    filter: (jsxName: string) => PatternNode[];
    isEmpty(): boolean;
    isDeprecated(name: string): boolean;
    saveOne(name: string, pattern: PatternConfig): void;
    remove(name: string): void;
    filterDetails(filters?: ArtifactFilters): PatternNode[];
    getPropertyValues: (patternName: string, property: string) => string[] | undefined;
    static isValidNode: (node: unknown) => node is PatternNode;
}
interface PatternNames {
    upperName: string;
    baseName: string;
    dashName: string;
    styleFnName: string;
    jsxName: string;
}
interface PatternNode extends PatternNames {
    props: string[];
    blocklistType: string;
    config: PatternConfig;
    type: 'pattern';
    match: RegExp;
    jsx: NonNullable<PatternConfig['jsx']>;
}

interface JsxMatcher {
    string: Set<string>;
    regex: RegExp[];
}
declare class JsxEngine {
    private context;
    nodes: Array<PatternNode | RecipeNode>;
    names: string[];
    recipeMatcher: JsxMatcher;
    recipePropertiesByJsxName: Map<string, Set<string>>;
    patternMatcher: JsxMatcher;
    patternPropertiesByJsxName: Map<string, Set<string>>;
    constructor(context: Pick<Context, 'patterns' | 'recipes' | 'config'>);
    assignRecipeMatcher(): void;
    assignPatternMatcher(): void;
    private get jsxFactory();
    get styleProps(): "all" | "none" | "minimal";
    get framework(): (string & {}) | _pandacss_types.JsxFramework | undefined;
    get isEnabled(): boolean;
    get factoryName(): string;
    get upperName(): string;
    get typeName(): string;
    get variantName(): string;
    get componentName(): string;
    isJsxFactory: (name: string) => boolean;
    isJsxTagRecipe: (tagName: string) => boolean;
    isJsxTagPattern: (tagName: string) => boolean;
    isRecipeOrPatternProp: (tagName: string, propName: string) => boolean;
}

declare class PathEngine {
    private context;
    constructor(context: Pick<Context, 'config'>);
    private get cwd();
    private get emitPackage();
    private get outdir();
    getFilePath(file?: string): string[];
    get root(): string[];
    get css(): string[];
    get token(): string[];
    get types(): string[];
    get recipe(): string[];
    get pattern(): string[];
    get outCss(): string[];
    get jsx(): string[];
    get themes(): string[];
}

interface StaticCssResults {
    css: Record<string, any>[];
    recipes: Record<string, any>[];
    patterns: Record<string, any>[];
}
interface StaticCssEngine {
    results: StaticCssResults;
    regex: () => RegExp;
    parse: (text: string) => string[];
    sheet: Stylesheet$1;
}
declare class StaticCss {
    private context;
    encoder: StyleEncoder;
    decoder: StyleDecoder;
    constructor(context: Pick<Context, 'encoder' | 'decoder' | 'utility' | 'patterns' | 'recipes' | 'createSheet' | 'config'>);
    clone(): this;
    /**
     * This transforms a static css config into the same format as in the ParserResult,
     * so that it can be processed by the same logic as styles found in app code.
     *
     * e.g.
     * @example { css: [{ color: ['red', 'blue'] }] } => { css: [{ color: 'red }, { color: 'blue }] }
     * @example { css: [{ color: ['red'], conditions: ['md'] }] } => { css: [{ color: { base: 'red', md: 'red' } }] }
     *
     */
    getStyleObjects(options: StaticCssOptions): StaticCssResults;
    createRegex: () => RegExp;
    process(options: StaticCssOptions, stylesheet?: Stylesheet$1): StaticCssEngine;
}

declare class Stylesheet {
    private context;
    constructor(context: StylesheetContext);
    get layers(): Layers;
    getLayer(layer: LayerName): postcss.AtRule | undefined;
    process(options: ProcessOptions): void;
    serialize: (styles: Dict) => Dict;
    processResetCss: (styles: Dict) => void;
    processGlobalCss: (styles: Dict) => void;
    processCss: (styles: SystemStyleObject | undefined, layer: LayerName) => void;
    processDecoder: (decoder: StyleDecoder) => void;
    getLayerCss: (...layers: CascadeLayer[]) => string;
    toCss: ({ optimize, minify }?: CssOptions) => string;
}

declare class Context {
    conf: LoadConfigResult;
    studio: RequiredBy<NonNullable<StudioOptions['studio']>, 'outdir'>;
    tokens: TokenDictionary;
    utility: Utility;
    recipes: Recipes;
    conditions: Conditions;
    patterns: Patterns;
    staticCss: StaticCss;
    jsx: JsxEngine;
    imports: ImportMap;
    paths: PathEngine;
    file: FileEngine;
    globalVars: GlobalVars;
    encoder: StyleEncoder;
    decoder: StyleDecoder;
    hooksApi: HooksApi;
    properties: Set<string>;
    isValidProperty: (key: string) => boolean;
    messages: Messages;
    parserOptions: ParserOptions;
    constructor(conf: LoadConfigResult);
    get config(): UserConfig;
    get hooks(): Partial<PandaHooks>;
    get isTemplateLiteralSyntax(): boolean;
    get hash(): HashOptions;
    get prefix(): PrefixOptions;
    createTokenDictionary: (theme: Theme, themeVariants?: ThemeVariantsMap) => TokenDictionary;
    createUtility: (config: UserConfig) => Utility;
    createConditions: (config: UserConfig) => Conditions;
    createLayers: (layers: CascadeLayers) => Layers;
    setupCompositions: (theme: Theme) => void;
    setupProperties: () => void;
    get baseSheetContext(): {
        conditions: Conditions;
        utility: Utility;
        hash: boolean | undefined;
        encoder: StyleEncoder;
        decoder: StyleDecoder;
        hooks: Partial<PandaHooks>;
        isValidProperty: (key: string) => boolean;
        browserslist: string[] | undefined;
        lightningcss: boolean | undefined;
        polyfill: boolean | undefined;
        cssVarRoot: string;
        helpers: {
            map: typeof _pandacss_shared.mapObject;
            isCssFunction: (v: unknown) => boolean;
            isCssVar: (v: unknown) => boolean;
            isCssUnit: (v: unknown) => boolean;
        };
        globalVars: GlobalVars;
    };
    createSheet: () => Stylesheet;
    createRecipes: (theme: Theme) => Recipes;
    isValidLayerParams: (params: string) => boolean;
}

declare const artifactsGenerated: (ctx: Pick<Context, 'config' | 'recipes' | 'patterns' | 'tokens' | 'jsx' | 'isTemplateLiteralSyntax'>) => () => string;
declare const configExists: (cmd: string) => string;
declare const thankYou: () => string;
declare const codegenComplete: () => string;
declare const noExtract: () => string;
declare const watch: () => string;
declare const configWatch: () => string;
declare const buildComplete: (count: number) => string;
declare const cssArtifactComplete: (type: string) => string;
declare const getMessages: (ctx: Pick<Context, 'config' | 'recipes' | 'patterns' | 'tokens' | 'jsx' | 'isTemplateLiteralSyntax'>) => {
    artifactsGenerated: () => string;
    configExists: (cmd: string) => string;
    thankYou: () => string;
    codegenComplete: () => string;
    noExtract: () => string;
    watch: () => string;
    buildComplete: (count: number) => string;
    configWatch: () => string;
    cssArtifactComplete: (type: string) => string;
};
type Messages = ReturnType<typeof getMessages>;

type messages_Messages = Messages;
declare const messages_artifactsGenerated: typeof artifactsGenerated;
declare const messages_buildComplete: typeof buildComplete;
declare const messages_codegenComplete: typeof codegenComplete;
declare const messages_configExists: typeof configExists;
declare const messages_configWatch: typeof configWatch;
declare const messages_cssArtifactComplete: typeof cssArtifactComplete;
declare const messages_getMessages: typeof getMessages;
declare const messages_noExtract: typeof noExtract;
declare const messages_thankYou: typeof thankYou;
declare const messages_watch: typeof watch;
declare namespace messages {
  export { type messages_Messages as Messages, messages_artifactsGenerated as artifactsGenerated, messages_buildComplete as buildComplete, messages_codegenComplete as codegenComplete, messages_configExists as configExists, messages_configWatch as configWatch, messages_cssArtifactComplete as cssArtifactComplete, messages_getMessages as getMessages, messages_noExtract as noExtract, messages_thankYou as thankYou, messages_watch as watch };
}

interface OptimizeOptions {
    minify?: boolean;
    lightningcss?: boolean;
    browserslist?: string[];
}
declare function optimizeCss(code: string | Root, options?: OptimizeOptions): string;
declare function expandNestedCss(code: string): string;

declare function extractParentSelectors(selector: string): string;

/** Returns a string of CSS from an object of CSS. */
declare function stringify(
/** Style object defintion. */
value: Dict): string;

export { Breakpoints, Conditions, Context, type CssOptions, FileMatcher, ImportMap, type ImportResult, JsxEngine, Layers, type ParserOptions, type PatternNode, Patterns, type RecipeNode, Recipes, RuleProcessor, StaticCss, StyleDecoder, StyleEncoder, Stylesheet, type StylesheetContext, type TransformResult, Utility, expandNestedCss, extractParentSelectors, messages, optimizeCss, stringify };
