import { JSX, Show } from 'solid-js';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  message?: string;
  theme?: 'light' | 'dark';
  overlay?: boolean;
  type?: 'spinner' | 'dots' | 'pulse' | 'bars';
  color?: string;
}

export default function LoadingSpinner(props: LoadingSpinnerProps) {
  const size = () => {
    switch (props.size || 'medium') {
      case 'small : return '24px';
      case 'large : return '64px';
      default: return '40px';
    }
  };

  const containerStyle = () => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column' as const,
    padding: props.overlay ? '0' : '20px',
    gap: '16px',
    ...(props.overlay && {
      position: 'fixed' as const,
      top: '0',
      left: '0',
      right: '0',
      bottom: '0',
      background: props.theme === 'dark' ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.8)',
      zIndex: '9999',
      'backdrop-filter' : 'blur(4px)',
    }),
  });

  const primaryColor = props.color || '#3b82f6';
  const backgroundColor = props.theme === 'dark' ? '#374151' : '#f3f4f6';

  // 不同类型的加载动画
  const SpinnerLoader = () => (
    <div style={{
      width: size(),
      height: size(),
      border: `4px solid ${backgroundColor}`,
      'border-top : `4px solid ${primaryColor}`,
      'border-radius' : '50%',
      animation: 'spin 1s linear infinite',
    }}></div>
  );

  const DotsLoader = () => (
    <div style={{
      display: 'flex',
      gap: '4px',
      'align-items' : 'center',
    }}>
      {[0, 1, 2].map(i => (
        <div style={{
          width: `calc(${size()} / 4)`,
          height: `calc(${size()} / 4)`,
          'background-color : primaryColor,
          'border-radius' : '50%',
          animation: `bounce 1.4s ease-in-out both infinite`,
          'animation-delay : `${i * 0.16}s`,
        }}></div>
      ))}
    </div>
  );

  const PulseLoader = () => (
    <div style={{
      width: size(),
      height: size(),
      'background-color : primaryColor,
      'border-radius' : '50%',
      animation: 'pulse 1.5s ease-in-out infinite',
    }}></div>
  );

  const BarsLoader = () => (
    <div style={{
      display: 'flex',
      gap: '4px',
      'align-items' : 'center',
      height: size(),
    }}>
      {[0, 1, 2, 3, 4].map(i => (
        <div style={{
          width: `calc(${size()} / 8)`,
          height: '100%',
          'background-color : primaryColor,
          animation: 'bars 1.2s ease-in-out infinite',
          'animation-delay : `${i * 0.1}s`,
        }}></div>
      ))}
    </div>
  );

  const renderLoader = () => {
    switch (props.type || 'spinner') {
      case 'dots : return <DotsLoader />;
      case 'pulse : return <PulseLoader />;
      case 'bars : return <BarsLoader />;
      default: return <SpinnerLoader />;
    }
  };

  return (
    <div style={containerStyle()}>
      {renderLoader()}
      
      <Show when={props.message}>
        <div style={{
          color: props.theme === 'dark' ? '#d1d5db' : '#374151',
          'font-size' : '14px',
          'text-align' : 'center',
          'max-width' : '300px',
        }}>
          {props.message}
        </div>
      </Show>

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @keyframes bounce {
          0%, 80%, 100% {
            transform: scale(0);
            opacity: 0.5;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }
        
        @keyframes pulse {
          0% {
            transform: scale(0);
            opacity: 1;
          }
          100% {
            transform: scale(1);
            opacity: 0;
          }
        }
        
        @keyframes bars {
          0%, 40%, 100% {
            transform: scaleY(0.4);
            opacity: 0.5;
          }
          20% {
            transform: scaleY(1);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
}

// 全屏加载组件
export function FullScreenLoading(props: {
  message?: string;
  theme?: 'light' | 'dark';
}) {
  return (
    <LoadingSpinner
      size="large"
      type="spinner"
      theme={props.theme}
      message={props.message || '加载中...'}
      overlay
    />
  );
}

// 内联加载组件
export function InlineLoading(props: {
  message?: string;
  size?: 'small' | 'medium' | 'large';
  type?: 'spinner' | 'dots' | 'pulse' | 'bars';
  theme?: 'light' | 'dark';
}) {
  return (
    <LoadingSpinner
      size={props.size || 'small'}
      type={props.type || 'dots'}
      theme={props.theme}
      message={props.message}
    />
  );
}

// 页面加载组件
export function PageLoading(props: {
  title?: string;
  subtitle?: string;
  theme?: 'light' | 'dark';
}) {
  return (
    <div style={{
      display: 'flex',
      'flex-direction' : 'column',
      'align-items' : 'center',
      'justify-content' : 'center',
      height: '50vh',
      gap: '24px',
      padding: '40px',
    }}>
      <LoadingSpinner
        size="large"
        type="spinner"
        theme={props.theme}
      />
      
      <div style={{
        'text-align' : 'center',
        'max-width' : '400px',
      }}>
        <Show when={props.title}>
          <h2 style={{
            margin: '0 0 8px 0',
            'font-size' : '24px',
            'font-weight' : 'bold',
            color: props.theme === 'dark' ? '#f9fafb' : '#111827',
          }}>
            {props.title}
          </h2>
        </Show>
        
        <Show when={props.subtitle}>
          <p style={{
            margin: '0',
            color: props.theme === 'dark' ? '#9ca3af' : '#6b7280',
            'line-height' : '1.6',
          }}>
            {props.subtitle}
          </p>
        </Show>
      </div>
    </div>
  );
}

// 骨架屏加载组件
export function SkeletonLoading(props: {
  lines?: number;
  theme?: 'light' | 'dark';
  width?: string;
  height?: string;
}) {
  const lines = props.lines || 3;
  const baseColor = props.theme === 'dark' ? '#374151' : '#f3f4f6';
  const highlightColor = props.theme === 'dark' ? '#4b5563' : '#e5e7eb';

  return (
    <div style={{
      width: props.width || '100%',
      padding: '16px',
    }}>
      {Array.from({ length: lines }, (_, i) => (
        <div
          key={i}
          style={{
            height: props.height || '16px',
            background: `linear-gradient(90deg, ${baseColor} 25%, ${highlightColor} 50%, ${baseColor} 75%)`,
            backgroundSize: '200% 100%',
            'border-radius' : '4px',
            animation: 'shimmer 2s infinite',
            'margin-bottom' : '12px',
            width: i === lines - 1 ? '60%' : '100%', // 最后一行短一些
          }}
        />
      ))}
      
      <style>{`
        @keyframes shimmer {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }
      `}</style>
    </div>
  );
}
