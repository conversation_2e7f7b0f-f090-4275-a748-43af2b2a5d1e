// src/index.ts
function defineConfig(config) {
  return config;
}
function defineRecipe(config) {
  return config;
}
function defineSlotRecipe(config) {
  return config;
}
function defineParts(parts) {
  return function(config) {
    return Object.fromEntries(
      Object.entries(config).map(([key, value]) => {
        const part = parts[key];
        if (part == null) {
          throw new Error(
            `Part "${key}" does not exist in the anatomy. Available parts: ${Object.keys(parts).join(", ")}`
          );
        }
        return [part.selector, value];
      })
    );
  };
}
function definePattern(config) {
  return config;
}
function definePreset(preset) {
  return preset;
}
function defineKeyframes(keyframes) {
  return keyframes;
}
function defineGlobalStyles(definition) {
  return definition;
}
function defineUtility(utility) {
  return utility;
}
function definePlugin(plugin) {
  return plugin;
}
function defineThemeVariant(theme) {
  return theme;
}
function defineThemeContract(_contract) {
  return (theme) => defineThemeVariant(theme);
}
function createProxy() {
  const identity = (v) => v;
  return new Proxy(identity, {
    get() {
      return identity;
    }
  });
}
var defineTokens = /* @__PURE__ */ createProxy();
var defineSemanticTokens = /* @__PURE__ */ createProxy();
function defineTextStyles(definition) {
  return definition;
}
function defineLayerStyles(definition) {
  return definition;
}
function defineStyles(definition) {
  return definition;
}
export {
  defineConfig,
  defineGlobalStyles,
  defineKeyframes,
  defineLayerStyles,
  defineParts,
  definePattern,
  definePlugin,
  definePreset,
  defineRecipe,
  defineSemanticTokens,
  defineSlotRecipe,
  defineStyles,
  defineTextStyles,
  defineThemeContract,
  defineThemeVariant,
  defineTokens,
  defineUtility
};
