## 6.2.1 (2025-05-14)

### Bug fixes

Fix an issue where the indentation logic would too eagerly align lines to the body directly above them.

## 6.2.0 (2025-04-25)

### Bug fixes

Add an indentation rule for member expressions.

### New features

Allow multi-line strings to be code-folded.

## 6.1.7 (2025-01-18)

### Bug fixes

Properly indent match/case statements.

## 6.1.6 (2024-04-29)

### Bug fixes

Improve the way indentation for the current body is preserved when inenting new lines.

## 6.1.5 (2024-03-28)

### Bug fixes

Properly indent `else:` when attached to a `for` or `while` statement.

## 6.1.4 (2024-02-02)

### Bug fixes

Explicitly declare dependencies on @lezer/common and @codemirror/state

## 6.1.3 (2023-06-12)

### Bug fixes

Fix a bug where blocks started after a wrapped argument list or similar construct were indented too far.

## 6.1.2 (2023-03-01)

### Bug fixes

Don't indent lines after a dedented comment line.

## 6.1.1 (2022-12-24)

### Bug fixes

Remove leftover log statements.

## 6.1.0 (2022-11-18)

### New features

The `globalCompletion` completion source (included in the language support returned from `python()`) completes standard Python globals and keywords.

Export a `localCompletionSource` function that completes locally defined variables. Included in the support extensions returned from `python()`.

## 6.0.4 (2022-10-24)

### Bug fixes

Make sure the language object has a name.

## 6.0.3 (2022-10-19)

### Bug fixes

Add proper indentation handling of `else` clauses in `try` statements.

## 6.0.2 (2022-09-22)

### Bug fixes

Allow prefixed strings to be closed by `closeBrackets`.

## 6.0.1 (2022-07-21)

### Bug fixes

Fix (non-)auto indentation in template strings and comments.

## 6.0.0 (2022-06-08)

### Breaking changes

Update dependencies to 6.0.0

## 0.20.0 (2022-04-20)

### Bug fixes

Add folding information for set and tuple expressions.

## 0.19.5 (2022-04-06)

### Bug fixes

Make sure * and ** modifiers are highlighted as such, add modifier tag for FormatSpec nodes.

## 0.19.4 (2022-01-26)

### Bug fixes

Fix issue where folding body nodes folded away the newline after the body.

## 0.19.3 (2022-01-20)

### Bug fixes

Fix the way block bodies are folded.

## 0.19.2 (2021-08-11)

### Bug fixes

Make sure that indenting an else/elif/except/finally that's indented too deep moves it back up.

## 0.19.1 (2021-08-11)

### Bug fixes

Fix incorrect versions for @lezer dependencies.

## 0.19.0 (2021-08-11)

### Bug fixes

Improve indentation for dictionaries, arrays, and tuples. Fix delimitedIndent calls

Indentation on deindented blank lines after a block will no longer return to the block's indentation level.

## 0.18.1 (2021-07-20)

### Bug fixes

Fix highlighting of property names. Improve indentation support

Don't deindent when pressing enter at the end of the document.

Properly indent else/elif/except syntax.

## 0.18.0 (2021-03-03)

### Breaking changes

Update dependencies to 0.18.

## 0.17.2 (2021-01-12)

### Bug fixes

Improves the highlighting specificity for names of function and class definitions.

## 0.17.1 (2021-01-06)

### New features

The package now also exports a CommonJS module.

## 0.17.0 (2020-12-29)

### Breaking changes

First numbered release.

