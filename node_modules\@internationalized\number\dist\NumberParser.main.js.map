{"mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC;AAcD,MAAM,4CAAsB,IAAI,OAAO;AACvC,MAAM,0CAAoB;IAAC;IAAQ;IAAQ;IAAW;IAAQ;IAAQ;CAAW;AAQ1E,MAAM;IASX;;GAEC,GACD,MAAM,KAAa,EAAU;QAC3B,OAAO,0CAAoB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;IACrE;IAEA;;;;GAIC,GACD,qBAAqB,KAAa,EAAE,QAAiB,EAAE,QAAiB,EAAW;QACjF,OAAO,0CAAoB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,oBAAoB,CAAC,OAAO,UAAU;IACrG;IAEA;;;;GAIC,GACD,mBAAmB,KAAa,EAAU;QACxC,OAAO,0CAAoB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,eAAe;IACtF;IA5BA,YAAY,MAAc,EAAE,UAAoC,CAAC,CAAC,CAAE;QAClE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACjB;AA0BF;AAEA,MAAM,0CAAoB,IAAI;AAC9B,SAAS,0CAAoB,MAAc,EAAE,OAAiC,EAAE,KAAa;IAC3F,iEAAiE;IACjE,IAAI,gBAAgB,4CAAsB,QAAQ;IAElD,uFAAuF;IACvF,oFAAoF;IACpF,IAAI,CAAC,OAAO,QAAQ,CAAC,WAAW,CAAC,cAAc,oBAAoB,CAAC,QAAQ;QAC1E,KAAK,IAAI,mBAAmB,wCAC1B,IAAI,oBAAoB,cAAc,OAAO,CAAC,eAAe,EAAE;YAC7D,IAAI,SAAS,4CAAsB,SAAU,CAAA,OAAO,QAAQ,CAAC,SAAS,SAAS,QAAO,IAAK,iBAAiB;YAC5G,IAAI,OAAO,oBAAoB,CAAC,QAC9B,OAAO;QAEX;IAEJ;IAEA,OAAO;AACT;AAEA,SAAS,4CAAsB,MAAc,EAAE,OAAiC;IAC9E,IAAI,WAAW,SAAU,CAAA,UAAU,OAAO,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,GAAG,IAAI,KAAK,EAAC;IAC1G,IAAI,SAAS,wCAAkB,GAAG,CAAC;IACnC,IAAI,CAAC,QAAQ;QACX,SAAS,IAAI,uCAAiB,QAAQ;QACtC,wCAAkB,GAAG,CAAC,UAAU;IAClC;IAEA,OAAO;AACT;AAEA,8EAA8E;AAC9E,+DAA+D;AAC/D,MAAM;IA6BJ,MAAM,KAAa,EAAE;QACnB,wIAAwI;QACxI,IAAI,sBAAsB,IAAI,CAAC,QAAQ,CAAC;QAExC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EACpB,sFAAsF;QACtF,sBAAsB,iCAAW,qBAAqB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;QAE5E,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EACtB,sBAAsB,oBAAoB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAG;QAE3E,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EACxB,sBAAsB,oBAAoB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAG;QAE7E,sBAAsB,oBAAoB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;QAE1F,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;YACpC,gIAAgI;YAChI,IAAI,aAAa,oBAAoB,OAAO,CAAC;YAC7C,sBAAsB,oBAAoB,OAAO,CAAC,KAAK;YACvD,sBAAsB,oBAAoB,OAAO,CAAC,KAAK;YACvD,IAAI,QAAQ,oBAAoB,OAAO,CAAC;YACxC,IAAI,UAAU,IACZ,QAAQ,oBAAoB,MAAM;YAEpC,sBAAsB,oBAAoB,OAAO,CAAC,KAAK;YACvD,IAAI,QAAQ,MAAM,GAChB,sBAAsB,CAAC,EAAE,EAAE,qBAAqB;iBAC3C,IAAI,QAAQ,MAAM,IACvB,sBAAsB,CAAC,GAAG,EAAE,qBAAqB;iBAC5C,IAAI,QAAQ,MAAM,IACvB,sBAAsB;iBAEtB,sBAAsB,GAAG,oBAAoB,KAAK,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,oBAAoB,KAAK,CAAC,QAAQ,IAAI;YAE5G,IAAI,aAAa,IACf,sBAAsB,CAAC,CAAC,EAAE,qBAAqB;QAEnD;QAEA,IAAI,WAAW,sBAAsB,CAAC,sBAAsB;QAC5D,IAAI,MAAM,WACR,OAAO;QAGT,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;gBAKD,qCACA;YALnC,sEAAsE;YACtE,IAAI,UAAU;gBACZ,GAAG,IAAI,CAAC,OAAO;gBACf,OAAO;gBACP,uBAAuB,KAAK,GAAG,CAAC,AAAC,CAAA,CAAA,sCAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,cAAlC,iDAAA,sCAAsC,CAAA,IAAK,GAAG;gBAC/E,uBAAuB,KAAK,GAAG,CAAC,AAAC,CAAA,CAAA,sCAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,cAAlC,iDAAA,sCAAsC,CAAA,IAAK,GAAG;YACjF;YACA,OAAO,AAAC,IAAI,0CAAa,IAAI,CAAC,MAAM,EAAE,SAAU,KAAK,CAAC,IAAI,CAAA,GAAA,yCAAc,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,MAAM,CAAC;QACzG;QAEA,wJAAwJ;QACxJ,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,gBAAgB,0CAAoB,IAAI,CAAC,QACzE,WAAW,KAAK;QAGlB,OAAO;IACT;IAEA,SAAS,KAAa,EAAE;QACtB,2EAA2E;QAC3E,QAAQ,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QAE7C,8EAA8E;QAC9E,6FAA6F;QAC7F,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EACxB,QAAQ,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS;QAGnD,8FAA8F;QAC9F,4EAA4E;QAC5E,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,QAAQ;YAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACxB,QAAQ,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO;gBAC/C,QAAQ,MAAM,OAAO,CAAC,OAAO,YAAY,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO;YACvE;YACA,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EACpB,QAAQ,iCAAW,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;QAErD;QAEA,4HAA4H;QAC5H,gEAAgE;QAChE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACzD,QAAQ,iCAAW,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;YACjD,QAAQ,iCAAW,OAAO,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK;QACzD;QAEA,OAAO;IACT;IAEA,qBAAqB,KAAa,EAAE,WAAmB,CAAC,QAAQ,EAAE,WAAmB,QAAQ,EAAW;QACtG,QAAQ,IAAI,CAAC,QAAQ,CAAC;QAEtB,uEAAuE;QACvE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,WAAW,GACnF,QAAQ,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM;aAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,WAAW,GACxF,QAAQ,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM;QAGlD,8CAA8C;QAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAC3D,OAAO;QAGT,kFAAkF;QAClF,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,qBAAqB,KAAK,GAC7G,OAAO;QAGT,wCAAwC;QACxC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EACpB,QAAQ,iCAAW,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;QAEhD,QAAQ,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;QAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EACtB,QAAQ,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;QAG9C,2DAA2D;QAC3D,OAAO,MAAM,MAAM,KAAK;IAC1B;IAtJA,YAAY,MAAc,EAAE,UAAoC,CAAC,CAAC,CAAE;QAClE,IAAI,CAAC,MAAM,GAAG;QACd,sJAAsJ;QACtJ,oFAAoF;QACpF,IAAI,QAAQ,iBAAiB,KAAK,KAAK,QAAQ,iBAAiB,IAAI,MAAM;YACxE,IAAI,QAAQ,qBAAqB,IAAI,QAAQ,QAAQ,qBAAqB,IAAI,MAAM;gBAClF,QAAQ,qBAAqB,GAAG;gBAChC,QAAQ,qBAAqB,GAAG;YAClC,OAAO,IAAI,QAAQ,qBAAqB,IAAI,MAC1C,QAAQ,qBAAqB,GAAG,QAAQ,qBAAqB;iBACxD,IAAI,QAAQ,qBAAqB,IAAI,MAC1C,QAAQ,qBAAqB,GAAG,QAAQ,qBAAqB;QAE/D,8DAA8D;QAChE;QACA,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,YAAY,CAAC,QAAQ;QAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe;QAC7C,IAAI,CAAC,OAAO,GAAG,iCAAW,QAAQ,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE;YACtB,qCAAkD;QAA5F,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,aAAc,CAAA,AAAC,CAAA,CAAA,sCAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,cAAlC,iDAAA,sCAAsC,CAAA,IAAK,MAAM,AAAC,CAAA,CAAA,sCAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,cAAlC,iDAAA,sCAAsC,CAAA,IAAK,EAAC,GACtI,QAAQ,IAAI,CAAC;IAEjB;AAkIF;AAEA,MAAM,wCAAkB,IAAI,IAAI;IAAC;IAAW;IAAY;IAAW;IAAa;IAAY;CAAQ;AAEpG,mIAAmI;AACnI,2GAA2G;AAC3G,2FAA2F;AAC3F,MAAM,sCAAgB;IACpB;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAG;IAAG;IAAK;IAAI;IAAK;CACzC;AAED,SAAS,iCAAW,MAAc,EAAE,SAA4B,EAAE,WAA6C,EAAE,eAAyC;QAexI,gBACD,mBAYD,oBACF;IA5BZ,mHAAmH;IACnH,IAAI,kBAAkB,IAAI,KAAK,YAAY,CAAC,QAAQ;QAAC,GAAG,WAAW;QACjE,6CAA6C;QAC7C,0BAA0B;QAC1B,0BAA0B;QAC1B,mBAAmB;QACnB,kBAAkB;QAClB,cAAc;IAChB;IACA,oFAAoF;IACpF,IAAI,WAAW,gBAAgB,aAAa,CAAC;IAC7C,IAAI,cAAc,gBAAgB,aAAa,CAAC;IAChD,IAAI,cAAc,oCAAc,GAAG,CAAC,CAAA,IAAK,gBAAgB,aAAa,CAAC;QAEvD;IAAhB,IAAI,YAAY,CAAA,wBAAA,iBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,0BAA9B,qCAAA,eAA4C,KAAK,cAAjD,kCAAA,uBAAqD;IACrE,IAAI,YAAW,oBAAA,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,yBAAjC,wCAAA,kBAA8C,KAAK;IAElE,sFAAsF;IACtF,8GAA8G;IAC9G,IAAI,CAAC,YAAa,CAAA,CAAA,4BAAA,sCAAA,gBAAiB,WAAW,MAAK,gBAAgB,CAAA,4BAAA,sCAAA,gBAAiB,WAAW,MAAK,QAAO,GACzG,WAAW;IAGb,kHAAkH;IAClH,wHAAwH;IACxH,IAAI,eAAe,IAAI,KAAK,YAAY,CAAC,QAAQ;QAAC,GAAG,WAAW;QAAE,uBAAuB;QAAG,uBAAuB;IAAC,GAAG,aAAa,CAAC;IAErI,IAAI,WAAU,qBAAA,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,wBAAlC,yCAAA,mBAA8C,KAAK;IACjE,IAAI,SAAQ,kBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,sBAA9B,sCAAA,gBAAwC,KAAK;IAEzD,+GAA+G;IAC/G,0CAA0C;IAC1C,IAAI,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,sCAAgB,GAAG,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,CAAA,IAAK,kCAAY,EAAE,KAAK;IACtG,IAAI,sBAAsB,YAAY,OAAO,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,CAAA,IAAK,CAAC,sCAAgB,GAAG,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,CAAA,IAAK,kCAAY,EAAE,KAAK;IAC3H,IAAI,iBAAiB;WAAI,IAAI,IAAI;eAAI;eAAqB;SAAoB;KAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;IAEnH,IAAI,WAAW,eAAe,MAAM,KAAK,IACrC,IAAI,OAAO,sBAAsB,QACjC,IAAI,OAAO,GAAG,eAAe,IAAI,CAAC,KAAK,mBAAmB,CAAC,EAAE;IAEjE,uEAAuE;IACvE,IAAI,WAAW;WAAI,IAAI,KAAK,YAAY,CAAC,YAAY,MAAM,EAAE;YAAC,aAAa;QAAK,GAAG,MAAM,CAAC;KAAY,CAAC,OAAO;IAC9G,IAAI,UAAU,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,GAAG,IAAM;YAAC;YAAG;SAAE;IACnD,IAAI,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;IACnD,IAAI,QAAQ,CAAA,IAAK,OAAO,QAAQ,GAAG,CAAC;IAEpC,OAAO;mBAAC;kBAAW;iBAAU;eAAS;kBAAO;iBAAU;eAAS;IAAK;AACvE;AAEA,SAAS,iCAAW,GAAW,EAAE,IAAqB,EAAE,OAAe;IACrE,IAAI,IAAI,UAAU,EAChB,OAAO,IAAI,UAAU,CAAC,MAAM;IAG9B,OAAO,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;AAC9B;AAEA,SAAS,kCAAY,MAAc;IACjC,OAAO,OAAO,OAAO,CAAC,uBAAuB;AAC/C", "sources": ["packages/@internationalized/number/src/NumberParser.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {NumberFormatter} from './NumberFormatter';\n\ninterface Symbols {\n  minusSign?: string,\n  plusSign?: string,\n  decimal?: string,\n  group?: string,\n  literals: RegExp,\n  numeral: RegExp,\n  index: (v: string) => string\n}\n\nconst CURRENCY_SIGN_REGEX = new RegExp('^.*\\\\(.*\\\\).*$');\nconst NUMBERING_SYSTEMS = ['latn', 'arab', 'hanidec', 'deva', 'beng', 'fullwide'];\n\n/**\n * A NumberParser can be used to perform locale-aware parsing of numbers from Unicode strings,\n * as well as validation of partial user input. It automatically detects the numbering system\n * used in the input, and supports parsing decimals, percentages, currency values, and units\n * according to the locale.\n */\nexport class NumberParser {\n  private locale: string;\n  private options: Intl.NumberFormatOptions;\n\n  constructor(locale: string, options: Intl.NumberFormatOptions = {}) {\n    this.locale = locale;\n    this.options = options;\n  }\n\n  /**\n   * Parses the given string to a number. Returns NaN if a valid number could not be parsed.\n   */\n  parse(value: string): number {\n    return getNumberParserImpl(this.locale, this.options, value).parse(value);\n  }\n\n  /**\n   * Returns whether the given string could potentially be a valid number. This should be used to\n   * validate user input as the user types. If a `minValue` or `maxValue` is provided, the validity\n   * of the minus/plus sign characters can be checked.\n   */\n  isValidPartialNumber(value: string, minValue?: number, maxValue?: number): boolean {\n    return getNumberParserImpl(this.locale, this.options, value).isValidPartialNumber(value, minValue, maxValue);\n  }\n\n  /**\n   * Returns a numbering system for which the given string is valid in the current locale.\n   * If no numbering system could be detected, the default numbering system for the current\n   * locale is returned.\n   */\n  getNumberingSystem(value: string): string {\n    return getNumberParserImpl(this.locale, this.options, value).options.numberingSystem;\n  }\n}\n\nconst numberParserCache = new Map<string, NumberParserImpl>();\nfunction getNumberParserImpl(locale: string, options: Intl.NumberFormatOptions, value: string) {\n  // First try the default numbering system for the provided locale\n  let defaultParser = getCachedNumberParser(locale, options);\n\n  // If that doesn't match, and the locale doesn't include a hard coded numbering system,\n  // try each of the other supported numbering systems until we find one that matches.\n  if (!locale.includes('-nu-') && !defaultParser.isValidPartialNumber(value)) {\n    for (let numberingSystem of NUMBERING_SYSTEMS) {\n      if (numberingSystem !== defaultParser.options.numberingSystem) {\n        let parser = getCachedNumberParser(locale + (locale.includes('-u-') ? '-nu-' : '-u-nu-') + numberingSystem, options);\n        if (parser.isValidPartialNumber(value)) {\n          return parser;\n        }\n      }\n    }\n  }\n\n  return defaultParser;\n}\n\nfunction getCachedNumberParser(locale: string, options: Intl.NumberFormatOptions) {\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  let parser = numberParserCache.get(cacheKey);\n  if (!parser) {\n    parser = new NumberParserImpl(locale, options);\n    numberParserCache.set(cacheKey, parser);\n  }\n\n  return parser;\n}\n\n// The actual number parser implementation. Instances of this class are cached\n// based on the locale, options, and detected numbering system.\nclass NumberParserImpl {\n  formatter: Intl.NumberFormat;\n  options: Intl.ResolvedNumberFormatOptions;\n  symbols: Symbols;\n  locale: string;\n\n  constructor(locale: string, options: Intl.NumberFormatOptions = {}) {\n    this.locale = locale;\n    // see https://tc39.es/ecma402/#sec-setnfdigitoptions, when using roundingIncrement, the maximumFractionDigits and minimumFractionDigits must be equal\n    // by default, they are 0 and 3 respectively, so we set them to 0 if neither are set\n    if (options.roundingIncrement !== 1 && options.roundingIncrement != null) {\n      if (options.maximumFractionDigits == null && options.minimumFractionDigits == null) {\n        options.maximumFractionDigits = 0;\n        options.minimumFractionDigits = 0;\n      } else if (options.maximumFractionDigits == null) {\n        options.maximumFractionDigits = options.minimumFractionDigits;\n      } else if (options.minimumFractionDigits == null) {\n        options.minimumFractionDigits = options.maximumFractionDigits;\n      }\n      // if both are specified, let the normal Range Error be thrown\n    }\n    this.formatter = new Intl.NumberFormat(locale, options);\n    this.options = this.formatter.resolvedOptions();\n    this.symbols = getSymbols(locale, this.formatter, this.options, options);\n    if (this.options.style === 'percent' && ((this.options.minimumFractionDigits ?? 0) > 18 || (this.options.maximumFractionDigits ?? 0) > 18)) {\n      console.warn('NumberParser cannot handle percentages with greater than 18 decimal places, please reduce the number in your options.');\n    }\n  }\n\n  parse(value: string) {\n    // to parse the number, we need to remove anything that isn't actually part of the number, for example we want '-10.40' not '-10.40 USD'\n    let fullySanitizedValue = this.sanitize(value);\n\n    if (this.symbols.group) {\n      // Remove group characters, and replace decimal points and numerals with ASCII values.\n      fullySanitizedValue = replaceAll(fullySanitizedValue, this.symbols.group, '');\n    }\n    if (this.symbols.decimal) {\n      fullySanitizedValue = fullySanitizedValue.replace(this.symbols.decimal!, '.');\n    }\n    if (this.symbols.minusSign) {\n      fullySanitizedValue = fullySanitizedValue.replace(this.symbols.minusSign!, '-');\n    }\n    fullySanitizedValue = fullySanitizedValue.replace(this.symbols.numeral, this.symbols.index);\n\n    if (this.options.style === 'percent') {\n      // javascript is bad at dividing by 100 and maintaining the same significant figures, so perform it on the string before parsing\n      let isNegative = fullySanitizedValue.indexOf('-');\n      fullySanitizedValue = fullySanitizedValue.replace('-', '');\n      fullySanitizedValue = fullySanitizedValue.replace('+', '');\n      let index = fullySanitizedValue.indexOf('.');\n      if (index === -1) {\n        index = fullySanitizedValue.length;\n      }\n      fullySanitizedValue = fullySanitizedValue.replace('.', '');\n      if (index - 2 === 0) {\n        fullySanitizedValue = `0.${fullySanitizedValue}`;\n      } else if (index - 2 === -1) {\n        fullySanitizedValue = `0.0${fullySanitizedValue}`;\n      } else if (index - 2 === -2) {\n        fullySanitizedValue = '0.00';\n      } else {\n        fullySanitizedValue = `${fullySanitizedValue.slice(0, index - 2)}.${fullySanitizedValue.slice(index - 2)}`;\n      }\n      if (isNegative > -1) {\n        fullySanitizedValue = `-${fullySanitizedValue}`;\n      }\n    }\n\n    let newValue = fullySanitizedValue ? +fullySanitizedValue : NaN;\n    if (isNaN(newValue)) {\n      return NaN;\n    }\n\n    if (this.options.style === 'percent') {\n      // extra step for rounding percents to what our formatter would output\n      let options = {\n        ...this.options,\n        style: 'decimal' as const,\n        minimumFractionDigits: Math.min((this.options.minimumFractionDigits ?? 0) + 2, 20),\n        maximumFractionDigits: Math.min((this.options.maximumFractionDigits ?? 0) + 2, 20)\n      };\n      return (new NumberParser(this.locale, options)).parse(new NumberFormatter(this.locale, options).format(newValue));\n    }\n\n    // accounting will always be stripped to a positive number, so if it's accounting and has a () around everything, then we need to make it negative again\n    if (this.options.currencySign === 'accounting' && CURRENCY_SIGN_REGEX.test(value)) {\n      newValue = -1 * newValue;\n    }\n\n    return newValue;\n  }\n\n  sanitize(value: string) {\n    // Remove literals and whitespace, which are allowed anywhere in the string\n    value = value.replace(this.symbols.literals, '');\n\n    // Replace the ASCII minus sign with the minus sign used in the current locale\n    // so that both are allowed in case the user's keyboard doesn't have the locale's minus sign.\n    if (this.symbols.minusSign) {\n      value = value.replace('-', this.symbols.minusSign);\n    }\n\n    // In arab numeral system, their decimal character is 1643, but most keyboards don't type that\n    // instead they use the , (44) character or apparently the (1548) character.\n    if (this.options.numberingSystem === 'arab') {\n      if (this.symbols.decimal) {\n        value = value.replace(',', this.symbols.decimal);\n        value = value.replace(String.fromCharCode(1548), this.symbols.decimal);\n      }\n      if (this.symbols.group) {\n        value = replaceAll(value, '.', this.symbols.group);\n      }\n    }\n\n    // fr-FR group character is narrow non-breaking space, char code 8239 (U+202F), but that's not a key on the french keyboard,\n    // so allow space and non-breaking space as a group char as well\n    if (this.options.locale === 'fr-FR' && this.symbols.group) {\n      value = replaceAll(value, ' ', this.symbols.group);\n      value = replaceAll(value, /\\u00A0/g, this.symbols.group);\n    }\n\n    return value;\n  }\n\n  isValidPartialNumber(value: string, minValue: number = -Infinity, maxValue: number = Infinity): boolean {\n    value = this.sanitize(value);\n\n    // Remove minus or plus sign, which must be at the start of the string.\n    if (this.symbols.minusSign && value.startsWith(this.symbols.minusSign) && minValue < 0) {\n      value = value.slice(this.symbols.minusSign.length);\n    } else if (this.symbols.plusSign && value.startsWith(this.symbols.plusSign) && maxValue > 0) {\n      value = value.slice(this.symbols.plusSign.length);\n    }\n\n    // Numbers cannot start with a group separator\n    if (this.symbols.group && value.startsWith(this.symbols.group)) {\n      return false;\n    }\n\n    // Numbers that can't have any decimal values fail if a decimal character is typed\n    if (this.symbols.decimal && value.indexOf(this.symbols.decimal) > -1 && this.options.maximumFractionDigits === 0) {\n      return false;\n    }\n\n    // Remove numerals, groups, and decimals\n    if (this.symbols.group) {\n      value = replaceAll(value, this.symbols.group, '');\n    }\n    value = value.replace(this.symbols.numeral, '');\n    if (this.symbols.decimal) {\n      value = value.replace(this.symbols.decimal, '');\n    }\n\n    // The number is valid if there are no remaining characters\n    return value.length === 0;\n  }\n}\n\nconst nonLiteralParts = new Set(['decimal', 'fraction', 'integer', 'minusSign', 'plusSign', 'group']);\n\n// This list is derived from https://www.unicode.org/cldr/charts/43/supplemental/language_plural_rules.html#comparison and includes\n// all unique numbers which we need to check in order to determine all the plural forms for a given locale.\n// See: https://github.com/adobe/react-spectrum/pull/5134/files#r1337037855 for used script\nconst pluralNumbers = [\n  0, 4, 2, 1, 11, 20, 3, 7, 100, 21, 0.1, 1.1\n];\n\nfunction getSymbols(locale: string, formatter: Intl.NumberFormat, intlOptions: Intl.ResolvedNumberFormatOptions, originalOptions: Intl.NumberFormatOptions): Symbols {\n  // formatter needs access to all decimal places in order to generate the correct literal strings for the plural set\n  let symbolFormatter = new Intl.NumberFormat(locale, {...intlOptions,\n    // Resets so we get the full range of symbols\n    minimumSignificantDigits: 1,\n    maximumSignificantDigits: 21,\n    roundingIncrement: 1,\n    roundingPriority: 'auto',\n    roundingMode: 'halfExpand'\n  });\n  // Note: some locale's don't add a group symbol until there is a ten thousands place\n  let allParts = symbolFormatter.formatToParts(-10000.111);\n  let posAllParts = symbolFormatter.formatToParts(10000.111);\n  let pluralParts = pluralNumbers.map(n => symbolFormatter.formatToParts(n));\n\n  let minusSign = allParts.find(p => p.type === 'minusSign')?.value ?? '-';\n  let plusSign = posAllParts.find(p => p.type === 'plusSign')?.value;\n\n  // Safari does not support the signDisplay option, but our number parser polyfills it.\n  // If no plus sign was returned, but the original options contained signDisplay, default to the '+' character.\n  if (!plusSign && (originalOptions?.signDisplay === 'exceptZero' || originalOptions?.signDisplay === 'always')) {\n    plusSign = '+';\n  }\n\n  // If maximumSignificantDigits is 1 (the minimum) then we won't get decimal characters out of the above formatters\n  // Percent also defaults to 0 fractionDigits, so we need to make a new one that isn't percent to get an accurate decimal\n  let decimalParts = new Intl.NumberFormat(locale, {...intlOptions, minimumFractionDigits: 2, maximumFractionDigits: 2}).formatToParts(0.001);\n\n  let decimal = decimalParts.find(p => p.type === 'decimal')?.value;\n  let group = allParts.find(p => p.type === 'group')?.value;\n\n  // this set is also for a regex, it's all literals that might be in the string we want to eventually parse that\n  // don't contribute to the numerical value\n  let allPartsLiterals = allParts.filter(p => !nonLiteralParts.has(p.type)).map(p => escapeRegex(p.value));\n  let pluralPartsLiterals = pluralParts.flatMap(p => p.filter(p => !nonLiteralParts.has(p.type)).map(p => escapeRegex(p.value)));\n  let sortedLiterals = [...new Set([...allPartsLiterals, ...pluralPartsLiterals])].sort((a, b) => b.length - a.length);\n\n  let literals = sortedLiterals.length === 0 ?\n      new RegExp('[\\\\p{White_Space}]', 'gu') :\n      new RegExp(`${sortedLiterals.join('|')}|[\\\\p{White_Space}]`, 'gu');\n\n  // These are for replacing non-latn characters with the latn equivalent\n  let numerals = [...new Intl.NumberFormat(intlOptions.locale, {useGrouping: false}).format(9876543210)].reverse();\n  let indexes = new Map(numerals.map((d, i) => [d, i]));\n  let numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n  let index = d => String(indexes.get(d));\n\n  return {minusSign, plusSign, decimal, group, literals, numeral, index};\n}\n\nfunction replaceAll(str: string, find: string | RegExp, replace: string) {\n  if (str.replaceAll) {\n    return str.replaceAll(find, replace);\n  }\n\n  return str.split(find).join(replace);\n}\n\nfunction escapeRegex(string: string) {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n"], "names": [], "version": 3, "file": "NumberParser.main.js.map"}