"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  defineConfig: () => defineConfig,
  defineGlobalStyles: () => defineGlobalStyles,
  defineKeyframes: () => defineKeyframes,
  defineLayerStyles: () => defineLayerStyles,
  defineParts: () => defineParts,
  definePattern: () => definePattern,
  definePlugin: () => definePlugin,
  definePreset: () => definePreset,
  defineRecipe: () => defineRecipe,
  defineSemanticTokens: () => defineSemanticTokens,
  defineSlotRecipe: () => defineSlotRecipe,
  defineStyles: () => defineStyles,
  defineTextStyles: () => defineTextStyles,
  defineThemeContract: () => defineThemeContract,
  defineThemeVariant: () => defineThemeVariant,
  defineTokens: () => defineTokens,
  defineUtility: () => defineUtility
});
module.exports = __toCommonJS(src_exports);
function defineConfig(config) {
  return config;
}
function defineRecipe(config) {
  return config;
}
function defineSlotRecipe(config) {
  return config;
}
function defineParts(parts) {
  return function(config) {
    return Object.fromEntries(
      Object.entries(config).map(([key, value]) => {
        const part = parts[key];
        if (part == null) {
          throw new Error(
            `Part "${key}" does not exist in the anatomy. Available parts: ${Object.keys(parts).join(", ")}`
          );
        }
        return [part.selector, value];
      })
    );
  };
}
function definePattern(config) {
  return config;
}
function definePreset(preset) {
  return preset;
}
function defineKeyframes(keyframes) {
  return keyframes;
}
function defineGlobalStyles(definition) {
  return definition;
}
function defineUtility(utility) {
  return utility;
}
function definePlugin(plugin) {
  return plugin;
}
function defineThemeVariant(theme) {
  return theme;
}
function defineThemeContract(_contract) {
  return (theme) => defineThemeVariant(theme);
}
function createProxy() {
  const identity = (v) => v;
  return new Proxy(identity, {
    get() {
      return identity;
    }
  });
}
var defineTokens = /* @__PURE__ */ createProxy();
var defineSemanticTokens = /* @__PURE__ */ createProxy();
function defineTextStyles(definition) {
  return definition;
}
function defineLayerStyles(definition) {
  return definition;
}
function defineStyles(definition) {
  return definition;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  defineConfig,
  defineGlobalStyles,
  defineKeyframes,
  defineLayerStyles,
  defineParts,
  definePattern,
  definePlugin,
  definePreset,
  defineRecipe,
  defineSemanticTokens,
  defineSlotRecipe,
  defineStyles,
  defineTextStyles,
  defineThemeContract,
  defineThemeVariant,
  defineTokens,
  defineUtility
});
