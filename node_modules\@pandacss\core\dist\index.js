"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/plugins/optimize-lightningcss.ts
var optimize_lightningcss_exports = {};
__export(optimize_lightningcss_exports, {
  default: () => optimizeLightCss
});
function optimizeLightCss(code, options = {}) {
  const { minify = false, browserslist: targets } = options;
  const codeStr = typeof code === "string" ? code : code.toString();
  const result = (0, import_lightningcss.transform)({
    code: Buffer.from(codeStr),
    minify,
    sourceMap: false,
    filename: "styles.css",
    include: import_lightningcss.Features.Nesting | import_lightningcss.Features.MediaRangeSyntax,
    targets: (0, import_lightningcss.browserslistToTargets)((0, import_browserslist.default)(targets)),
    errorRecovery: true
  });
  if (result.warnings.length) {
    const split = codeStr.split("\n");
    import_logger3.logger.warn(
      "css",
      result.warnings.map((w) => ({ ...w, line: split[w.loc.line - 1] }))
    );
  }
  return decoder.decode(result.code);
}
var import_logger3, import_lightningcss, import_postcss5, import_browserslist, decoder;
var init_optimize_lightningcss = __esm({
  "src/plugins/optimize-lightningcss.ts"() {
    "use strict";
    import_logger3 = require("@pandacss/logger");
    import_lightningcss = require("lightningcss");
    import_postcss5 = require("postcss");
    import_browserslist = __toESM(require("browserslist"));
    decoder = new TextDecoder();
  }
});

// src/index.ts
var src_exports = {};
__export(src_exports, {
  Breakpoints: () => Breakpoints,
  Conditions: () => Conditions,
  Context: () => Context,
  FileMatcher: () => FileMatcher,
  ImportMap: () => ImportMap,
  JsxEngine: () => JsxEngine,
  Layers: () => Layers,
  Patterns: () => Patterns,
  Recipes: () => Recipes,
  RuleProcessor: () => RuleProcessor,
  StaticCss: () => StaticCss,
  StyleDecoder: () => StyleDecoder,
  StyleEncoder: () => StyleEncoder,
  Stylesheet: () => Stylesheet,
  Utility: () => Utility,
  expandNestedCss: () => expandNestedCss,
  extractParentSelectors: () => extractParentSelectors,
  messages: () => messages_exports,
  optimizeCss: () => optimizeCss,
  stringify: () => stringify
});
module.exports = __toCommonJS(src_exports);

// src/messages.ts
var messages_exports = {};
__export(messages_exports, {
  artifactsGenerated: () => artifactsGenerated,
  buildComplete: () => buildComplete,
  codegenComplete: () => codegenComplete,
  configExists: () => configExists,
  configWatch: () => configWatch,
  cssArtifactComplete: () => cssArtifactComplete,
  getMessages: () => getMessages,
  noExtract: () => noExtract,
  thankYou: () => thankYou,
  watch: () => watch
});
var import_logger = require("@pandacss/logger");
var import_outdent = require("outdent");
var tick = import_logger.colors.green().bold("\u2714\uFE0F");
var artifactsGenerated = (ctx) => {
  const {
    config: { outdir, themes },
    recipes,
    patterns,
    tokens,
    jsx
  } = ctx;
  return () => [
    import_outdent.outdent`
    ${tick} ${(0, import_logger.quote)(outdir, "/css")}: the css function to author styles
    `,
    !tokens.isEmpty && import_outdent.outdent`
    ${tick} ${(0, import_logger.quote)(outdir, "/tokens")}: the css variables and js function to query your tokens
    `,
    !patterns.isEmpty() && !ctx.isTemplateLiteralSyntax && import_outdent.outdent`
    ${tick} ${(0, import_logger.quote)(outdir, "/patterns")}: functions to implement and apply common layout patterns
    `,
    !recipes.isEmpty() && import_outdent.outdent`
      ${tick} ${(0, import_logger.quote)(outdir, "/recipes")}: functions to create multi-variant styles
      `,
    jsx.framework && import_outdent.outdent`
      ${tick} ${(0, import_logger.quote)(outdir, "/jsx")}: styled jsx elements for ${jsx.framework}
      `,
    themes && import_outdent.outdent`
    ${tick} ${(0, import_logger.quote)(outdir, "/themes")}: theme variants for your design system
    `,
    "\n"
  ].filter(Boolean).join("\n");
};
var configExists = (cmd) => import_outdent.outdent`
      \n
      It looks like you already have panda created\`.

      You can now run ${(0, import_logger.quote)(cmd, " panda --watch")}.

      `;
var thankYou = () => import_outdent.outdent`

  🚀 Thanks for choosing ${import_logger.colors.cyan("Panda")} to write your css.

  You are set up to start using Panda!

  `;
var codegenComplete = () => import_outdent.outdent`

  ${import_logger.colors.bold().cyan("Next steps:")}

  [1] Create a ${(0, import_logger.quote)("index.css")} file in your project that contains:

      @layer reset, base, tokens, recipes, utilities;


  [2] Import the ${(0, import_logger.quote)("index.css")} file at the root of your project.

  `;
var noExtract = () => import_outdent.outdent`
  No style object or props were detected in your source files.
  If this is unexpected, double-check the \`include\` options in your Panda config\n
`;
var watch = () => import_outdent.outdent`
  Watching for file changes...
  `;
var configWatch = () => import_outdent.outdent`
  Watching for config file changes...
  `;
var buildComplete = (count) => import_outdent.outdent`
  Successfully extracted css from ${count} file(s) ✨
  `;
var cssArtifactComplete = (type) => `Successfully generated ${type} css artifact \u2728`;
var getMessages = (ctx) => ({
  artifactsGenerated: artifactsGenerated(ctx),
  configExists,
  thankYou,
  codegenComplete,
  noExtract,
  watch,
  buildComplete,
  configWatch,
  cssArtifactComplete
});

// src/breakpoints.ts
var import_shared = require("@pandacss/shared");
var Breakpoints = class {
  constructor(breakpoints) {
    this.breakpoints = breakpoints;
    this.sorted = sortBreakpoints(breakpoints);
    this.values = Object.fromEntries(this.sorted);
    this.keys = ["base", ...Object.keys(this.values)];
    this.ranges = this.getRanges();
    this.conditions = this.getConditions();
  }
  sorted;
  values;
  keys;
  ranges;
  conditions;
  get = (name) => {
    return this.values[name];
  };
  build = ({ min, max }) => {
    if (min == null && max == null)
      return "";
    return ["screen", min && `(min-width: ${min})`, max && `(max-width: ${max})`].filter(Boolean).join(" and ");
  };
  only = (name) => {
    const { min, max } = this.get(name);
    return this.build({ min, max });
  };
  getRanges = () => {
    const breakpoints = Object.keys(this.values);
    const permuations = getPermutations(breakpoints);
    const values = breakpoints.flatMap((name) => {
      const value = this.get(name);
      const down = [`${name}Down`, this.build({ max: adjust(value.min) })];
      const up = [name, this.build({ min: value.min })];
      const only = [`${name}Only`, this.only(name)];
      return [up, only, down];
    }).filter(([_, value]) => value !== "").concat(
      permuations.map(([min, max]) => {
        const minValue = this.get(min);
        const maxValue2 = this.get(max);
        return [`${min}To${(0, import_shared.capitalize)(max)}`, this.build({ min: minValue.min, max: adjust(maxValue2.min) })];
      })
    );
    return Object.fromEntries(values);
  };
  getConditions = () => {
    const values = Object.entries(this.ranges).map(([key, value]) => {
      return [key, toCondition(key, value)];
    });
    return Object.fromEntries(values);
  };
  getCondition = (key) => {
    return this.conditions[key];
  };
  expandScreenAtRule = (root) => {
    root.walkAtRules("breakpoint", (rule) => {
      const value = this.getCondition(rule.params);
      if (!value) {
        throw rule.error(`No \`${rule.params}\` screen found.`);
      }
      if (value.type !== "at-rule") {
        throw rule.error(`\`${rule.params}\` is not a valid screen.`);
      }
      rule.name = "media";
      rule.params = value.params;
    });
  };
};
function adjust(value) {
  const computedMax = parseFloat((0, import_shared.toPx)(value) ?? "") - 0.04;
  return (0, import_shared.toRem)(`${computedMax}px`);
}
function sortBreakpoints(breakpoints) {
  return Object.entries(breakpoints).sort(([, minA], [, minB]) => {
    return parseInt(minA, 10) < parseInt(minB, 10) ? -1 : 1;
  }).map(([name, min], index, entries) => {
    let max = null;
    if (index <= entries.length - 1) {
      max = entries[index + 1]?.[1];
    }
    if (max != null) {
      max = adjust(max);
    }
    return [name, { name, min: (0, import_shared.toRem)(min), max }];
  });
}
var toCondition = (key, value) => ({
  type: "at-rule",
  name: "breakpoint",
  value: key,
  raw: `@media ${value}`,
  params: value
});
function getPermutations(values) {
  const result = [];
  values.forEach((current, index) => {
    let idx = index;
    idx++;
    let next = values[idx];
    while (next) {
      result.push([current, next]);
      idx++;
      next = values[idx];
    }
  });
  return result;
}

// src/conditions.ts
var import_logger2 = require("@pandacss/logger");
var import_shared3 = require("@pandacss/shared");

// src/parse-condition.ts
var import_postcss2 = require("postcss");

// src/safe-parse.ts
var import_postcss = __toESM(require("postcss"));
function safeParse(str) {
  try {
    return import_postcss.default.parse(str);
  } catch (error) {
    return import_postcss.default.root();
  }
}

// src/parse-condition.ts
function parseAtRule(value) {
  const result = safeParse(value);
  const rule = result.nodes[0];
  return {
    type: "at-rule",
    name: rule.name,
    value: rule.params,
    raw: value,
    params: rule.params
  };
}
function parseCondition(condition) {
  if (Array.isArray(condition)) {
    return {
      type: "mixed",
      raw: condition,
      value: condition.map(parseCondition)
    };
  }
  if (condition.startsWith("@")) {
    return parseAtRule(condition);
  }
  let type;
  if (condition.startsWith("&")) {
    type = "self-nesting";
  } else if (condition.endsWith(" &")) {
    type = "parent-nesting";
  } else if (condition.includes("&")) {
    type = "combinator-nesting";
  }
  if (type) {
    return { type, value: condition, raw: condition };
  }
}

// src/sort-at-rules.ts
var minMaxWidth = /(!?\(\s*min(-device-)?-width)(.|\n)+\(\s*max(-device)?-width/i;
var minWidth = /\(\s*min(-device)?-width/i;
var maxMinWidth = /(!?\(\s*max(-device)?-width)(.|\n)+\(\s*min(-device)?-width/i;
var maxWidth = /\(\s*max(-device)?-width/i;
var isMinWidth = _testQuery(minMaxWidth, maxMinWidth, minWidth);
var isMaxWidth = _testQuery(maxMinWidth, minMaxWidth, maxWidth);
var minMaxHeight = /(!?\(\s*min(-device)?-height)(.|\n)+\(\s*max(-device)?-height/i;
var minHeight = /\(\s*min(-device)?-height/i;
var maxMinHeight = /(!?\(\s*max(-device)?-height)(.|\n)+\(\s*min(-device)?-height/i;
var maxHeight = /\(\s*max(-device)?-height/i;
var isMinHeight = _testQuery(minMaxHeight, maxMinHeight, minHeight);
var isMaxHeight = _testQuery(maxMinHeight, minMaxHeight, maxHeight);
var isPrint = /print/i;
var isPrintOnly = /^print$/i;
var maxValue = Number.MAX_VALUE;
function getQueryLength(query) {
  let length = /(-?\d*\.?\d+)(ch|em|ex|px|rem)/.exec(query);
  if (length === null && (isMinWidth(query) || isMinHeight(query))) {
    length = /(\d)/.exec(query);
  }
  if (length === "0") {
    return 0;
  }
  if (length === null) {
    return maxValue;
  }
  let number = length[1];
  const unit = length[2];
  switch (unit) {
    case "ch":
      number = parseFloat(number) * 8.8984375;
      break;
    case "em":
    case "rem":
      number = parseFloat(number) * 16;
      break;
    case "ex":
      number = parseFloat(number) * 8.296875;
      break;
    case "px":
      number = parseFloat(number);
      break;
  }
  return +number;
}
function _testQuery(doubleTestTrue, doubleTestFalse, singleTest) {
  return function(query) {
    if (doubleTestTrue.test(query)) {
      return true;
    } else if (doubleTestFalse.test(query)) {
      return false;
    }
    return singleTest.test(query);
  };
}
function _testIsPrint(a, b) {
  const isPrintA = isPrint.test(a);
  const isPrintOnlyA = isPrintOnly.test(a);
  const isPrintB = isPrint.test(b);
  const isPrintOnlyB = isPrintOnly.test(b);
  if (isPrintA && isPrintB) {
    if (!isPrintOnlyA && isPrintOnlyB) {
      return 1;
    }
    if (isPrintOnlyA && !isPrintOnlyB) {
      return -1;
    }
    return a.localeCompare(b);
  }
  if (isPrintA) {
    return 1;
  }
  if (isPrintB) {
    return -1;
  }
  return null;
}
function createSort(config = {}) {
  const { unitlessMqAlwaysFirst } = config;
  return function sortCSSmq(a, b) {
    const testIsPrint = _testIsPrint(a, b);
    if (testIsPrint !== null) {
      return testIsPrint;
    }
    const minA = isMinWidth(a) || isMinHeight(a);
    const maxA = isMaxWidth(a) || isMaxHeight(a);
    const minB = isMinWidth(b) || isMinHeight(b);
    const maxB = isMaxWidth(b) || isMaxHeight(b);
    if (unitlessMqAlwaysFirst && (!minA && !maxA || !minB && !maxB)) {
      if (!minA && !maxA && !minB && !maxB) {
        return a.localeCompare(b);
      }
      return !minB && !maxB ? 1 : -1;
    } else {
      if (minA && maxB) {
        return -1;
      }
      if (maxA && minB) {
        return 1;
      }
      const lengthA = getQueryLength(a);
      const lengthB = getQueryLength(b);
      if (lengthA === maxValue && lengthB === maxValue) {
        return a.localeCompare(b);
      } else if (lengthA === maxValue) {
        return 1;
      } else if (lengthB === maxValue) {
        return -1;
      }
      if (lengthA > lengthB) {
        if (maxA) {
          return -1;
        }
        return 1;
      }
      if (lengthA < lengthB) {
        if (maxA) {
          return 1;
        }
        return -1;
      }
      return a.localeCompare(b);
    }
  };
}
var sortAtRules = createSort();

// src/sort-style-rules.ts
var import_shared2 = require("@pandacss/shared");
var hasAtRule = (conditions) => conditions.some((details) => details.type === "at-rule" || details.type === "mixed");
var styleOrder = [":link", ":visited", ":focus-within", ":focus", ":focus-visible", ":hover", ":active"];
var pseudoSelectorScore = (selector) => {
  const index = styleOrder.findIndex((pseudoClass) => selector.includes(pseudoClass));
  return index + 1;
};
var compareSelectors = (a, b) => {
  const aConds = a.conditions;
  const bConds = b.conditions;
  if (aConds.length === bConds.length) {
    const selector1 = aConds[0].value;
    const selector2 = bConds[0].value;
    return pseudoSelectorScore(selector1) - pseudoSelectorScore(selector2);
  }
  return aConds.length - bConds.length;
};
var flatten = (conds) => conds.flatMap((cond) => cond.type === "mixed" ? cond.value : cond);
var compareAtRuleOrMixed = (a, b) => {
  const aConds = flatten(a.conditions);
  const bConds = flatten(b.conditions);
  let aCond, bCond;
  const max = Math.max(aConds.length, bConds.length);
  for (let i = 0; i < max; i++) {
    aCond = aConds[i];
    bCond = bConds[i];
    if (!aCond)
      return -1;
    if (!bCond)
      return 1;
    if (aCond.type === "at-rule" && bCond.type.includes("nesting")) {
      return 1;
    }
    if (aCond.type.includes("nesting") && bCond.type === "at-rule") {
      return -1;
    }
    if (aCond.type === "at-rule" && bCond.type === "at-rule") {
      const atRule1 = aCond.params ?? aCond.raw;
      const atRule2 = bCond.params ?? bCond.raw;
      if (!atRule1)
        return -1;
      if (!atRule2)
        return 1;
      const score = sortAtRules(atRule1, atRule2);
      if (score !== 0) {
        return score;
      }
      continue;
    }
    if (aCond.type.includes("nesting") && bCond.type.includes("nesting")) {
      const nextACond = aConds[i + 1];
      const nextBCond = bConds[i + 1];
      if (Boolean(nextACond) === Boolean(nextBCond)) {
        const score = pseudoSelectorScore(aCond.value) - pseudoSelectorScore(bCond.value);
        if (score !== 0) {
          return score;
        }
      }
    }
  }
  return 0;
};
var sortByPropertyPriority = (a, b) => {
  if (a.entry.prop === b.entry.prop)
    return 0;
  return (0, import_shared2.getPropertyPriority)(a.entry.prop) - (0, import_shared2.getPropertyPriority)(b.entry.prop);
};
var sortStyleRules = (styleRules) => {
  const declarations = [];
  const withSelectorsOnly = [];
  const withAtRules = [];
  for (const styleRule of styleRules) {
    if (!styleRule.conditions?.length) {
      declarations.push(styleRule);
    } else if (!hasAtRule(styleRule.conditions)) {
      withSelectorsOnly.push(styleRule);
    } else {
      withAtRules.push(styleRule);
    }
  }
  withSelectorsOnly.sort((a, b) => {
    const selectorDiff = compareSelectors(a, b);
    if (selectorDiff !== 0)
      return selectorDiff;
    return sortByPropertyPriority(a, b);
  });
  withAtRules.sort((a, b) => {
    const conditionDiff = compareAtRuleOrMixed(a, b);
    if (conditionDiff !== 0)
      return conditionDiff;
    return sortByPropertyPriority(a, b);
  });
  const sorted = declarations.sort(sortByPropertyPriority);
  sorted.push(...withSelectorsOnly, ...withAtRules);
  return sorted;
};

// src/conditions.ts
var order = ["at-rule", "self-nesting", "combinator-nesting", "parent-nesting"];
var underscoreRegex = /^_/;
var selectorRegex = /&|@/;
var Conditions = class {
  constructor(options) {
    this.options = options;
    const { breakpoints: breakpointValues = {}, conditions = {} } = options;
    const breakpoints = new Breakpoints(breakpointValues);
    this.breakpoints = breakpoints;
    const entries = Object.entries(conditions).map(([key, value]) => [`_${key}`, parseCondition(value)]);
    const containers = this.setupContainers();
    const themes = this.setupThemes();
    this.values = {
      ...Object.fromEntries(entries),
      ...breakpoints.conditions,
      ...containers,
      ...themes
    };
  }
  values;
  breakpoints;
  setupContainers = () => {
    const { containerNames = [], containerSizes = {} } = this.options;
    const containers = {};
    containerNames.unshift("");
    containerNames.forEach((name) => {
      Object.entries(containerSizes).forEach(([size, value]) => {
        const _value = (0, import_shared3.toRem)(value) ?? value;
        containers[`@${name}/${size}`] = {
          type: "at-rule",
          name: "container",
          value: _value,
          raw: `@container ${name} (min-width: ${_value})`,
          params: `${name} ${value}`
        };
      });
    });
    return containers;
  };
  setupThemes = () => {
    const { themes = {} } = this.options;
    const themeVariants = {};
    Object.entries(themes).forEach(([theme, _themeVariant]) => {
      const condName = this.getThemeName(theme);
      const cond = parseCondition("& " + this.getThemeSelector(theme));
      if (!cond)
        return;
      themeVariants[condName] = cond;
    });
    return themeVariants;
  };
  getThemeSelector = (name) => {
    return `[data-panda-theme=${name}]`;
  };
  getThemeName = (theme) => {
    return "_theme" + (0, import_shared3.capitalize)(theme);
  };
  finalize = (paths) => {
    return paths.map((path) => {
      if (this.has(path)) {
        return path.replace(underscoreRegex, "");
      }
      if (selectorRegex.test(path)) {
        return `[${(0, import_shared3.withoutSpace)(path.trim())}]`;
      }
      return path;
    });
  };
  shift = (paths) => {
    return paths.map((path) => path.trim()).sort((a, b) => {
      const aIsCondition = this.isCondition(a);
      const bIsCondition = this.isCondition(b);
      if (aIsCondition && !bIsCondition)
        return 1;
      if (!aIsCondition && bIsCondition)
        return -1;
      if (!aIsCondition && !bIsCondition)
        return -1;
      return 0;
    });
  };
  segment = (paths) => {
    const condition = [];
    const selector = [];
    for (const path of paths) {
      if (this.isCondition(path)) {
        condition.push(path);
      } else {
        selector.push(path);
      }
    }
    return { condition, selector };
  };
  has = (key) => {
    return Object.prototype.hasOwnProperty.call(this.values, key);
  };
  isCondition = (key) => {
    return this.has(key) || !!this.getRaw(key) || (0, import_shared3.isBaseCondition)(key);
  };
  isEmpty = () => {
    return Object.keys(this.values).length === 0;
  };
  get = (key) => {
    const details = this.values[key];
    return details?.raw;
  };
  getRaw = (condNameOrQuery) => {
    if (typeof condNameOrQuery === "string" && this.values[condNameOrQuery])
      return this.values[condNameOrQuery];
    try {
      return parseCondition(condNameOrQuery);
    } catch (error) {
      import_logger2.logger.error("core:condition", error);
    }
  };
  sort = (conditions) => {
    const rawConditions = conditions.map(this.getRaw).filter(Boolean);
    return rawConditions.sort((a, b) => order.indexOf(a.type) - order.indexOf(b.type));
  };
  normalize = (condition) => {
    return (0, import_shared3.isObject)(condition) ? condition : this.getRaw(condition);
  };
  keys = () => {
    return Object.keys(this.values);
  };
  saveOne = (key, value) => {
    const parsed = parseCondition(value);
    if (!parsed)
      return;
    this.values[`_${key}`] = parsed;
  };
  remove(key) {
    delete this.values[`_${key}`];
  }
  getSortedKeys = () => {
    return Object.keys(this.values).sort((a, b) => {
      const aCondition = this.values[a];
      const bCondition = this.values[b];
      const score = compareAtRuleOrMixed(
        { entry: {}, conditions: [aCondition] },
        { entry: {}, conditions: [bCondition] }
      );
      return score;
    });
  };
};

// src/context.ts
var import_is_valid_prop = require("@pandacss/is-valid-prop");
var import_logger5 = require("@pandacss/logger");
var import_shared19 = require("@pandacss/shared");
var import_token_dictionary = require("@pandacss/token-dictionary");

// src/file.ts
var import_shared4 = require("@pandacss/shared");
var FileEngine = class {
  constructor(context) {
    this.context = context;
  }
  get forceConsistentTypeExtension() {
    return this.context.config.forceConsistentTypeExtension || false;
  }
  get outExtension() {
    return this.context.config.outExtension;
  }
  ext(file) {
    return `${file}.${this.outExtension}`;
  }
  extDts(file) {
    const dts = this.outExtension === "mjs" && this.forceConsistentTypeExtension ? "d.mts" : "d.ts";
    return `${file}.${dts}`;
  }
  __extDts(file) {
    return this.forceConsistentTypeExtension ? this.extDts(file) : file;
  }
  import(mod, file) {
    return `import { ${mod} } from '${this.ext(file)}';`;
  }
  importType(mod, file) {
    return `import type { ${mod} } from '${this.__extDts(file)}';`;
  }
  exportType(mod, file) {
    return `export type { ${mod} } from '${this.__extDts(file)}';`;
  }
  exportStar(file) {
    return `export * from '${this.ext(file)}';`;
  }
  exportTypeStar(file) {
    return `export * from '${this.__extDts(file)}';`;
  }
  isTypeFile(file) {
    return file.endsWith(".d.ts") || file.endsWith(".d.mts");
  }
  jsDocComment(comment, options) {
    const { deprecated, default: defaultValue } = options ?? {};
    if (!comment && !deprecated && !defaultValue)
      return "";
    const comments = ["/**"];
    if (comment) {
      comments.push(` * ${comment}`, "\n");
    }
    if (deprecated) {
      const suffix = (0, import_shared4.isString)(deprecated) ? ` ${deprecated}` : "";
      comments.push(` * @deprecated${suffix}`);
    }
    if (defaultValue) {
      comments.push(` * @default ${defaultValue}`);
    }
    comments.push(" */");
    return comments.join("\n");
  }
};

// src/global-vars.ts
var import_outdent2 = require("outdent");

// src/stringify.ts
var import_shared6 = require("@pandacss/shared");

// src/unitless.ts
var import_shared5 = require("@pandacss/shared");
var keys = {
  animationIterationCount: true,
  aspectRatio: true,
  borderImageOutset: true,
  borderImageSlice: true,
  borderImageWidth: true,
  boxFlex: true,
  boxFlexGroup: true,
  boxOrdinalGroup: true,
  columnCount: true,
  columns: true,
  flex: true,
  flexGrow: true,
  flexPositive: true,
  flexShrink: true,
  flexNegative: true,
  flexOrder: true,
  gridRow: true,
  gridRowEnd: true,
  gridRowSpan: true,
  gridRowStart: true,
  gridColumn: true,
  gridColumnEnd: true,
  gridColumnSpan: true,
  gridColumnStart: true,
  msGridRow: true,
  msGridRowSpan: true,
  msGridColumn: true,
  msGridColumnSpan: true,
  fontWeight: true,
  lineClamp: true,
  lineHeight: true,
  opacity: true,
  order: true,
  orphans: true,
  scale: true,
  tabSize: true,
  widows: true,
  zIndex: true,
  zoom: true,
  WebkitLineClamp: true,
  // SVG-related properties
  fillOpacity: true,
  floodOpacity: true,
  stopOpacity: true,
  strokeDasharray: true,
  strokeDashoffset: true,
  strokeMiterlimit: true,
  strokeOpacity: true,
  strokeWidth: true
};
var unitlessProperties = /* @__PURE__ */ new Set();
Object.keys(keys).forEach((key) => {
  unitlessProperties.add(key);
  unitlessProperties.add((0, import_shared5.hypenateProperty)(key));
});

// src/stringify.ts
var toString = Object.prototype.toString;
function stringify(value) {
  const used = /* @__PURE__ */ new WeakSet();
  const write = (cssText, selectors, conditions, name, data, isAtRuleLike, isVariableLike) => {
    if (data === false)
      return "";
    for (let i = 0; i < conditions.length; ++i) {
      if (!used.has(conditions[i])) {
        used.add(conditions[i]);
        cssText += `${conditions[i]} {`;
      }
    }
    if (selectors.length && !used.has(selectors)) {
      used.add(selectors);
      cssText += `${selectors.map((s) => s.replace("& ", ""))} {`;
    }
    let value2 = data;
    if (typeof value2 === "number") {
      const shouldAddPx = !(value2 === 0 || unitlessProperties.has(name) || isVariableLike);
      if (shouldAddPx) {
        value2 = `${value2}px`;
      }
    }
    if (isAtRuleLike) {
      name = `${name} `;
    } else if (isVariableLike) {
      name = `${name}: `;
    } else {
      name = `${(0, import_shared6.hypenateProperty)(name)}: `;
    }
    cssText += `${name + String(value2)};
`;
    return cssText;
  };
  const parse = (style, selectors, conditions) => {
    let cssText = "";
    for (const name in style) {
      const isAtRuleLike = name[0] === "@";
      const isVariableLike = !isAtRuleLike && name.startsWith("--");
      const rules = isAtRuleLike && Array.isArray(style[name]) ? style[name] : [style[name]];
      for (const data of rules) {
        const isObjectLike = typeof data === "object" && data && data.toString === toString;
        if (!isObjectLike) {
          cssText = write(cssText, selectors, conditions, name, data, isAtRuleLike, isVariableLike);
          continue;
        }
        if (used.has(selectors)) {
          used.delete(selectors);
          cssText += "}";
        }
        const usedName = Object(name);
        let nextSelectors;
        if (isAtRuleLike) {
          nextSelectors = selectors;
          cssText += parse(data, nextSelectors, conditions.concat(usedName));
        } else {
          const nestedSelectors = parseSelectors(name);
          nextSelectors = selectors.length ? getResolvedSelectors(selectors, nestedSelectors) : nestedSelectors;
          cssText += parse(data, nextSelectors, conditions);
        }
        if (used.has(usedName)) {
          used.delete(usedName);
          cssText += "}\n";
        }
        if (used.has(nextSelectors)) {
          used.delete(nextSelectors);
          cssText += "}\n";
        }
      }
    }
    return cssText;
  };
  return parse(value, [], []);
}
function parseSelectors(selector) {
  const result = [];
  let parenCount = 0;
  let currentSelector = "";
  let inEscape = false;
  for (let i = 0; i < selector.length; i++) {
    const char = selector[i];
    if (char === "\\" && !inEscape) {
      inEscape = true;
      currentSelector += char;
      continue;
    }
    if (inEscape) {
      inEscape = false;
      currentSelector += char;
      continue;
    }
    if (char === "(") {
      parenCount++;
    } else if (char === ")") {
      parenCount--;
    }
    if (char === "," && parenCount === 0) {
      result.push(currentSelector.trim());
      currentSelector = "";
    } else {
      currentSelector += char;
    }
  }
  if (currentSelector) {
    result.push(currentSelector.trim());
  }
  return result;
}
var parentSelectorRegex = /&/g;
var descendantSelectorRegex = /[ +>|~]/g;
var surroundedRegex = /&.*&/g;
var getResolvedSelectors = (parentSelectors, nestedSelectors) => {
  const resolved = [];
  parentSelectors.forEach((parentSelector) => {
    resolved.push(
      ...nestedSelectors.map((selector) => {
        if (!selector.includes("&"))
          return parentSelector + " " + selector;
        return selector.replace(
          parentSelectorRegex,
          descendantSelectorRegex.test(parentSelector) && surroundedRegex.test(selector) ? `:is(${parentSelector})` : parentSelector
        );
      })
    );
  });
  return resolved;
};

// src/global-vars.ts
var GlobalVars = class {
  constructor(options) {
    this.options = options;
    const { globalVars = {} } = options;
    this.keys = new Set(Object.keys(globalVars));
    const arr = Array.from(this.keys);
    this.names = arr.map((v) => `${v.slice(2)}`);
    this.vars = arr.map((v) => `var(${v})`);
  }
  keys;
  vars;
  names;
  isEmpty() {
    return this.keys.size === 0;
  }
  toString() {
    const { globalVars = {}, cssVarRoot } = this.options;
    return stringifyGlobalVars(globalVars, cssVarRoot);
  }
};
var stringifyGlobalVars = (globalVars, cssVarRoot) => {
  if (!globalVars)
    return "";
  const decls = [];
  const vars = { [cssVarRoot]: {} };
  const base = vars[cssVarRoot];
  Object.entries(globalVars).forEach(([key, value]) => {
    if (typeof value === "string") {
      base[key] = value;
      return;
    }
    const css = stringifyProperty(key, value);
    decls.push(css);
  });
  const lines = [];
  lines.push(stringify(vars));
  lines.push(...decls);
  return lines.join("\n\n");
};
function stringifyProperty(key, config) {
  return import_outdent2.outdent`@property ${key} {
    syntax: '${config.syntax}';
    inherits: ${config.inherits};
    ${config.initialValue == null ? "" : `initial-value: ${config.initialValue};`}
  }`;
}

// src/rule-processor.ts
var import_shared7 = require("@pandacss/shared");
var RuleProcessor = class {
  constructor(context) {
    this.context = context;
    this.encoder = context.encoder;
    this.decoder = context.decoder;
    this.sheet = context.createSheet();
  }
  encoder;
  decoder;
  sheet;
  getParamsOrThrow() {
    const isReady = Boolean(this.encoder && this.decoder && this.sheet);
    if (!isReady) {
      throw new import_shared7.PandaError("MISSING_PARAMS", "RuleProcessor is missing params, please call `clone` first");
    }
    return {
      encoder: this.encoder,
      decoder: this.decoder,
      sheet: this.sheet
    };
  }
  clone() {
    this.encoder = this.context.encoder.clone();
    this.decoder = this.context.decoder.clone();
    this.sheet = this.context.createSheet();
    return this;
  }
  toCss(options) {
    const { decoder: decoder2, sheet } = this.getParamsOrThrow();
    sheet.processDecoder(decoder2);
    return sheet.toCss({ optimize: true, ...options });
  }
  css(styles) {
    const { encoder, decoder: decoder2 } = this.getParamsOrThrow();
    encoder.processAtomic(styles);
    decoder2.collect(encoder);
    return {
      styles,
      getClassNames: () => Array.from(decoder2.classNames.keys()),
      toCss: this.toCss.bind(this)
    };
  }
  cva(recipeConfig) {
    const { encoder, decoder: decoder2 } = this.getParamsOrThrow();
    encoder.processAtomicRecipe(recipeConfig);
    decoder2.collect(encoder);
    return {
      config: recipeConfig,
      getClassNames: () => Array.from(decoder2.classNames.keys()),
      toCss: this.toCss.bind(this)
    };
  }
  sva(recipeConfig) {
    const { encoder, decoder: decoder2 } = this;
    this.getParamsOrThrow();
    encoder.processAtomicSlotRecipe(recipeConfig);
    decoder2.collect(encoder);
    return {
      config: recipeConfig,
      getClassNames: () => Array.from(decoder2.classNames.keys()),
      toCss: this.toCss.bind(this)
    };
  }
  recipe(name, variants = {}) {
    const { encoder, decoder: decoder2 } = this;
    this.getParamsOrThrow();
    encoder.processRecipe(name, variants);
    decoder2.collect(encoder);
    return {
      variants,
      getClassNames: () => Array.from(decoder2.classNames.keys()),
      toCss: this.toCss.bind(this)
    };
  }
};

// src/hooks-api.ts
var HooksApi = class {
  constructor(ctx) {
    this.ctx = ctx;
    this.processor = new RuleProcessor(ctx);
  }
  processor;
  get config() {
    return this.ctx.conf.config;
  }
  get configPath() {
    return this.ctx.conf.path;
  }
  get configDependencies() {
    return this.ctx.conf.dependencies;
  }
  get classNames() {
    return this.ctx.utility.classNames;
  }
  get generatedClassNames() {
    return this.ctx.decoder.classNames;
  }
};

// src/import-map.ts
var import_shared9 = require("@pandacss/shared");

// src/file-matcher.ts
var import_shared8 = require("@pandacss/shared");
var cssEntrypointFns = /* @__PURE__ */ new Set(["css", "cva", "sva"]);
var FileMatcher = class {
  constructor(context, opts) {
    this.context = context;
    const { value, importMap } = opts;
    this.importMap = importMap;
    this.imports = value;
    this.imports.forEach((result) => {
      if (result.kind === "namespace") {
        this.namespaces.set(result.name, result);
      }
    });
    this.assignAliases();
    this.assignProperties();
  }
  imports;
  namespaces = /* @__PURE__ */ new Map();
  importMap;
  cssAliases = /* @__PURE__ */ new Set();
  cvaAliases = /* @__PURE__ */ new Set();
  svaAliases = /* @__PURE__ */ new Set();
  jsxFactoryAliases = /* @__PURE__ */ new Set();
  recipeAliases = /* @__PURE__ */ new Set();
  patternAliases = /* @__PURE__ */ new Set();
  propertiesMap = /* @__PURE__ */ new Map();
  functions = /* @__PURE__ */ new Map();
  components = /* @__PURE__ */ new Map();
  assignAliases() {
    const isCssEntrypoint = this.createMatch(this.importMap.css, Array.from(cssEntrypointFns));
    this.imports.forEach((result) => {
      if (this.isValidRecipe(result.alias)) {
        this.recipeAliases.add(result.alias);
      }
      if (this.isValidPattern(result.alias)) {
        this.patternAliases.add(result.alias);
      }
      if (isCssEntrypoint(result.alias)) {
        if (result.name === "css") {
          this.cssAliases.add(result.alias);
        }
        if (result.name === "cva") {
          this.cvaAliases.add(result.alias);
        }
        if (result.name === "sva") {
          this.svaAliases.add(result.alias);
        }
      }
      if (result.name === this.context.jsx.factoryName) {
        this.jsxFactoryAliases.add(result.alias);
      }
      if (result.kind === "namespace") {
        if (this.importMap.pattern.some((m) => result.mod.includes(m))) {
          this.context.patterns.keys.forEach((pattern) => {
            this.patternAliases.add(pattern);
          });
        }
        if (this.importMap.recipe.some((m) => result.mod.includes(m))) {
          this.context.recipes.keys.forEach((recipe) => {
            this.recipeAliases.add(recipe);
          });
        }
      }
    });
  }
  assignProperties() {
    this.context.jsx.nodes.forEach((node) => {
      const aliases = this.getAliases(node.jsxName);
      aliases.forEach((alias) => {
        node.props?.forEach((prop) => this.propertiesMap.set(prop, true));
        this.functions.set(node.baseName, this.propertiesMap);
        this.functions.set(alias, this.propertiesMap);
        this.components.set(alias, this.propertiesMap);
      });
    });
  }
  isEmpty = () => {
    return this.imports.length === 0;
  };
  toString = () => {
    return this.imports.map((item) => item.alias).join(", ");
  };
  find = (id) => {
    return this.imports.find((o) => o.alias === id);
  };
  createMatch = (mods, keys2) => {
    const matchingImports = this.imports.filter((o) => {
      const isFromMod = mods.some((m) => o.mod.includes(m) || o.importMapValue?.includes(m));
      const isOneOfKeys = o.kind === "namespace" ? true : keys2.includes(o.name);
      return isFromMod && isOneOfKeys;
    });
    return (0, import_shared8.memo)((id) => {
      return !!matchingImports.find((mod) => {
        if (mod.kind === "namespace") {
          return keys2.includes(id.replace(`${mod.alias}.`, ""));
        }
        return mod.alias === id || mod.name === id;
      });
    });
  };
  match = (id) => {
    return !!this.find(id);
  };
  getName = (id) => {
    return this.find(id)?.name || id;
  };
  getAliases = (id) => {
    return this.imports.filter((o) => o.name === id).map((o) => o.alias || id);
  };
  _patternsMatcher;
  isValidPattern = (id) => {
    this._patternsMatcher ||= this.createMatch(this.importMap.pattern, this.context.patterns.keys);
    return this._patternsMatcher(id);
  };
  _recipesMatcher;
  isValidRecipe = (id) => {
    this._recipesMatcher ||= this.createMatch(this.importMap.recipe, this.context.recipes.keys);
    return this._recipesMatcher(id);
  };
  isRawFn = (fnName) => {
    const name = fnName.split(".raw")[0] ?? "";
    return name === "css" || this.isValidPattern(name) || this.isValidRecipe(name);
  };
  isNamespaced = (fnName) => {
    return this.namespaces.has(fnName.split(".")[0]);
  };
  normalizeFnName = (fnName) => {
    let name = fnName;
    if (this.isNamespaced(fnName)) {
      name = name.split(".").slice(1).join(".");
    }
    if (this.isRawFn(name))
      return name.replace(".raw", "");
    return name;
  };
  isAliasFnName = (0, import_shared8.memo)((fnName) => {
    return this.cvaAliases.has(fnName) || this.cssAliases.has(fnName) || this.svaAliases.has(fnName) || this.isJsxFactory(fnName);
  });
  matchFn = (0, import_shared8.memo)((fnName) => {
    if (this.recipeAliases.has(fnName) || this.patternAliases.has(fnName))
      return true;
    if (this.isAliasFnName(fnName) || this.isRawFn(fnName))
      return true;
    if (this.functions.has(fnName))
      return true;
    const [namespace, identifier] = fnName.split(".");
    const ns = this.namespaces.get(namespace);
    if (ns) {
      if (this.importMap.css.some((m) => ns.mod.includes(m)) && cssEntrypointFns.has(identifier))
        return true;
      if (this.importMap.recipe.some((m) => ns.mod.includes(m)) && this.recipeAliases.has(identifier))
        return true;
      if (this.importMap.pattern.some((m) => ns.mod.includes(m)) && this.patternAliases.has(identifier))
        return true;
      return this.functions.has(identifier);
    }
    return false;
  });
  isJsxFactory = (0, import_shared8.memo)((tagName) => {
    const { jsx } = this.context;
    if (!jsx.isEnabled)
      return false;
    for (const alias of this.jsxFactoryAliases) {
      if (tagName.startsWith(alias))
        return true;
    }
    const [namespace, identifier] = tagName.split(".");
    const ns = this.namespaces.get(namespace);
    if (ns && this.importMap.jsx.some((m) => ns.mod.includes(m)) && identifier === this.context.jsx.factoryName) {
      return true;
    }
  });
  isPandaComponent = (0, import_shared8.memo)((tagName) => {
    if (!tagName)
      return false;
    const { jsx } = this.context;
    return this.components.has(tagName) || this.isJsxFactory(tagName) || jsx.isJsxTagRecipe(tagName) || jsx.isJsxTagPattern(tagName);
  });
  matchTag = (0, import_shared8.memo)((tagName) => {
    return this.isPandaComponent(tagName) || isUpperCase(tagName);
  });
  matchTagProp = (0, import_shared8.memo)((tagName, propName) => {
    const { jsx, isValidProperty } = this.context;
    switch (jsx.styleProps) {
      case "all":
        return Boolean(this.components.get(tagName)?.has(propName)) || isValidProperty(propName) || this.propertiesMap.has(propName) || jsx.isRecipeOrPatternProp(tagName, propName);
      case "minimal":
        return propName === "css" || jsx.isRecipeOrPatternProp(tagName, propName);
      case "none":
        return jsx.isRecipeOrPatternProp(tagName, propName);
      default:
        return false;
    }
  });
};
var isUpperCase = (value) => value[0] === value[0]?.toUpperCase();

// src/import-map.ts
var ImportMap = class {
  constructor(context) {
    this.context = context;
    const { jsx } = this.context;
    this.outdir = this.getOutdir();
    const importMap = this.buildImportMap(context.config.importMap);
    this.matchers.css = this.createMatcher(importMap.css, ["css", "cva", "sva"]);
    this.matchers.recipe = this.createMatcher(importMap.recipe);
    this.matchers.pattern = this.createMatcher(importMap.pattern);
    if (jsx.isEnabled) {
      this.matchers.jsx = this.createMatcher(importMap.jsx, jsx.names);
    }
    this.value = importMap;
  }
  value;
  matchers = {};
  outdir;
  /**
   * Normalize one/many import map inputs to a single import map output with absolute paths.
   * @example
   * ```ts
   * importMap: '@acme/org'
   * ```
   *
   * will be normalized to
   * ```ts
   * {
   *   css: ['@acme/org/css'],
   *   recipe: ['@acme/org/recipes'],
   *   pattern: ['@acme/org/patterns'],
   *   jsx: ['@acme/org/jsx'],
   * }
   * ```
   *
   * @exammple
   * importMap: ['@acme/org', '@foo/org', '@bar/org']
   * ```
   *
   * will be normalized to
   * ```ts
   * {
   *   css: ['@acme/org/css', '@foo/org/css', '@bar/org/css'],
   *   recipe: ['@acme/org/recipes', '@foo/org/recipes', '@bar/org/recipes'],
   *   pattern: ['@acme/org/patterns', '@foo/org/patterns', '@bar/org/patterns'],
   *   jsx: ['@acme/org/jsx', '@foo/org/jsx', '@bar/org/jsx'],
   * }
   * ```
   */
  buildImportMap = (option) => {
    const output = { css: [], recipe: [], pattern: [], jsx: [] };
    const inputs = asArray(option);
    inputs.forEach((input) => {
      const normalized = this.normalize(input);
      output.css.push(...normalized.css);
      output.recipe.push(...normalized.recipe);
      output.pattern.push(...normalized.pattern);
      if (normalized.jsx)
        output.jsx.push(...normalized.jsx);
    });
    return output;
  };
  fromString = (map) => {
    return {
      css: [[map, "css"].join("/")],
      recipe: [[map, "recipes"].join("/")],
      pattern: [[map, "patterns"].join("/")],
      jsx: [[map, "jsx"].join("/")]
    };
  };
  fromInput = (map) => {
    const { css, recipes, patterns, jsx } = map ?? {};
    return {
      css: css ? asArray(css) : [[this.outdir, "css"].join("/")],
      recipe: recipes ? asArray(recipes) : [[this.outdir, "recipes"].join("/")],
      pattern: patterns ? asArray(patterns) : [[this.outdir, "patterns"].join("/")],
      jsx: jsx ? asArray(jsx) : [[this.outdir, "jsx"].join("/")]
    };
  };
  getOutdir = () => {
    const { outdir } = this.context.config;
    const split = outdir.split("/");
    return split[split.length - 1];
  };
  normalize = (map) => {
    if ((0, import_shared9.isString)(map))
      return this.fromString(map);
    return this.fromInput(map);
  };
  createMatcher = (mods, values) => {
    const regex = values ? new RegExp(`^(${values.join("|")})$`) : /.*/;
    const match3 = (value) => regex.test(value);
    return { mods, regex, match: match3 };
  };
  match = (result, resolveTsPath) => {
    if (!result)
      return false;
    for (const { regex, mods } of Object.values(this.matchers)) {
      if (result.kind !== "namespace" && !regex.test(result.name))
        continue;
      if (mods.some((m) => result.mod.includes(m))) {
        return true;
      }
      const resolvedMod = resolveTsPath?.(result.mod);
      for (const mod of mods) {
        const absMod = [this.context.config.cwd, mod].join("/");
        if (resolvedMod?.includes(absMod) || resolvedMod === mod) {
          result.importMapValue = resolvedMod;
          return true;
        }
      }
    }
    return false;
  };
  file = (results) => {
    return new FileMatcher(this.context, { importMap: this.value, value: results });
  };
};
var asArray = (value) => Array.isArray(value) ? value : [value];

// src/jsx.ts
var import_shared10 = require("@pandacss/shared");
var JsxEngine = class {
  constructor(context) {
    this.context = context;
    this.nodes = [...context.patterns.details, ...context.recipes.details];
    this.names = [this.factoryName, ...this.nodes.map((node) => node.jsxName)];
    this.assignRecipeMatcher();
    this.assignPatternMatcher();
  }
  nodes = [];
  names = [];
  recipeMatcher = { string: /* @__PURE__ */ new Set(), regex: [] };
  recipePropertiesByJsxName = /* @__PURE__ */ new Map();
  patternMatcher = { string: /* @__PURE__ */ new Set(), regex: [] };
  patternPropertiesByJsxName = /* @__PURE__ */ new Map();
  assignRecipeMatcher() {
    if (!this.isEnabled)
      return;
    for (const recipe of this.context.recipes.details) {
      this.recipePropertiesByJsxName.set(recipe.jsxName, new Set(recipe.props ?? []));
      recipe.jsx.forEach((jsx) => {
        if (typeof jsx === "string") {
          this.recipeMatcher.string.add(jsx);
        } else {
          this.recipeMatcher.regex.push(jsx);
        }
      });
    }
  }
  assignPatternMatcher() {
    if (!this.isEnabled)
      return;
    for (const pattern of this.context.patterns.details) {
      this.patternPropertiesByJsxName.set(pattern.jsxName, new Set(pattern.props ?? []));
      pattern.jsx.forEach((jsx) => {
        if (typeof jsx === "string") {
          this.patternMatcher.string.add(jsx);
        } else {
          this.patternMatcher.regex.push(jsx);
        }
      });
    }
  }
  get jsxFactory() {
    return this.context.config.jsxFactory ?? "styled";
  }
  get styleProps() {
    return this.context.config.jsxStyleProps ?? "all";
  }
  get framework() {
    return this.context.config.jsxFramework;
  }
  get isEnabled() {
    return this.framework != null;
  }
  get factoryName() {
    return this.jsxFactory;
  }
  get upperName() {
    return (0, import_shared10.capitalize)(this.jsxFactory);
  }
  get typeName() {
    return `HTML${(0, import_shared10.capitalize)(this.jsxFactory)}Props`;
  }
  get variantName() {
    return `${(0, import_shared10.capitalize)(this.jsxFactory)}VariantProps`;
  }
  get componentName() {
    return `${(0, import_shared10.capitalize)(this.jsxFactory)}Component`;
  }
  isJsxFactory = (name) => {
    const isFactory = name === this.factoryName;
    if (isFactory)
      return true;
    const [_namespace, identifier] = name.split(".");
    return identifier === this.factoryName;
  };
  isJsxTagRecipe = (0, import_shared10.memo)((tagName) => {
    return this.recipeMatcher.string.has(tagName) || this.recipeMatcher.regex.some((regex) => regex.test(tagName));
  });
  isJsxTagPattern = (0, import_shared10.memo)((tagName) => {
    return this.patternMatcher.string.has(tagName) || this.patternMatcher.regex.some((regex) => regex.test(tagName));
  });
  isRecipeOrPatternProp = (0, import_shared10.memo)((tagName, propName) => {
    if (this.isJsxTagRecipe(tagName)) {
      const recipeList = this.context.recipes.filter(tagName);
      return recipeList.some((recipe) => this.recipePropertiesByJsxName.get(recipe.jsxName)?.has(propName));
    }
    if (this.isJsxTagPattern(tagName)) {
      const patternList = this.context.patterns.filter(tagName);
      return patternList.some((pattern) => this.patternPropertiesByJsxName.get(pattern.jsxName)?.has(propName));
    }
    return false;
  });
};

// src/layers.ts
var import_shared11 = require("@pandacss/shared");
var import_postcss3 = __toESM(require("postcss"));
var Layers = class {
  constructor(names) {
    this.names = names;
    this.root = import_postcss3.default.root();
    this.reset = import_postcss3.default.atRule({ name: "layer", params: names.reset, nodes: [] });
    this.base = import_postcss3.default.atRule({ name: "layer", params: names.base, nodes: [] });
    this.tokens = import_postcss3.default.atRule({ name: "layer", params: names.tokens, nodes: [] });
    this.recipes = import_postcss3.default.atRule({ name: "layer", params: names.recipes, nodes: [] });
    this.recipes_base = import_postcss3.default.atRule({ name: "layer", params: "_base", nodes: [] });
    this.recipes_slots = import_postcss3.default.atRule({ name: "layer", params: names.recipes + ".slots", nodes: [] });
    this.recipes_slots_base = import_postcss3.default.atRule({ name: "layer", params: "_base", nodes: [] });
    this.utilities = import_postcss3.default.atRule({ name: "layer", params: names.utilities, nodes: [] });
    this.compositions = import_postcss3.default.atRule({ name: "layer", params: "compositions", nodes: [] });
  }
  root;
  reset;
  base;
  tokens;
  recipes;
  recipes_base;
  recipes_slots;
  recipes_slots_base;
  utilities;
  compositions;
  utilityRuleMap = /* @__PURE__ */ new Map();
  getLayerRoot(layer) {
    const { reset, base, tokens, recipes, recipes_base, recipes_slots, recipes_slots_base, utilities, compositions } = this;
    switch (layer) {
      case "base":
        return base;
      case "reset":
        return reset;
      case "tokens": {
        return tokens;
      }
      case "recipes": {
        const recipeRoot = import_postcss3.default.root();
        if (recipes_base.nodes?.length)
          recipes.prepend(recipes_base);
        if (recipes_slots_base.nodes?.length)
          recipes_slots.prepend(recipes_slots_base);
        if (recipes.nodes?.length)
          recipeRoot.append(recipes);
        if (recipes_slots.nodes?.length)
          recipeRoot.append(recipes_slots);
        return recipeRoot;
      }
      case "utilities": {
        if (compositions.nodes?.length)
          utilities.prepend(compositions);
        this.utilityRuleMap.forEach((rules) => {
          if (rules.nodes?.length)
            utilities.append(rules);
        });
        return utilities;
      }
      default:
        throw new import_shared11.PandaError("INVALID_LAYER", `Unknown layer: ${layer}`);
    }
  }
  insert() {
    const { root } = this;
    const reset = this.getLayerRoot("reset");
    if (reset.nodes?.length)
      root.append(reset);
    const base = this.getLayerRoot("base");
    if (base.nodes?.length)
      root.append(base);
    const tokens = this.getLayerRoot("tokens");
    if (tokens.nodes?.length)
      root.append(tokens);
    const recipes = this.getLayerRoot("recipes");
    if (recipes.nodes?.length)
      root.append(recipes);
    const utilities = this.getLayerRoot("utilities");
    if (utilities.nodes?.length)
      root.append(utilities);
    return root;
  }
  get layerNames() {
    return Object.values(this.names);
  }
  get params() {
    return `@layer ${this.layerNames.join(", ")};`;
  }
};

// src/path.ts
var PathEngine = class {
  constructor(context) {
    this.context = context;
  }
  get cwd() {
    return this.context.config.cwd;
  }
  get emitPackage() {
    return this.context.config.emitPackage || false;
  }
  get outdir() {
    return this.context.config.outdir;
  }
  getFilePath(file) {
    return [this.cwd, this.emitPackage ? "node_modules" : void 0, this.outdir, file].filter(Boolean);
  }
  get root() {
    return this.getFilePath();
  }
  get css() {
    return this.getFilePath("css");
  }
  get token() {
    return this.getFilePath("tokens");
  }
  get types() {
    return this.getFilePath("types");
  }
  get recipe() {
    return this.getFilePath("recipes");
  }
  get pattern() {
    return this.getFilePath("patterns");
  }
  get outCss() {
    return this.getFilePath("styles.css");
  }
  get jsx() {
    return this.getFilePath("jsx");
  }
  get themes() {
    return this.getFilePath("themes");
  }
};

// src/patterns.ts
var import_shared12 = require("@pandacss/shared");
var Patterns = class {
  constructor(options) {
    this.options = options;
    this.patterns = options.config.patterns ?? {};
    this.details = Object.entries(this.patterns).map(([name, pattern]) => this.createDetail(name, pattern));
    this.keys = Object.keys(this.patterns);
    this.utility = options.utility;
    this.tokens = options.tokens;
  }
  patterns;
  details;
  keys;
  utility;
  tokens;
  deprecated = /* @__PURE__ */ new Set();
  createDetail(name, pattern) {
    const names = this.getNames(name);
    const jsx = (pattern.jsx ?? []).concat([names.jsxName]);
    if (pattern.deprecated) {
      this.deprecated.add(name);
    }
    return {
      ...names,
      props: Object.keys(pattern?.properties ?? {}),
      blocklistType: pattern?.blocklist ? `| '${pattern.blocklist.join("' | '")}'` : "",
      config: pattern,
      type: "pattern",
      match: (0, import_shared12.createRegex)(jsx),
      jsx
    };
  }
  getConfig(name) {
    return this.patterns[name];
  }
  transform(name, styles) {
    const pattern = this.patterns[name];
    const _styles = (0, import_shared12.getPatternStyles)(pattern, styles);
    return pattern?.transform?.(_styles, this.options.helpers) ?? {};
  }
  getNames(name) {
    const upperName = (0, import_shared12.capitalize)(name);
    return {
      upperName,
      baseName: name,
      dashName: (0, import_shared12.dashCase)(name),
      styleFnName: `get${upperName}Style`,
      jsxName: this.patterns[name]?.jsxName ?? upperName
    };
  }
  find = (0, import_shared12.memo)((jsxName) => {
    return this.details.find((node) => node.match.test(jsxName))?.baseName ?? (0, import_shared12.uncapitalize)(jsxName);
  });
  filter = (0, import_shared12.memo)((jsxName) => {
    return this.details.filter((node) => node.match.test(jsxName));
  });
  isEmpty() {
    return this.keys.length === 0;
  }
  isDeprecated(name) {
    return this.deprecated.has(name);
  }
  saveOne(name, pattern) {
    this.patterns[name] = pattern;
    const detailIndex = this.details.findIndex((detail) => detail.baseName === name);
    const updated = this.createDetail(name, pattern);
    if (detailIndex > -1) {
      this.details[detailIndex] = updated;
    } else {
      this.details.push(updated);
    }
  }
  remove(name) {
    delete this.patterns[name];
    const detailIndex = this.details.findIndex((detail) => detail.baseName === name);
    if (detailIndex > -1) {
      this.details.splice(detailIndex, 1);
    }
  }
  filterDetails(filters) {
    const patternDiffs = filters?.affecteds?.patterns;
    return patternDiffs ? this.details.filter((pattern) => patternDiffs.includes(pattern.dashName)) : this.details;
  }
  getPropertyValues = (patternName, property) => {
    const patternConfig = this.getConfig(patternName);
    if (!patternConfig)
      return [];
    const propType = patternConfig.properties?.[property];
    if (!propType)
      return;
    if (propType.type === "enum") {
      return propType.value;
    }
    if (propType.type === "boolean") {
      return ["true", "false"];
    }
    if (propType.type === "property") {
      return this.utility.getPropertyKeys(propType.value || property);
    }
    if (propType.type === "token") {
      const values = this.tokens.view.getCategoryValues(propType.value);
      return Object.keys(values ?? {});
    }
  };
  static isValidNode = (node) => {
    return (0, import_shared12.isObject)(node) && "type" in node && node.type === "recipe";
  };
};

// src/recipes.ts
var import_shared14 = require("@pandacss/shared");
var import_lodash2 = __toESM(require("lodash.merge"));

// src/serialize.ts
var import_shared13 = require("@pandacss/shared");
var import_lodash = __toESM(require("lodash.merge"));
function transformStyles(context, styleObj, key) {
  const encoder = context.encoder.clone();
  const decoder2 = context.decoder.clone();
  const hashSet = /* @__PURE__ */ new Set();
  encoder.hashStyleObject(hashSet, styleObj);
  const group = decoder2.getGroup(hashSet, key);
  return group.result;
}
function serializeStyles(context, groupedObject) {
  const result = {};
  for (const [scope, styles] of Object.entries(groupedObject)) {
    result[scope] ||= {};
    const styleObject = (0, import_shared13.walkObject)(styles, (value) => value, {
      getKey: (prop, value) => {
        if ((0, import_shared13.isObject)(value) && !context.conditions.isCondition(prop) && !context.isValidProperty(prop)) {
          const selectors = parseSelectors(prop);
          return selectors.map((s) => "& " + s).join(", ");
        }
        return prop;
      }
    });
    (0, import_lodash.default)(result[scope], transformStyles(context, styleObject, scope));
  }
  return result;
}

// src/recipes.ts
var sharedState = {
  /**
   * The map of recipe names to their resolved class names
   */
  classNames: /* @__PURE__ */ new Map(),
  /**
   * The map of the property to their resolved styles
   */
  styles: /* @__PURE__ */ new Map(),
  /**
   * The map of the recipes with their resolved styles
   */
  nodes: /* @__PURE__ */ new Map(),
  /**
   * The map of recipe key to slot key + slot recipe
   */
  slots: /* @__PURE__ */ new Map()
};
var Recipes = class _Recipes {
  constructor(recipes = {}) {
    this.recipes = recipes;
    this.prune();
  }
  slotSeparator = "__";
  keys = [];
  deprecated = /* @__PURE__ */ new Set();
  context;
  getPropKey = (recipe, variant, value) => {
    return `${recipe} (${variant} = ${value})`;
  };
  get separator() {
    return this.context.utility.separator ?? "_";
  }
  getClassName = (className, variant, value) => {
    return `${className}--${variant}${this.separator}${value}`;
  };
  // check this.recipes against sharedState.nodes
  // and remove any recipes (in sharedState) that are no longer in use
  prune = () => {
    const recipeNames = Object.keys(this.recipes);
    const cachedRecipeNames = Array.from(sharedState.nodes.keys());
    const removedRecipes = cachedRecipeNames.filter((name) => !recipeNames.includes(name));
    removedRecipes.forEach((name) => {
      this.remove(name);
    });
  };
  save = (context) => {
    this.context = context;
    for (const [name, recipe] of Object.entries(this.recipes)) {
      this.saveOne(name, recipe);
    }
    this.keys = Object.keys(this.recipes);
  };
  saveOne = (name, recipe) => {
    if (_Recipes.isSlotRecipeConfig(recipe)) {
      const slots = (0, import_shared14.getSlotRecipes)(recipe);
      const slotsMap = /* @__PURE__ */ new Map();
      Object.entries(slots).forEach(([slot, slotRecipe]) => {
        const slotName = this.getSlotKey(name, slot);
        this.normalize(slotName, slotRecipe);
        slotsMap.set(slotName, slotRecipe);
      });
      this.assignRecipe(name, recipe);
      sharedState.slots.set(name, slotsMap);
    } else {
      this.assignRecipe(name, this.normalize(name, recipe));
    }
  };
  remove(name) {
    sharedState.nodes.delete(name);
    sharedState.classNames.delete(name);
    sharedState.styles.delete(name);
  }
  assignRecipe = (name, recipe) => {
    if (recipe.deprecated)
      this.deprecated.add(name);
    const variantKeys = Object.keys(recipe.variants ?? {});
    const capitalized = (0, import_shared14.capitalize)(name);
    const jsx = Array.from(recipe.jsx ?? [capitalized]);
    if ("slots" in recipe) {
      jsx.push(...recipe.slots.map((slot) => capitalized + "." + (0, import_shared14.capitalize)(slot)));
    }
    const match3 = (0, import_shared14.createRegex)(jsx);
    sharedState.nodes.set(name, {
      ...this.getNames(name),
      jsx,
      type: "recipe",
      variantKeys,
      variantKeyMap: Object.fromEntries(
        Object.entries(recipe.variants ?? {}).map(([key, value]) => {
          return [key, Object.keys(value)];
        })
      ),
      match: match3,
      config: recipe,
      splitProps: (props) => {
        return (0, import_shared14.splitProps)(props, variantKeys);
      },
      props: variantKeys
    });
  };
  getSlotKey = (name, slot) => {
    return `${name}${this.slotSeparator}${slot}`;
  };
  isEmpty = () => {
    return sharedState.nodes.size === 0;
  };
  isDeprecated = (name) => {
    return this.deprecated.has(name);
  };
  getNames = (0, import_shared14.memo)((name) => {
    return {
      baseName: name,
      upperName: (0, import_shared14.capitalize)(name),
      dashName: (0, import_shared14.dashCase)(name),
      jsxName: (0, import_shared14.capitalize)(name)
    };
  });
  getRecipe = (0, import_shared14.memo)((name) => {
    return sharedState.nodes.get(name);
  });
  getConfig = (0, import_shared14.memo)((name) => {
    return this.recipes[name];
  });
  getConfigOrThrow = (0, import_shared14.memo)((name) => {
    const config = this.getConfig(name);
    if (!config)
      throw new import_shared14.PandaError("UNKNOWN_RECIPE", `Recipe "${name}" not found`);
    return config;
  });
  find = (0, import_shared14.memo)((jsxName) => {
    return this.details.find((node) => node.match.test(jsxName));
  });
  filter = (0, import_shared14.memo)((jsxName) => {
    return this.details.filter((node) => node.match.test(jsxName));
  });
  get details() {
    return Array.from(sharedState.nodes.values());
  }
  splitProps = (recipeName, props) => {
    const recipe = this.details.find((node) => node.baseName === recipeName);
    if (!recipe)
      return [{}, props];
    return recipe.splitProps(props);
  };
  isSlotRecipe = (name) => {
    return sharedState.slots.has(name);
  };
  static isSlotRecipeConfig = (config) => {
    return "slots" in config && Array.isArray(config.slots) && config.slots.length > 0;
  };
  normalize = (name, config) => {
    const {
      className,
      jsx = [(0, import_shared14.capitalize)(name)],
      base = {},
      variants = {},
      defaultVariants = {},
      description = "",
      compoundVariants = [],
      staticCss = []
    } = config;
    const recipe = {
      ...config,
      deprecated: config.deprecated == null ? false : config.deprecated,
      jsx,
      className,
      description,
      base: {},
      variants: {},
      defaultVariants,
      compoundVariants,
      staticCss
    };
    recipe.base = transformStyles(this.context, base, name);
    sharedState.styles.set(name, recipe.base);
    sharedState.classNames.set(name, className);
    for (const [key, variant] of Object.entries(variants)) {
      for (const [variantKey, styles] of Object.entries(variant)) {
        const propKey = this.getPropKey(name, key, variantKey);
        const className2 = this.getClassName(config.className, key, variantKey);
        const styleObject = transformStyles(this.context, styles, className2);
        sharedState.styles.set(propKey, styleObject);
        sharedState.classNames.set(propKey, className2);
        (0, import_lodash2.default)(recipe.variants, {
          [key]: { [variantKey]: styleObject }
        });
      }
    }
    return recipe;
  };
  getTransform = (name, slot) => {
    return (variant, value) => {
      if (value === "__ignore__") {
        return {
          layer: slot ? "recipes_slots_base" : "recipes_base",
          className: sharedState.classNames.get(name),
          styles: sharedState.styles.get(name) ?? {}
        };
      }
      const propKey = this.getPropKey(name, variant, value);
      return {
        className: sharedState.classNames.get(propKey),
        styles: sharedState.styles.get(propKey) ?? {}
      };
    };
  };
  filterDetails = (filters) => {
    const recipeDiffs = filters?.affecteds?.recipes;
    return recipeDiffs ? this.details.filter((recipe) => recipeDiffs.includes(recipe.dashName)) : this.details;
  };
  static inferSlots = (recipe) => {
    const slots = /* @__PURE__ */ new Set();
    Object.keys(recipe.base ?? {}).forEach((name) => {
      slots.add(name);
    });
    Object.values(recipe.variants ?? {}).forEach((variants) => {
      Object.keys(variants).forEach((name) => {
        slots.add(name);
      });
    });
    return Array.from(slots);
  };
  static isValidNode = (node) => {
    return (0, import_shared14.isObject)(node) && "type" in node && node.type === "recipe";
  };
};

// src/static-css.ts
var import_shared17 = require("@pandacss/shared");

// src/style-decoder.ts
var import_shared16 = require("@pandacss/shared");

// src/style-encoder.ts
var import_shared15 = require("@pandacss/shared");

// package.json
var version = "0.39.2";

// src/style-encoder.ts
var urlRegex = /^https?:\/\//;
var StyleEncoder = class _StyleEncoder {
  constructor(context) {
    this.context = context;
  }
  static separator = "]___[";
  static conditionSeparator = "<___>";
  atomic = /* @__PURE__ */ new Set();
  compound_variants = /* @__PURE__ */ new Set();
  //
  recipes = /* @__PURE__ */ new Map();
  recipes_base = /* @__PURE__ */ new Map();
  filterStyleProps = (props) => {
    if (this.context.isTemplateLiteralSyntax)
      return props;
    return filterProps(this.context.isValidProperty, props);
  };
  clone = () => {
    return new _StyleEncoder(this.context);
  };
  isEmpty = () => {
    return !this.atomic.size && !this.recipes.size && !this.compound_variants.size && !this.recipes_base.size;
  };
  get results() {
    return {
      atomic: this.atomic,
      recipes: this.recipes,
      recipes_base: this.recipes_base
    };
  }
  /**
   * Hashes a style object and adds the resulting hashes to a set.
   * @param set - The set to add the resulting hashes to.
   * @param obj - The style object to hash.
   * @param baseEntry - An optional base style entry to use when hashing the style object.
   */
  hashStyleObject = (set, obj, baseEntry) => {
    const isCondition = this.context.conditions.isCondition;
    const traverseOptions = { separator: _StyleEncoder.conditionSeparator };
    let prop = "";
    let prevProp = "";
    const isRecipe = !!baseEntry?.variants;
    const normalized = (0, import_shared15.normalizeStyleObject)(obj, this.context, !isRecipe);
    (0, import_shared15.traverse)(
      normalized,
      ({ key, value: rawValue, path }) => {
        if (rawValue === void 0) {
          return;
        }
        if (urlRegex.test(rawValue)) {
          return;
        }
        const value = Array.isArray(rawValue) ? (0, import_shared15.toResponsiveObject)(rawValue, this.context.conditions.breakpoints.keys) : rawValue;
        prop = key;
        if (isCondition(key)) {
          if ((0, import_shared15.isObjectOrArray)(value)) {
            return;
          }
          prop = prevProp;
        } else if ((0, import_shared15.isObjectOrArray)(value)) {
          prevProp = prop;
          return;
        }
        const resolvedCondition = getResolvedCondition(path, isCondition);
        const hashed = hashStyleEntry(Object.assign(baseEntry ?? {}, { prop, value, cond: resolvedCondition }));
        set.add(hashed);
        prevProp = prop;
      },
      traverseOptions
    );
  };
  processAtomic = (styles) => {
    this.hashStyleObject(this.atomic, styles);
  };
  processStyleProps = (styleProps) => {
    const styles = this.filterStyleProps(styleProps);
    if (styles.css) {
      if (Array.isArray(styles.css)) {
        styles.css.forEach((style) => this.processAtomic(style));
      } else {
        this.processAtomic(styles.css);
      }
    }
    this.processAtomic(styles.css ? Object.assign({}, styles, { css: void 0 }) : styles);
  };
  processConfigSlotRecipeBase = (recipeName, config) => {
    config.slots.forEach((slot) => {
      const recipeKey = this.context.recipes.getSlotKey(recipeName, slot);
      const slotBase = config.base?.[slot];
      if (!slotBase || this.recipes_base.has(recipeKey))
        return;
      const base_set = (0, import_shared15.getOrCreateSet)(this.recipes_base, recipeKey);
      this.hashStyleObject(base_set, slotBase, { recipe: recipeName, slot });
    });
  };
  processConfigSlotRecipe = (recipeName, variants) => {
    const config = this.context.recipes.getConfig(recipeName);
    if (!Recipes.isSlotRecipeConfig(config))
      return;
    this.processConfigSlotRecipeBase(recipeName, config);
    const set = (0, import_shared15.getOrCreateSet)(this.recipes, recipeName);
    const computedVariants = Object.assign({}, config.defaultVariants, variants);
    this.hashStyleObject(set, computedVariants, { recipe: recipeName, variants: true });
    if (!config.compoundVariants || this.compound_variants.has(recipeName))
      return;
    this.compound_variants.add(recipeName);
    config.compoundVariants.forEach((compoundVariant) => {
      if (!compoundVariant)
        return;
      Object.values(compoundVariant.css).forEach((values) => {
        if (!values)
          return;
        this.processAtomic(values);
      });
    });
  };
  processConfigRecipeBase = (recipeName, config) => {
    if (!config.base || this.recipes_base.has(recipeName))
      return;
    const base_set = (0, import_shared15.getOrCreateSet)(this.recipes_base, recipeName);
    this.hashStyleObject(base_set, config.base, { recipe: recipeName });
  };
  processConfigRecipe = (recipeName, variants) => {
    const config = this.context.recipes.getConfig(recipeName);
    if (!config)
      return;
    this.processConfigRecipeBase(recipeName, config);
    const set = (0, import_shared15.getOrCreateSet)(this.recipes, recipeName);
    const computedVariants = Object.assign({}, config.defaultVariants, variants);
    this.hashStyleObject(set, computedVariants, { recipe: recipeName, variants: true });
    if (!config.compoundVariants || this.compound_variants.has(recipeName))
      return;
    this.compound_variants.add(recipeName);
    config.compoundVariants.forEach((compoundVariant) => {
      if (!compoundVariant)
        return;
      this.processAtomic(compoundVariant.css);
    });
  };
  processRecipe = (recipeName, variants) => {
    if (this.context.recipes.isSlotRecipe(recipeName)) {
      this.processConfigSlotRecipe(recipeName, variants);
    } else {
      this.processConfigRecipe(recipeName, variants);
    }
  };
  processRecipeBase(recipeName) {
    const config = this.context.recipes.getConfig(recipeName);
    if (!config)
      return;
    if (this.context.recipes.isSlotRecipe(recipeName)) {
      this.processConfigSlotRecipeBase(recipeName, config);
    } else {
      this.processConfigRecipeBase(recipeName, config);
    }
  }
  processPattern = (name, patternProps, type, jsxName) => {
    let fnName = name;
    if (type === "jsx-pattern" && jsxName) {
      fnName = this.context.patterns.find(jsxName);
    }
    const styleProps = this.context.patterns.transform(fnName, patternProps);
    this.processStyleProps(styleProps);
  };
  processAtomicRecipe = (recipe) => {
    const { base = {}, variants = {}, compoundVariants = [] } = recipe;
    this.processAtomic(base);
    for (const variant of Object.values(variants)) {
      for (const styles of Object.values(variant)) {
        this.processAtomic(styles);
      }
    }
    compoundVariants.forEach((compoundVariant) => {
      if (!compoundVariant)
        return;
      this.processAtomic(compoundVariant.css);
    });
  };
  processAtomicSlotRecipe = (recipe) => {
    recipe.slots ||= Recipes.inferSlots(recipe);
    const slots = (0, import_shared15.getSlotRecipes)(recipe);
    for (const slotRecipe of Object.values(slots)) {
      this.processAtomicRecipe(slotRecipe);
    }
  };
  getConfigRecipeHash = (recipeName) => {
    return {
      atomic: this.atomic,
      base: this.recipes_base.get(recipeName),
      variants: this.recipes.get(recipeName)
    };
  };
  getConfigSlotRecipeHash = (recipeName) => {
    const recipeConfig = this.context.recipes.getConfigOrThrow(recipeName);
    if (!Recipes.isSlotRecipeConfig(recipeConfig)) {
      throw new import_shared15.PandaError("INVALID_RECIPE", `Recipe "${recipeName}" is not a slot recipe`);
    }
    const base = {};
    recipeConfig.slots.map((slot) => {
      const recipeKey = this.context.recipes.getSlotKey(recipeName, slot);
      base[slot] = this.recipes_base.get(recipeKey);
    });
    return {
      atomic: this.atomic,
      base,
      variants: this.recipes.get(recipeName)
    };
  };
  getRecipeHash = (recipeName) => {
    if (this.context.recipes.isSlotRecipe(recipeName)) {
      return this.getConfigSlotRecipeHash(recipeName);
    }
    return this.getConfigRecipeHash(recipeName);
  };
  toJSON = () => {
    const styles = {
      atomic: Array.from(this.atomic),
      recipes: Object.fromEntries(Array.from(this.recipes.entries()).map(([name, set]) => [name, Array.from(set)]))
    };
    return {
      schemaVersion: version,
      styles
    };
  };
  fromJSON = (json) => {
    const { styles } = json;
    styles.atomic?.forEach((hash) => this.atomic.add(hash));
    Object.entries(styles.recipes ?? {}).forEach(([recipeName, hashes]) => {
      this.processRecipeBase(recipeName);
      const set = (0, import_shared15.getOrCreateSet)(this.recipes, recipeName);
      hashes.forEach((hash) => set.add(hash));
    });
    return this;
  };
};
var filterProps = (isValidProperty, props) => {
  const clone = {};
  for (const [key, value] of Object.entries(props)) {
    if (isValidProperty(key) && value !== void 0) {
      clone[key] = value;
    }
  }
  return clone;
};
var hashStyleEntry = (entry) => {
  const parts = [`${entry.prop}${StyleEncoder.separator}value:${entry.value}`];
  if (entry.cond) {
    parts.push(`cond:${entry.cond}`);
  }
  if (entry.recipe) {
    parts.push(`recipe:${entry.recipe}`);
  }
  if (entry.layer) {
    parts.push(`layer:${entry.layer}`);
  }
  if (entry.slot) {
    parts.push(`slot:${entry.slot}`);
  }
  return parts.join(StyleEncoder.separator);
};
var getResolvedCondition = (cond, isCondition) => {
  if (!cond) {
    return "";
  }
  const parts = cond.split(StyleEncoder.conditionSeparator);
  const relevantParts = parts.filter((part) => part !== "base" && isCondition(part));
  if (parts.length !== relevantParts.length) {
    return relevantParts.join(StyleEncoder.conditionSeparator);
  }
  return cond;
};

// src/style-decoder.ts
var StyleDecoder = class _StyleDecoder {
  constructor(context) {
    this.context = context;
  }
  classNames = /* @__PURE__ */ new Map();
  //
  atomic_cache = /* @__PURE__ */ new Map();
  group_cache = /* @__PURE__ */ new Map();
  recipe_base_cache = /* @__PURE__ */ new Map();
  //
  atomic = /* @__PURE__ */ new Set();
  //
  recipes = /* @__PURE__ */ new Map();
  recipes_base = /* @__PURE__ */ new Map();
  clone = () => {
    return new _StyleDecoder(this.context);
  };
  isEmpty = () => {
    return !this.atomic.size && !this.recipes.size && !this.recipes_base.size;
  };
  get results() {
    return {
      atomic: this.atomic,
      recipes: this.recipes,
      recipes_base: this.recipes_base
    };
  }
  formatSelector = (conditions, className) => {
    const { conditions: cond, hash, utility } = this.context;
    const conds = cond.finalize(conditions);
    let result;
    if (hash.className) {
      conds.push(className);
      result = utility.formatClassName(utility.toHash(conds, utility.defaultHashFn));
    } else {
      conds.push(utility.formatClassName(className));
      result = conds.join(":");
    }
    return (0, import_shared16.esc)(result);
  };
  getRecipeName = (hash) => {
    const entry = getEntryFromHash(hash);
    if (!entry.recipe)
      return;
    return entry.slot ? this.context.recipes.getSlotKey(entry.recipe, entry.slot) : entry.recipe;
  };
  getTransformResult = (hash) => {
    const entry = getEntryFromHash(hash);
    const recipeName = this.getRecipeName(hash);
    const transform2 = recipeName ? this.context.recipes.getTransform(recipeName) : this.context.utility.transform;
    const transformed = transform2(entry.prop, (0, import_shared16.withoutImportant)(entry.value));
    if (!transformed.className) {
      return;
    }
    const important = (0, import_shared16.isImportant)(entry.value);
    const styles = important ? (0, import_shared16.markImportant)(transformed.styles) : transformed.styles;
    const parts = entry.cond ? entry.cond.split(StyleEncoder.conditionSeparator) : [];
    const className = this.formatSelector(parts, transformed.className);
    const classSelector = important ? `.${className}\\!` : `.${className}`;
    return {
      className,
      classSelector,
      styles,
      transformed,
      parts
    };
  };
  resolveCondition = (condition) => {
    return Array.isArray(condition.raw) ? condition.raw.map((c) => this.context.utility.tokens.resolveReference(c)) : this.context.utility.tokens.resolveReference(condition.raw);
  };
  getAtomic = (hash) => {
    const cached = this.atomic_cache.get(hash);
    if (cached)
      return cached;
    const entry = getEntryFromHash(hash);
    const transformResult = this.getTransformResult(hash);
    if (!transformResult)
      return;
    const { className, classSelector, styles, transformed, parts } = transformResult;
    const basePath = [classSelector];
    const obj = {};
    let conditions;
    if (entry.cond) {
      conditions = this.context.conditions.sort(parts);
      const path = basePath.concat(conditions.flatMap((c) => this.resolveCondition(c)));
      (0, import_shared16.deepSet)(obj, path, styles);
    } else {
      (0, import_shared16.deepSet)(obj, basePath, styles);
    }
    const styleResult = {
      result: obj,
      entry,
      hash,
      conditions,
      className,
      layer: transformed.layer
    };
    this.atomic_cache.set(hash, styleResult);
    return styleResult;
  };
  getGroup = (hashSet, key) => {
    const cached = this.group_cache.get(key);
    if (cached)
      return cached;
    let obj = {};
    const basePath = [];
    const details = [];
    const transform2 = this.context.utility.transform.bind(this.context.utility);
    hashSet.forEach((hash) => {
      const entry = getEntryFromHash(hash);
      const transformed = transform2(entry.prop, (0, import_shared16.withoutImportant)(entry.value));
      if (!transformed.className)
        return;
      const important = (0, import_shared16.isImportant)(entry.value);
      const result2 = important ? (0, import_shared16.markImportant)(transformed.styles) : transformed.styles;
      const parts = entry.cond ? entry.cond.split(StyleEncoder.conditionSeparator) : [];
      let conditions;
      if (entry.cond) {
        conditions = this.context.conditions.sort(parts);
      }
      details.push({ hash, entry, conditions, result: result2 });
    });
    const sorted = sortStyleRules(details);
    sorted.forEach((value) => {
      if (value.conditions) {
        const path = basePath.concat(value.conditions.flatMap((c) => this.resolveCondition(c)));
        obj = (0, import_shared16.deepSet)(obj, path, value.result);
      } else {
        obj = (0, import_shared16.deepSet)(obj, basePath, value.result);
      }
    });
    const result = { result: obj, hashSet, details, className: key };
    this.group_cache.set(key, result);
    return result;
  };
  getRecipeBase = (hashSet, recipeName, slot) => {
    const recipeConfig = this.context.recipes.getConfig(recipeName);
    if (!recipeConfig)
      return;
    const className = "slots" in recipeConfig && slot ? this.context.recipes.getSlotKey(recipeConfig.className, slot) : recipeConfig.className;
    const cached = this.recipe_base_cache.get(className);
    if (cached)
      return cached;
    const selector = this.formatSelector([], className);
    const style = this.getGroup(hashSet, className);
    const result = Object.assign({}, style, {
      result: { ["." + selector]: style.result },
      recipe: recipeName,
      className,
      slot
    });
    this.recipe_base_cache.set(className, result);
    return result;
  };
  collectAtomic = (encoder) => {
    const atomic = [];
    encoder.atomic.forEach((item) => {
      const result = this.getAtomic(item);
      if (!result)
        return;
      atomic.push(result);
    });
    const sorted = sortStyleRules(atomic);
    sorted.forEach((styleResult) => {
      this.atomic.add(styleResult);
      this.classNames.set(styleResult.className, styleResult);
    });
    return this;
  };
  processClassName = (recipeName, hash) => {
    const result = this.getAtomic(hash);
    if (!result)
      return;
    const styleSet = (0, import_shared16.getOrCreateSet)(this.recipes, recipeName);
    styleSet.add(result);
    this.classNames.set(result.className, result);
  };
  collectRecipe = (encoder) => {
    encoder.recipes.forEach((hashSet, recipeName) => {
      const recipeConfig = this.context.recipes.getConfig(recipeName);
      if (!recipeConfig)
        return;
      hashSet.forEach((hash) => {
        if ("slots" in recipeConfig) {
          recipeConfig.slots.forEach((slot) => {
            const slotHash = hash + StyleEncoder.separator + "slot:" + slot;
            this.processClassName(recipeName, slotHash);
          });
        } else {
          this.processClassName(recipeName, hash);
        }
      });
    });
  };
  collectRecipeBase = (encoder) => {
    encoder.recipes_base.forEach((hashSet, recipeKey) => {
      const [recipeName, slot] = recipeKey.split(this.context.recipes.slotSeparator);
      const recipeConfig = this.context.recipes.getConfig(recipeName);
      if (!recipeConfig)
        return;
      const result = this.getRecipeBase(hashSet, recipeName, slot);
      if (!result)
        return;
      const styleSet = (0, import_shared16.getOrCreateSet)(this.recipes_base, recipeKey);
      styleSet.add(result);
      this.classNames.set(result.className, result);
    });
  };
  /**
   * Collect and re-create all styles and recipes objects from the style encoder
   * So that we can just iterate over them and transform resulting CSS objects into CSS strings
   */
  collect = (encoder) => {
    this.collectAtomic(encoder);
    this.collectRecipe(encoder);
    this.collectRecipeBase(encoder);
    return this;
  };
  getConfigRecipeResult = (recipeName) => {
    return {
      atomic: this.atomic,
      base: this.recipes_base.get(recipeName),
      variants: this.recipes.get(recipeName)
    };
  };
  getConfigSlotRecipeResult = (recipeName) => {
    const recipeConfig = this.context.recipes.getConfigOrThrow(recipeName);
    if (!Recipes.isSlotRecipeConfig(recipeConfig)) {
      throw new import_shared16.PandaError("UNKNOWN_RECIPE", `Recipe "${recipeName}" is not a slot recipe`);
    }
    const base = {};
    recipeConfig.slots.map((slot) => {
      const recipeKey = this.context.recipes.getSlotKey(recipeName, slot);
      base[slot] = this.recipes_base.get(recipeKey);
    });
    return {
      atomic: this.atomic,
      base,
      variants: this.recipes.get(recipeName)
    };
  };
  getRecipeResult = (recipeName) => {
    if (this.context.recipes.isSlotRecipe(recipeName)) {
      return this.getConfigSlotRecipeResult(recipeName);
    }
    return this.getConfigRecipeResult(recipeName);
  };
};
var entryKeys = ["cond", "recipe", "layer", "slot"];
var getEntryFromHash = (hash) => {
  const parts = hash.split(StyleEncoder.separator);
  const prop = parts[0];
  const rawValue = parts[1].replace("value:", "");
  const value = parseValue(rawValue);
  const entry = { prop, value };
  parts.forEach((part) => {
    const key = entryKeys.find((k) => part.startsWith(k));
    if (key) {
      entry[key] = part.slice(key.length + 1);
    }
  });
  return entry;
};
var startsWithZero = /^0\d+$/;
var parseValue = (value) => {
  if (startsWithZero.test(value)) {
    return value;
  }
  const asNumber = Number(value);
  if (!Number.isNaN(asNumber))
    return asNumber;
  return castBoolean(value);
};
var castBoolean = (value) => {
  if (value === "true")
    return true;
  if (value === "false")
    return false;
  return value;
};

// src/static-css.ts
var StaticCss = class {
  constructor(context) {
    this.context = context;
    this.encoder = context.encoder;
    this.decoder = context.decoder;
  }
  encoder;
  decoder;
  clone() {
    this.encoder = this.encoder.clone();
    this.decoder = this.decoder.clone();
    return this;
  }
  /**
   * This transforms a static css config into the same format as in the ParserResult,
   * so that it can be processed by the same logic as styles found in app code.
   *
   * e.g.
   * @example { css: [{ color: ['red', 'blue'] }] } => { css: [{ color: 'red }, { color: 'blue }] }
   * @example { css: [{ color: ['red'], conditions: ['md'] }] } => { css: [{ color: { base: 'red', md: 'red' } }] }
   *
   */
  getStyleObjects(options) {
    const { config, utility, patterns: _patterns } = this.context;
    const breakpoints = Object.keys(config.theme?.breakpoints ?? {});
    const getRecipe = (recipeName) => {
      const node = this.context.recipes.details.find((detail) => detail.baseName === recipeName);
      return node;
    };
    const { css = [], patterns = {} } = options;
    const recipes = options.recipes ?? {};
    const results = { css: [], recipes: [], patterns: [] };
    css.forEach((rule) => {
      const conditions = rule.conditions || [];
      if (rule.responsive) {
        conditions.push(...breakpoints);
      }
      Object.entries(rule.properties).forEach(([property, values]) => {
        const propKeys = utility.getPropertyKeys(property);
        const computedValues = values.flatMap((value) => value === "*" ? propKeys : value);
        computedValues.forEach((value) => {
          const conditionalValues = conditions.reduce(
            (acc, condition) => ({
              base: value,
              ...acc,
              [formatCondition(breakpoints, condition)]: value
            }),
            {}
          );
          results.css.push({
            [property]: conditions.length ? conditionalValues : value
          });
        });
      });
    });
    Object.entries(recipes).forEach(([recipe, rules]) => {
      const recipeNode = getRecipe(recipe);
      if (!recipeNode)
        return;
      results.recipes.push({ [recipe]: {} });
      if (recipeNode.config.compoundVariants) {
        recipeNode.config.compoundVariants.forEach((compoundRule) => {
          const css2 = compoundRule.css;
          const isSlot = "slots" in recipeNode.config && recipeNode.config.slots.length;
          if (isSlot) {
            Object.entries(css2).forEach(([_slot, styles]) => {
              Object.entries(styles).forEach(([prop, value]) => {
                results.css.push({ [prop]: value });
              });
            });
          } else {
            Object.entries(css2).forEach(([prop, value]) => {
              results.css.push({ [prop]: value });
            });
          }
        });
      }
      rules.forEach((rule) => {
        const recipeKeys = recipeNode?.variantKeyMap;
        if (!recipeKeys)
          return;
        const useAllKeys = rule === "*";
        const { conditions = [], responsive, ...variants } = useAllKeys ? recipeKeys : rule;
        if (responsive) {
          conditions.push(...breakpoints);
        }
        Object.entries(variants).forEach(([variant, values]) => {
          if (!Array.isArray(values))
            return;
          const computedValues = values.flatMap((value) => value === "*" ? recipeKeys[variant] : value);
          computedValues.forEach((value) => {
            const conditionalValues = conditions.reduce(
              (acc, condition) => ({
                base: value,
                ...acc,
                [formatCondition(breakpoints, condition)]: value
              }),
              {}
            );
            results.recipes.push({
              [recipe]: { [variant]: conditions.length ? conditionalValues : value }
            });
          });
        });
      });
    });
    Object.entries(patterns).forEach(([pattern, rules]) => {
      rules.forEach((rule) => {
        const details = this.context.patterns.details.find((d) => d.baseName === pattern);
        if (!details)
          return;
        let props = {};
        const useAllKeys = rule === "*";
        if (useAllKeys) {
          props = Object.fromEntries((details.props ?? []).map((key) => [key, ["*"]]));
        }
        const { conditions = [], responsive = false, properties = props } = useAllKeys ? {} : rule;
        if (responsive) {
          conditions.push(...breakpoints);
        }
        Object.entries(properties).forEach(([property, values]) => {
          const propValues = _patterns.getPropertyValues(pattern, property);
          const computedValues = values.flatMap((value) => value === "*" ? propValues : value);
          computedValues.forEach((patternValue) => {
            const value = this.context.patterns.transform(pattern, { [property]: patternValue });
            const conditionalValues = conditions.reduce(
              (acc, condition) => ({
                base: value,
                ...acc,
                [formatCondition(breakpoints, condition)]: value
              }),
              {}
            );
            results.patterns.push(conditions.length ? conditionalValues : value);
          });
        });
      });
    });
    return results;
  }
  createRegex = () => {
    const { decoder: decoder2 } = this;
    return createClassNameRegex(Array.from(decoder2.classNames.keys()));
  };
  process(options, stylesheet) {
    const { encoder, decoder: decoder2, context } = this;
    const sheet = stylesheet ?? context.createSheet();
    const staticCss = {
      ...options,
      recipes: { ...typeof options.recipes === "string" ? {} : options.recipes }
    };
    const { theme = {} } = context.config;
    const recipeConfigs = Object.assign({}, theme.recipes, theme.slotRecipes);
    const useAllRecipes = options.recipes === "*";
    Object.entries(recipeConfigs).forEach(([name, recipe]) => {
      if (useAllRecipes) {
        staticCss.recipes[name] = ["*"];
      }
      if (recipe.staticCss) {
        staticCss.recipes[name] = recipe.staticCss;
      }
    });
    const results = this.getStyleObjects(staticCss);
    results.css.forEach((css) => {
      encoder.hashStyleObject(encoder.atomic, css);
    });
    results.recipes.forEach((result) => {
      Object.entries(result).forEach(([name, value]) => {
        encoder.processRecipe(name, value);
      });
    });
    results.patterns.forEach((result) => {
      encoder.hashStyleObject(encoder.atomic, result);
    });
    sheet.processDecoder(decoder2.collect(encoder));
    const parse = (text) => {
      const regex = this.createRegex();
      const matches = text.match(regex);
      if (!matches)
        return [];
      return matches.map((match3) => match3.replace(".", ""));
    };
    return {
      results,
      regex: this.createRegex.bind(this),
      parse,
      sheet
    };
  }
};
function createClassNameRegex(classNames) {
  const escapedClassNames = classNames.map((name) => (0, import_shared17.esc)(name));
  const pattern = `(${escapedClassNames.join("|")})`;
  return new RegExp(`\\b${pattern}\\b`, "g");
}
function formatCondition(breakpoints, condition) {
  return breakpoints.includes(condition) ? condition : `_${condition}`;
}

// src/stylesheet.ts
var import_postcss_cascade_layers = __toESM(require("@csstools/postcss-cascade-layers"));
var import_logger4 = require("@pandacss/logger");
var import_postcss7 = __toESM(require("postcss"));

// src/optimize.ts
var import_postcss6 = __toESM(require("postcss"));
var import_postcss_nested2 = __toESM(require("postcss-nested"));

// src/plugins/optimize-postcss.ts
var import_postcss4 = __toESM(require("postcss"));
var import_postcss_discard_duplicates = __toESM(require("postcss-discard-duplicates"));
var import_postcss_discard_empty = __toESM(require("postcss-discard-empty"));
var import_postcss_merge_rules = __toESM(require("postcss-merge-rules"));
var import_postcss_minify_selectors = __toESM(require("postcss-minify-selectors"));
var import_postcss_nested = __toESM(require("postcss-nested"));
var import_postcss_normalize_whitespace = __toESM(require("postcss-normalize-whitespace"));

// src/plugins/prettify.ts
function prettifyNode(node, indent = 0) {
  node.each && node.each((child, i) => {
    if (!child.raws.before || !child.raws.before.trim() || child.raws.before.includes("\n")) {
      child.raws.before = `
${node.type !== "rule" && i > 0 ? "\n" : ""}${"  ".repeat(indent)}`;
    }
    prettifyNode(child, indent + 1);
  });
}
function prettify() {
  return (root) => {
    prettifyNode(root);
    if (root.first) {
      root.first.raws.before = "";
    }
  };
}
prettify.postcssPlugin = "panda-prettify";

// src/plugins/optimize-postcss.ts
function optimizePostCss(code, options = {}) {
  const { minify = false } = options;
  const plugins = [
    (0, import_postcss_nested.default)(),
    (0, import_postcss_discard_duplicates.default)(),
    (0, import_postcss_merge_rules.default)(),
    (0, import_postcss_discard_empty.default)()
  ];
  if (minify) {
    plugins.push((0, import_postcss_normalize_whitespace.default)(), (0, import_postcss_minify_selectors.default)());
  } else {
    plugins.push(prettify());
  }
  const { css } = (0, import_postcss4.default)(plugins).process(code);
  return css;
}

// src/optimize.ts
function optimizeCss(code, options = {}) {
  const { lightningcss } = options;
  if (lightningcss) {
    const light = (init_optimize_lightningcss(), __toCommonJS(optimize_lightningcss_exports));
    return light.default(code, options);
  }
  return optimizePostCss(code, options);
}
function expandNestedCss(code) {
  const { css } = (0, import_postcss6.default)([(0, import_postcss_nested2.default)(), prettify()]).process(code);
  return css;
}

// src/plugins/sort-mq.ts
var import_ts_pattern = require("ts-pattern");
var atRuleName = import_ts_pattern.P.union("media", "container");
function sortMediaQueries() {
  const inner = (root) => {
    root.nodes?.sort((a, b) => {
      return (0, import_ts_pattern.match)({ a, b }).with(
        {
          a: { type: "atrule", name: atRuleName },
          b: { type: "atrule", name: atRuleName }
        },
        ({ a: a2, b: b2 }) => {
          return sortAtRules(a2.params, b2.params);
        }
      ).with({ a: { type: "atrule", name: atRuleName }, b: import_ts_pattern.P.any }, () => {
        return 1;
      }).with({ a: import_ts_pattern.P.any, b: { type: "atrule", name: atRuleName } }, () => {
        return -1;
      }).otherwise(() => {
        return 0;
      });
    });
    root.nodes?.forEach((node) => {
      if ("nodes" in node) {
        inner(node);
      }
    });
  };
  return inner;
}
sortMediaQueries.postcssPlugin = "panda-sort-mq";

// src/stylesheet.ts
var Stylesheet = class {
  constructor(context) {
    this.context = context;
  }
  get layers() {
    return this.context.layers;
  }
  getLayer(layer) {
    return this.context.layers[layer];
  }
  process(options) {
    const layer = this.getLayer(options.layer);
    if (!layer)
      return;
    const { styles } = options;
    if (typeof styles !== "object")
      return;
    try {
      layer.append(stringify(styles));
    } catch (error) {
      if (error instanceof import_postcss7.CssSyntaxError) {
        import_logger4.logger.error("sheet:process", error.showSourceCode(true));
      }
    }
    return;
  }
  serialize = (styles) => {
    return serializeStyles(this.context, styles);
  };
  processResetCss = (styles) => {
    const result = this.serialize(styles);
    let css = stringify(result);
    if (this.context.hooks["cssgen:done"]) {
      css = this.context.hooks["cssgen:done"]({ artifact: "reset", content: css }) ?? css;
    }
    this.context.layers.reset.append(css);
  };
  processGlobalCss = (styles) => {
    const result = this.serialize(styles);
    let css = stringify(result);
    css += this.context.globalVars.toString();
    if (this.context.hooks["cssgen:done"]) {
      css = this.context.hooks["cssgen:done"]({ artifact: "global", content: css }) ?? css;
    }
    this.context.layers.base.append(css);
  };
  processCss = (styles, layer) => {
    if (!styles)
      return;
    this.process({ styles, layer });
  };
  processDecoder = (decoder2) => {
    sortStyleRules([...decoder2.atomic]).forEach((css) => {
      this.processCss(css.result, css.layer ?? "utilities");
    });
    decoder2.recipes.forEach((recipeSet) => {
      recipeSet.forEach((recipe) => {
        this.processCss(recipe.result, recipe.entry.slot ? "recipes_slots" : "recipes");
      });
    });
    decoder2.recipes_base.forEach((recipeSet) => {
      recipeSet.forEach((recipe) => {
        this.processCss(recipe.result, recipe.slot ? "recipes_slots_base" : "recipes_base");
      });
    });
  };
  getLayerCss = (...layers) => {
    return optimizeCss(
      layers.map((layer) => {
        return this.context.layers.getLayerRoot(layer).toString();
      }).join("\n"),
      {
        minify: false,
        lightningcss: this.context.lightningcss,
        browserslist: this.context.browserslist
      }
    );
  };
  toCss = ({ optimize = false, minify } = {}) => {
    try {
      const breakpoints = this.context.conditions.breakpoints;
      const root = this.context.layers.insert();
      breakpoints.expandScreenAtRule(root);
      const plugins = [sortMediaQueries()];
      if (this.context.polyfill) {
        plugins.push((0, import_postcss_cascade_layers.default)());
      }
      const result = (0, import_postcss7.default)(plugins).process(root);
      const css = result.toString();
      if (!optimize)
        return css;
      return optimizeCss(css, {
        minify,
        lightningcss: this.context.lightningcss,
        browserslist: this.context.browserslist
      });
    } catch (error) {
      if (error instanceof import_postcss7.CssSyntaxError) {
        import_logger4.logger.error("sheet:toCss", error.showSourceCode(true));
      }
      throw error;
    }
  };
};

// src/utility.ts
var import_shared18 = require("@pandacss/shared");

// src/color-mix.ts
var colorMix = (value, token) => {
  if (!value || typeof value !== "string")
    return { invalid: true, value };
  const [rawColor, rawOpacity] = value.split("/");
  if (!rawColor || !rawOpacity) {
    return { invalid: true, value: rawColor };
  }
  const colorToken = token(`colors.${rawColor}`);
  const opacityToken = token.raw(`opacity.${rawOpacity}`)?.value;
  if (!opacityToken && isNaN(Number(rawOpacity))) {
    return { invalid: true, value: rawColor };
  }
  const percent = opacityToken ? Number(opacityToken) * 100 + "%" : `${rawOpacity}%`;
  const color = colorToken ?? rawColor;
  return {
    invalid: false,
    color,
    value: `color-mix(in srgb, ${color} ${percent}, transparent)`
  };
};

// src/utility.ts
var Utility = class {
  constructor(options) {
    this.options = options;
    const { tokens, config = {}, separator, prefix, shorthands, strictTokens } = options;
    this.tokens = tokens;
    this.config = this.normalizeConfig(config);
    if (separator) {
      this.separator = separator;
    }
    if (prefix) {
      this.prefix = prefix;
    }
    if (strictTokens) {
      this.strictTokens = strictTokens;
    }
    if (shorthands) {
      this.assignShorthands();
    }
    this.assignColorPaletteProperty();
    this.assignProperties();
    this.assignPropertyTypes();
  }
  /**
   * The token map or dictionary of tokens
   */
  tokens;
  /**
   * [cache] The map of property names to their resolved class names
   */
  classNames = /* @__PURE__ */ new Map();
  /**
   * [cache] The map of the property to their resolved styless
   */
  styles = /* @__PURE__ */ new Map();
  /**
   * Map of shorthand properties to their longhand properties
   */
  shorthands = /* @__PURE__ */ new Map();
  /**
   * The map of possible values for each property
   */
  types = /* @__PURE__ */ new Map();
  /**
   * The map of the property keys
   */
  propertyTypeKeys = /* @__PURE__ */ new Map();
  /**
   * The utility config
   */
  config = {};
  /**
   * The map of property names to their transform functions
   */
  transforms = /* @__PURE__ */ new Map();
  /**
   * The map of property names to their config
   */
  configs = /* @__PURE__ */ new Map();
  /**
   * The map of deprecated properties
   */
  deprecated = /* @__PURE__ */ new Set();
  separator = "_";
  prefix = "";
  strictTokens = false;
  defaultHashFn = import_shared18.toHash;
  toHash = (path, hashFn) => hashFn(path.join(":"));
  normalizeConfig(config) {
    return Object.fromEntries(
      Object.entries(config).map(([property, propertyConfig]) => {
        return [property, this.normalize(propertyConfig)];
      })
    );
  }
  assignDeprecated = (property, config) => {
    if (!config.deprecated)
      return;
    this.deprecated.add(property);
    if ((0, import_shared18.isString)(config.shorthand))
      this.deprecated.add(config.shorthand);
    if (Array.isArray(config.shorthand)) {
      config.shorthand.forEach((shorthand) => this.deprecated.add(shorthand));
    }
  };
  register = (property, config) => {
    this.config[property] = this.normalize(config);
    this.assignProperty(property, config);
    this.assignPropertyType(property, config);
  };
  assignShorthands = () => {
    for (const [property, config] of Object.entries(this.config)) {
      const { shorthand } = config ?? {};
      if (!shorthand)
        continue;
      const values = Array.isArray(shorthand) ? shorthand : [shorthand];
      values.forEach((shorthandName) => {
        this.shorthands.set(shorthandName, property);
      });
    }
  };
  assignColorPaletteProperty = () => {
    if (!this.tokens.view.colorPalettes.size)
      return;
    const values = (0, import_shared18.mapToJson)(this.tokens.view.colorPalettes);
    this.config.colorPalette = {
      values: Object.keys(values),
      transform(value) {
        return values[value];
      }
    };
  };
  resolveShorthand = (prop) => {
    return this.shorthands.get(prop) ?? prop;
  };
  get hasShorthand() {
    return this.shorthands.size > 0;
  }
  get isEmpty() {
    return Object.keys(this.config).length === 0;
  }
  entries = () => {
    const value = Object.entries(this.config).filter(([, value2]) => !!value2?.className).map(([key, value2]) => [key, value2.className]);
    return value;
  };
  getPropKey = (prop, value) => {
    return `(${prop} = ${value})`;
  };
  hash = (prop, value) => {
    return `${prop}${this.separator}${value}`;
  };
  /**
   * Get all the possible values for the defined property
   */
  getPropertyValues = (config, resolveFn) => {
    const { values } = config;
    const fn = (key) => {
      const categoryValues = this.getTokenCategoryValues(key);
      if (!categoryValues)
        return;
      const prop = resolveFn?.(key);
      if (!prop)
        return;
      return { [prop]: categoryValues };
    };
    if ((0, import_shared18.isString)(values)) {
      return fn?.(values) ?? this.tokens.view.getCategoryValues(values) ?? {};
    }
    if (Array.isArray(values)) {
      return values.reduce((result, value) => {
        result[value] = value;
        return result;
      }, {});
    }
    if ((0, import_shared18.isFunction)(values)) {
      return values(resolveFn ? fn : this.getTokenCategoryValues.bind(this));
    }
    return values;
  };
  getPropertyRawValue(config, value) {
    const { values } = config;
    if (!values)
      return value;
    if ((0, import_shared18.isString)(values)) {
      return this.tokens.view.valuesByCategory.get(values)?.get(String(value)) || value;
    }
    if (Array.isArray(values)) {
      return value;
    }
    if ((0, import_shared18.isFunction)(values)) {
      return values(this.getTokenCategoryValues.bind(this))[value] || value;
    }
    if (values.type) {
      return value;
    }
    return values[value] || value;
  }
  getToken = (path) => {
    return this.tokens.view.get(path);
  };
  getTokenCategoryValues = (category) => {
    return this.tokens.view.getCategoryValues(category);
  };
  /**
   * Normalize the property config
   */
  normalize = (propertyConfig) => {
    const config = { ...propertyConfig };
    if (config.values === "keyframes") {
      config.values = Object.keys(this.options.keyframes ?? {});
    }
    if (config.shorthand && !config.className) {
      config.className = Array.isArray(config.shorthand) ? config.shorthand[0] : config.shorthand;
    }
    return config;
  };
  assignProperty = (property, config) => {
    this.setTransform(property, config?.transform);
    this.assignDeprecated(property, config);
    if (!config)
      return;
    this.configs.set(property, config);
  };
  assignProperties = () => {
    for (const [property, propertyConfig] of Object.entries(this.config)) {
      if (!propertyConfig)
        continue;
      this.assignProperty(property, propertyConfig);
    }
  };
  assignPropertiesValues = () => {
    for (const [property, propertyConfig] of Object.entries(this.config)) {
      if (!propertyConfig)
        continue;
      this.assignPropertyValues(property, propertyConfig);
    }
    return this;
  };
  assignPropertyValues = (property, config) => {
    const values = this.getPropertyValues(config);
    if (!values)
      return;
    for (const [alias, raw] of Object.entries(values)) {
      const propKey = this.getPropKey(property, alias);
      this.setStyles(property, raw, alias, propKey);
      this.getOrCreateClassName(property, alias);
    }
  };
  getPropertyKeys = (prop) => {
    const propConfig = this.config[prop];
    if (!propConfig)
      return [];
    const values = this.getPropertyValues(propConfig);
    if (!values)
      return [];
    return Object.keys(values);
  };
  getPropertyTypeKeys = (property) => {
    const keys2 = this.propertyTypeKeys.get(property);
    return keys2 ? Array.from(keys2) : [];
  };
  assignPropertyType = (property, config) => {
    if (!config)
      return;
    const values = this.getPropertyValues(config, (key) => `type:Tokens["${key}"]`);
    if (typeof values === "object" && values.type) {
      this.types.set(property, /* @__PURE__ */ new Set([`type:${values.type}`]));
      return;
    }
    if (values) {
      const keys2 = new Set(Object.keys(values));
      this.types.set(property, keys2);
      this.propertyTypeKeys.set(property, keys2);
    }
    const set = this.types.get(property) ?? /* @__PURE__ */ new Set();
    if (!this.strictTokens && config.property) {
      this.types.set(property, set.add(`CssProperties["${config.property}"]`));
    }
  };
  assignPropertyTypes = () => {
    for (const [property, propertyConfig] of Object.entries(this.config)) {
      if (!propertyConfig)
        continue;
      this.assignPropertyType(property, propertyConfig);
    }
  };
  /**
   * Returns the Typescript type for the define properties
   */
  getTypes = () => {
    const map = /* @__PURE__ */ new Map();
    for (const [prop, tokens] of this.types.entries()) {
      if (tokens.size === 0) {
        continue;
      }
      const typeValues = Array.from(tokens).map((key) => {
        if (key.startsWith("CssProperties"))
          return key;
        if (key.startsWith("type:"))
          return key.replace("type:", "");
        return JSON.stringify(key);
      });
      map.set(prop, typeValues);
    }
    return map;
  };
  defaultTransform = (0, import_shared18.memo)((value, prop) => {
    const isCssVar = prop.startsWith("--");
    if (isCssVar) {
      const tokenValue = this.tokens.view.get(value);
      value = typeof tokenValue === "string" ? tokenValue : value;
    }
    return { [prop]: value };
  });
  setTransform = (property, transform2) => {
    const defaultTransform = (value) => this.defaultTransform(value, property);
    const transformFn = transform2 ?? defaultTransform;
    this.transforms.set(property, transformFn);
    return this;
  };
  getTransformArgs = (raw) => {
    const token = Object.assign(this.getToken.bind(this), {
      raw: (path) => this.tokens.getByName(path)
    });
    const _colorMix = (value) => colorMix(value, token);
    return {
      token,
      raw,
      utils: { colorMix: _colorMix }
    };
  };
  setStyles = (property, raw, alias, propKey) => {
    propKey = propKey ?? this.getPropKey(property, raw);
    const defaultTransform = (value) => this.defaultTransform(value, property);
    const getStyles = this.transforms.get(property) ?? defaultTransform;
    const styles = getStyles(raw, this.getTransformArgs(alias));
    this.styles.set(propKey, styles ?? {});
    return this;
  };
  formatClassName = (className) => {
    return [this.prefix, className].filter(Boolean).join("-");
  };
  /**
   * Returns the resolved className for a given property and value
   */
  getClassName = (property, raw) => {
    const config = this.configs.get(property);
    if (!config || !config.className) {
      return this.hash((0, import_shared18.hypenateProperty)(property), raw);
    }
    return this.hash(config.className, raw);
  };
  getOrCreateClassName = (property, raw) => {
    const propKey = this.getPropKey(property, raw);
    let className = this.classNames.get(propKey);
    if (!className) {
      className = this.getClassName(property, raw);
      this.classNames.set(propKey, className);
    }
    return className;
  };
  /**
   * Whether a given property exists in the config
   */
  has = (prop) => {
    return this.configs.has(prop);
  };
  /**
   * Get or create the resolved styles for a given property and value
   */
  getOrCreateStyle = (prop, value) => {
    const propKey = this.getPropKey(prop, value);
    const styles = this.styles.get(propKey);
    if (styles)
      return styles;
    const config = this.configs.get(prop);
    const raw = config ? this.getPropertyRawValue(config, value) : value;
    this.setStyles(prop, raw, value, propKey);
    return this.styles.get(propKey);
  };
  /**
   * Returns the resolved className and styles for a given property and value
   */
  transform = (prop, value) => {
    if (value == null) {
      return { className: "", styles: {} };
    }
    const key = this.resolveShorthand(prop);
    let styleValue = (0, import_shared18.getArbitraryValue)(value);
    if ((0, import_shared18.isString)(styleValue)) {
      styleValue = this.tokens.expandReferenceInValue(styleValue);
    }
    return (0, import_shared18.compact)({
      layer: this.configs.get(key)?.layer,
      className: this.getOrCreateClassName(key, (0, import_shared18.withoutSpace)(value)),
      styles: this.getOrCreateStyle(key, styleValue)
    });
  };
  /**
   * All keys including shorthand keys
   */
  keys = () => {
    const shorthands = Array.from(this.shorthands.keys());
    const properties = Object.keys(this.config);
    return [...shorthands, ...properties];
  };
  /**
   * Returns a map of the property keys and their shorthands
   */
  getPropShorthandsMap = () => {
    const shorthandsByProp = /* @__PURE__ */ new Map();
    this.shorthands.forEach((prop, shorthand) => {
      const list = shorthandsByProp.get(prop) ?? [];
      list.push(shorthand);
      shorthandsByProp.set(prop, list);
    });
    return shorthandsByProp;
  };
  /**
   * Returns the shorthands for a given property
   */
  getPropShorthands = (prop) => {
    return this.getPropShorthandsMap().get(prop) ?? [];
  };
  /**
   * Whether a given property is deprecated
   */
  isDeprecated = (prop) => {
    return this.deprecated.has(prop);
  };
};

// src/context.ts
var defaults = (config) => ({
  cssVarRoot: ":where(:root, :host)",
  jsxFactory: "styled",
  jsxStyleProps: "all",
  outExtension: "mjs",
  shorthands: true,
  syntax: "object-literal",
  ...config,
  layers: {
    reset: "reset",
    base: "base",
    tokens: "tokens",
    recipes: "recipes",
    utilities: "utilities",
    ...config.layers
  }
});
var Context = class {
  constructor(conf) {
    this.conf = conf;
    const config = defaults(conf.config);
    const theme = config.theme ?? {};
    conf.config = config;
    this.tokens = this.createTokenDictionary(theme, config.themes);
    this.hooks["tokens:created"]?.({
      configure: (opts) => {
        if (opts.formatTokenName) {
          this.tokens.formatTokenName = opts.formatTokenName;
        }
        if (opts.formatCssVar) {
          this.tokens.formatCssVar = opts.formatCssVar;
        }
      }
    });
    this.tokens.init();
    this.utility = this.createUtility(config);
    this.hooks["utility:created"]?.({
      configure: (opts) => {
        if (opts.toHash) {
          this.utility.toHash = opts.toHash;
        }
      }
    });
    this.conditions = this.createConditions(config);
    this.patterns = new Patterns({
      config,
      tokens: this.tokens,
      utility: this.utility,
      helpers: import_shared19.patternFns
    });
    this.studio = { outdir: `${config.outdir}-studio`, ...conf.config.studio };
    this.setupProperties();
    this.recipes = this.createRecipes(theme);
    this.encoder = new StyleEncoder({
      utility: this.utility,
      recipes: this.recipes,
      conditions: this.conditions,
      patterns: this.patterns,
      isTemplateLiteralSyntax: this.isTemplateLiteralSyntax,
      isValidProperty: this.isValidProperty
    });
    this.decoder = new StyleDecoder({
      conditions: this.conditions,
      utility: this.utility,
      recipes: this.recipes,
      hash: this.hash
    });
    this.setupCompositions(theme);
    this.recipes.save(this.baseSheetContext);
    this.staticCss = new StaticCss({
      config,
      utility: this.utility,
      patterns: this.patterns,
      recipes: this.recipes,
      createSheet: this.createSheet,
      encoder: this.encoder,
      decoder: this.decoder
    });
    this.jsx = new JsxEngine({
      patterns: this.patterns,
      recipes: this.recipes,
      config
    });
    this.imports = new ImportMap({
      jsx: this.jsx,
      conf: this.conf,
      config: this.config,
      patterns: this.patterns,
      recipes: this.recipes,
      isValidProperty: this.isValidProperty
    });
    this.paths = new PathEngine({
      config: this.config
    });
    this.file = new FileEngine({
      config: this.config
    });
    this.globalVars = new GlobalVars({
      globalVars: this.config.globalVars,
      cssVarRoot: this.config.cssVarRoot
    });
    this.messages = getMessages({
      jsx: this.jsx,
      config: this.config,
      tokens: this.tokens,
      recipes: this.recipes,
      patterns: this.patterns,
      isTemplateLiteralSyntax: this.isTemplateLiteralSyntax
    });
    this.parserOptions = {
      hash: this.hash,
      compilerOptions: this.conf.tsconfig?.compilerOptions ?? {},
      recipes: this.recipes,
      patterns: this.patterns,
      jsx: this.jsx,
      syntax: config.syntax,
      encoder: this.encoder,
      tsOptions: this.conf.tsOptions,
      join: (...paths) => paths.join("/"),
      imports: this.imports
    };
    this.hooksApi = new HooksApi(this);
    this.hooks["context:created"]?.({ ctx: this.hooksApi, logger: import_logger5.logger });
  }
  studio;
  // Engines
  tokens;
  utility;
  recipes;
  conditions;
  patterns;
  staticCss;
  jsx;
  imports;
  paths;
  file;
  globalVars;
  encoder;
  decoder;
  hooksApi;
  // Props
  properties;
  isValidProperty;
  messages;
  parserOptions;
  get config() {
    return this.conf.config;
  }
  get hooks() {
    return this.conf.hooks ?? {};
  }
  get isTemplateLiteralSyntax() {
    return this.config.syntax === "template-literal";
  }
  get hash() {
    return {
      tokens: (0, import_shared19.isBoolean)(this.config.hash) ? this.config.hash : this.config.hash?.cssVar,
      className: (0, import_shared19.isBoolean)(this.config.hash) ? this.config.hash : this.config.hash?.className
    };
  }
  get prefix() {
    return {
      tokens: (0, import_shared19.isString)(this.config.prefix) ? this.config.prefix : this.config.prefix?.cssVar,
      className: (0, import_shared19.isString)(this.config.prefix) ? this.config.prefix : this.config.prefix?.className
    };
  }
  createTokenDictionary = (theme, themeVariants) => {
    return new import_token_dictionary.TokenDictionary({
      breakpoints: theme.breakpoints,
      tokens: theme.tokens,
      semanticTokens: theme.semanticTokens,
      themes: themeVariants,
      prefix: this.prefix.tokens,
      hash: this.hash.tokens
    });
  };
  createUtility = (config) => {
    return new Utility({
      prefix: this.prefix.className,
      tokens: this.tokens,
      config: this.isTemplateLiteralSyntax ? {} : Object.assign({}, config.utilities),
      separator: config.separator,
      shorthands: config.shorthands,
      strictTokens: config.strictTokens,
      keyframes: config.theme?.keyframes
    });
  };
  createConditions = (config) => {
    return new Conditions({
      conditions: config.conditions,
      containerNames: config.theme?.containerNames,
      containerSizes: config.theme?.containerSizes,
      breakpoints: config.theme?.breakpoints,
      themes: config.themes
    });
  };
  createLayers = (layers) => {
    return new Layers(layers);
  };
  setupCompositions = (theme) => {
    const { textStyles, layerStyles } = theme;
    const compositions = (0, import_shared19.compact)({ textStyle: textStyles, layerStyle: layerStyles });
    const stylesheetCtx = {
      ...this.baseSheetContext,
      layers: this.createLayers(this.config.layers)
    };
    for (const [key, values] of Object.entries(compositions)) {
      this.properties.add(key);
      const flatValues = (0, import_shared19.flatten)(values ?? {});
      const config = {
        layer: "compositions",
        className: key,
        values: Object.keys(flatValues),
        transform: (value) => {
          return transformStyles(stylesheetCtx, flatValues[value], key + "." + value);
        }
      };
      this.utility.register(key, config);
    }
  };
  setupProperties = () => {
    this.properties = /* @__PURE__ */ new Set(["css", ...this.utility.keys(), ...this.conditions.keys()]);
    this.isValidProperty = (0, import_shared19.memo)((key) => this.properties.has(key) || (0, import_is_valid_prop.isCssProperty)(key));
  };
  get baseSheetContext() {
    return {
      conditions: this.conditions,
      utility: this.utility,
      hash: this.hash.className,
      encoder: this.encoder,
      decoder: this.decoder,
      hooks: this.hooks,
      isValidProperty: this.isValidProperty,
      browserslist: this.config.browserslist,
      lightningcss: this.config.lightningcss,
      polyfill: this.config.polyfill,
      cssVarRoot: this.config.cssVarRoot,
      helpers: import_shared19.patternFns,
      globalVars: this.globalVars
    };
  }
  createSheet = () => {
    return new Stylesheet({
      ...this.baseSheetContext,
      layers: this.createLayers(this.config.layers)
    });
  };
  createRecipes = (theme) => {
    const recipeConfigs = Object.assign({}, theme.recipes ?? {}, theme.slotRecipes ?? {});
    return new Recipes(recipeConfigs);
  };
  isValidLayerParams = (params) => {
    const names = new Set(params.split(",").map((name) => name.trim()));
    return names.size >= 5 && Object.values(this.config.layers).every((name) => names.has(name));
  };
};

// src/selector.ts
var import_postcss_selector_parser = __toESM(require("postcss-selector-parser"));
var import_ts_pattern2 = require("ts-pattern");
var parentNestingRegex = /\s&/g;
function extractParentSelectors(selector) {
  const result = /* @__PURE__ */ new Set();
  (0, import_postcss_selector_parser.default)((selectors) => {
    selectors.each((selector2) => {
      const condition = parseCondition(selector2.toString());
      (0, import_ts_pattern2.match)(condition).with({ type: "parent-nesting" }, () => {
        result.add(selector2.toString().replace(parentNestingRegex, "").trim());
      }).otherwise(() => {
      });
    });
  }).processSync(selector);
  const finalized = Array.from(result).join(", ").trim();
  return result.size > 1 ? `:where(${finalized})` : finalized;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Breakpoints,
  Conditions,
  Context,
  FileMatcher,
  ImportMap,
  JsxEngine,
  Layers,
  Patterns,
  Recipes,
  RuleProcessor,
  StaticCss,
  StyleDecoder,
  StyleEncoder,
  Stylesheet,
  Utility,
  expandNestedCss,
  extractParentSelectors,
  messages,
  optimizeCss,
  stringify
});
