## 1.1.18 (2025-04-17)

### Bug fixes

Fix parsing of match guards with conditional tests.

## 1.1.17 (2025-03-29)

### Bug fixes

Support escaped quotes inside raw strings.

## 1.1.16 (2025-03-05)

### Bug fixes

Allow type declarations on rest parameters.

## 1.1.15 (2024-12-06)

### Bug fixes

Fix an issue where a commented line at the end of the input would emit an error node.

## 1.1.14 (2024-05-25)

### Bug fixes

Allow dotted names and list of imports in `import` statements.

## 1.1.13 (2024-03-18)

### Bug fixes

Fix an issue where single-quoted strings inside format strings weren't tokenized properly.

## 1.1.12 (2024-03-12)

### Bug fixes

Emit syntax nodes for backslash escapes in strings.

## 1.1.11 (2024-01-16)

### Bug fixes

Support the `=` flag to values in format strings.

## 1.1.10 (2023-12-28)

### Bug fixes

Tag comments and strings as isolating for the purpose of bidirectional text.

## 1.1.9 (2023-10-20)

### Bug fixes

Fix parsing of argument lists that are a comprehension that start with an assignment.

Add support for PEP 654 `except*` syntax.

Implement PEP 695 type parameter syntax.

## 1.1.8 (2023-07-03)

### Bug fixes

Make the package work with new TS resolution styles.

## 1.1.7 (2023-05-27)

### Bug fixes

Fix a bug that broke handling of escaped quotes in format strings.

## 1.1.6 (2023-05-18)

### Bug fixes

Fix parsing of blank lines at the start of the document.

## 1.1.5 (2023-04-28)

### Bug fixes

Fix a bug that caused triple-quoted format strings with quotes in them to be parsed incorrectly.

## 1.1.4 (2023-03-30)

### Bug fixes

Make sure blocks stop at their final newline, and don't include any following blank lines.

## 1.1.3 (2023-03-13)

### Bug fixes

Fix parsing of empty tuple expressions.

Allow `:=` in subscript index expressions.

## 1.1.2 (2023-02-03)

### Bug fixes

Fix a bug that caused binary * and ** operators to be highlighted as keywords/modifiers.

## 1.1.1 (2022-10-31)

### Bug fixes

Add missing highlighting info for `match` and `case` keywords.

## 1.1.0 (2022-07-27)

### New features

Parse Python 3.10 match statements (PEP 634).

## 1.0.0 (2022-06-06)

### New features

First stable version.

## 0.16.1 (2022-05-24)

### Bug fixes

Fix a bug that caused `f"""` strings to be terminated at the first double quote in their content.

## 0.16.0 (2022-04-20)

### Breaking changes

Move to 0.16 serialized parser format.

### New features

The parser now includes syntax highlighting information in its node types.

## 0.15.1 (2022-03-16)

### Bug fixes

Fix a bug where indentation with tabs was tracked incorrectly, leading to spurious indent tokens and malformed trees.

## 0.15.0 (2021-08-11)

### Breaking changes

The module's name changed from `lezer-python` to `@lezer/python`.

Upgrade to the 0.15.0 lezer interfaces.

## 0.13.7 (2021-07-12)

### Bug fixes

Fix a bug that caused newlines to be disallowed in argument and parameter lists.

## 0.13.6 (2021-02-17)

### Bug fixes

Fix a bug where incremental parses could get confused about block nesting.

## 0.13.5 (2021-02-11)

### Bug fixes

Fixes an inefficiency in the parsing of large strings.

## 0.13.4 (2021-01-27)

### Bug fixes

Fix a bug where keywords like `else` or `except` would be consumed even if they don't match the indentation of the parent statement.

## 0.13.3 (2021-01-25)

### Bug fixes

Fix an issue where non-indented lines after a colon were parsed as part of the body.

## 0.13.2 (2021-01-19)

### Bug fixes

Add support for return statements without expressions.

## 0.13.1 (2020-12-04)

### Bug fixes

Fix versions of lezer packages depended on.

## 0.13.0 (2020-12-04)

## 0.12.0 (2020-10-23)

### Breaking changes

Adjust to changed serialized parser format.

## 0.11.1 (2020-09-26)

### Bug fixes

Fix lezer depencency versions

## 0.11.0 (2020-09-26)

### Breaking changes

Follow change in serialized parser format.

## 0.10.0 (2020-08-07)

### Breaking changes

Upgrade to 0.10 parser serialization

## 0.9.0 (2020-06-08)

### Breaking changes

Upgrade to 0.9 parser serialization

## 0.8.1 (2020-04-15)

### Bug fixes

Include TypeScript definition file.

Only treat `print` as a keyword when it looks like a Python 2 style print statement.

## 0.8.0 (2020-04-15)

### New Features

First numbered release.
