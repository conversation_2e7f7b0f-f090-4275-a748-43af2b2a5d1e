const fs = require('fs');
const path = require('path');
const glob = require('glob');

function fixSyntaxErrors(content) {
  let fixed = content;
  
  // 1. 修复 import 语句的引号问题
  fixed = fixed.replace(/import\s+([^;]+);'/g, "import $1;");
  fixed = fixed.replace(/from\s*'([^']+)';'/g, "from '$1';");
  fixed = fixed.replace(/from'([^']+)'/g, "from '$1'");
  fixed = fixed.replace(/from\s+'([^']+)'/g, "from '$1'");
  
  // 2. 修复 JSX 属性中的引号问题
  fixed = fixed.replace(/component=\{([^}]+)\}'/g, "component={$1}");
  fixed = fixed.replace(/path="([^"]+)'/g, 'path="$1"');
  
  // 3. 修复对象属性中的引号问题  
  fixed = fixed.replace(/:\s*'([^']+)','/g, ": '$1',");
  fixed = fixed.replace(/}\s*}>'/g, "}}>");
  fixed = fixed.replace(/}\s*};'/g, "});");
  
  // 4. 修复 JSX 结束标签的引号问题
  fixed = fixed.replace(/<\/([^>]+)>'/g, "</$1>");
  
  // 5. 修复注释中的引号问题
  fixed = fixed.replace(/\/\/\s*([^']+)'/g, "// $1");
  
  // 6. 修复条件语句中的引号问题
  fixed = fixed.replace(/if\s*\([^)]+\)\s*\{'/g, function(match) {
    return match.replace("{'", "{");
  });
  
  // 7. 修复函数和代码块结尾的多余引号
  fixed = fixed.replace(/}'/g, "}");
  fixed = fixed.replace(/];'/g, "];");
  
  // 8. 修复字符串中的多余引号
  fixed = fixed.replace(/','/g, ",");
  fixed = fixed.replace(/';'/g, "';");
  
  // 9. 修复 lazy import 语句
  fixed = fixed.replace(/lazy\(\(\)\s*=>\s*import\('([^']+)'\)\);'/g, "lazy(() => import('$1'));");
  
  return fixed;
}

// 获取所有需要修复的文件
const patterns = [
  'src/**/*.{ts,tsx}',
  'src/*.{ts,tsx}'
];

let allFiles = [];
patterns.forEach(pattern => {
  const files = glob.sync(pattern, {
    ignore: ['node_modules/**', 'dist/**', '**/*.d.ts']
  });
  allFiles = allFiles.concat(files);
});

// 去重
allFiles = [...new Set(allFiles)];

console.log(`🔧 开始修复 ${allFiles.length} 个文件的语法错误...`);

let fixedCount = 0;
let errorCount = 0;

allFiles.forEach(filePath => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixed = fixSyntaxErrors(content);
    
    if (content !== fixed) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ 修复: ${filePath}`);
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ 错误处理文件 ${filePath}:`, error.message);
    errorCount++;
  }
});

console.log(`\n🎉 修复完成!`);
console.log(`✅ 成功修复: ${fixedCount} 个文件`);
console.log(`❌ 处理失败: ${errorCount} 个文件`);