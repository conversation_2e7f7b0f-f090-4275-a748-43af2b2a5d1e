import { createSignal, createEffect, onMount, Show, For, createMemo } from 'solid-js'
import { createStore } from 'solid-js/store'
import { css } from '../../styled-system/css'
import { formatTime } from '../../utils/formatters'
import { useAtom } from 'jotai'
import { tradingAtom } from '../../stores/atoms'

interface Order {
  orderNo: string
  symbol: string
  direction: 'buy' | 'sell'
  orderType: 'limit' | 'market' | 'stop' | 'takeProfit'
  price: number
  quantity: number
  filledQuantity: number
  status: 'pending' | 'filled' | 'cancelled' | 'partiallyFilled'
  createTime: string
  updateTime: string
  avgPrice?: number
  filledAmount?: number
  commission?: number
}

interface ModifyForm {
  orderNo: string
  symbol: string
  price: number
  quantity: number
}

interface Props {
  onOrderSelect?: (order: Order) => void
  onOrderModify?: (orderNo: string, data: Partial<Order>) => void
  onOrderCancel?: (orderNo: string) => void
}

export function OrderManagement(props: Props) {
  // State management
  const [trading] = useAtom(tradingAtom)
  const [activeTab, setActiveTab] = createSignal<'pending' | 'filled' | 'cancelled'>('pending')
  const [loading, setLoading] = createSignal(false)
  const [modifying, setModifying] = createSignal(false)
  const [modifyDialogVisible, setModifyDialogVisible] = createSignal(false)
  const [detailDialogVisible, setDetailDialogVisible] = createSignal(false)
  const [selectedOrder, setSelectedOrder] = createSignal<Order | null>(null)
  
  // Mock orders data
  const [orders, setOrders] = createStore<Order[]>([
    {
      orderNo: 'ORD001',
      symbol: 'IF2312',
      direction: 'buy',
      orderType: 'limit',
      price: 3850.0,
      quantity: 200,
      filledQuantity: 100,
      status: 'partiallyFilled',
      createTime: '2024-01-15 09:30:00',
      updateTime: '2024-01-15 09:35:00',
      avgPrice: 3849.5,
      filledAmount: 384950,
      commission: 15.4
    },
    {
      orderNo: 'ORD002',
      symbol: 'IC2312',
      direction: 'sell',
      orderType: 'market',
      price: 0,
      quantity: 100,
      filledQuantity: 100,
      status: 'filled',
      createTime: '2024-01-15 10:15:00',
      updateTime: '2024-01-15 10:15:30',
      avgPrice: 5420.2,
      filledAmount: 542020,
      commission: 21.68
    },
    {
      orderNo: 'ORD003',
      symbol: 'IH2312',
      direction: 'buy',
      orderType: 'limit',
      price: 2680.0,
      quantity: 300,
      filledQuantity: 0,
      status: 'cancelled',
      createTime: '2024-01-15 11:00:00',
      updateTime: '2024-01-15 11:30:00'
    }
  ])

  const [modifyForm, setModifyForm] = createStore<ModifyForm>({
    orderNo: '',
    symbol: '',
    price: 0,
    quantity: 0
  })

  // Computed values
  const pendingOrders = createMemo(() => {
    return orders.filter(order => order.status === 'pending' || order.status === 'partiallyFilled')
  })

  const filledOrders = createMemo(() => {
    return orders.filter(order => order.status === 'filled')
  })

  const cancelledOrders = createMemo(() => {
    return orders.filter(order => order.status === 'cancelled')
  })

  const currentOrders = createMemo(() => {
    switch (activeTab()) {
      case 'pending :
        return pendingOrders()
      case 'filled :
        return filledOrders()
      case 'cancelled :
        return cancelledOrders()
      default:
        return []
    }
  })

  // Methods
  const getOrderTypeText = (type: string): string => {
    const types = {
      limit: '限价',
      market: '市价',
      stop: '止损',
      takeProfit: '止盈'
    }
    return types[type as keyof typeof types] || type
  }

  const getStatusType = (status: string): string => {
    const types = {
      pending: 'warning',
      filled: 'success',
      cancelled: 'info',
      partiallyFilled: 'primary'
    }
    return types[status as keyof typeof types] || 'info'
  }

  const getStatusText = (status: string): string => {
    const texts = {
      pending: '待成交',
      filled: '已成交',
      cancelled: '已撤销',
      partiallyFilled: '部分成交'
    }
    return texts[status as keyof typeof texts] || status
  }

  const getStatusColor = (status: string): string => {
    const colors = {
      pending: '#e6a23c',
      filled: '#67c23a',
      cancelled: '#909399',
      partiallyFilled: '#409eff'
    }
    return colors[status as keyof typeof colors] || '#909399'
  }

  const refreshOrders = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      console.log('订单刷新成功')
    } catch (error) {
      console.error('订单刷新失败')
    } finally {
      setLoading(false)
    }
  }

  const cancelAllOrders = async () => {
    if (pendingOrders().length === 0) return

    const confirmed = confirm('确定要撤销所有待成交订单吗？')
    if (!confirmed) return

    setLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Update order status
      setOrders(prev => prev.map(order => {
        if (order.status === 'pending' || order.status === 'partiallyFilled') {
          return {
            ...order,
            status: 'cancelled' as const,
            updateTime: new Date().toISOString()
          }
        }
        return order
      }))

      console.log('所有订单已撤销')
    } catch (error) {
      console.error('撤销失败')
    } finally {
      setLoading(false)
    }
  }

  const modifyOrder = (order: Order) => {
    setModifyForm({
      orderNo: order.orderNo,
      symbol: order.symbol,
      price: order.price,
      quantity: order.quantity
    })
    setModifyDialogVisible(true)
  }

  const confirmModify = async () => {
    if (!modifyForm.price || !modifyForm.quantity) {
      console.error('请填写完整信息')
      return
    }

    setModifying(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Update order
      setOrders(prev => prev.map(order => {
        if (order.orderNo === modifyForm.orderNo) {
          return {
            ...order,
            price: modifyForm.price,
            quantity: modifyForm.quantity,
            updateTime: new Date().toISOString()
          }
        }
        return order
      }))

      console.log('订单修改成功')
      setModifyDialogVisible(false)
      props.onOrderModify?.(modifyForm.orderNo, {
        price: modifyForm.price,
        quantity: modifyForm.quantity
      })
    } catch (error) {
      console.error('订单修改失败')
    } finally {
      setModifying(false)
    }
  }

  const cancelOrder = async (order: Order) => {
    const confirmed = confirm(`确定要撤销订单 ${order.orderNo} 吗？`)
    if (!confirmed) return

    setLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))

      // Update order status
      setOrders(prev => prev.map(o => {
        if (o.orderNo === order.orderNo) {
          return {
            ...o,
            status: 'cancelled' as const,
            updateTime: new Date().toISOString()
          }
        }
        return o
      }))

      console.log('订单已撤销')
      props.onOrderCancel?.(order.orderNo)
    } catch (error) {
      console.error('撤销失败')
    } finally {
      setLoading(false)
    }
  }

  const viewOrderDetail = (order: Order) => {
    setSelectedOrder(order)
    setDetailDialogVisible(true)
    props.onOrderSelect?.(order)
  }

  const resetModifyForm = () => {
    setModifyForm({
      orderNo: '',
      symbol: '',
      price: 0,
      quantity: 0
    })
  }

  // Effects
  onMount(() => {
    refreshOrders()
  })

  return (
    <div class={css({
      bg: 'white',
      borderRadius: '8px',
      padding: '20px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
    })}>
      {/* Header */}
      <div class={css({
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        paddingBottom: '16px',
        borderBottom: '1px solid #ebeef5',
        '@media (max-width: 768px)': {
          flexDirection: 'column',
          gap: '12px',
          alignItems: 'stretch'
        }
      })}>
        <div class={css({ display: 'flex', gap: '8px' })}>
          <button
            onClick={() => setActiveTab('pending')}
            class={css({
              padding: '8px 16px',
              border: '1px solid',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              borderColor: activeTab() === 'pending' ? '#409eff' : '#dcdfe6',
              bg: activeTab() === 'pending' ? '#409eff' : 'white',
              color: activeTab() === 'pending' ? 'white' : '#606266'
            })}
          >
            待成交 ({pendingOrders().length})
          </button>
          <button
            onClick={() => setActiveTab('filled')}
            class={css({
              padding: '8px 16px',
              border: '1px solid',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              borderColor: activeTab() === 'filled' ? '#409eff' : '#dcdfe6',
              bg: activeTab() === 'filled' ? '#409eff' : 'white',
              color: activeTab() === 'filled' ? 'white' : '#606266'
            })}
          >
            已成交 ({filledOrders().length})
          </button>
          <button
            onClick={() => setActiveTab('cancelled')}
            class={css({
              padding: '8px 16px',
              border: '1px solid',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              borderColor: activeTab() === 'cancelled' ? '#409eff' : '#dcdfe6',
              bg: activeTab() === 'cancelled' ? '#409eff' : 'white',
              color: activeTab() === 'cancelled' ? 'white' : '#606266'
            })}
          >
            已撤销 ({cancelledOrders().length})
          </button>
        </div>

        <div class={css({ display: 'flex', gap: '8px' })}>
          <button
            onClick={refreshOrders}
            disabled={loading()}
            class={css({
              padding: '8px 16px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              bg: 'white',
              opacity: loading() ? 0.6 : 1
            })}
          >
            {loading() ? '刷新中...' : '🔄 刷新'}
          </button>
          <button
            onClick={cancelAllOrders}
            disabled={pendingOrders().length === 0}
            class={css({
              padding: '8px 16px',
              border: '1px solid #f56c6c',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              bg: 'white',
              color: '#f56c6c',
              opacity: pendingOrders().length === 0 ? 0.6 : 1
            })}
          >
            全部撤销
          </button>
        </div>
      </div>

      {/* Order Table */}
      <div class={css({
        border: '1px solid #ebeef5',
        borderRadius: '4px',
        overflow: 'hidden'
      })}>
        <div class={css({
          display: 'grid',
          gridTemplateColumns: '120px 100px 60px 80px 80px 80px 80px 80px 140px 120px',
          bg: '#f5f7fa',
          borderBottom: '1px solid #ebeef5',
          fontSize: '14px',
          fontWeight: 'bold'
        })}>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>订单号</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>品种</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>方向</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>类型</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>价格</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>数量</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>已成交</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>状态</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>委托时间</div>
          <div class={css({ padding: '12px 8px' })}>操作</div>
        </div>

        <Show when={currentOrders().length > 0} fallback={
          <div class={css({
            padding: '40px',
            textAlign: 'center',
            color: '#909399',
            fontSize: '14px'
          })}>
            暂无数据
          </div>
        }>
          <div class={css({ maxHeight: '400px', overflowY: 'auto' })}>
            <For each={currentOrders()}>
              {(order) => (
                <div class={css({
                  display: 'grid',
                  gridTemplateColumns: '120px 100px 60px 80px 80px 80px 80px 80px 140px 120px',
                  borderBottom: '1px solid #ebeef5',
                  fontSize: '14px',
                  '&:hover': { bg: '#f5f7fa' }
                })}>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>
                    {order.orderNo}
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>
                    {order.symbol}
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>
                    <span class={css({
                      padding: '2px 6px',
                      borderRadius: '3px',
                      fontSize: '12px',
                      bg: order.direction === 'buy' ? '#f0f9ff' : '#fef2f2',
                      color: order.direction === 'buy' ? '#1d4ed8' : '#dc2626'
                    })}>
                      {order.direction === 'buy' ? '买入' : '卖出'}
                    </span>
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>
                    {getOrderTypeText(order.orderType)}
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>
                    {order.orderType === 'market' ? '市价' : order.price.toFixed(2)}
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>
                    {order.quantity}
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>
                    {order.filledQuantity}
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>
                    <span class={css({
                      padding: '2px 6px',
                      borderRadius: '3px',
                      fontSize: '12px',
                      color: 'white',
                      bg: getStatusColor(order.status)
                    })}>
                      {getStatusText(order.status)}
                    </span>
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', fontSize: '12px' })}>
                    {new Date(order.createTime).toLocaleString('zh-CN')}
                  </div>
                  <div class={css({ padding: '12px 8px', display: 'flex', gap: '4px' })}>
                    <Show when={order.status === 'pending'}>
                      <button
                        onClick={() => modifyOrder(order)}
                        class={css({
                          padding: '4px 8px',
                          border: '1px solid #409eff',
                          borderRadius: '3px',
                          fontSize: '12px',
                          cursor: 'pointer',
                          bg: 'white',
                          color: '#409eff'
                        })}
                      >
                        修改
                      </button>
                      <button
                        onClick={() => cancelOrder(order)}
                        class={css({
                          padding: '4px 8px',
                          border: '1px solid #f56c6c',
                          borderRadius: '3px',
                          fontSize: '12px',
                          cursor: 'pointer',
                          bg: 'white',
                          color: '#f56c6c'
                        })}
                      >
                        撤销
                      </button>
                    </Show>
                    <Show when={order.status === 'filled'}>
                      <button
                        onClick={() => viewOrderDetail(order)}
                        class={css({
                          padding: '4px 8px',
                          border: '1px solid #909399',
                          borderRadius: '3px',
                          fontSize: '12px',
                          cursor: 'pointer',
                          bg: 'white',
                          color: '#909399'
                        })}
                      >
                        详情
                      </button>
                    </Show>
                  </div>
                </div>
              )}
            </For>
          </div>
        </Show>
      </div>

      {/* Modify Order Dialog */}
      <Show when={modifyDialogVisible()}>
        <div class={css({
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          bg: 'rgba(0, 0, 0, 0.5)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        })}>
          <div class={css({
            bg: 'white',
            borderRadius: '8px',
            padding: '24px',
            width: '400px',
            maxWidth: '90vw'
          })}>
            <h3 class={css({ margin: '0 0 16px 0', fontSize: '18px' })}>修改订单</h3>
            
            <div class={css({ marginBottom: '16px' })}>
              <label class={css({ display: 'block', marginBottom: '4px', fontSize: '14px' })}>订单号</label>
              <input
                value={modifyForm.orderNo}
                disabled
                class={css({
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  bg: '#f5f7fa',
                  color: '#909399'
                })}
              />
            </div>

            <div class={css({ marginBottom: '16px' })}>
              <label class={css({ display: 'block', marginBottom: '4px', fontSize: '14px' })}>品种</label>
              <input
                value={modifyForm.symbol}
                disabled
                class={css({
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  bg: '#f5f7fa',
                  color: '#909399'
                })}
              />
            </div>

            <div class={css({ marginBottom: '16px' })}>
              <label class={css({ display: 'block', marginBottom: '4px', fontSize: '14px' })}>价格</label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={modifyForm.price}
                onInput={(e) => setModifyForm('price', Number(e.currentTarget.value))}
                class={css({
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px'
                })}
              />
            </div>

            <div class={css({ marginBottom: '24px' })}>
              <label class={css({ display: 'block', marginBottom: '4px', fontSize: '14px' })}>数量</label>
              <input
                type="number"
                step="100"
                min="100"
                value={modifyForm.quantity}
                onInput={(e) => setModifyForm('quantity', Number(e.currentTarget.value))}
                class={css({
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px'
                })}
              />
            </div>

            <div class={css({ display: 'flex', gap: '8px', justifyContent: 'flex-end' })}>
              <button
                onClick={() => {
                  setModifyDialogVisible(false)
                  resetModifyForm()
                }}
                class={css({
                  padding: '8px 16px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  bg: 'white'
                })}
              >
                取消
              </button>
              <button
                onClick={confirmModify}
                disabled={modifying()}
                class={css({
                  padding: '8px 16px',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  bg: '#409eff',
                  color: 'white',
                  opacity: modifying() ? 0.6 : 1
                })}
              >
                {modifying() ? '修改中...' : '确认修改'}
              </button>
            </div>
          </div>
        </div>
      </Show>

      {/* Order Detail Dialog */}
      <Show when={detailDialogVisible()}>
        <div class={css({
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          bg: 'rgba(0, 0, 0, 0.5)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        })}>
          <div class={css({
            bg: 'white',
            borderRadius: '8px',
            padding: '24px',
            width: '600px',
            maxWidth: '90vw',
            maxHeight: '80vh',
            overflowY: 'auto'
          })}>
            <h3 class={css({ margin: '0 0 16px 0', fontSize: '18px' })}>订单详情</h3>
            
            <Show when={selectedOrder()}>
              {(order) => (
                <div class={css({ padding: '16px 0' })}>
                  {/* Basic Info */}
                  <div class={css({ marginBottom: '24px' })}>
                    <h4 class={css({
                      margin: '0 0 12px 0',
                      fontSize: '16px',
                      color: '#303133',
                      borderBottom: '1px solid #ebeef5',
                      paddingBottom: '8px'
                    })}>
                      基本信息
                    </h4>
                    <div class={css({
                      display: 'grid',
                      gridTemplateColumns: 'repeat(2, 1fr)',
                      gap: '12px',
                      '@media (max-width: 768px)': {
                        gridTemplateColumns: '1fr'
                      }
                    })}>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>订单号:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>{order().orderNo}</span>
                      </div>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>品种:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>{order().symbol}</span>
                      </div>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>方向:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>
                          {order().direction === 'buy' ? '买入' : '卖出'}
                        </span>
                      </div>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>类型:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>
                          {getOrderTypeText(order().orderType)}
                        </span>
                      </div>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>委托价格:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>{order().price.toFixed(2)}</span>
                      </div>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>委托数量:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>{order().quantity}</span>
                      </div>
                    </div>
                  </div>

                  {/* Fill Info */}
                  <div class={css({ marginBottom: '24px' })}>
                    <h4 class={css({
                      margin: '0 0 12px 0',
                      fontSize: '16px',
                      color: '#303133',
                      borderBottom: '1px solid #ebeef5',
                      paddingBottom: '8px'
                    })}>
                      成交信息
                    </h4>
                    <div class={css({
                      display: 'grid',
                      gridTemplateColumns: 'repeat(2, 1fr)',
                      gap: '12px',
                      '@media (max-width: 768px)': {
                        gridTemplateColumns: '1fr'
                      }
                    })}>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>已成交数量:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>{order().filledQuantity}</span>
                      </div>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>平均成交价:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>
                          {order().avgPrice?.toFixed(2) || '--'}
                        </span>
                      </div>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>成交金额:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>
                          {order().filledAmount?.toFixed(2) || '--'}
                        </span>
                      </div>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>手续费:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>
                          {order().commission?.toFixed(2) || '--'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Time Info */}
                  <div>
                    <h4 class={css({
                      margin: '0 0 12px 0',
                      fontSize: '16px',
                      color: '#303133',
                      borderBottom: '1px solid #ebeef5',
                      paddingBottom: '8px'
                    })}>
                      时间信息
                    </h4>
                    <div class={css({
                      display: 'grid',
                      gridTemplateColumns: 'repeat(2, 1fr)',
                      gap: '12px',
                      '@media (max-width: 768px)': {
                        gridTemplateColumns: '1fr'
                      }
                    })}>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>委托时间:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>
                          {new Date(order().createTime).toLocaleString('zh-CN')}
                        </span>
                      </div>
                      <div class={css({ display: 'flex', justifyContent: 'space-between', padding: '8px 0' })}>
                        <span class={css({ fontSize: '14px', color: '#606266', fontWeight: 500 })}>更新时间:</span>
                        <span class={css({ fontSize: '14px', color: '#303133' })}>
                          {new Date(order().updateTime).toLocaleString('zh-CN')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </Show>

            <div class={css({ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' })}>
              <button
                onClick={() => setDetailDialogVisible(false)}
                class={css({
                  padding: '8px 16px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  bg: 'white'
                })}
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      </Show>
    </div>
  )
}

export default OrderManagement