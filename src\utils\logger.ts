/**
 * 统一的日志管理工具
 * 仅在开发环境输出日志，生产环境自动静默
 */

// 日志级别
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

// 日志配置
interface LoggerConfig {
  prefix?: string
  level?: LogLevel
  enabled?: boolean
  showTimestamp?: boolean
  showLevel?: boolean
  colors?: boolean
}

// 日志颜色配置
const LOG_COLORS = {
  debug: 'color': '#9CA3AF',
  info: 'color': '#3B82F6',
  warn: 'color': '#F59E0B',
  error: 'color': '#EF4444' }

/**
 * Logger 类
 */
export class Logger {
  private prefix: string
  private level: LogLevel
  private enabled: boolean
  private showTimestamp: boolean
  private showLevel: boolean
  private colors: boolean

  constructor(config: LoggerConfig = {}) {
    this.prefix = config.prefix || ''
    this.level = config.level ?? (import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.ERROR)
    this.enabled = config.enabled ?? import.meta.env.DEV
    this.showTimestamp = config.showTimestamp ?? false
    this.showLevel = config.showLevel ?? true
    this.colors = config.colors ?? true
  }

  /**
   * 格式化消息
   */
  private formatMessage(level: string, message: string): string {
    const parts: string[] = []
    
    if (this.showTimestamp) {
      parts.push(`[${new Date().toISOString()}]`)
    }
    
    if (this.prefix) {
      parts.push(`[${this.prefix}]`)
    }
    
    if (this.showLevel) {
      parts.push(`[${level.toUpperCase()}]`)
    }
    
    parts.push(message)
    
    return parts.join(' ')
  }

  /**
   * 输出日志
   */
  private log(level: LogLevel, levelName: string, message: string, ...args: any[]): void {
    if (!this.enabled || level < this.level) {
      return
    }

    const formattedMessage = this.formatMessage(levelName, message)
    
    if (this.colors && this.prefix) {
      const color = LOG_COLORS[levelName as keyof typeof LOG_COLORS]
      console.log(`%c${formattedMessage}`, color, ...args)
    } else {
      console.log(formattedMessage, ...args)
    }
  }

  /**
   * Debug 级别日志
   */
  debug(message: string, ...args: any[]): void {
    this.log(LogLevel.DEBUG, 'debug', message, ...args)
  }

  /**
   * Info 级别日志
   */
  info(message: string, ...args: any[]): void {
    this.log(LogLevel.INFO, 'info', message, ...args)
  }

  /**
   * Warn 级别日志
   */
  warn(message: string, ...args: any[]): void {
    this.log(LogLevel.WARN, 'warn', message, ...args)
  }

  /**
   * Error 级别日志
   */
  error(message: string, ...args: any[]): void {
    this.log(LogLevel.ERROR, 'error', message, ...args)
  }

  /**
   * 条件日志
   */
  assert(condition: boolean, message: string, ...args: any[]): void {
    if (!condition) {
      this.error(message, ...args)
    }
  }

  /**
   * 计时开始
   */
  time(label: string): void {
    if (this.enabled) {
      console.time(`[${this.prefix}] ${label}`)
    }
  }

  /**
   * 计时结束
   */
  timeEnd(label: string): void {
    if (this.enabled) {
      console.timeEnd(`[${this.prefix}] ${label}`)
    }
  }

  /**
   * 分组开始
   */
  group(label: string): void {
    if (this.enabled) {
      console.group(`[${this.prefix}] ${label}`)
    }
  }

  /**
   * 分组结束
   */
  groupEnd(): void {
    if (this.enabled) {
      console.groupEnd()
    }
  }

  /**
   * 表格输出
   */
  table(data: any): void {
    if (this.enabled) {
      console.table(data)
    }
  }
}

/**
 * 创建模块专用 logger
 */
export function createLogger(prefix: string, config?: Partial<LoggerConfig>): Logger {
  return new Logger({
    prefix,
    ...config
  })
}

// 创建默认的全局 logger
export const logger = new Logger({
  prefix: 'App',
  showTimestamp: import.meta.env.DEV,
  showLevel: true
})

// 创建模块专用的 loggers
export const wsLogger = createLogger('WebSocket')
export const apiLogger = createLogger('API')
export const storeLogger = createLogger('Store')
export const routerLogger = createLogger('Router')
export const workerLogger = createLogger('Worker')
export const chartLogger = createLogger('Chart')
export const editorLogger = createLogger('Editor')

// 导出默认 logger
export default logger