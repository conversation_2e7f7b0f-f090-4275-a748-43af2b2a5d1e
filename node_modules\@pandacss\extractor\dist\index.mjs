// src/get-typeof-literal.ts
import { PandaError } from "@pandacss/shared";
var getTypeOfLiteral = (value) => {
  if (Array.isArray(value))
    return "array";
  if (typeof value === "string")
    return "string";
  if (typeof value === "number")
    return "number";
  if (typeof value === "boolean")
    return "boolean";
  if (value === null)
    return "null";
  if (value === void 0)
    return "undefined";
  throw new PandaError("UNKNOWN_TYPE", `Unexpected literal type: ${value}`);
};

// src/utils.ts
import { Node } from "ts-morph";
var isNotNullish = (element) => element != null;
var isNullish = (element) => element == null;
var isTruthyOrZero = (element) => !!element || element === 0;
var isObject = (value) => value != null && typeof value === "object";
var isArray = (value) => Array.isArray(value);
var isPrimitiveType = (value) => {
  const type = typeof value;
  return type === "string" || type === "number" || type === "boolean" || value === null || value === void 0;
};
var unwrapExpression = (node) => {
  if (Node.isAsExpression(node)) {
    return unwrapExpression(node.getExpression());
  }
  if (Node.isParenthesizedExpression(node)) {
    return unwrapExpression(node.getExpression());
  }
  if (Node.isNonNullExpression(node)) {
    return unwrapExpression(node.getExpression());
  }
  if (Node.isTypeAssertion(node)) {
    return unwrapExpression(node.getExpression());
  }
  if (Node.isSatisfiesExpression(node)) {
    return unwrapExpression(node.getExpression());
  }
  return node;
};
var getComponentName = (node) => {
  return node.getTagNameNode().getText();
};
var whitespaceRegex = /\s+/g;
var trimWhitespace = (str) => {
  return str.replaceAll(whitespaceRegex, " ");
};

// src/get-node-range.ts
var getNodeRange = (node) => {
  const src = node.getSourceFile();
  const [startPosition, endPosition] = [node.getStart(), node.getEnd()];
  const startInfo = src.getLineAndColumnAtPos(startPosition);
  const endInfo = src.getLineAndColumnAtPos(endPosition);
  return {
    startPosition,
    startLineNumber: startInfo.line,
    startColumn: startInfo.column,
    endPosition,
    endLineNumber: endInfo.line,
    endColumn: endInfo.column
  };
};

// src/box-factory.ts
var BoxNodeType = class {
  type;
  stack = [];
  node;
  constructor(definition) {
    this.type = definition.type;
    this.node = definition.node;
    this.stack = [...definition.stack ?? []];
  }
  getNode() {
    return this.node;
  }
  getStack() {
    return this.stack;
  }
  getRange = () => getNodeRange(this.node);
  toJSON() {
    const range = this.getRange();
    return {
      type: this.type,
      // @ts-expect-error
      value: this.value,
      node: this.node.getKindName(),
      line: range.startLineNumber,
      column: range.startColumn
    };
  }
  toString() {
    return JSON.stringify(this.toJSON(), null, 2);
  }
};
var BoxNodeObject = class extends BoxNodeType {
  value;
  isEmpty;
  constructor(definition) {
    super(definition);
    this.value = definition.value;
    this.isEmpty = definition.isEmpty;
  }
};
var BoxNodeLiteral = class extends BoxNodeType {
  value;
  kind;
  constructor(definition) {
    super(definition);
    this.value = definition.value;
    this.kind = definition.kind;
  }
};
var recipeProps = ["compoundVariants", "defaultVariants", "variants", "base"];
var BoxNodeMap = class extends BoxNodeType {
  value;
  spreadConditions;
  constructor(definition) {
    super(definition);
    this.value = definition.value;
  }
  isRecipe = () => {
    return recipeProps.some((prop) => this.value.has(prop));
  };
};
var BoxNodeArray = class extends BoxNodeType {
  value;
  constructor(definition) {
    super(definition);
    this.value = definition.value;
  }
};
var BoxNodeUnresolvable = class extends BoxNodeType {
};
var BoxNodeConditional = class extends BoxNodeType {
  whenTrue;
  whenFalse;
  constructor(definition) {
    super(definition);
    this.whenTrue = definition.whenTrue;
    this.whenFalse = definition.whenFalse;
  }
};
var BoxNodeEmptyInitializer = class extends BoxNodeType {
};
var isBoxNode = (value) => value instanceof BoxNodeType;

// src/to-box-node.ts
function toBoxNode(value, node, stack) {
  if (isNullish(value))
    return;
  if (isBoxNode(value))
    return value;
  if (isObject(value))
    return box.object(value, node, stack);
  if (isArray(value)) {
    if (value.length === 1)
      return toBoxNode(value[0], node, stack);
    return value.map((item) => toBoxNode(item, node, stack));
  }
  if (isPrimitiveType(value))
    return box.literal(value, node, stack);
}

// src/box.ts
var box = {
  object(value, node, stack) {
    return new BoxNodeObject({ type: "object", value, node, stack });
  },
  literal(value, node, stack) {
    return new BoxNodeLiteral({ type: "literal", value, kind: getTypeOfLiteral(value), node, stack });
  },
  map(value, node, stack) {
    return new BoxNodeMap({ type: "map", value, node, stack });
  },
  array(value, node, stack) {
    return new BoxNodeArray({ type: "array", value, node, stack });
  },
  conditional(whenTrue, whenFalse, node, stack) {
    return new BoxNodeConditional({ type: "conditional", whenTrue, whenFalse, node, stack });
  },
  from: toBoxNode,
  //
  emptyObject: (node, stack) => {
    return new BoxNodeObject({ type: "object", value: {}, isEmpty: true, node, stack });
  },
  emptyInitializer: (node, stack) => {
    return new BoxNodeEmptyInitializer({ type: "empty-initializer", node, stack });
  },
  unresolvable: (node, stack) => {
    return new BoxNodeUnresolvable({ type: "unresolvable", node, stack });
  },
  fallback(node) {
    return {
      value: void 0,
      getNode: () => node.getNode(),
      getStack: () => node.getStack()
    };
  },
  /**
   * box.type === “object” -> object that was resolved using ts-evaluator, most likely from a
   * complex condition OR a simple CallExpression eval result, we don’t have access to individual
   * AST nodes here so we need the distinction
   */
  isObject(value) {
    return value?.type === "object";
  },
  isLiteral(value) {
    return value?.type === "literal";
  },
  /**
   * box.type === “map” -> basically any object that was statically analyzable, we store each
   * prop + value in a Map
   */
  isMap(value) {
    return value?.type === "map";
  },
  isRecipe(value) {
    return box.isMap(value) && value.isRecipe();
  },
  isArray(value) {
    return value?.type === "array";
  },
  isUnresolvable(value) {
    return value?.type === "unresolvable";
  },
  isConditional(value) {
    return value?.type === "conditional";
  },
  isEmptyInitializer(value) {
    return value?.type === "empty-initializer";
  },
  isNumberLiteral(node) {
    return box.isLiteral(node) && node.kind === "number";
  },
  hasValue: (node) => {
    return box.isObject(node) || box.isLiteral(node) || box.isMap(node) || box.isArray(node);
  }
};

// src/maybe-box-node.ts
import { Node as Node5, ts as ts2 } from "ts-morph";

// src/evaluate-node.ts
import { evaluate } from "ts-evaluator";
import { ts } from "ts-morph";
var TsEvalError = Symbol("EvalError");
var cacheMap = /* @__PURE__ */ new WeakMap();
var evaluateNode = (node, stack, ctx) => {
  if (ctx.flags?.skipEvaluate)
    return;
  if (ctx.canEval && !ctx.canEval?.(node, stack))
    return;
  if (cacheMap.has(node)) {
    return cacheMap.get(node);
  }
  const result = evaluate({
    policy: {
      deterministic: true,
      network: false,
      console: false,
      maxOps: Number.POSITIVE_INFINITY,
      maxOpDuration: 1e3,
      io: { read: true, write: false },
      process: { exit: false, spawnChild: false }
    },
    ...ctx.getEvaluateOptions?.(node, stack),
    node: node.compilerNode,
    typescript: ts
  });
  const expr = result.success ? result.value : TsEvalError;
  cacheMap.set(node, expr);
  return expr;
};
var safeEvaluateNode = (node, stack, ctx) => {
  const result = evaluateNode(node, stack, ctx);
  if (result === TsEvalError)
    return;
  return result;
};

// src/find-identifier-value-declaration.ts
import {
  Node as Node2
} from "ts-morph";
function isScope(node) {
  return Node2.isFunctionDeclaration(node) || Node2.isFunctionExpression(node) || Node2.isArrowFunction(node) || Node2.isSourceFile(node);
}
function getDeclarationFor(node, stack, ctx) {
  const parent = node.getParent();
  if (!parent)
    return;
  const declarationStack = [];
  let declaration;
  if ((Node2.isVariableDeclaration(parent) || Node2.isParameterDeclaration(parent) || Node2.isFunctionDeclaration(parent) || Node2.isEnumDeclaration(parent) || Node2.isBindingElement(parent)) && parent.getNameNode() == node) {
    declarationStack.push(parent);
    declaration = parent;
  } else if (Node2.isImportSpecifier(parent) && parent.getNameNode() == node) {
    if (ctx.flags?.skipTraverseFiles)
      return;
    const sourceFile = getModuleSpecifierSourceFile(parent.getImportDeclaration());
    if (sourceFile) {
      const exportStack = [parent, sourceFile];
      const maybeVar = getExportedVarDeclarationWithName(node.getText(), sourceFile, exportStack, ctx);
      if (maybeVar) {
        declarationStack.push(...exportStack.concat(maybeVar));
        declaration = maybeVar;
      }
    }
  }
  if (declaration) {
    stack.push(...declarationStack);
  }
  return declaration;
}
var getInnermostScope = (from) => {
  let scope = from.getParent();
  while (scope && !isScope(scope)) {
    scope = scope.getParent();
  }
  return scope;
};
function findIdentifierValueDeclaration(identifier, stack, ctx, visitedsWithStack = /* @__PURE__ */ new Map()) {
  let scope = identifier;
  let foundNode;
  let isUnresolvable = false;
  let count = 0;
  const innerStack = [];
  do {
    scope = getInnermostScope(scope);
    count++;
    if (!scope)
      return;
    const refName = identifier.getText();
    scope.forEachDescendant((node, traversal) => {
      if (visitedsWithStack.has(node)) {
        traversal.skip();
        innerStack.push(...visitedsWithStack.get(node));
        return;
      }
      if (node == identifier)
        return;
      visitedsWithStack.set(node, innerStack);
      if (Node2.isIdentifier(node) && node.getText() == refName) {
        const declarationStack = [node];
        const maybeDeclaration = getDeclarationFor(node, declarationStack, ctx);
        if (maybeDeclaration) {
          if (Node2.isParameterDeclaration(maybeDeclaration)) {
            const initializer = maybeDeclaration.getInitializer();
            const typeNode = maybeDeclaration.getTypeNode();
            if (initializer) {
              innerStack.push(...declarationStack.concat(initializer));
              foundNode = maybeDeclaration;
            } else if (typeNode && Node2.isTypeLiteral(typeNode)) {
              innerStack.push(...declarationStack.concat(typeNode));
              foundNode = maybeDeclaration;
            } else {
              isUnresolvable = true;
            }
            traversal.stop();
            return;
          }
          innerStack.push(...declarationStack);
          foundNode = maybeDeclaration;
          traversal.stop();
        }
      }
    });
    if (foundNode || isUnresolvable) {
      if (foundNode) {
        stack.push(...innerStack);
      }
      return foundNode;
    }
  } while (scope && !Node2.isSourceFile(scope) && !foundNode && !isUnresolvable && count < 100);
}

// src/get-object-literal-expression-prop-pairs.ts
import { Node as Node4 } from "ts-morph";

// src/get-property-name.ts
import { Node as Node3 } from "ts-morph";
var getPropertyName = (property, stack, ctx) => {
  if (!property)
    return;
  if (Node3.isPropertyAssignment(property)) {
    const node = unwrapExpression(property.getNameNode());
    if (Node3.isIdentifier(node))
      return box.from(node.getText(), node, stack);
    if (Node3.isComputedPropertyName(node)) {
      const expression = node.getExpression();
      stack.push(expression);
      return maybePropName(expression, stack, ctx);
    }
    if (Node3.isStringLiteral(node) || Node3.isNumericLiteral(node))
      return box.from(node.getLiteralText(), node, stack);
  }
  if (Node3.isShorthandPropertyAssignment(property)) {
    const name = property.getName();
    if (name != null)
      return box.from(name, property, stack);
  }
};

// src/get-object-literal-expression-prop-pairs.ts
var getObjectLiteralExpressionPropPairs = (expression, expressionStack, ctx, matchProp) => {
  const properties = expression.getProperties();
  if (properties.length === 0) {
    return box.emptyObject(expression, expressionStack);
  }
  const extractedPropValues = [];
  const spreadConditions = [];
  properties.forEach((property) => {
    const stack = [...expressionStack];
    stack.push(property);
    if (Node4.isPropertyAssignment(property) || Node4.isShorthandPropertyAssignment(property)) {
      const propNameBox = getPropertyName(property, stack, ctx);
      if (!propNameBox)
        return;
      const propName = propNameBox.value;
      if (isNullish(propName))
        return;
      if (matchProp && !matchProp?.({ propName, propNode: property })) {
        return;
      }
      if (Node4.isShorthandPropertyAssignment(property)) {
        const initializer2 = property.getNameNode();
        stack.push(initializer2);
        const maybeValue2 = maybeBoxNode(initializer2, stack, ctx);
        if (maybeValue2) {
          extractedPropValues.push([propName.toString(), maybeValue2]);
          return;
        }
      }
      const init = property.getInitializer();
      if (!init)
        return;
      const initializer = unwrapExpression(init);
      stack.push(initializer);
      const maybeValue = maybeBoxNode(initializer, stack, ctx);
      if (maybeValue) {
        extractedPropValues.push([propName.toString(), maybeValue]);
        return;
      }
    }
    if (Node4.isSpreadAssignment(property)) {
      const initializer = unwrapExpression(property.getExpression());
      stack.push(initializer);
      const maybeObject = maybeBoxNode(initializer, stack, ctx, matchProp);
      if (!maybeObject)
        return;
      if (box.isObject(maybeObject)) {
        Object.entries(maybeObject.value).forEach(([propName, value]) => {
          const boxNode = box.from(value, initializer, stack);
          if (!boxNode)
            return;
          extractedPropValues.push([propName, boxNode]);
        });
        return;
      }
      if (box.isMap(maybeObject)) {
        maybeObject.value.forEach((nested, propName) => {
          extractedPropValues.push([propName, nested]);
        });
        return;
      }
      if (box.isConditional(maybeObject)) {
        spreadConditions.push(maybeObject);
      }
    }
  });
  const orderedMapValue = /* @__PURE__ */ new Map();
  extractedPropValues.forEach(([propName, value]) => {
    if (orderedMapValue.has(propName)) {
      orderedMapValue.delete(propName);
    }
    orderedMapValue.set(propName, value);
  });
  const map = box.map(orderedMapValue, expression, expressionStack);
  if (spreadConditions.length > 0) {
    map.spreadConditions = spreadConditions;
  }
  return map;
};

// src/maybe-box-node.ts
var cacheMap2 = /* @__PURE__ */ new WeakMap();
var isCached = (node) => cacheMap2.has(node);
var getCached = (node) => cacheMap2.get(node);
var isPlusSyntax = (op) => op === ts2.SyntaxKind.PlusToken;
var isLogicalSyntax = (op) => op === ts2.SyntaxKind.BarBarToken || op === ts2.SyntaxKind.QuestionQuestionToken || op === ts2.SyntaxKind.AmpersandAmpersandToken || op === ts2.SyntaxKind.EqualsEqualsEqualsToken || op === ts2.SyntaxKind.EqualsEqualsToken || op === ts2.SyntaxKind.ExclamationEqualsEqualsToken || op === ts2.SyntaxKind.ExclamationEqualsToken || op === ts2.SyntaxKind.GreaterThanEqualsToken || op === ts2.SyntaxKind.GreaterThanToken || op === ts2.SyntaxKind.LessThanEqualsToken || op === ts2.SyntaxKind.LessThanToken || op === ts2.SyntaxKind.InstanceOfKeyword || op === ts2.SyntaxKind.InKeyword;
var isOperationSyntax = (op) => op === ts2.SyntaxKind.AsteriskToken || op === ts2.SyntaxKind.SlashToken || op === ts2.SyntaxKind.PercentToken || op === ts2.SyntaxKind.AsteriskAsteriskToken || op === ts2.SyntaxKind.MinusToken;
var canReturnWhenTrueInLogicalExpression = (op) => {
  return op === ts2.SyntaxKind.BarBarToken || op === ts2.SyntaxKind.QuestionQuestionToken;
};
function maybeBoxNode(node, stack, ctx, matchProp) {
  const cache = (value) => {
    cacheMap2.set(node, value);
    return value;
  };
  if (isCached(node)) {
    return getCached(node);
  }
  if (Node5.isStringLiteral(node) || Node5.isNoSubstitutionTemplateLiteral(node)) {
    const value = trimWhitespace(node.getLiteralValue());
    return cache(box.literal(value, node, stack));
  }
  if (Node5.isObjectLiteralExpression(node)) {
    return cache(getObjectLiteralExpressionPropPairs(node, stack, ctx, matchProp));
  }
  if (Node5.isTrueLiteral(node) || Node5.isFalseLiteral(node)) {
    const value = node.getLiteralValue();
    return cache(box.literal(value, node, stack));
  }
  if (Node5.isNumericLiteral(node)) {
    const value = node.getLiteralValue();
    return cache(box.literal(value, node, stack));
  }
  if (Node5.isNullLiteral(node)) {
    return cache(box.literal(null, node, stack));
  }
  if (Node5.isPrefixUnaryExpression(node)) {
    const operand = node.getOperand();
    const operator = node.getOperatorToken();
    const boxNode = maybeBoxNode(operand, stack, ctx);
    if (!box.isNumberLiteral(boxNode))
      return;
    return cache(operator === ts2.SyntaxKind.MinusToken ? box.literal(-Number(boxNode.value), node, stack) : boxNode);
  }
  if (Node5.isArrayLiteralExpression(node)) {
    const boxNodes = node.getElements().map((element) => {
      return maybeBoxNode(element, stack, ctx) ?? cache(box.unresolvable(element, stack));
    });
    return cache(box.array(boxNodes, node, stack));
  }
  if (Node5.isIdentifier(node)) {
    const value = node.getText();
    if (value === "undefined")
      return cache(box.literal(void 0, node, stack));
    return cache(maybeIdentifierValue(node, stack, ctx));
  }
  if (Node5.isTemplateHead(node)) {
    return cache(box.literal(node.getLiteralText(), node, stack));
  }
  if (Node5.isTemplateExpression(node)) {
    const value = maybeTemplateStringValue(node, stack, ctx);
    return cache(box.literal(value, node, stack));
  }
  if (Node5.isTaggedTemplateExpression(node)) {
    return cache(maybeBoxNode(node.getTemplate(), stack, ctx));
  }
  if (Node5.isElementAccessExpression(node)) {
    return cache(getElementAccessedExpressionValue(node, stack, ctx));
  }
  if (Node5.isPropertyAccessExpression(node)) {
    return cache(getPropertyAccessedExpressionValue(node, [], stack, ctx));
  }
  if (Node5.isConditionalExpression(node)) {
    if (ctx.flags?.skipConditions) {
      return cache(box.unresolvable(node, stack));
    }
    const condExpr = unwrapExpression(node.getCondition());
    const condBoxNode = (Node5.isIdentifier(condExpr) ? maybeBoxNode(condExpr, [], ctx) : safeEvaluateNode(condExpr, stack, ctx)) ?? box.unresolvable(condExpr, stack);
    const condValue = isBoxNode(condBoxNode) ? condBoxNode : box.from(condBoxNode, node, stack);
    if (box.isEmptyInitializer(condValue))
      return;
    const isFromDefaultBinding = condValue.getStack().some((node2) => Node5.isBindingElement(node2));
    if (box.isUnresolvable(condValue) || box.isConditional(condValue) || isFromDefaultBinding) {
      const whenTrueExpr = unwrapExpression(node.getWhenTrue());
      const whenFalseExpr2 = unwrapExpression(node.getWhenFalse());
      return cache(maybeResolveConditionalExpression({ whenTrueExpr, whenFalseExpr: whenFalseExpr2, node, stack }, ctx));
    }
    if (condValue.value) {
      const whenTrueExpr = unwrapExpression(node.getWhenTrue());
      const innerStack2 = [...stack];
      const maybeValue2 = maybeBoxNode(whenTrueExpr, innerStack2, ctx);
      return cache(maybeValue2 ?? box.unresolvable(whenTrueExpr, stack));
    }
    const whenFalseExpr = unwrapExpression(node.getWhenFalse());
    const innerStack = [...stack];
    const maybeValue = maybeBoxNode(whenFalseExpr, innerStack, ctx);
    return cache(maybeValue ?? box.unresolvable(node, stack));
  }
  if (Node5.isCallExpression(node)) {
    const value = safeEvaluateNode(node, stack, ctx);
    if (!value)
      return;
    return cache(box.from(value, node, stack));
  }
  if (Node5.isBinaryExpression(node)) {
    const operatorKind = node.getOperatorToken().getKind();
    if (isPlusSyntax(operatorKind)) {
      const value = tryComputingPlusTokenBinaryExpressionToString(node, stack, ctx) ?? safeEvaluateNode(node, stack, ctx);
      if (!value)
        return;
      return cache(box.from(value, node, stack));
    }
    if (isLogicalSyntax(operatorKind)) {
      const whenTrueExpr = unwrapExpression(node.getLeft());
      const whenFalseExpr = unwrapExpression(node.getRight());
      const exprObject = {
        whenTrueExpr,
        whenFalseExpr,
        node,
        stack,
        canReturnWhenTrue: canReturnWhenTrueInLogicalExpression(operatorKind)
      };
      return cache(maybeResolveConditionalExpression(exprObject, ctx));
    }
    if (isOperationSyntax(operatorKind)) {
      return cache(box.literal(safeEvaluateNode(node, stack, ctx), node, stack));
    }
  }
}
var onlyStringLiteral = (boxNode) => {
  if (!boxNode)
    return;
  if (isBoxNode(boxNode) && box.isLiteral(boxNode) && typeof boxNode.value === "string") {
    return boxNode;
  }
};
var onlyNumberLiteral = (boxNode) => {
  if (!boxNode)
    return;
  if (isBoxNode(boxNode) && box.isLiteral(boxNode) && typeof boxNode.value === "number") {
    return boxNode;
  }
};
var maybeStringLiteral = (node, stack, ctx) => onlyStringLiteral(maybeBoxNode(node, stack, ctx));
var maybePropName = (node, stack, ctx) => {
  const boxed = maybeBoxNode(node, stack, ctx);
  const strBox = onlyStringLiteral(boxed);
  if (strBox)
    return strBox;
  const numberBox = onlyNumberLiteral(boxed);
  if (numberBox)
    return numberBox;
};
var maybeResolveConditionalExpression = ({
  whenTrueExpr,
  whenFalseExpr,
  node,
  stack,
  canReturnWhenTrue
}, ctx) => {
  const whenTrueValue = maybeBoxNode(whenTrueExpr, stack, ctx);
  const whenFalseValue = maybeBoxNode(whenFalseExpr, stack, ctx);
  if (canReturnWhenTrue && whenTrueValue && !box.isUnresolvable(whenTrueValue)) {
    return whenTrueValue;
  }
  if (Node5.isBinaryExpression(node) && node.getOperatorToken().getKind() === ts2.SyntaxKind.AmpersandAmpersandToken && whenTrueValue && whenFalseValue && box.isLiteral(whenTrueValue) && whenTrueValue.value === true) {
    return whenFalseValue;
  }
  if (!whenTrueValue && !whenFalseValue) {
    return;
  }
  if (whenTrueValue && !whenFalseValue) {
    return whenTrueValue;
  }
  if (!whenTrueValue && whenFalseValue) {
    return whenFalseValue;
  }
  const whenTrue = whenTrueValue;
  const whenFalse = whenFalseValue;
  if (box.isLiteral(whenTrue) && box.isLiteral(whenFalse) && whenTrue.value === whenFalse.value) {
    return whenTrue;
  }
  return box.conditional(whenTrue, whenFalse, node, stack);
};
var findProperty = (node, propName, _stack, ctx) => {
  const stack = [..._stack];
  if (Node5.isPropertyAssignment(node)) {
    const name = node.getNameNode();
    if (Node5.isIdentifier(name) && name.getText() === propName) {
      stack.push(name);
      return node;
    }
    if (Node5.isStringLiteral(name) && name.getLiteralText() === propName) {
      stack.push(name);
      return name.getLiteralText();
    }
    if (Node5.isComputedPropertyName(name)) {
      const expression = unwrapExpression(name.getExpression());
      const computedPropNameBox = maybePropName(expression, stack, ctx);
      if (!computedPropNameBox)
        return;
      if (String(computedPropNameBox.value) === propName) {
        stack.push(name, expression);
        return node;
      }
    }
  }
  if (Node5.isShorthandPropertyAssignment(node)) {
    const name = node.getNameNode();
    if (Node5.isIdentifier(name) && name.getText() === propName) {
      stack.push(name);
      return node;
    }
  }
};
var getObjectLiteralPropValue = (initializer, accessList, _stack, ctx) => {
  const stack = [..._stack];
  const propName = accessList.pop();
  const property = initializer.getProperty(propName) ?? initializer.getProperties().find((p) => findProperty(p, propName, stack, ctx));
  if (!property)
    return;
  stack.push(property);
  if (Node5.isPropertyAssignment(property)) {
    const propInit = property.getInitializer();
    if (!propInit)
      return;
    if (Node5.isObjectLiteralExpression(propInit)) {
      if (accessList.length > 0) {
        return getObjectLiteralPropValue(propInit, accessList, stack, ctx);
      }
      return maybeBoxNode(propInit, stack, ctx);
    }
    const maybePropValue = maybeBoxNode(propInit, stack, ctx);
    if (maybePropValue) {
      return maybePropValue;
    }
  }
  if (Node5.isShorthandPropertyAssignment(property)) {
    const identifier = property.getNameNode();
    if (accessList.length > 0) {
      return maybePropIdentifierValue(identifier, accessList, stack, ctx);
    }
    const maybePropValue = maybeBoxNode(identifier, stack, ctx);
    if (maybePropValue) {
      return maybePropValue;
    }
  }
};
var maybeTemplateStringValue = (template, stack, ctx) => {
  const head = template.getHead();
  const tail = template.getTemplateSpans();
  const headValue = maybeStringLiteral(head, stack, ctx);
  if (!headValue)
    return;
  const tailValues = tail.map((t) => {
    const expression = t.getExpression();
    const propBox = maybePropName(expression, stack, ctx);
    if (!propBox)
      return;
    const literal = t.getLiteral();
    return propBox.value + literal.getLiteralText();
  });
  if (tailValues.every(isNotNullish)) {
    return headValue.value + tailValues.join("");
  }
};
var maybeBindingElementValue = (def, stack, propName, ctx) => {
  const parent = def.getParent();
  if (!parent)
    return;
  const grandParent = parent.getParent();
  if (!grandParent)
    return;
  if (Node5.isArrayBindingPattern(parent)) {
    const index = parent.getChildIndex();
    if (Number.isNaN(index))
      return;
    if (Node5.isVariableDeclaration(grandParent)) {
      const init = grandParent.getInitializer();
      if (!init)
        return;
      const initializer = unwrapExpression(init);
      if (!Node5.isArrayLiteralExpression(initializer))
        return;
      const element = initializer.getElements()[index + 1];
      if (!element)
        return;
      const innerStack = [...stack, initializer, element];
      const maybeObject = maybeBoxNode(element, innerStack, ctx);
      if (!maybeObject)
        return;
      if (box.isObject(maybeObject)) {
        const propValue2 = maybeObject.value[propName];
        return box.from(propValue2, element, innerStack);
      }
      if (!box.isMap(maybeObject)) {
        return maybeObject;
      }
      const propValue = maybeObject.value.get(propName);
      if (!propValue)
        return;
      return propValue;
    }
  }
  if (Node5.isObjectBindingPattern(parent)) {
  }
};
function maybePropDefinitionValue(def, accessList, _stack, ctx) {
  const propName = accessList.at(-1);
  if (Node5.isVariableDeclaration(def)) {
    const init = def.getInitializer();
    if (!init) {
      const type = def.getTypeNode();
      if (!type)
        return;
      if (Node5.isTypeLiteral(type)) {
        if (accessList.length > 0) {
          const stack = [..._stack];
          stack.push(type);
          let propName2 = accessList.pop();
          let typeProp = type.getProperty(propName2);
          let typeLiteral = typeProp?.getTypeNode();
          while (typeProp && accessList.length > 0 && typeLiteral && Node5.isTypeLiteral(typeLiteral)) {
            stack.push(typeProp, typeLiteral);
            propName2 = accessList.pop();
            typeProp = typeLiteral.getProperty(propName2);
            typeLiteral = typeProp?.getTypeNode();
          }
          if (!typeLiteral)
            return;
          const typeValue = getTypeNodeValue(typeLiteral, stack, ctx);
          return box.from(typeValue, typeLiteral, stack);
        }
        const propValue = getTypeLiteralNodePropValue(type, propName, _stack, ctx);
        _stack.push(type);
        return box.from(propValue, type, _stack);
      }
      return;
    }
    const initializer = unwrapExpression(init);
    if (Node5.isObjectLiteralExpression(initializer)) {
      const propValue = getObjectLiteralPropValue(initializer, accessList, _stack, ctx);
      if (!propValue)
        return;
      _stack.push(initializer);
      return propValue;
    }
    if (Node5.isArrayLiteralExpression(initializer)) {
      const index = Number(propName);
      if (Number.isNaN(index))
        return;
      const element = initializer.getElements()[index];
      if (!element)
        return;
      _stack.push(initializer);
      const boxed = maybeBoxNode(element, _stack, ctx);
      if (boxed && isBoxNode(boxed) && box.isLiteral(boxed)) {
        return boxed;
      }
    }
    const innerStack = [..._stack, initializer];
    const maybeValue = maybeBoxNode(initializer, innerStack, ctx);
    if (maybeValue)
      return maybeValue;
  }
  if (Node5.isBindingElement(def)) {
    const value = maybeBindingElementValue(def, _stack, propName, ctx);
    if (value)
      return value;
  }
  if (Node5.isEnumDeclaration(def)) {
    const member = def.getMember(propName);
    if (!member)
      return;
    const initializer = member.getInitializer();
    if (!initializer)
      return;
    const innerStack = [..._stack, initializer];
    const maybeValue = maybeBoxNode(initializer, innerStack, ctx);
    if (maybeValue)
      return maybeValue;
  }
}
var maybePropIdentifierValue = (identifier, accessList, _stack, ctx) => {
  const maybeValueDeclaration = findIdentifierValueDeclaration(identifier, _stack, ctx);
  if (!maybeValueDeclaration) {
    return box.unresolvable(identifier, _stack);
  }
  const maybeValue = maybePropDefinitionValue(maybeValueDeclaration, accessList, _stack, ctx);
  if (maybeValue)
    return maybeValue;
  return box.unresolvable(identifier, _stack);
};
var typeLiteralCache = /* @__PURE__ */ new WeakMap();
var getTypeLiteralNodePropValue = (type, propName, stack, ctx) => {
  if (typeLiteralCache.has(type)) {
    const map = typeLiteralCache.get(type);
    if (map === null)
      return;
    if (map?.has(propName)) {
      return map.get(propName);
    }
  }
  const members = type.getMembers();
  const prop = members.find((member) => Node5.isPropertySignature(member) && member.getName() === propName);
  if (Node5.isPropertySignature(prop) && prop.isReadonly()) {
    const propType = prop.getTypeNode();
    if (!propType) {
      typeLiteralCache.set(type, null);
      return;
    }
    const propValue = getTypeNodeValue(propType, stack, ctx);
    if (isNotNullish(propValue)) {
      if (!typeLiteralCache.has(type)) {
        typeLiteralCache.set(type, /* @__PURE__ */ new Map());
      }
      const map = typeLiteralCache.get(type);
      map.set(propName, propValue);
      return propValue;
    }
  }
  typeLiteralCache.set(type, null);
};
function getNameLiteral(wrapper) {
  if (Node5.isStringLiteral(wrapper))
    return wrapper.getLiteralText();
  return wrapper.getText();
}
var typeNodeCache = /* @__PURE__ */ new WeakMap();
var getTypeNodeValue = (type, stack, ctx) => {
  if (typeNodeCache.has(type)) {
    return typeNodeCache.get(type);
  }
  if (Node5.isLiteralTypeNode(type)) {
    const literal = type.getLiteral();
    if (Node5.isStringLiteral(literal)) {
      const result = literal.getLiteralText();
      typeNodeCache.set(type, result);
      return result;
    }
  }
  if (Node5.isTypeLiteral(type)) {
    const members = type.getMembers();
    if (!members.some((member) => !Node5.isPropertySignature(member) || !member.isReadonly())) {
      const props = members;
      const entries = props.map((member) => {
        const nameNode = member.getNameNode();
        const nameText = nameNode.getText();
        const name = getNameLiteral(nameNode);
        if (!name)
          return;
        const value = getTypeLiteralNodePropValue(type, nameText, stack, ctx);
        return [name, value];
      }).filter(isNotNullish);
      const result = Object.fromEntries(entries);
      typeNodeCache.set(type, result);
      return result;
    }
  }
  typeNodeCache.set(type, void 0);
};
var maybeDefinitionValue = (def, stack, ctx) => {
  if (Node5.isShorthandPropertyAssignment(def)) {
    const propNameNode = def.getNameNode();
    return maybePropIdentifierValue(propNameNode, [propNameNode.getText()], stack, ctx);
  }
  if (Node5.isVariableDeclaration(def)) {
    const init = def.getInitializer();
    if (!init) {
      const type = def.getTypeNode();
      if (!type)
        return;
      if (Node5.isTypeLiteral(type)) {
        stack.push(type);
        const maybeTypeValue = getTypeNodeValue(type, stack, ctx);
        if (isNotNullish(maybeTypeValue))
          return box.from(maybeTypeValue, def, stack);
      }
      return box.unresolvable(def, stack);
    }
    const initializer = unwrapExpression(init);
    const innerStack = [...stack, initializer];
    const maybeValue = maybeBoxNode(initializer, innerStack, ctx);
    if (maybeValue)
      return maybeValue;
  }
  if (Node5.isBindingElement(def)) {
    const init = def.getInitializer();
    if (!init) {
      const nameNode = def.getPropertyNameNode() ?? def.getNameNode();
      const propName = nameNode.getText();
      const innerStack2 = [...stack, nameNode];
      const value = maybeBindingElementValue(def, innerStack2, propName, ctx);
      if (value)
        return value;
      return box.unresolvable(def, stack);
    }
    const initializer = unwrapExpression(init);
    const innerStack = [...stack, initializer];
    const maybeValue = maybeBoxNode(initializer, innerStack, ctx);
    if (maybeValue)
      return maybeValue;
  }
};
var getExportedVarDeclarationWithName = (varName, sourceFile, stack, ctx) => {
  const maybeVar = sourceFile.getVariableDeclaration(varName);
  if (maybeVar)
    return maybeVar;
  const exportDeclaration = resolveVarDeclarationFromExportWithName(varName, sourceFile, stack, ctx);
  if (!exportDeclaration)
    return;
  return exportDeclaration;
};
var hasNamedExportWithName = (name, exportDeclaration) => {
  const namedExports = exportDeclaration.getNamedExports();
  if (namedExports.length === 0)
    return true;
  for (const namedExport of namedExports) {
    const exportedName = namedExport.getNameNode().getText();
    if (exportedName === name) {
      return true;
    }
  }
};
var getModuleSpecifierSourceFile = (declaration) => {
  const project = declaration.getProject();
  const moduleName = declaration.getModuleSpecifierValue();
  if (!moduleName)
    return;
  const containingFile = declaration.getSourceFile().getFilePath();
  const resolved = ts2.resolveModuleName(
    moduleName,
    containingFile,
    project.getCompilerOptions(),
    project.getModuleResolutionHost()
  );
  if (!resolved.resolvedModule)
    return;
  const sourceFile = project.addSourceFileAtPath(resolved.resolvedModule.resolvedFileName);
  return sourceFile;
};
function resolveVarDeclarationFromExportWithName(symbolName, sourceFile, stack, ctx) {
  for (const exportDeclaration of sourceFile.getExportDeclarations()) {
    const exportStack = [exportDeclaration];
    if (!hasNamedExportWithName(symbolName, exportDeclaration))
      continue;
    const maybeFile = getModuleSpecifierSourceFile(exportDeclaration);
    if (!maybeFile)
      continue;
    exportStack.push(maybeFile);
    const maybeVar = getExportedVarDeclarationWithName(symbolName, maybeFile, stack, ctx);
    if (maybeVar) {
      stack.push(...exportStack.concat(maybeVar));
      return maybeVar;
    }
  }
}
var maybeIdentifierValue = (identifier, _stack, ctx) => {
  const valueDeclaration = findIdentifierValueDeclaration(identifier, _stack, ctx);
  if (!valueDeclaration) {
    return box.unresolvable(identifier, _stack);
  }
  const declaration = unwrapExpression(valueDeclaration);
  const stack = [..._stack];
  const maybeValue = maybeDefinitionValue(declaration, stack, ctx);
  if (maybeValue)
    return maybeValue;
  return box.unresolvable(identifier, stack);
};
var tryComputingPlusTokenBinaryExpressionToString = (node, stack, ctx) => {
  const left = unwrapExpression(node.getLeft());
  const right = unwrapExpression(node.getRight());
  const leftValue = maybePropName(left, stack, ctx);
  const rightValue = maybePropName(right, stack, ctx);
  if (!leftValue || !rightValue)
    return;
  if (isNotNullish(leftValue.value) && isNotNullish(rightValue.value)) {
    return box.literal(String(leftValue.value) + String(rightValue.value), node, stack);
  }
};
var getElementAccessedExpressionValue = (expression, _stack, ctx) => {
  const elementAccessed = unwrapExpression(expression.getExpression());
  const argExpr = expression.getArgumentExpression();
  if (!argExpr)
    return;
  const arg = unwrapExpression(argExpr);
  const stack = [..._stack, elementAccessed, arg];
  const argLiteral = maybePropName(arg, stack, ctx);
  if (Node5.isIdentifier(elementAccessed) && argLiteral) {
    if (!isNotNullish(argLiteral.value))
      return;
    return maybePropIdentifierValue(elementAccessed, [argLiteral.value.toString()], stack, ctx);
  }
  if (Node5.isBinaryExpression(arg)) {
    if (arg.getOperatorToken().getKind() !== ts2.SyntaxKind.PlusToken)
      return;
    const propName = tryComputingPlusTokenBinaryExpressionToString(arg, stack, ctx) ?? maybePropName(arg, stack, ctx);
    if (propName && Node5.isIdentifier(elementAccessed)) {
      if (!isNotNullish(propName.value))
        return;
      return maybePropIdentifierValue(elementAccessed, [propName.value.toString()], stack, ctx);
    }
  }
  if (Node5.isTemplateExpression(arg)) {
    const propName = maybeTemplateStringValue(arg, stack, ctx);
    if (propName && Node5.isIdentifier(elementAccessed)) {
      return maybePropIdentifierValue(elementAccessed, [propName], stack, ctx);
    }
  }
  if (Node5.isObjectLiteralExpression(elementAccessed) && argLiteral) {
    if (!isNotNullish(argLiteral.value))
      return;
    return getObjectLiteralPropValue(elementAccessed, [argLiteral.value.toString()], stack, ctx);
  }
  if (Node5.isPropertyAccessExpression(arg)) {
    return getPropertyAccessedExpressionValue(arg, [], stack, ctx);
  }
  if (Node5.isPropertyAccessExpression(elementAccessed) && argLiteral && isNotNullish(argLiteral.value)) {
    const propRefValue = getPropertyAccessedExpressionValue(elementAccessed, [], stack, ctx);
    if (!propRefValue)
      return box.unresolvable(elementAccessed, stack);
    const propName = argLiteral.value.toString();
    if (box.isObject(propRefValue)) {
      const propValue = propRefValue.value[propName];
      return box.from(propValue, arg, stack);
    }
    if (box.isMap(propRefValue)) {
      const propValue = propRefValue.value.get(propName);
      return box.from(propValue, arg, stack);
    }
    if (box.isArray(propRefValue)) {
      const propValue = propRefValue.value[Number(propName)];
      return box.from(propValue, arg, stack);
    }
    return box.unresolvable(elementAccessed, stack);
  }
  if (Node5.isIdentifier(elementAccessed) && Node5.isElementAccessExpression(arg)) {
    const propName = getElementAccessedExpressionValue(arg, stack, ctx);
    if (typeof propName === "string" && isNotNullish(propName)) {
      return maybePropIdentifierValue(elementAccessed, [propName], stack, ctx);
    }
  }
  if (Node5.isElementAccessExpression(elementAccessed) && argLiteral && isNotNullish(argLiteral.value)) {
    const identifier = getElementAccessedExpressionValue(elementAccessed, stack, ctx);
    if (isObject(identifier)) {
      const argValue = argLiteral.value.toString();
      if (box.isMap(identifier)) {
        const maybeValue = identifier.value.get(argValue);
        return maybeValue;
      }
      if (box.isObject(identifier)) {
        const maybeLiteralValue = identifier.value[argValue];
        if (!maybeLiteralValue)
          return;
        return box.from(maybeLiteralValue, expression, stack);
      }
    }
  }
  if (Node5.isArrayLiteralExpression(elementAccessed) && argLiteral) {
    return getArrayElementValueAtIndex(elementAccessed, Number(argLiteral.value), stack, ctx);
  }
  if (Node5.isConditionalExpression(arg)) {
    if (ctx.flags?.skipConditions)
      return box.unresolvable(arg, stack);
    const propName = maybePropName(arg, stack, ctx);
    if (isNotNullish(propName) && isNotNullish(propName.value)) {
      if (Node5.isIdentifier(elementAccessed)) {
        return maybePropIdentifierValue(elementAccessed, [propName.value.toString()], stack, ctx);
      }
    }
    const whenTrueExpr = unwrapExpression(arg.getWhenTrue());
    const whenFalseExpr = unwrapExpression(arg.getWhenFalse());
    const whenTrueValue = maybePropName(whenTrueExpr, stack, ctx);
    const whenFalseValue = maybePropName(whenFalseExpr, stack, ctx);
    if (Node5.isIdentifier(elementAccessed)) {
      const whenTrueResolved = whenTrueValue && isNotNullish(whenTrueValue.value) ? maybePropIdentifierValue(elementAccessed, [whenTrueValue.value.toString()], stack, ctx) : void 0;
      const whenFalseResolved = whenFalseValue && isNotNullish(whenFalseValue.value) ? maybePropIdentifierValue(elementAccessed, [whenFalseValue.value.toString()], stack, ctx) : void 0;
      if (!whenTrueResolved && !whenFalseResolved) {
        return;
      }
      if (whenTrueResolved && !whenFalseResolved) {
        return whenTrueResolved;
      }
      if (!whenTrueResolved && whenFalseResolved) {
        return whenFalseResolved;
      }
      return box.conditional(whenTrueResolved, whenFalseResolved, arg, stack);
    }
  }
};
var getArrayElementValueAtIndex = (array, index, stack, ctx) => {
  const element = array.getElements()[index];
  if (!element)
    return;
  const value = maybeBoxNode(element, stack, ctx);
  if (isNotNullish(value)) {
    return value;
  }
};
var getPropertyAccessedExpressionValue = (expression, _accessList, stack, ctx) => {
  const propName = expression.getName();
  const elementAccessed = unwrapExpression(expression.getExpression());
  const accessList = _accessList.concat(propName);
  stack.push(elementAccessed);
  if (Node5.isIdentifier(elementAccessed)) {
    return maybePropIdentifierValue(elementAccessed, accessList, stack, ctx);
  }
  if (Node5.isPropertyAccessExpression(elementAccessed)) {
    const propValue = getPropertyAccessedExpressionValue(elementAccessed, accessList, stack, ctx);
    return propValue;
  }
  if (Node5.isElementAccessExpression(elementAccessed)) {
    const leftElementAccessed = getElementAccessedExpressionValue(elementAccessed, stack, ctx);
    if (!leftElementAccessed)
      return;
    if (box.isObject(leftElementAccessed)) {
      const propValue = leftElementAccessed.value[propName];
      return box.from(propValue, expression, stack);
    }
    if (box.isMap(leftElementAccessed)) {
      const propValue = leftElementAccessed.value.get(propName);
      return box.from(propValue, expression, stack);
    }
  }
};

// src/call-expression.ts
var trueFn = () => true;
var extractCallExpressionArguments = (node, ctx, matchProp = trueFn, matchArg = trueFn) => {
  const fnArguments = node.getArguments();
  const fnName = node.getExpression().getText();
  if (fnArguments.length === 0) {
    return box.array([], node, []);
  }
  return box.array(
    fnArguments.map((argument, index) => {
      const argNode = unwrapExpression(argument);
      const stack = [node, argNode];
      if (matchArg({ fnNode: node, fnName, argNode, index })) {
        return maybeBoxNode(argNode, stack, ctx, matchProp) ?? box.unresolvable(argNode, stack);
      }
      return box.unresolvable(argNode, stack);
    }),
    node,
    []
  );
};

// src/extract.ts
import { Node as Node7 } from "ts-morph";

// src/jsx-attribute.ts
import { Node as Node6 } from "ts-morph";
var extractJsxAttribute = (jsxAttribute, ctx) => {
  const initializer = jsxAttribute.getInitializer();
  const stack = [jsxAttribute, initializer];
  if (!initializer) {
    const nameNode = jsxAttribute.getNameNode();
    return box.emptyInitializer(nameNode, stack);
  }
  if (Node6.isStringLiteral(initializer)) {
    const literalText = initializer.getLiteralText();
    return box.literal(trimWhitespace(literalText), initializer, stack);
  }
  if (Node6.isJsxExpression(initializer)) {
    const expr = initializer.getExpression();
    if (!expr)
      return;
    const expression = unwrapExpression(expr);
    if (!expression)
      return;
    stack.push(expression);
    const maybeValue = maybeBoxNode(expression, stack, ctx);
    if (maybeValue)
      return maybeValue;
  }
};

// src/jsx-spread-attribute.ts
var extractJsxSpreadAttributeValues = (node, ctx, matchProp) => {
  const expr = unwrapExpression(node.getExpression());
  const stack = [];
  return maybeBoxNode(expr, stack, ctx, matchProp);
};

// src/object-like-to-map.ts
var objectLikeToMap = (maybeObject, node) => {
  if (!maybeObject) {
    return /* @__PURE__ */ new Map();
  }
  if (!isBoxNode(maybeObject)) {
    return new Map(Object.entries(maybeObject));
  }
  if (box.isUnresolvable(maybeObject) || box.isConditional(maybeObject)) {
    return /* @__PURE__ */ new Map();
  }
  if (box.isMap(maybeObject)) {
    return maybeObject.value;
  }
  return new Map(
    Object.entries(maybeObject.value).map(([key, value]) => {
      const boxed = box.from(value, maybeObject.getNode() ?? node, maybeObject.getStack() ?? []);
      return [key, boxed || null];
    }).filter(([, value]) => value !== null)
  );
};

// src/extract.ts
var isImportOrExport = (node) => Node7.isImportDeclaration(node) || Node7.isExportDeclaration(node);
var isJsxElement = (node) => Node7.isJsxOpeningElement(node) || Node7.isJsxSelfClosingElement(node);
var extract = ({ ast, ...ctx }) => {
  const { components, functions, taggedTemplates } = ctx;
  const byName = /* @__PURE__ */ new Map();
  const componentByNode = /* @__PURE__ */ new Map();
  ast.forEachDescendant((node, traversal) => {
    if (isImportOrExport(node)) {
      traversal.skip();
      return;
    }
    if (components) {
      if (Node7.isJsxOpeningElement(node) || Node7.isJsxSelfClosingElement(node)) {
        const componentNode = node;
        const componentName = getComponentName(componentNode);
        const isFactory = componentName.includes(".");
        if (!components.matchTag({ tagNode: componentNode, tagName: componentName, isFactory })) {
          return;
        }
        if (!byName.has(componentName)) {
          byName.set(componentName, { kind: "component", nodesByProp: /* @__PURE__ */ new Map(), queryList: [] });
        }
        if (!componentByNode.has(componentNode)) {
          componentByNode.set(componentNode, { name: componentName, props: /* @__PURE__ */ new Map(), conditionals: [] });
        }
      }
      if (Node7.isJsxSpreadAttribute(node)) {
        const componentNode = node.getFirstAncestor(isJsxElement);
        const component = componentByNode.get(componentNode);
        if (!componentNode || !component)
          return;
        const componentName = getComponentName(componentNode);
        const boxByProp = byName.get(componentName).nodesByProp;
        const matchProp = ({ propName, propNode }) => components.matchProp({ tagNode: componentNode, tagName: componentName, propName, propNode });
        const spreadNode = extractJsxSpreadAttributeValues(node, ctx, matchProp);
        if (!spreadNode)
          return;
        const processObjectLike = (objLike) => {
          const mapValue = objectLikeToMap(objLike, node);
          const isMap = box.isMap(objLike);
          const boxNode = box.map(mapValue, node, [componentNode]);
          if (isMap && objLike.spreadConditions?.length) {
            boxNode.spreadConditions = objLike.spreadConditions;
          }
          mapValue.forEach((propValue, propName) => {
            if (matchProp({ propName, propNode: node })) {
              component.props.set(propName, propValue);
              boxByProp.set(propName, (boxByProp.get(propName) ?? []).concat(propValue));
            }
          });
        };
        const processBoxNode = (boxNode) => {
          if (box.isConditional(boxNode)) {
            component.conditionals.push(boxNode);
            return;
          }
          if (box.isObject(boxNode) || box.isMap(boxNode)) {
            return processObjectLike(boxNode);
          }
        };
        processBoxNode(spreadNode);
        return;
      }
      if (Node7.isJsxAttribute(node)) {
        const componentNode = node.getFirstAncestor(isJsxElement);
        const component = componentByNode.get(componentNode);
        if (!componentNode || !component)
          return;
        const componentName = getComponentName(componentNode);
        const boxByProp = byName.get(componentName).nodesByProp;
        const propName = node.getNameNode().getText();
        if (!components.matchProp({ tagNode: componentNode, tagName: componentName, propName, propNode: node })) {
          return;
        }
        const maybeBox = extractJsxAttribute(node, ctx);
        if (!maybeBox)
          return;
        component.props.set(propName, maybeBox);
        boxByProp.set(propName, (boxByProp.get(propName) ?? []).concat(maybeBox));
      }
    }
    if (functions && Node7.isCallExpression(node)) {
      const expr = node.getExpression();
      const fnName = Node7.isCallExpression(expr) ? expr.getExpression().getText() : expr.getText();
      if (!functions.matchFn({ fnNode: node, fnName }))
        return;
      const matchProp = ({ propName, propNode }) => functions.matchProp({ fnNode: node, fnName, propName, propNode });
      if (!byName.has(fnName)) {
        byName.set(fnName, { kind: "function", nodesByProp: /* @__PURE__ */ new Map(), queryList: [] });
      }
      const fnResultMap = byName.get(fnName);
      const boxByProp = fnResultMap.nodesByProp;
      const boxNodeArray = extractCallExpressionArguments(node, ctx, matchProp, functions.matchArg);
      const nodeList = boxNodeArray.value.map((boxNode) => {
        if (box.isObject(boxNode) || box.isMap(boxNode)) {
          const mapValue = objectLikeToMap(boxNode, node);
          const isMap = box.isMap(boxNode);
          mapValue.forEach((propValue, propName) => {
            if (isMap ? true : matchProp({ propName, propNode: node })) {
              boxByProp.set(propName, (boxByProp.get(propName) ?? []).concat(propValue));
            }
          });
          const boxMap = box.map(mapValue, node, boxNode.getStack());
          if (box.isMap(boxNode) && boxNode.spreadConditions?.length) {
            boxMap.spreadConditions = boxNode.spreadConditions;
          }
          return boxMap;
        }
        return boxNode;
      });
      const query = {
        kind: "call-expression",
        name: fnName,
        box: box.array(nodeList, node, [])
      };
      fnResultMap.queryList.push(query);
    }
    if (taggedTemplates && Node7.isTaggedTemplateExpression(node)) {
      const tag = node.getTag();
      const fnName = Node7.isCallExpression(tag) ? tag.getExpression().getText() : tag.getText();
      if (!taggedTemplates.matchTaggedTemplate({ taggedTemplateNode: node, fnName }))
        return;
      if (!byName.has(fnName)) {
        byName.set(fnName, { kind: "function", nodesByProp: /* @__PURE__ */ new Map(), queryList: [] });
      }
      const fnResultMap = byName.get(fnName);
      const query = {
        kind: "tagged-template",
        name: fnName,
        box: maybeBoxNode(node, [], ctx)
      };
      fnResultMap.queryList.push(query);
    }
  });
  componentByNode.forEach((parentRef, componentNode) => {
    const component = componentByNode.get(componentNode);
    if (!component)
      return;
    const query = {
      name: parentRef.name,
      box: box.map(component.props, componentNode, [])
    };
    if (component.conditionals?.length) {
      query.box.spreadConditions = component.conditionals;
    }
    const componentName = parentRef.name;
    const queryList = byName.get(componentName).queryList;
    queryList.push(query);
  });
  return byName;
};

// src/jsx-element-props.ts
import { Node as Node8 } from "ts-morph";
var isObjectLike = (node) => box.isObject(node) || box.isMap(node);
var extractJsxElementProps = (node, ctx) => {
  const tagName = node.getTagNameNode().getText();
  const jsxAttributes = node.getAttributes();
  const props = /* @__PURE__ */ new Map();
  jsxAttributes.forEach((attrNode) => {
    if (Node8.isJsxAttribute(attrNode)) {
      const nameNode = attrNode.getNameNode();
      const maybeValue = extractJsxAttribute(attrNode, ctx);
      if (!maybeValue)
        return;
      props.set(nameNode.getText(), maybeValue);
      return;
    }
    if (Node8.isJsxSpreadAttribute(attrNode)) {
      const maybeValue = extractJsxSpreadAttributeValues(attrNode, ctx, () => true);
      if (!isObjectLike(maybeValue))
        return;
      if (box.isMap(maybeValue)) {
        maybeValue.value.forEach((value, propName) => {
          props.set(propName, value);
        });
      }
      if (box.isObject(maybeValue)) {
        Object.entries(maybeValue.value).forEach(([propName, value]) => {
          props.set(propName, box.literal(value, node, []));
        });
      }
    }
  });
  return { name: tagName, props };
};

// src/unbox.ts
import { Node as Node9 } from "ts-morph";
var makeObjAt = (path, value) => {
  if (!path.length)
    return value;
  const obj = {};
  path.reduce((acc, key, i) => {
    const isLast = i === path.length - 1;
    acc[key] = isLast ? value : {};
    return isLast ? obj : acc[key];
  }, obj);
  return obj;
};
var makeArrayWithValueAt = (index, value) => {
  if (index < 0)
    return [];
  const arr = [];
  for (let i = 0; i <= index; i++) {
    arr[i] = i === index ? value : void 0;
  }
  return arr;
};
var getLiteralValue = (node, ctx) => {
  if (!node)
    return;
  if (box.isConditional(node)) {
    const path = ctx.path;
    const whenTrue = getLiteralValue(node.whenTrue, Object.assign({}, ctx, { path, parent: node }));
    const whenFalse = getLiteralValue(node.whenFalse, Object.assign({}, ctx, { path, parent: node }));
    const last = node.getStack().at(-1);
    const maybeIndex = Number(path[path.length - 1]);
    if (last && Node9.isArrayLiteralExpression(last) && !Number.isNaN(maybeIndex)) {
      const sliced = path.slice(0, -1);
      if (whenTrue) {
        ctx.conditions.push(makeObjAt(sliced, makeArrayWithValueAt(maybeIndex, whenTrue)));
      }
      if (whenFalse) {
        ctx.conditions.push(makeObjAt(sliced, makeArrayWithValueAt(maybeIndex, whenFalse)));
      }
      return void 0;
    }
    if (isTruthyOrZero(whenTrue)) {
      ctx.conditions.push(makeObjAt(path, whenTrue));
    }
    if (isTruthyOrZero(whenFalse)) {
      ctx.conditions.push(makeObjAt(path, whenFalse));
    }
    return void 0;
  }
  if (box.isLiteral(node) || box.isObject(node)) {
    return node.value;
  }
  if (box.isEmptyInitializer(node)) {
    return true;
  }
  if (box.isMap(node)) {
    if (node.spreadConditions) {
      const path = ctx.path;
      node.spreadConditions.forEach((spread) => {
        const whenTrue = getLiteralValue(spread.whenTrue, Object.assign({}, ctx, { path, parent: node }));
        const whenFalse = getLiteralValue(spread.whenFalse, Object.assign({}, ctx, { path, parent: node }));
        if (whenTrue) {
          ctx.spreadConditions.push(makeObjAt(path, whenTrue));
        }
        if (whenFalse) {
          ctx.spreadConditions.push(makeObjAt(path, whenFalse));
        }
      });
    }
    const obj = {};
    node.value.forEach((propNode, key) => {
      const value = getLiteralValue(propNode, Object.assign({}, ctx, { path: ctx.path.concat(key), parent: node }));
      if (isNotNullish(value)) {
        obj[key] = value;
      }
    });
    return obj;
  }
  if (box.isArray(node)) {
    return node.value.flatMap(
      (elementNode, index) => getLiteralValue(elementNode, Object.assign({}, ctx, { path: ctx.path.concat(String(index)), parent: node }))
    );
  }
};
var cacheMap3 = /* @__PURE__ */ new WeakMap();
var createCache = (map) => ({
  value: map,
  has: (node) => map.has(node),
  get: (node) => map.get(node),
  set: (node, value) => map.set(node, value)
});
var unbox = (node, ctx) => {
  const _ctx = {
    cache: ctx?.cache ?? cacheMap3,
    ...ctx,
    path: [],
    parent: void 0,
    conditions: [],
    spreadConditions: []
  };
  const cache = createCache(_ctx.cache);
  if (cache.has(node)) {
    return cache.get(node);
  }
  let raw;
  if (Array.isArray(node)) {
    raw = node.map((boxNode) => getLiteralValue(boxNode, _ctx)).filter(isNotNullish)[0];
  } else {
    raw = getLiteralValue(node, _ctx);
  }
  const result = { raw: raw ?? {}, conditions: _ctx.conditions, spreadConditions: _ctx.spreadConditions };
  if (raw) {
    cache.set(node, result);
  }
  return result;
};
export {
  BoxNodeArray,
  BoxNodeConditional,
  BoxNodeEmptyInitializer,
  BoxNodeLiteral,
  BoxNodeMap,
  BoxNodeObject,
  BoxNodeUnresolvable,
  box,
  extract,
  extractCallExpressionArguments,
  extractJsxAttribute,
  extractJsxElementProps,
  extractJsxSpreadAttributeValues,
  findIdentifierValueDeclaration,
  isBoxNode,
  maybeBoxNode,
  maybeIdentifierValue,
  unbox
};
