import{c as I,o as at,t as B,i as C,b as L,e as y,s as V,h as k,d as it,j as lt,g as et,k as nt,a as O,m as st,F as rt}from"./vendor-solid-gbiNAvvL.js";import{c as w}from"./index-w_0cBiiQ.js";import{V as ot}from"./vendor-charts-DZALwqbE.js";import"./vendor-editor-BmK5o4Wa.js";var ct=B("<div><div></div><div><div></div><div></div><div></div><div>"),dt=B("<button>");function ut(t){let n,e,o,a;const[l,i]=I(null),[r,m]=I(null),[b,M]=I("equity"),g=()=>{if(!n)return;const c=ot(n,{width:n.clientWidth,height:t.height||400,layout:{background:{color:"#ffffff"},textColor:"#333"},grid:{vertLines:{color:"#f0f0f0"},horzLines:{color:"#f0f0f0"}},rightPriceScale:{borderColor:"#cccccc"},timeScale:{borderColor:"#cccccc",timeVisible:!0,secondsVisible:!1}}),f=c.addLineSeries({color:"#1890ff",lineWidth:2,title:"权益曲线"}),u=t.result.equity.map(p=>({time:new Date(p.timestamp).getTime()/1e3,value:p.equity}));f.setData(u),i(c);const h=new ResizeObserver(()=>{c.applyOptions({width:n.clientWidth})});h.observe(n),et(()=>{h.disconnect(),c.remove()})},z=()=>{if(!e)return;const c=ot(e,{width:e.clientWidth,height:t.height||400,layout:{background:{color:"#ffffff"},textColor:"#333"},grid:{vertLines:{color:"#f0f0f0"},horzLines:{color:"#f0f0f0"}},rightPriceScale:{borderColor:"#cccccc"},timeScale:{borderColor:"#cccccc",timeVisible:!0,secondsVisible:!1}}),f=c.addAreaSeries({topColor:"rgba(255, 77, 79, 0.3)",bottomColor:"rgba(255, 77, 79, 0.1)",lineColor:"#ff4d4f",lineWidth:2,title:"回撤曲线"}),u=t.result.drawdownSeries?.map(p=>({time:new Date(p.timestamp).getTime()/1e3,value:p.drawdown}))||[];f.setData(u),m(c);const h=new ResizeObserver(()=>{c.applyOptions({width:e.clientWidth})});h.observe(e),et(()=>{h.disconnect(),c.remove()})},s=()=>{if(!o)return;const c=[];for(let R=1;R<t.result.equity.length;R++){const d=t.result.equity[R-1].equity,_=(t.result.equity[R].equity-d)/d;c.push(_)}const f=20,u=Math.min(...c),p=(Math.max(...c)-u)/f,S=new Array(f).fill(0);c.forEach(R=>{const d=Math.min(Math.floor((R-u)/p),f-1);S[d]++});const x=document.createElement("canvas");x.width=o.clientWidth,x.height=t.height||400;const T=x.getContext("2d");o.innerHTML="",o.appendChild(x);const D=Math.max(...S),E=x.width/f;T.fillStyle="#1890ff",S.forEach((R,d)=>{const q=R/D*(x.height-40),_=d*E,W=x.height-q-20;T.fillRect(_,W,E-2,q)}),T.fillStyle="#666",T.font="12px Arial",T.textAlign="center";for(let R=0;R<=f;R+=5){const d=R*E,q=(u+R*p)*100;T.fillText(`${q.toFixed(1)}%`,d,x.height-5)}},$=()=>{if(!a||!t.result.monthlyReturns)return;const c=a;c.innerHTML="";const f=document.createElement("div");f.style.cssText=`
      display: grid;
      grid-template-columns: repeat(12, 1fr);
      gap: 2px;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    `;const u=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],h=t.result.monthlyReturns.map(x=>x.return),p=Math.min(...h),S=Math.max(...h);t.result.monthlyReturns.forEach((x,T)=>{const D=document.createElement("div");(x.return-p)/(S-p);let E;x.return<0?E=`rgba(255, 77, 79, ${Math.abs(x.return)/Math.abs(p)*.8})`:E=`rgba(82, 196, 26, ${x.return/S*.8})`,D.style.cssText=`
        background-color: ${E};
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        padding: 8px;
        text-align: center;
        font-size: 12px;
        min-height: 40px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;
        transition: transform 0.2s;
      `,D.innerHTML=`
        <div style="font-weight: bold; margin-bottom: 2px;">${u[T%12]}</div>
        <div style="color: ${x.return>=0?"#52c41a":"#ff4d4f"};">
          ${x.return>=0?"+":""}${(x.return*100).toFixed(1)}%
        </div>
      `,D.addEventListener("mouseenter",()=>{D.style.transform="scale(1.05)"}),D.addEventListener("mouseleave",()=>{D.style.transform="scale(1)"}),f.appendChild(D)}),c.appendChild(f)};return at(()=>{lt(()=>{const c=b();c==="equity"?setTimeout(g,100):c==="drawdown"?setTimeout(z,100):c==="returns"?setTimeout(s,100):c==="monthly"&&setTimeout($,100)})}),(()=>{var c=ct(),f=c.firstChild,u=f.nextSibling,h=u.firstChild,p=h.nextSibling,S=p.nextSibling,x=S.nextSibling;C(f,()=>[{key:"equity",label:"权益曲线"},{key:"drawdown",label:"回撤分析"},{key:"returns",label:"收益分布"},{key:"monthly",label:"月度热力图"}].map(d=>(()=>{var q=dt();return q.$$click=()=>M(d.key),C(q,()=>d.label),L(()=>y(q,w({px:"16px",py:"12px",border:"none",bg:"transparent",cursor:"pointer",fontSize:"14px",fontWeight:b()===d.key?"600":"400",color:b()===d.key?"#1890ff":"#666",borderBottom:b()===d.key?"2px solid #1890ff":"2px solid transparent",transition:"all 0.3s ease",_hover:{color:"#1890ff"}}))),q})()));var T=n;typeof T=="function"?k(T,h):n=h;var D=e;typeof D=="function"?k(D,p):e=p;var E=o;typeof E=="function"?k(E,S):o=S;var R=a;return typeof R=="function"?k(R,x):a=x,L(d=>{var q=w({bg:"white",borderRadius:"12px",p:"24px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}),_=w({display:"flex",borderBottom:"1px solid #f0f0f0",mb:"24px"}),W=w({minHeight:`${t.height||400}px`}),v=b()==="equity"?"block":"none",A=b()==="drawdown"?"block":"none",F=b()==="returns"?"block":"none",H=b()==="monthly"?"block":"none";return q!==d.e&&y(c,d.e=q),_!==d.t&&y(f,d.t=_),W!==d.a&&y(u,d.a=W),v!==d.o&&V(h,"display",d.o=v),A!==d.i&&V(p,"display",d.i=A),F!==d.n&&V(S,"display",d.n=F),H!==d.s&&V(x,"display",d.s=H),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),c})()}it(["click"]);function ft(t,n,e,o=.03){if(t.length===0)throw new Error("权益数据不能为空");const a=ht(t),l=vt(t),i=mt(a),r=l,m=gt(a),{maxDrawdown:b,maxDrawdownDuration:M}=xt(t),g=pt(a,o),z=yt(i,b),s=wt(a,o),$=e?bt(a,e):0,c=Rt(n),f=St(a),u=e?Mt(a,e):0,h=e?Ct(i,u,e,o):0,p=e?qt(a,e):0,S=u!==0?(i-o)/u:0;return{totalReturn:l,annualizedReturn:i,cumulativeReturn:r,volatility:m,maxDrawdown:b,maxDrawdownDuration:M,sharpeRatio:g,calmarRatio:z,sortinoRatio:s,informationRatio:$,...c,...f,beta:u,alpha:h,trackingError:p,treynorRatio:S}}function ht(t){const n=[];for(let e=1;e<t.length;e++){const o=t[e-1].equity,l=(t[e].equity-o)/o;n.push(l)}return n}function vt(t){if(t.length===0)return 0;const n=t[0].equity;return(t[t.length-1].equity-n)/n}function mt(t){if(t.length===0)return 0;const n=t.reduce((o,a)=>o*(1+a),1)-1,e=t.length/252;return Math.pow(1+n,1/e)-1}function gt(t){if(t.length===0)return 0;const n=t.reduce((o,a)=>o+a,0)/t.length,e=t.reduce((o,a)=>o+Math.pow(a-n,2),0)/t.length;return Math.sqrt(e*252)}function xt(t){let n=0,e=0,o=t[0]?.equity||0,a=0,l=0;for(let i=0;i<t.length;i++){const r=t[i].equity;if(r>o)o=r,l>0&&(e=Math.max(e,l),l=0);else{const m=(o-r)/o;n=Math.max(n,m),l===0&&(a=i),l=i-a+1}}return l>0&&(e=Math.max(e,l)),{maxDrawdown:n,maxDrawdownDuration:e}}function pt(t,n){if(t.length===0)return 0;const e=t.map(l=>l-n/252),o=e.reduce((l,i)=>l+i,0)/e.length,a=Math.sqrt(e.reduce((l,i)=>l+Math.pow(i-o,2),0)/e.length);return a>0?o*Math.sqrt(252)/(a*Math.sqrt(252)):0}function yt(t,n){return n>0?t/n:0}function wt(t,n){if(t.length===0)return 0;const e=t.map(i=>i-n/252),o=e.reduce((i,r)=>i+r,0)/e.length,a=e.filter(i=>i<0);if(a.length===0)return 1/0;const l=Math.sqrt(a.reduce((i,r)=>i+Math.pow(r,2),0)/t.length);return l>0?o*Math.sqrt(252)/(l*Math.sqrt(252)):0}function bt(t,n){if(t.length!==n.length||t.length===0)return 0;const e=t.map((l,i)=>l-n[i]),o=e.reduce((l,i)=>l+i,0)/e.length,a=Math.sqrt(e.reduce((l,i)=>l+Math.pow(i-o,2),0)/e.length);return a>0?o/a:0}function Rt(t){const n=t.filter(g=>(g.pnl||0)>0),e=t.filter(g=>(g.pnl||0)<0),o=t.length>0?n.length/t.length:0,a=n.reduce((g,z)=>g+(z.pnl||0),0),l=Math.abs(e.reduce((g,z)=>g+(z.pnl||0),0)),i=l>0?a/l:0,r=n.length>0?a/n.length:0,m=e.length>0?l/e.length:0,b=n.length>0?Math.max(...n.map(g=>g.pnl||0)):0,M=e.length>0?Math.min(...e.map(g=>g.pnl||0)):0;return{winRate:o,profitFactor:i,averageWin:r,averageLoss:m,largestWin:b,largestLoss:M}}function St(t){if(t.length===0)return{var95:0,var99:0,cvar95:0,cvar99:0};const n=[...t].sort((M,g)=>M-g),e=Math.floor(t.length*.05),o=Math.floor(t.length*.01),a=n[e]||0,l=n[o]||0,i=n.slice(0,e+1),r=n.slice(0,o+1),m=i.length>0?i.reduce((M,g)=>M+g,0)/i.length:0,b=r.length>0?r.reduce((M,g)=>M+g,0)/r.length:0;return{var95:a,var99:l,cvar95:m,cvar99:b}}function Mt(t,n){if(t.length!==n.length||t.length===0)return 0;const e=t.reduce((i,r)=>i+r,0)/t.length,o=n.reduce((i,r)=>i+r,0)/n.length;let a=0,l=0;for(let i=0;i<t.length;i++){const r=t[i]-e,m=n[i]-o;a+=r*m,l+=m*m}return a/=t.length,l/=t.length,l>0?a/l:0}function Ct(t,n,e,o){const a=e.reduce((l,i)=>l+i,0)/e.length*252;return t-(o+n*(a-o))}function qt(t,n){if(t.length!==n.length||t.length===0)return 0;const e=t.map((l,i)=>l-n[i]),o=e.reduce((l,i)=>l+i,0)/e.length,a=e.reduce((l,i)=>l+Math.pow(i-o,2),0)/e.length;return Math.sqrt(a*252)}var $t=B("<div><div></div><div><h3> - 详细分析</h3><div>"),Dt=B("<div><div><h3></h3><div></div></div><div> · <!> · </div><div><div><div>总收益率</div><div>%</div></div><div><div>夏普比率</div><div></div></div></div><div>创建时间: "),Et=B("<div><div></div><div>");function P(t,n,e){const o=[];let a=t;const l=Math.pow(1+e/100,1/n)-1,i=new Date("2023-01-01");for(let r=0;r<n;r++){const m=new Date(i);m.setDate(i.getDate()+r);const b=1+(Math.random()-.5)*.04,M=l*b;a*=1+M;const g=Math.max(...o.map(s=>s.equity),a),z=g>0?(a-g)/g:0;o.push({timestamp:m.toISOString(),equity:a,drawdown:z})}return o}function j(t){const n=[],e=["000001","000002","000858","600036","600519"],o=new Date("2023-01-01");for(let a=0;a<t;a++){const l=new Date(o);l.setDate(o.getDate()+Math.floor(Math.random()*250));const i=Math.random()>.5?"buy":"sell",r=10+Math.random()*90,m=Math.floor(Math.random()*1e3)+100,b=(Math.random()-.4)*1e3;n.push({timestamp:l.toISOString(),symbol:e[Math.floor(Math.random()*e.length)],side:i,quantity:m,price:r,pnl:b})}return n.sort((a,l)=>new Date(a.timestamp).getTime()-new Date(l.timestamp).getTime())}function N(t){const n=[],e=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"];for(let o=0;o<t;o++)n.push({month:e[o],return:(Math.random()-.4)*.12});return n}function Y(t){const n=[],e=new Date("2023-01-01");let o=1e5,a=1e5;for(let l=0;l<t;l++){const i=new Date(e);i.setDate(e.getDate()+l);const r=(Math.random()-.48)*.02;a*=1+r,a>o&&(o=a);const m=(a-o)/o;n.push({timestamp:i.toISOString(),drawdown:m})}return n}function At(){const[t,n]=I("bt_001"),e=[{id:"bt_001",name:"动量策略回测",strategy:"动量策略",symbol:"000001",period:"2023-01-01 至 2024-01-01",status:"completed",totalReturn:15.6,sharpeRatio:1.85,maxDrawdown:-3.2,winRate:68.5,trades:156,createdAt:"2024-01-15",equity:P(1e5,252,15.6),tradeHistory:j(156),monthlyReturns:N(12),drawdownSeries:Y(252)},{id:"bt_002",name:"均值回归策略回测",strategy:"均值回归策略",symbol:"000002",period:"2023-06-01 至 2024-06-01",status:"completed",totalReturn:8.3,sharpeRatio:1.42,maxDrawdown:-2.1,winRate:72.3,trades:89,createdAt:"2024-01-10",equity:P(1e5,252,8.3),tradeHistory:j(89),monthlyReturns:N(12),drawdownSeries:Y(252)},{id:"bt_003",name:"网格交易策略回测",strategy:"网格交易策略",symbol:"000858",period:"2023-03-01 至 2024-03-01",status:"running",totalReturn:5.7,sharpeRatio:1.23,maxDrawdown:-1.8,winRate:65.4,trades:234,createdAt:"2024-01-08",equity:P(1e5,252,5.7),tradeHistory:j(234),monthlyReturns:N(12),drawdownSeries:Y(252)}],o=()=>e.find(r=>r.id===t()),a=nt(()=>{const r=o();return r?ft(r.equity,r.tradeHistory,void 0,.03):null}),l=()=>{const r=o();return r?{totalReturn:r.totalReturn,annualizedReturn:r.totalReturn*1.2,sharpeRatio:r.sharpeRatio,maxDrawdown:r.maxDrawdown,winRate:r.winRate,trades:r.tradeHistory,equity:r.equity,monthlyReturns:r.monthlyReturns,drawdownSeries:r.drawdownSeries}:{}},i=nt(()=>{const r=a(),m=o();return!r||!m?[]:[{label:"总收益率",value:`${r.totalReturn>=0?"+":""}${(r.totalReturn*100).toFixed(2)}%`,trend:"up"},{label:"年化收益率",value:`${r.annualizedReturn>=0?"+":""}${(r.annualizedReturn*100).toFixed(2)}%`,trend:"up"},{label:"最大回撤",value:`${(r.maxDrawdown*100).toFixed(2)}%`,trend:"down"},{label:"夏普比率",value:r.sharpeRatio.toFixed(3),trend:"up"},{label:"卡尔马比率",value:r.calmarRatio.toFixed(3),trend:"up"},{label:"索提诺比率",value:r.sortinoRatio.toFixed(3),trend:"up"},{label:"胜率",value:`${(r.winRate*100).toFixed(1)}%`,trend:"up"},{label:"盈亏比",value:r.profitFactor.toFixed(2),trend:"neutral"},{label:"波动率",value:`${(r.volatility*100).toFixed(2)}%`,trend:"neutral"},{label:"VaR(95%)",value:`${(r.var95*100).toFixed(2)}%`,trend:"down"},{label:"CVaR(95%)",value:`${(r.cvar95*100).toFixed(2)}%`,trend:"down"},{label:"交易次数",value:m.trades.toString(),trend:"neutral"}]});return(()=>{var r=$t(),m=r.firstChild,b=m.nextSibling,M=b.firstChild,g=M.firstChild,z=M.nextSibling;return C(m,O(rt,{each:e,children:s=>(()=>{var $=Dt(),c=$.firstChild,f=c.firstChild,u=f.nextSibling,h=c.nextSibling,p=h.firstChild,S=p.nextSibling;S.nextSibling;var x=h.nextSibling,T=x.firstChild,D=T.firstChild,E=D.nextSibling,R=E.firstChild,d=T.nextSibling,q=d.firstChild,_=q.nextSibling,W=x.nextSibling;return W.firstChild,$.$$click=()=>n(s.id),C(f,()=>s.name),C(u,(()=>{var v=st(()=>s.status==="completed");return()=>v()?"已完成":s.status==="running"?"运行中":"失败"})()),C(h,()=>s.strategy,p),C(h,()=>s.symbol,S),C(h,()=>s.period,null),C(E,()=>s.totalReturn>=0?"+":"",R),C(E,()=>s.totalReturn,R),C(_,()=>s.sharpeRatio),C(W,()=>s.createdAt,null),L(v=>{var A=w({bg:"white",borderRadius:"12px",p:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",border:t()===s.id?"2px solid #1890ff":"1px solid #f0f0f0",cursor:"pointer",transition:"all 0.3s ease",_hover:{boxShadow:"0 4px 16px rgba(0,0,0,0.15)",transform:"translateY(-2px)"}}),F=w({display:"flex",alignItems:"center",justifyContent:"space-between",mb:"12px"}),H=w({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),G=w({px:"8px",py:"4px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",bg:s.status==="completed"?"#f6ffed":s.status==="running"?"#e6f7ff":"#fff2f0",color:s.status==="completed"?"#52c41a":s.status==="running"?"#1890ff":"#ff4d4f"}),J=w({fontSize:"14px",color:"#8c8c8c",mb:"12px"}),K=w({display:"grid",gridTemplateColumns:"1fr 1fr",gap:"12px",mb:"12px"}),Q=w({fontSize:"12px",color:"#8c8c8c",mb:"4px"}),U=w({fontSize:"16px",fontWeight:"700",color:s.totalReturn>=0?"#52c41a":"#ff4d4f"}),X=w({fontSize:"12px",color:"#8c8c8c",mb:"4px"}),Z=w({fontSize:"16px",fontWeight:"700",color:"#262626"}),tt=w({fontSize:"12px",color:"#8c8c8c"});return A!==v.e&&y($,v.e=A),F!==v.t&&y(c,v.t=F),H!==v.a&&y(f,v.a=H),G!==v.o&&y(u,v.o=G),J!==v.i&&y(h,v.i=J),K!==v.n&&y(x,v.n=K),Q!==v.s&&y(D,v.s=Q),U!==v.h&&y(E,v.h=U),X!==v.r&&y(q,v.r=X),Z!==v.d&&y(_,v.d=Z),tt!==v.l&&y(W,v.l=tt),v},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0}),$})()})),C(M,()=>o()?.name,g),C(z,O(rt,{get each(){return i()},children:s=>(()=>{var $=Et(),c=$.firstChild,f=c.nextSibling;return C(c,()=>s.label),C(f,()=>s.value),L(u=>{var h=w({textAlign:"center",p:"12px",borderRadius:"8px",bg:"#fafafa",border:"1px solid #f0f0f0",transition:"all 0.3s ease",_hover:{bg:"#f0f0f0",transform:"translateY(-2px)",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"}}),p=w({fontSize:"11px",color:"#8c8c8c",mb:"6px",fontWeight:"500"}),S=w({fontSize:"16px",fontWeight:"700",color:s.trend==="up"?"#52c41a":s.trend==="down"?"#ff4d4f":"#262626"});return h!==u.e&&y($,u.e=h),p!==u.t&&y(c,u.t=p),S!==u.a&&y(f,u.a=S),u},{e:void 0,t:void 0,a:void 0}),$})()})),C(r,O(ut,{get result(){return l()},height:450}),null),L(s=>{var $=w({display:"flex",flexDirection:"column",gap:"24px"}),c=w({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(350px, 1fr))",gap:"16px"}),f=w({bg:"white",borderRadius:"16px",p:"24px",boxShadow:"0 4px 20px rgba(0,0,0,0.08)",border:"1px solid #f0f0f0"}),u=w({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0,mb:"20px"}),h=w({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(140px, 1fr))",gap:"12px"});return $!==s.e&&y(r,s.e=$),c!==s.t&&y(m,s.t=c),f!==s.a&&y(b,s.a=f),u!==s.o&&y(M,s.o=u),h!==s.i&&y(z,s.i=h),s},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),r})()}it(["click"]);export{At as default};
