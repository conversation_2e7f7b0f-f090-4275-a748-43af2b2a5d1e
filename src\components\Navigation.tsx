import { A } from '@solidjs/router';
import { Show } from'solid-js';
import { userStore } from'../stores';

export default function Navigation() {
  const isAuthenticated = () => userStore.state.isAuthenticated;
  const user = () => userStore.state.user;

  return (
    <nav style={{
      background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding:'16px 24px',
      boxShadow:'0 2px 8px rgba(0,0,0,0.1)'
    }}>
      <div style={{
        maxWidth:'1200px',
        margin:'0 auto',
        display:'flex',
        justifyContent:'space-between',
        alignItems:'center'
      }}>
        {/* Logo */}
        <A href="/" style={{
          fontSize:'24px',
          fontWeight:'bold',
          color:'white',
          textDecoration:'none',
          display:'flex',
          alignItems:'center',
          gap:'8px'
        }}>
          <div style={{
            width:'32px',
            height:'32px',
            background:'rgba(255,255,255,0.2)',
            borderRadius:'8px',
            display:'flex',
            alignItems:'center',
            justifyContent:'center',
            fontSize:'18px'
          }}>
            Q
          </div>
          量化交易平台
        </A>

        {/* 导航链接 */}
        <div style={{
          display:'flex',
          alignItems:'center',
          gap:'24px'
        }}>
          <A href="/" style={{
            color:'rgba(255,255,255,0.9)',
            textDecoration:'none',
            padding:'8px 16px',
            borderRadius:'6px',
            transition:'all 0.2s',
            fontSize:'14px',
            fontWeight:'500'
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor ='rgba(255,255,255,0.1)';
            e.target.style.color ='white';
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor ='transparent';
            e.target.style.color ='rgba(255,255,255,0.9)';
          }}>
            🏠 首页
          </A>

          <Show when={isAuthenticated()}>
            <A href="/strategy" style={{
              color:'rgba(255,255,255,0.9)',
              textDecoration:'none',
              padding:'8px 16px',
              borderRadius:'6px',
              transition:'all 0.2s',
              fontSize:'14px',
              fontWeight:'500'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor ='rgba(255,255,255,0.1)';
              e.target.style.color ='white';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor ='transparent';
              e.target.style.color ='rgba(255,255,255,0.9)';
            }}>
              📊 策略管理
            </A>

            <A href="/market" style={{
              color:'rgba(255,255,255,0.9)',
              textDecoration:'none',
              padding:'8px 16px',
              borderRadius:'6px',
              transition:'all 0.2s',
              fontSize:'14px',
              fontWeight:'500'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor ='rgba(255,255,255,0.1)';
              e.target.style.color ='white';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor ='transparent';
              e.target.style.color ='rgba(255,255,255,0.9)';
            }}>
              📈 市场数据
            </A>
          </Show>

          {/* 用户信息 */}
          <Show when={isAuthenticated()} fallback={
            <A href="/login" style={{
              background:'rgba(255,255,255,0.2)',
              color:'white',
              textDecoration:'none',
              padding:'8px 16px',
              borderRadius:'6px',
              fontSize:'14px',
              fontWeight:'500',
              border:'1px solid rgba(255,255,255,0.3)',
              transition:'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor ='rgba(255,255,255,0.3)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor ='rgba(255,255,255,0.2)';
            }}>
              🔑 登录
            </A>
          }>
            <div style={{
              display:'flex',
              alignItems:'center',
              gap:'12px',
              background:'rgba(255,255,255,0.1)',
              padding:'8px 12px',
              borderRadius:'8px',
              border:'1px solid rgba(255,255,255,0.2)'
            }}>
              <img
                src={user()?.avatar}
                alt="用户头像"
                style={{
                  width:'24px',
                  height:'24px',
                  borderRadius:''50%',
                  border:'1px solid rgba(255,255,255,0.3)'
                }}
              />
              <span style={{
                color:'white',
                fontSize:'14px',
                fontWeight:'500'
              }}>
                {user()?.nickname}
              </span>
              <button
                style={{
                  background:'none',
                  border:'none',
                  color:'rgba(255,255,255,0.8)',
                  cursor:'pointer',
                  padding:'4px',
                  borderRadius:'4px',
                  fontSize:'12px',
                  transition:'all 0.2s'
                }}
                onClick={async () => {
                  if (confirm('确定要退出登录吗？')) {
                    await userStore.logout();
                    window.location.href ='/login';
                  }
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor ='rgba(255,255,255,0.1)';
                  e.target.style.color ='white';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor ='transparent';
                  e.target.style.color ='rgba(255,255,255,0.8)';
                }}
                title="退出登录"
              >
                🚪
              </button>
            </div>
          </Show>
        </div>
      </div>
    </nav>
  );
}