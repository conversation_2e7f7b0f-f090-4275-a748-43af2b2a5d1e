{"name": "quant-frontend", "version": "1.0.0", "description": "轻量级量化交易前端平台", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "echo '<PERSON><PERSON> skipped'", "format": "echo 'Formatting skipped'", "type-check": "tsc --noEmit", "panda": "panda", "panda:codegen": "panda codegen", "panda:watch": "panda --watch"}, "dependencies": {"@codemirror/autocomplete": "^6.12.0", "@codemirror/basic-setup": "^0.20.0", "@codemirror/commands": "^6.3.3", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/lang-python": "^6.1.3", "@codemirror/search": "^6.5.5", "@codemirror/state": "^6.4.0", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.23.0", "@kobalte/core": "^0.13.11", "@kobalte/vanilla-extract": "^0.5.0", "@monaco-editor/loader": "^1.4.0", "@pandacss/dev": "^0.39.2", "@park-ui/panda-preset": "^0.32.0", "@solidjs/router": "^0.13.0", "@xenova/transformers": "^2.17.1", "big.js": "^6.2.1", "codemirror": "^6.0.1", "date-fns": "^3.0.0", "decimal.js": "^10.4.3", "jotai": "^2.13.0", "lightweight-charts": "^4.1.0", "monaco-editor": "^0.45.0", "numeral": "^2.0.6", "socket.io-client": "^4.7.0", "solid-js": "^1.8.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/big.js": "^6.2.2", "@types/node": "^20.0.0", "@types/numeral": "^2.0.5", "@types/uuid": "^9.0.7", "autoprefixer": "^10.4.0", "glob": "^10.4.5", "postcss": "^8.4.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-solid": "^2.8.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["quantitative-trading", "solidjs", "lightweight", "financial", "charts", "ai-assisted"], "author": "Quant Frontend Team", "license": "MIT"}