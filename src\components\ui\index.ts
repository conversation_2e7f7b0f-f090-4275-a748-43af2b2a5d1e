// UI组件库配置和导出
// 统一管理Hope UI、Kobalte、Panda CSS组件

// Hope UI 核心组件
export {
  Button,
  Box,
  Container,
  Flex,
  Grid,
  GridItem,
  HStack,
  VStack,
  Stack,
  Spacer,
  Center,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Text,
  Heading,
  Badge,
  Avatar,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  Textarea,
  FormControl,
  FormLabel,
  FormErrorMessage,
  FormHelperText,
  Checkbox,
  Radio,
  RadioGroup,
  Switch,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Drawer,
  DrawerOverlay,
  DrawerContent,
  DrawerHeader,
  DrawerBody,
  DrawerFooter,
  DrawerCloseButton,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverArrow,
  PopoverHeader,
  PopoverBody,
  PopoverFooter,
  PopoverCloseButton,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Progress,
  CircularProgress,
  Skeleton,
  SkeletonText,
  Spinner,
  Table,
  TableContainer,
  Thead,
  Tbody,
  Tfoot,
  Tr,
  Th,
  Td,
  TableCaption,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator
} from '@hope-ui/solid';

// Kobalte 无样式组件 (用于更复杂的交互)
export {
  Dialog,
  DropdownMenu,
  NavigationMenu,
  Select as KobalteSelect,
  Slider,
  Toast,
  ToggleGroup
} from '@kobalte/core';

// 自定义组件类型
export interface UIComponentProps {
  children?: any;
  class?: string;
  style?: any;
}

// 组件主题配置
export const theme = {
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    success: '#22c55e',
    warning: '#eab308',
    error: '#ef4444',
    info: '#0ea5e9'
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
  },
  radius: {
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px'
  }
};