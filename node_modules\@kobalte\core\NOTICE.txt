kobalte
=================
The following is a list of sources from which code was used/modified in this codebase.

-------------------------------------------------------------------------------
This codebase contains a modified portion of code from Adobe which can be obtained at:
  * SOURCE:
    * https://www.npmjs.com/package/@adobe/react-spectrum
  * LICENSE:
    * https://unpkg.com/@adobe/react-spectrum@3.17.0/LICENSE

  * SOURCE:
    * https://www.npmjs.com/package/react-aria
  * LICENSE:
    * https://unpkg.com/react-aria@3.15.0/LICENSE

  * SOURCE:
    * https://www.npmjs.com/package/react-stately
  * LICENSE:
    * https://unpkg.com/react-stately@3.13.0/LICENSE

  * SOURCE:
    * https://www.npmjs.com/package/@adobe/react-spectrum-ui
  * LICENSE:
    * https://unpkg.com/@adobe/react-spectrum-ui@1.0.1/LICENSE

  * SOURCE:
    * https://www.npmjs.com/package/@adobe/react-spectrum-workflow
  * LICENSE:
    * https://unpkg.com/@adobe/react-spectrum-workflow@1.0.1/LICENSE

  * SOURCE:
    * https://www.npmjs.com/package/@adobe/react-spectrum-workflow-color
  * LICENSE:
    * https://unpkg.com/@adobe/react-spectrum-workflow-color@1.0.1/LICENSE

-------------------------------------------------------------------------------
This codebase contains a portion of code from react which can be obtained at:
  * SOURCE:
    * https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions

  * LICENSE:
    MIT License

    Copyright (c) Facebook, Inc. and its affiliates.

    Permission is hereby granted, free of charge, to any person obtaining a copy
    of this software and associated documentation files (the "Software"), to deal
    in the Software without restriction, including without limitation the rights
    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
    copies of the Software, and to permit persons to whom the Software is
    furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in all
    copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
    SOFTWARE.

-------------------------------------------------------------------------------
This codebase contains a modified portion of code from Modernizr which can be obtained at:
  * SOURCE:
    * https://github.com/Modernizr/Modernizr/blob/7efb9d0edd66815fb115fdce95fabaf019ce8db5/feature-detects/css/flexgap.js

  * LICENSE:
    * https://github.com/Modernizr/Modernizr/blob/master/LICENSE

-------------------------------------------------------------------------------
This codebase contains a modified portion of code from react-window which can be obtained at:
  * SOURCE:
    * https://github.com/bvaughn/react-window/blob/master/src/createGridComponent.js

  * LICENSE:
    The MIT License (MIT)

    Copyright (c) 2018 Brian Vaughn

    Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

-------------------------------------------------------------------------------
This codebase contains a modified portion of code from focus-options-polyfill which can be obtained at:
  * SOURCE:
    * https://github.com/calvellido/focus-options-polyfill

  * LICENSE:
    MIT License

    Copyright (c) 2018 Juan Valencia

    Permission is hereby granted, free of charge, to any person obtaining a copy
    of this software and associated documentation files (the "Software"), to deal
    in the Software without restriction, including without limitation the rights
    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
    copies of the Software, and to permit persons to whom the Software is
    furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in all
    copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
    SOFTWARE.

-------------------------------------------------------------------------------
This codebase contains ResizeObserver.d.ts type declaration file which can be obtained at:
  * SOURCE:
    * https://gist.github.com/strothj/708afcf4f01dd04de8f49c92e88093c3

  * LICENSE:
    Copyright 2020 Jason Strothmann

    Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

------------------------------------------------------------------------------

This codebase contains a portion of code that vuejs adapted from jest-dom which can be obtained at:
  * SOURCE:
    * https://github.com/vuejs/vue-test-utils-next/blob/master/src/utils/isElementVisible.ts
  * LICENSE:
    * https://github.com/vuejs/vue-test-utils-next/blob/master/LICENSE

  * SOURCE:
    * https://github.com/testing-library/jest-dom/blob/main/src/to-be-visible.js
  * LICENSE:
    * https://github.com/testing-library/jest-dom/blob/main/LICENSE

------------------------------------------------------------------------------
This codebase contains a modified portion of code from ICU which can be obtained at:
  * SOURCE:
    * https://github.com/unicode-org/icu

  * LICENSE:
    COPYRIGHT AND PERMISSION NOTICE (ICU 58 and later)

    Copyright © 1991-2020 Unicode, Inc. All rights reserved.
    Distributed under the Terms of Use in https://www.unicode.org/copyright.html.

    Permission is hereby granted, free of charge, to any person obtaining
    a copy of the Unicode data files and any associated documentation
    (the "Data Files") or Unicode software and any associated documentation
    (the "Software") to deal in the Data Files or Software
    without restriction, including without limitation the rights to use,
    copy, modify, merge, publish, distribute, and/or sell copies of
    the Data Files or Software, and to permit persons to whom the Data Files
    or Software are furnished to do so, provided that either
    (a) this copyright and permission notice appear with all copies
    of the Data Files or Software, or
    (b) this copyright and permission notice appear in associated
    Documentation.

    THE DATA FILES AND SOFTWARE ARE PROVIDED "AS IS", WITHOUT WARRANTY OF
    ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
    WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
    NONINFRINGEMENT OF THIRD PARTY RIGHTS.
    IN NO EVENT SHALL THE COPYRIGHT HOLDER OR HOLDERS INCLUDED IN THIS
    NOTICE BE LIABLE FOR ANY CLAIM, OR ANY SPECIAL INDIRECT OR CONSEQUENTIAL
    DAMAGES, OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE,
    DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
    TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THE DATA FILES OR SOFTWARE.

    Except as contained in this notice, the name of a copyright holder
    shall not be used in advertising or otherwise to promote the sale,
    use or other dealings in these Data Files or Software without prior
    written authorization of the copyright holder.

-------------------------------------------------------------------------------
This codebase contains a modified portion of code from the TC39 Temporal proposal which can be obtained at:
  * SOURCE:
    * https://github.com/tc39/proposal-temporal

  * LICENSE:
    Copyright (c) 2017, 2018, 2019, 2020
        Ecma International. All rights reserved.

    All Software contained in this document ("Software") is protected by copyright
    and is being made available under the "BSD License", included below.

    This Software may be subject to third party rights (rights from parties other
    than Ecma International), including patent rights, and no licenses under such
    third party rights are granted under this license even if the third party
    concerned is a member of Ecma International.

    SEE THE ECMA CODE OF CONDUCT IN PATENT MATTERS AVAILABLE AT
    https://ecma-international.org/memento/codeofconduct.htm
    FOR INFORMATION REGARDING THE LICENSING OF PATENT CLAIMS THAT ARE REQUIRED TO
    IMPLEMENT ECMA INTERNATIONAL STANDARDS.


    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this
      list of conditions and the following disclaimer.

    2. Redistributions in binary form must reproduce the above copyright notice,
      this list of conditions and the following disclaimer in the documentation
      and/or other materials provided with the distribution.

    3. Neither the name of the authors nor Ecma International may be used to
    endorse or promote products derived from this software without specific prior
    written permission.

    THIS SOFTWARE IS PROVIDED BY THE ECMA INTERNATIONAL "AS IS" AND
    ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
    ARE DISCLAIMED. IN NO EVENT SHALL ECMA INTERNATIONAL BE LIABLE
    FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
    DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
    OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
    HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGE.
