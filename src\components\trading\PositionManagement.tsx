import { createSignal, createEffect, onMount, Show, For, createMemo } from 'solid-js'
import { createStore } from 'solid-js/store'
import { css } from '../../styled-system/css'
import { formatCurrency, formatPercent } from '../../utils/formatters'
import { useAtom } from 'jotai'
import { tradingAtom } from '../../stores/atoms'

interface Position {
  id: string
  symbol: string
  direction: 'long' | 'short'
  quantity: number
  avgPrice: number
  currentPrice: number
  marketValue: number
  unrealizedPnl: number
  unrealizedPnlPercent: number
  realizedPnl: number
  openTime: string
}

interface PartialCloseForm {
  symbol: string
  direction: string
  totalQuantity: number
  closeQuantity: number
  closeType: 'market' | 'limit'
  closePrice: number
}

interface Props {
  onPositionClose?: (positionId: string) => void
  onPartialClose?: (positionId: string, quantity: number) => void
  onRefresh?: () => void
}

export function PositionManagement(props: Props) {
  // State management
  const [trading] = useAtom(tradingAtom)
  const [loading, setLoading] = createSignal(false)
  const [closing, setClosing] = createSignal(false)
  const [partialCloseDialogVisible, setPartialCloseDialogVisible] = createSignal(false)
  
  // Mock positions data
  const [positions, setPositions] = createStore<Position[]>([
    {
      id: 'POS001',
      symbol: 'IF2312',
      direction: 'long',
      quantity: 200,
      avgPrice: 3840.5,
      currentPrice: 3850.2,
      marketValue: 770040,
      unrealizedPnl: 1940,
      unrealizedPnlPercent: 0.25,
      realizedPnl: 0,
      openTime: '2024-01-15 09:30:00'
    },
    {
      id: 'POS002',
      symbol: 'IC2312',
      direction: 'short',
      quantity: 100,
      avgPrice: 5430.0,
      currentPrice: 5420.8,
      marketValue: 542080,
      unrealizedPnl: 920,
      unrealizedPnlPercent: 0.17,
      realizedPnl: 0,
      openTime: '2024-01-15 10:15:00'
    }
  ])

  const [partialCloseForm, setPartialCloseForm] = createStore<PartialCloseForm>({
    symbol: '',
    direction: '',
    totalQuantity: 0,
    closeQuantity: 0,
    closeType: 'market',
    closePrice: 0
  })

  // Computed values
  const totalMarketValue = createMemo(() => {
    return positions.reduce((sum, pos) => sum + pos.marketValue, 0)
  })

  const totalUnrealizedPnl = createMemo(() => {
    return positions.reduce((sum, pos) => sum + pos.unrealizedPnl, 0)
  })

  const totalRealizedPnl = createMemo(() => {
    return positions.reduce((sum, pos) => sum + pos.realizedPnl, 0)
  })

  const totalPnl = createMemo(() => {
    return totalUnrealizedPnl() + totalRealizedPnl()
  })

  // Methods
  const formatNumber = (value: number)' : string => {
    return value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  }

  const formatTime = (time: string)' : string => {
    return new Date(time).toLocaleString('zh-CN')
  }

  const getPnlClass = (value: number): string => {
    if (value > 0) return css({ color: '#f56c6c' })
    if (value < 0) return css({ color: '#67c23a' })
    return css({ color: '#303133' })
  }

  const refreshPositions = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update position data (simulate price changes)
      setPositions(prev => prev.map(pos => {
        const priceChange = (Math.random() - 0.5) * 10
        const newCurrentPrice = pos.currentPrice + priceChange
        const newMarketValue = newCurrentPrice * pos.quantity
        
        let newUnrealizedPnl: number
        if (pos.direction === 'long') {
          newUnrealizedPnl = (newCurrentPrice - pos.avgPrice) * pos.quantity
        } else {
          newUnrealizedPnl = (pos.avgPrice - newCurrentPrice) * pos.quantity
        }
        
        const newUnrealizedPnlPercent = (newUnrealizedPnl / (pos.avgPrice * pos.quantity)) * 100

        return {
          ...pos,
          currentPrice: newCurrentPrice,
          marketValue: newMarketValue,
          unrealizedPnl: newUnrealizedPnl,
          unrealizedPnlPercent: newUnrealizedPnlPercent
        }
      }))
      
      console.log('持仓刷新成功')
      props.onRefresh?.()
    } catch (error) {
      console.error('持仓刷新失败')
    } finally {
      setLoading(false)
    }
  }

  const closeAllPositions = async () => {
    if (positions.length === 0) return

    const confirmed = confirm('确定要平仓所有持仓吗？')
    if (!confirmed) return

    setLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Clear all positions
      setPositions([])

      console.log('所有持仓已平仓')
    } catch (error) {
      console.error('平仓失败')
    } finally {
      setLoading(false)
    }
  }

  const partialClose = (position: Position) => {
    setPartialCloseForm({
      symbol: position.symbol,
      direction: position.direction === 'long' ? '多头' : '空头',
      totalQuantity: position.quantity,
      closeQuantity: 0,
      closeType: 'market',
      closePrice: position.currentPrice
    })
    setPartialCloseDialogVisible(true)
  }

  const confirmPartialClose = async () => {
    if (!partialCloseForm.closeQuantity) {
      console.error('请输入平仓数量')
      return
    }

    if (partialCloseForm.closeType === 'limit' && !partialCloseForm.closePrice) {
      console.error('请输入平仓价格')
      return
    }

    setClosing(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Update position
      setPositions(prev => prev.map(pos => {
        if (pos.symbol === partialCloseForm.symbol) {
          const newQuantity = pos.quantity - partialCloseForm.closeQuantity
          
          if (newQuantity <= 0) {
            // Position will be removed below
            return pos
          }

          const newMarketValue = pos.currentPrice * newQuantity
          
          let newUnrealizedPnl: number
          if (pos.direction === 'long') {
            newUnrealizedPnl = (pos.currentPrice - pos.avgPrice) * newQuantity
          } else {
            newUnrealizedPnl = (pos.avgPrice - pos.currentPrice) * newQuantity
          }
          
          const newUnrealizedPnlPercent = (newUnrealizedPnl / (pos.avgPrice * newQuantity)) * 100

          return {
            ...pos,
            quantity: newQuantity,
            marketValue: newMarketValue,
            unrealizedPnl: newUnrealizedPnl,
            unrealizedPnlPercent: newUnrealizedPnlPercent
          }
        }
        return pos
      }).filter(pos => pos.quantity > 0)) // Remove positions with 0 quantity

      console.log('部分平仓成功')
      setPartialCloseDialogVisible(false)
      props.onPartialClose?.(partialCloseForm.symbol, partialCloseForm.closeQuantity)
    } catch (error) {
      console.error('部分平仓失败')
    } finally {
      setClosing(false)
    }
  }

  const closePosition = async (position: Position) => {
    const confirmed = confirm(`确定要平仓 ${position.symbol} 的全部持仓吗？`)
    if (!confirmed) return

    setLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Remove position
      setPositions(prev => prev.filter(p => p.id !== position.id))

      console.log('持仓已平仓')
      props.onPositionClose?.(position.id)
    } catch (error) {
      console.error('平仓失败')
    } finally {
      setLoading(false)
    }
  }

  const resetPartialCloseForm = () => {
    setPartialCloseForm({
      symbol: '',
      direction: '',
      totalQuantity: 0,
      closeQuantity: 0,
      closeType: 'market',
      closePrice: 0
    })
  }

  // Effects
  onMount(() => {
    refreshPositions()
  })

  return (
    <div class={css({
      bg: 'white',
      borderRadius: '8px',
      padding: '20px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
    })}>
      {/* Header */}
      <div class={css({
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        paddingBottom: '16px',
        borderBottom: '1px solid #ebeef5',
        '@media (max-width: 768px)': {
          flexDirection: 'column',
          gap: '12px',
          alignItems: 'stretch'
        }
      })}>
        <div class={css({ display: 'flex', alignItems: 'center', gap: '12px' })}>
          <h3 class={css({ margin: 0, fontSize: '18px', color: '#303133' })}>持仓管理</h3>
          <span class={css({
            padding: '2px 8px',
            bg: '#f0f2f5',
            borderRadius: '12px',
            fontSize: '12px',
            color: '#606266'
          })}>
            总持仓: {positions.length}
          </span>
        </div>

        <div class={css({ display: 'flex', gap: '8px' })}>
          <button
            onClick={refreshPositions}
            disabled={loading()}
            class={css({
              padding: '8px 16px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              bg: 'white',
              opacity: loading() ? 0.6 ' : 1
            })}
          >
            {loading() ? '刷新中...' : '🔄 刷新'}
          </button>
          <button
            onClick={closeAllPositions}
            disabled={positions.length === 0}
            class={css({
              padding: '8px 16px',
              border: '1px solid #f56c6c',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer',
              bg: 'white',
              color: '#f56c6c',
              opacity: positions.length === 0 ? 0.6 : 1
            })}
          >
            全部平仓
          </button>
        </div>
      </div>

      {/* Position Summary */}
      <div class={css({
        display: 'grid',
        gridTemplateColumns: 'repeat(4, 1fr)',
        gap: '16px',
        marginBottom: '20px',
        padding: '16px',
        bg: '#f5f7fa',
        borderRadius: '6px',
        '@media (max-width: 768px)': {
          gridTemplateColumns: 'repeat(2, 1fr)'
        }
      })}>
        <div class={css({ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' })}>
          <span class={css({ fontSize: '12px', color: '#909399', marginBottom: '4px' })}>总持仓市值</span>
          <span class={css({ fontSize: '16px', fontWeight: 600, color: '#303133' })}>
            {formatNumber(totalMarketValue())}
          </span>
        </div>
        <div class={css({ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' })}>
          <span class={css({ fontSize: '12px', color: '#909399', marginBottom: '4px' })}>浮动盈亏</span>
          <span class={`${css({ fontSize: '16px', fontWeight: 600 })} ${getPnlClass(totalUnrealizedPnl())}`}>
            {totalUnrealizedPnl() >= 0 ? '+' : ''}{formatNumber(totalUnrealizedPnl())}
          </span>
        </div>
        <div class={css({ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' })}>
          <span class={css({ fontSize: '12px', color: '#909399', marginBottom: '4px' })}>已实现盈亏</span>
          <span class={`${css({ fontSize: '16px', fontWeight: 600 })} ${getPnlClass(totalRealizedPnl())}`}>
            {totalRealizedPnl() >= 0 ? '+' : ''}{formatNumber(totalRealizedPnl())}
          </span>
        </div>
        <div class={css({ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' })}>
          <span class={css({ fontSize: '12px', color: '#909399', marginBottom: '4px' })}>总盈亏</span>
          <span class={`${css({ fontSize: '16px', fontWeight: 600 })} ${getPnlClass(totalPnl())}`}>
            {totalPnl() >= 0 ? '+' : ''}{formatNumber(totalPnl())}
          </span>
        </div>
      </div>

      {/* Positions Table */}
      <div class={css({
        border: '1px solid #ebeef5',
        borderRadius: '4px',
        overflow: 'hidden'
      })}>
        <div class={css({
          display: 'grid',
          gridTemplateColumns: '100px 60px 80px 80px 80px 100px 100px 80px 140px 160px',
          bg: '#f5f7fa',
          borderBottom: '1px solid #ebeef5',
          fontSize: '14px',
          fontWeight: 'bold'
        })}>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>品种</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>方向</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>持仓量</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>均价</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>现价</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>市值</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>浮动盈亏</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>盈亏比例</div>
          <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>开仓时间</div>
          <div class={css({ padding: '12px 8px' })}>操作</div>
        </div>

        <Show when={positions.length > 0} fallback={
          <div class={css({
            padding: '40px',
            textAlign: 'center',
            color: '#909399',
            fontSize: '14px'
          })}>
            暂无持仓
          </div>
        }>
          <div class={css({ maxHeight: '400px', overflowY: 'auto' })}>
            <For each={positions}>
              {(position) => (
                <div class={css({
                  display: 'grid',
                  gridTemplateColumns: '100px 60px 80px 80px 80px 100px 100px 80px 140px 160px',
                  borderBottom: '1px solid #ebeef5',
                  fontSize: '14px',
                  '&:hover: { bg: '#f5f7fa' }
                })}>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>
                    {position.symbol}
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5' })}>
                    <span class={css({
                      padding: '2px 6px',
                      borderRadius: '3px',
                      fontSize: '12px',
                      bg: position.direction === 'long' ? '#f0f9ff ' : '#fef2f2',
                      color: position.direction === 'long' ? '#1d4ed8 ' : '#dc2626'
                    })}>
                      {position.direction === 'long' ? '多' : '空'}
                    </span>
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>
                    {position.quantity}
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>
                    {position.avgPrice.toFixed(2)}
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>
                    {position.currentPrice.toFixed(2)}
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })}>
                    {formatNumber(position.marketValue)}
                  </div>
                  <div class={`${css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })} ${getPnlClass(position.unrealizedPnl)}`}>
                    {position.unrealizedPnl >= 0 ? '+' : ''}{formatNumber(position.unrealizedPnl)}
                  </div>
                  <div class={`${css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', textAlign: 'right' })} ${getPnlClass(position.unrealizedPnlPercent)}`}>
                    {position.unrealizedPnlPercent >= 0 ? '+' : ''}{position.unrealizedPnlPercent.toFixed(2)}%
                  </div>
                  <div class={css({ padding: '12px 8px', borderRight: '1px solid #ebeef5', fontSize: '12px' })}>
                    {formatTime(position.openTime)}
                  </div>
                  <div class={css({ padding: '12px 8px', display: 'flex', gap: '4px' })}>
                    <button
                      onClick={() => partialClose(position)}
                      class={css({
                        padding: '4px 8px',
                        border: '1px solid #409eff',
                        borderRadius: '3px',
                        fontSize: '12px',
                        cursor: 'pointer',
                        bg: 'white',
                        color: '#409eff'
                      })}
                    >
                      部分平仓
                    </button>
                    <button
                      onClick={() => closePosition(position)}
                      class={css({
                        padding: '4px 8px',
                        border: '1px solid #f56c6c',
                        borderRadius: '3px',
                        fontSize: '12px',
                        cursor: 'pointer',
                        bg: 'white',
                        color: '#f56c6c'
                      })}
                    >
                      全部平仓
                    </button>
                  </div>
                </div>
              )}
            </For>
          </div>
        </Show>
      </div>

      {/* Partial Close Dialog */}
      <Show when={partialCloseDialogVisible()}>
        <div class={css({
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          bg: 'rgba(0, 0, 0, 0.5)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        })}>
          <div class={css({
            bg: 'white',
            borderRadius: '8px',
            padding: '24px',
            width: '400px',
            maxWidth: '90vw'
          })}>
            <h3 class={css({ margin: '0 0 16px 0', fontSize: '18px' })}>部分平仓</h3>
            
            <div class={css({ marginBottom: '16px' })}>
              <label class={css({ display: 'block', marginBottom: '4px', fontSize: '14px' })}>品种</label>
              <input
                value={partialCloseForm.symbol}
                disabled
                class={css({
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  bg: '#f5f7fa',
                  color: '#909399'
                })}
              />
            </div>

            <div class={css({ marginBottom: '16px' })}>
              <label class={css({ display: 'block', marginBottom: '4px', fontSize: '14px' })}>方向</label>
              <input
                value={partialCloseForm.direction}
                disabled
                class={css({
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  bg: '#f5f7fa',
                  color: '#909399'
                })}
              />
            </div>

            <div class={css({ marginBottom: '16px' })}>
              <label class={css({ display: 'block', marginBottom: '4px', fontSize: '14px' })}>持仓量</label>
              <input
                value={partialCloseForm.totalQuantity}
                disabled
                class={css({
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  bg: '#f5f7fa',
                  color: '#909399'
                })}
              />
            </div>

            <div class={css({ marginBottom: '16px' })}>
              <label class={css({ display: 'block', marginBottom: '4px', fontSize: '14px' })}>平仓数量</label>
              <input
                type="number"
                step="100"
                min="100"
                max={partialCloseForm.totalQuantity}
                value={partialCloseForm.closeQuantity}
                onInput={(e) => setPartialCloseForm('closeQuantity', Number(e.currentTarget.value))}
                class={css({
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px'
                })}
              />
            </div>

            <div class={css({ marginBottom: '16px' })}>
              <label class={css({ display: 'block', marginBottom: '4px', fontSize: '14px' })}>平仓类型</label>
              <select
                value={partialCloseForm.closeType}
                onChange={(e) => setPartialCloseForm('closeType', e.currentTarget.value as 'market' | 'limit')}
                class={css({
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px'
                })}
              >
                <option value="market">市价平仓</option>
                <option value="limit">限价平仓</option>
              </select>
            </div>

            <Show when={partialCloseForm.closeType === 'limit'}>
              <div class={css({ marginBottom: '16px' })}>
                <label class={css({ display: 'block', marginBottom: '4px', fontSize: '14px' })}>平仓价格</label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={partialCloseForm.closePrice}
                  onInput={(e) => setPartialCloseForm('closePrice', Number(e.currentTarget.value))}
                  class={css({
                    width: '100%',
                    padding: '8px 12px',
                    border: '1px solid #dcdfe6',
                    borderRadius: '4px'
                  })}
                />
              </div>
            </Show>

            <div class={css({ display: 'flex', gap: '8px', justifyContent: 'flex-end', marginTop: '24px' })}>
              <button
                onClick={() => {
                  setPartialCloseDialogVisible(false)
                  resetPartialCloseForm()
                }}
                class={css({
                  padding: '8px 16px',
                  border: '1px solid #dcdfe6',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  bg: 'white'
                })}
              >
                取消
              </button>
              <button
                onClick={confirmPartialClose}
                disabled={closing()}
                class={css({
                  padding: '8px 16px',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  bg: '#409eff',
                  color: 'white',
                  opacity: closing() ? 0.6 ' : 1
                })}
              >
                {closing() ? '平仓中...' : '确认平仓'}
              </button>
            </div>
          </div>
        </div>
      </Show>
    </div>
  )
}

export default PositionManagement