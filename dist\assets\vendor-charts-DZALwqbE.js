function S(h){var t=h.width,i=h.height;if(t<0)throw new Error("Negative width is not allowed for Size");if(i<0)throw new Error("Negative height is not allowed for Size");return{width:t,height:i}}function j(h,t){return h.width===t.width&&h.height===t.height}var As=function(){function h(t){var i=this;this._resolutionListener=function(){return i._onResolutionChanged()},this._resolutionMediaQueryList=null,this._observers=[],this._window=t,this._installResolutionListener()}return h.prototype.dispose=function(){this._uninstallResolutionListener(),this._window=null},Object.defineProperty(h.prototype,"value",{get:function(){return this._window.devicePixelRatio},enumerable:!1,configurable:!0}),h.prototype.subscribe=function(t){var i=this,s={next:t};return this._observers.push(s),{unsubscribe:function(){i._observers=i._observers.filter(function(e){return e!==s})}}},h.prototype._installResolutionListener=function(){if(this._resolutionMediaQueryList!==null)throw new Error("Resolution listener is already installed");var t=this._window.devicePixelRatio;this._resolutionMediaQueryList=this._window.matchMedia("all and (resolution: ".concat(t,"dppx)")),this._resolutionMediaQueryList.addListener(this._resolutionListener)},h.prototype._uninstallResolutionListener=function(){this._resolutionMediaQueryList!==null&&(this._resolutionMediaQueryList.removeListener(this._resolutionListener),this._resolutionMediaQueryList=null)},h.prototype._reinstallResolutionListener=function(){this._uninstallResolutionListener(),this._installResolutionListener()},h.prototype._onResolutionChanged=function(){var t=this;this._observers.forEach(function(i){return i.next(t._window.devicePixelRatio)}),this._reinstallResolutionListener()},h}();function Us(h){return new As(h)}var qs=function(){function h(t,i,s){var e;this._canvasElement=null,this._bitmapSizeChangedListeners=[],this._suggestedBitmapSize=null,this._suggestedBitmapSizeChangedListeners=[],this._devicePixelRatioObservable=null,this._canvasElementResizeObserver=null,this._canvasElement=t,this._canvasElementClientSize=S({width:this._canvasElement.clientWidth,height:this._canvasElement.clientHeight}),this._transformBitmapSize=i??function(n){return n},this._allowResizeObserver=(e=s?.allowResizeObserver)!==null&&e!==void 0?e:!0,this._chooseAndInitObserver()}return h.prototype.dispose=function(){var t,i;if(this._canvasElement===null)throw new Error("Object is disposed");(t=this._canvasElementResizeObserver)===null||t===void 0||t.disconnect(),this._canvasElementResizeObserver=null,(i=this._devicePixelRatioObservable)===null||i===void 0||i.dispose(),this._devicePixelRatioObservable=null,this._suggestedBitmapSizeChangedListeners.length=0,this._bitmapSizeChangedListeners.length=0,this._canvasElement=null},Object.defineProperty(h.prototype,"canvasElement",{get:function(){if(this._canvasElement===null)throw new Error("Object is disposed");return this._canvasElement},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"canvasElementClientSize",{get:function(){return this._canvasElementClientSize},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"bitmapSize",{get:function(){return S({width:this.canvasElement.width,height:this.canvasElement.height})},enumerable:!1,configurable:!0}),h.prototype.resizeCanvasElement=function(t){this._canvasElementClientSize=S(t),this.canvasElement.style.width="".concat(this._canvasElementClientSize.width,"px"),this.canvasElement.style.height="".concat(this._canvasElementClientSize.height,"px"),this._invalidateBitmapSize()},h.prototype.subscribeBitmapSizeChanged=function(t){this._bitmapSizeChangedListeners.push(t)},h.prototype.unsubscribeBitmapSizeChanged=function(t){this._bitmapSizeChangedListeners=this._bitmapSizeChangedListeners.filter(function(i){return i!==t})},Object.defineProperty(h.prototype,"suggestedBitmapSize",{get:function(){return this._suggestedBitmapSize},enumerable:!1,configurable:!0}),h.prototype.subscribeSuggestedBitmapSizeChanged=function(t){this._suggestedBitmapSizeChangedListeners.push(t)},h.prototype.unsubscribeSuggestedBitmapSizeChanged=function(t){this._suggestedBitmapSizeChangedListeners=this._suggestedBitmapSizeChangedListeners.filter(function(i){return i!==t})},h.prototype.applySuggestedBitmapSize=function(){if(this._suggestedBitmapSize!==null){var t=this._suggestedBitmapSize;this._suggestedBitmapSize=null,this._resizeBitmap(t),this._emitSuggestedBitmapSizeChanged(t,this._suggestedBitmapSize)}},h.prototype._resizeBitmap=function(t){var i=this.bitmapSize;j(i,t)||(this.canvasElement.width=t.width,this.canvasElement.height=t.height,this._emitBitmapSizeChanged(i,t))},h.prototype._emitBitmapSizeChanged=function(t,i){var s=this;this._bitmapSizeChangedListeners.forEach(function(e){return e.call(s,t,i)})},h.prototype._suggestNewBitmapSize=function(t){var i=this._suggestedBitmapSize,s=S(this._transformBitmapSize(t,this._canvasElementClientSize)),e=j(this.bitmapSize,s)?null:s;i===null&&e===null||i!==null&&e!==null&&j(i,e)||(this._suggestedBitmapSize=e,this._emitSuggestedBitmapSizeChanged(i,e))},h.prototype._emitSuggestedBitmapSizeChanged=function(t,i){var s=this;this._suggestedBitmapSizeChangedListeners.forEach(function(e){return e.call(s,t,i)})},h.prototype._chooseAndInitObserver=function(){var t=this;if(!this._allowResizeObserver){this._initDevicePixelRatioObservable();return}Gs().then(function(i){return i?t._initResizeObserver():t._initDevicePixelRatioObservable()})},h.prototype._initDevicePixelRatioObservable=function(){var t=this;if(this._canvasElement!==null){var i=xi(this._canvasElement);if(i===null)throw new Error("No window is associated with the canvas");this._devicePixelRatioObservable=Us(i),this._devicePixelRatioObservable.subscribe(function(){return t._invalidateBitmapSize()}),this._invalidateBitmapSize()}},h.prototype._invalidateBitmapSize=function(){var t,i;if(this._canvasElement!==null){var s=xi(this._canvasElement);if(s!==null){var e=(i=(t=this._devicePixelRatioObservable)===null||t===void 0?void 0:t.value)!==null&&i!==void 0?i:s.devicePixelRatio,n=this._canvasElement.getClientRects(),r=n[0]!==void 0?Qs(n[0],e):S({width:this._canvasElementClientSize.width*e,height:this._canvasElementClientSize.height*e});this._suggestNewBitmapSize(r)}}},h.prototype._initResizeObserver=function(){var t=this;this._canvasElement!==null&&(this._canvasElementResizeObserver=new ResizeObserver(function(i){var s=i.find(function(r){return r.target===t._canvasElement});if(!(!s||!s.devicePixelContentBoxSize||!s.devicePixelContentBoxSize[0])){var e=s.devicePixelContentBoxSize[0],n=S({width:e.inlineSize,height:e.blockSize});t._suggestNewBitmapSize(n)}}),this._canvasElementResizeObserver.observe(this._canvasElement,{box:"device-pixel-content-box"}))},h}();function Js(h,t){return new qs(h,t.transform,t.options)}function xi(h){return h.ownerDocument.defaultView}function Gs(){return new Promise(function(h){var t=new ResizeObserver(function(i){h(i.every(function(s){return"devicePixelContentBoxSize"in s})),t.disconnect()});t.observe(document.body,{box:"device-pixel-content-box"})}).catch(function(){return!1})}function Qs(h,t){return S({width:Math.round(h.left*t+h.width*t)-Math.round(h.left*t),height:Math.round(h.top*t+h.height*t)-Math.round(h.top*t)})}var Ys=function(){function h(t,i,s){if(i.width===0||i.height===0)throw new TypeError("Rendering target could only be created on a media with positive width and height");if(this._mediaSize=i,s.width===0||s.height===0)throw new TypeError("Rendering target could only be created using a bitmap with positive integer width and height");this._bitmapSize=s,this._context=t}return h.prototype.useMediaCoordinateSpace=function(t){try{return this._context.save(),this._context.setTransform(1,0,0,1,0,0),this._context.scale(this._horizontalPixelRatio,this._verticalPixelRatio),t({context:this._context,mediaSize:this._mediaSize})}finally{this._context.restore()}},h.prototype.useBitmapCoordinateSpace=function(t){try{return this._context.save(),this._context.setTransform(1,0,0,1,0,0),t({context:this._context,mediaSize:this._mediaSize,bitmapSize:this._bitmapSize,horizontalPixelRatio:this._horizontalPixelRatio,verticalPixelRatio:this._verticalPixelRatio})}finally{this._context.restore()}},Object.defineProperty(h.prototype,"_horizontalPixelRatio",{get:function(){return this._bitmapSize.width/this._mediaSize.width},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"_verticalPixelRatio",{get:function(){return this._bitmapSize.height/this._mediaSize.height},enumerable:!1,configurable:!0}),h}();function F(h,t){var i=h.canvasElementClientSize;if(i.width===0||i.height===0)return null;var s=h.bitmapSize;if(s.width===0||s.height===0)return null;var e=h.canvasElement.getContext("2d",t);return e===null?null:new Ys(e,i,s)}/*!
 * @license
 * TradingView Lightweight Charts™ v4.2.3
 * Copyright (c) 2025 TradingView, Inc.
 * Licensed under Apache License 2.0 https://www.apache.org/licenses/LICENSE-2.0
 */const Zs={upColor:"#26a69a",downColor:"#ef5350",wickVisible:!0,borderVisible:!0,borderColor:"#378658",borderUpColor:"#26a69a",borderDownColor:"#ef5350",wickColor:"#737375",wickUpColor:"#26a69a",wickDownColor:"#ef5350"},te={upColor:"#26a69a",downColor:"#ef5350",openVisible:!0,thinBars:!0},ie={color:"#2196f3",lineStyle:0,lineWidth:3,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},se={topColor:"rgba( 46, 220, 135, 0.4)",bottomColor:"rgba( 40, 221, 100, 0)",invertFilledArea:!1,lineColor:"#33D778",lineStyle:0,lineWidth:3,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},ee={baseValue:{type:"price",price:0},topFillColor1:"rgba(38, 166, 154, 0.28)",topFillColor2:"rgba(38, 166, 154, 0.05)",topLineColor:"rgba(38, 166, 154, 1)",bottomFillColor1:"rgba(239, 83, 80, 0.05)",bottomFillColor2:"rgba(239, 83, 80, 0.28)",bottomLineColor:"rgba(239, 83, 80, 1)",lineWidth:3,lineStyle:0,lineType:0,lineVisible:!0,crosshairMarkerVisible:!0,crosshairMarkerRadius:4,crosshairMarkerBorderColor:"",crosshairMarkerBorderWidth:2,crosshairMarkerBackgroundColor:"",lastPriceAnimation:0,pointMarkersVisible:!1},he={color:"#26a69a",base:0},ws={color:"#2196f3"},ys={title:"",visible:!0,lastValueVisible:!0,priceLineVisible:!0,priceLineSource:0,priceLineWidth:1,priceLineColor:"",priceLineStyle:2,baseLineVisible:!0,baseLineWidth:1,baseLineColor:"#B2B5BE",baseLineStyle:0,priceFormat:{type:"price",precision:2,minMove:.01}};var zi,Ci;function X(h,t){const i={0:[],1:[h.lineWidth,h.lineWidth],2:[2*h.lineWidth,2*h.lineWidth],3:[6*h.lineWidth,6*h.lineWidth],4:[h.lineWidth,4*h.lineWidth]}[t];h.setLineDash(i)}function Ss(h,t,i,s){h.beginPath();const e=h.lineWidth%2?.5:0;h.moveTo(i,t+e),h.lineTo(s,t+e),h.stroke()}function I(h,t){if(!h)throw new Error("Assertion failed"+(t?": "+t:""))}function k(h){if(h===void 0)throw new Error("Value is undefined");return h}function v(h){if(h===null)throw new Error("Value is null");return h}function J(h){return v(k(h))}(function(h){h[h.Simple=0]="Simple",h[h.WithSteps=1]="WithSteps",h[h.Curved=2]="Curved"})(zi||(zi={})),function(h){h[h.Solid=0]="Solid",h[h.Dotted=1]="Dotted",h[h.Dashed=2]="Dashed",h[h.LargeDashed=3]="LargeDashed",h[h.SparseDotted=4]="SparseDotted"}(Ci||(Ci={}));const Ei={khaki:"#f0e68c",azure:"#f0ffff",aliceblue:"#f0f8ff",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",gray:"#808080",green:"#008000",honeydew:"#f0fff0",floralwhite:"#fffaf0",lightblue:"#add8e6",lightcoral:"#f08080",lemonchiffon:"#fffacd",hotpink:"#ff69b4",lightyellow:"#ffffe0",greenyellow:"#adff2f",lightgoldenrodyellow:"#fafad2",limegreen:"#32cd32",linen:"#faf0e6",lightcyan:"#e0ffff",magenta:"#f0f",maroon:"#800000",olive:"#808000",orange:"#ffa500",oldlace:"#fdf5e6",mediumblue:"#0000cd",transparent:"#0000",lime:"#0f0",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",midnightblue:"#191970",orchid:"#da70d6",mediumorchid:"#ba55d3",mediumturquoise:"#48d1cc",orangered:"#ff4500",royalblue:"#4169e1",powderblue:"#b0e0e6",red:"#f00",coral:"#ff7f50",turquoise:"#40e0d0",white:"#fff",whitesmoke:"#f5f5f5",wheat:"#f5deb3",teal:"#008080",steelblue:"#4682b4",bisque:"#ffe4c4",aquamarine:"#7fffd4",aqua:"#0ff",sienna:"#a0522d",silver:"#c0c0c0",springgreen:"#00ff7f",antiquewhite:"#faebd7",burlywood:"#deb887",brown:"#a52a2a",beige:"#f5f5dc",chocolate:"#d2691e",chartreuse:"#7fff00",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cadetblue:"#5f9ea0",tomato:"#ff6347",fuchsia:"#f0f",blue:"#00f",salmon:"#fa8072",blanchedalmond:"#ffebcd",slateblue:"#6a5acd",slategray:"#708090",thistle:"#d8bfd8",tan:"#d2b48c",cyan:"#0ff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",blueviolet:"#8a2be2",black:"#000",darkmagenta:"#8b008b",darkslateblue:"#483d8b",darkkhaki:"#bdb76b",darkorchid:"#9932cc",darkorange:"#ff8c00",darkgreen:"#006400",darkred:"#8b0000",dodgerblue:"#1e90ff",darkslategray:"#2f4f4f",dimgray:"#696969",deepskyblue:"#00bfff",firebrick:"#b22222",forestgreen:"#228b22",indigo:"#4b0082",ivory:"#fffff0",lavenderblush:"#fff0f5",feldspar:"#d19275",indianred:"#cd5c5c",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightskyblue:"#87cefa",lightslategray:"#789",lightslateblue:"#8470ff",snow:"#fffafa",lightseagreen:"#20b2aa",lightsalmon:"#ffa07a",darksalmon:"#e9967a",darkviolet:"#9400d3",mediumpurple:"#9370d8",mediumaquamarine:"#66cdaa",skyblue:"#87ceeb",lavender:"#e6e6fa",lightsteelblue:"#b0c4de",mediumvioletred:"#c71585",mintcream:"#f5fffa",navajowhite:"#ffdead",navy:"#000080",olivedrab:"#6b8e23",palevioletred:"#d87093",violetred:"#d02090",yellow:"#ff0",yellowgreen:"#9acd32",lawngreen:"#7cfc00",pink:"#ffc0cb",paleturquoise:"#afeeee",palegoldenrod:"#eee8aa",darkolivegreen:"#556b2f",darkseagreen:"#8fbc8f",darkturquoise:"#00ced1",peachpuff:"#ffdab9",deeppink:"#ff1493",violet:"#ee82ee",palegreen:"#98fb98",mediumseagreen:"#3cb371",peru:"#cd853f",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",purple:"#800080",seagreen:"#2e8b57",seashell:"#fff5ee",papayawhip:"#ffefd5",mediumslateblue:"#7b68ee",plum:"#dda0dd",mediumspringgreen:"#00fa9a"};function N(h){return h<0?0:h>255?255:Math.round(h)||0}function _s(h){return h<=0||h>1?Math.min(Math.max(h,0),1):Math.round(1e4*h)/1e4}const ne=/^#([0-9a-f])([0-9a-f])([0-9a-f])([0-9a-f])?$/i,re=/^#([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})?$/i,le=/^rgb\(\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*\)$/,oe=/^rgba\(\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?\d{1,10})\s*,\s*(-?\d*\.?\d+)\s*\)$/;function rt(h){(h=h.toLowerCase())in Ei&&(h=Ei[h]);{const t=oe.exec(h)||le.exec(h);if(t)return[N(parseInt(t[1],10)),N(parseInt(t[2],10)),N(parseInt(t[3],10)),_s(t.length<5?1:parseFloat(t[4]))]}{const t=re.exec(h);if(t)return[N(parseInt(t[1],16)),N(parseInt(t[2],16)),N(parseInt(t[3],16)),1]}{const t=ne.exec(h);if(t)return[N(17*parseInt(t[1],16)),N(17*parseInt(t[2],16)),N(17*parseInt(t[3],16)),1]}throw new Error(`Cannot parse color: ${h}`)}function Ms(h){return .199*h[0]+.687*h[1]+.114*h[2]}function Et(h){const t=rt(h);return{t:`rgb(${t[0]}, ${t[1]}, ${t[2]})`,i:Ms(t)>160?"black":"white"}}class M{constructor(){this.h=[]}l(t,i,s){const e={o:t,_:i,u:s===!0};this.h.push(e)}v(t){const i=this.h.findIndex(s=>t===s.o);i>-1&&this.h.splice(i,1)}p(t){this.h=this.h.filter(i=>i._!==t)}m(t,i,s){const e=[...this.h];this.h=this.h.filter(n=>!n.u),e.forEach(n=>n.o(t,i,s))}M(){return this.h.length>0}S(){this.h=[]}}function R(h,...t){for(const i of t)for(const s in i)i[s]!==void 0&&Object.prototype.hasOwnProperty.call(i,s)&&!["__proto__","constructor","prototype"].includes(s)&&(typeof i[s]!="object"||h[s]===void 0||Array.isArray(i[s])?h[s]=i[s]:R(h[s],i[s]));return h}function P(h){return typeof h=="number"&&isFinite(h)}function lt(h){return typeof h=="number"&&h%1==0}function ct(h){return typeof h=="string"}function mt(h){return typeof h=="boolean"}function W(h){const t=h;if(!t||typeof t!="object")return t;let i,s,e;for(s in i=Array.isArray(t)?[]:{},t)t.hasOwnProperty(s)&&(e=t[s],i[s]=e&&typeof e=="object"?W(e):e);return i}function ae(h){return h!==null}function ot(h){return h===null?void 0:h}const oi="-apple-system, BlinkMacSystemFont, 'Trebuchet MS', Roboto, Ubuntu, sans-serif";function Y(h,t,i){return t===void 0&&(t=oi),`${i=i!==void 0?`${i} `:""}${h}px ${t}`}class ue{constructor(t){this.k={C:1,T:5,P:NaN,R:"",D:"",V:"",O:"",B:0,A:0,I:0,L:0,N:0},this.F=t}W(){const t=this.k,i=this.j(),s=this.H();return t.P===i&&t.D===s||(t.P=i,t.D=s,t.R=Y(i,s),t.L=2.5/12*i,t.B=t.L,t.A=i/12*t.T,t.I=i/12*t.T,t.N=0),t.V=this.$(),t.O=this.U(),this.k}$(){return this.F.W().layout.textColor}U(){return this.F.q()}j(){return this.F.W().layout.fontSize}H(){return this.F.W().layout.fontFamily}}class ai{constructor(){this.Y=[]}Z(t){this.Y=t}X(t,i,s){this.Y.forEach(e=>{e.X(t,i,s)})}}class T{X(t,i,s){t.useBitmapCoordinateSpace(e=>this.K(e,i,s))}}class ce extends T{constructor(){super(...arguments),this.G=null}J(t){this.G=t}K({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(this.G===null||this.G.tt===null)return;const e=this.G.tt,n=this.G,r=Math.max(1,Math.floor(i))%2/2,l=o=>{t.beginPath();for(let a=e.to-1;a>=e.from;--a){const u=n.it[a],c=Math.round(u.nt*i)+r,f=u.st*s,d=o*s+r;t.moveTo(c,f),t.arc(c,f,d,0,2*Math.PI)}t.fill()};n.et>0&&(t.fillStyle=n.rt,l(n.ht+n.et)),t.fillStyle=n.lt,l(n.ht)}}function fe(){return{it:[{nt:0,st:0,ot:0,_t:0}],lt:"",rt:"",ht:0,et:0,tt:null}}const de={from:0,to:1};class me{constructor(t,i){this.ut=new ai,this.ct=[],this.dt=[],this.ft=!0,this.F=t,this.vt=i,this.ut.Z(this.ct)}bt(t){const i=this.F.wt();i.length!==this.ct.length&&(this.dt=i.map(fe),this.ct=this.dt.map(s=>{const e=new ce;return e.J(s),e}),this.ut.Z(this.ct)),this.ft=!0}gt(){return this.ft&&(this.Mt(),this.ft=!1),this.ut}Mt(){const t=this.vt.W().mode===2,i=this.F.wt(),s=this.vt.xt(),e=this.F.St();i.forEach((n,r)=>{var l;const o=this.dt[r],a=n.kt(s);if(t||a===null||!n.yt())return void(o.tt=null);const u=v(n.Ct());o.lt=a.Tt,o.ht=a.ht,o.et=a.Pt,o.it[0]._t=a._t,o.it[0].st=n.Dt().Rt(a._t,u.Vt),o.rt=(l=a.Ot)!==null&&l!==void 0?l:this.F.Bt(o.it[0].st/n.Dt().At()),o.it[0].ot=s,o.it[0].nt=e.It(s),o.tt=de})}}class pe extends T{constructor(t){super(),this.zt=t}K({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:e}){if(this.zt===null)return;const n=this.zt.Lt.yt,r=this.zt.Et.yt;if(!n&&!r)return;const l=Math.round(this.zt.nt*s),o=Math.round(this.zt.st*e);t.lineCap="butt",n&&l>=0&&(t.lineWidth=Math.floor(this.zt.Lt.et*s),t.strokeStyle=this.zt.Lt.V,t.fillStyle=this.zt.Lt.V,X(t,this.zt.Lt.Nt),function(a,u,c,f){a.beginPath();const d=a.lineWidth%2?.5:0;a.moveTo(u+d,c),a.lineTo(u+d,f),a.stroke()}(t,l,0,i.height)),r&&o>=0&&(t.lineWidth=Math.floor(this.zt.Et.et*e),t.strokeStyle=this.zt.Et.V,t.fillStyle=this.zt.Et.V,X(t,this.zt.Et.Nt),Ss(t,o,0,i.width))}}class ve{constructor(t){this.ft=!0,this.Ft={Lt:{et:1,Nt:0,V:"",yt:!1},Et:{et:1,Nt:0,V:"",yt:!1},nt:0,st:0},this.Wt=new pe(this.Ft),this.jt=t}bt(){this.ft=!0}gt(){return this.ft&&(this.Mt(),this.ft=!1),this.Wt}Mt(){const t=this.jt.yt(),i=v(this.jt.Ht()),s=i.$t().W().crosshair,e=this.Ft;if(s.mode===2)return e.Et.yt=!1,void(e.Lt.yt=!1);e.Et.yt=t&&this.jt.Ut(i),e.Lt.yt=t&&this.jt.qt(),e.Et.et=s.horzLine.width,e.Et.Nt=s.horzLine.style,e.Et.V=s.horzLine.color,e.Lt.et=s.vertLine.width,e.Lt.Nt=s.vertLine.style,e.Lt.V=s.vertLine.color,e.nt=this.jt.Yt(),e.st=this.jt.Zt()}}function be(h,t,i,s,e,n){h.fillRect(t+n,i,s-2*n,n),h.fillRect(t+n,i+e-n,s-2*n,n),h.fillRect(t,i,n,e),h.fillRect(t+s-n,i,n,e)}function kt(h,t,i,s,e,n){h.save(),h.globalCompositeOperation="copy",h.fillStyle=n,h.fillRect(t,i,s,e),h.restore()}function ki(h,t,i,s,e,n){h.beginPath(),h.roundRect?h.roundRect(t,i,s,e,n):(h.lineTo(t+s-n[1],i),n[1]!==0&&h.arcTo(t+s,i,t+s,i+n[1],n[1]),h.lineTo(t+s,i+e-n[2]),n[2]!==0&&h.arcTo(t+s,i+e,t+s-n[2],i+e,n[2]),h.lineTo(t+n[3],i+e),n[3]!==0&&h.arcTo(t,i+e,t,i+e-n[3],n[3]),h.lineTo(t,i+n[0]),n[0]!==0&&h.arcTo(t,i,t+n[0],i,n[0]))}function Li(h,t,i,s,e,n,r=0,l=[0,0,0,0],o=""){if(h.save(),!r||!o||o===n)return ki(h,t,i,s,e,l),h.fillStyle=n,h.fill(),void h.restore();const a=r/2;var u;ki(h,t+a,i+a,s-r,e-r,(u=-a,l.map(c=>c===0?c:c+u))),n!=="transparent"&&(h.fillStyle=n,h.fill()),o!=="transparent"&&(h.lineWidth=r,h.strokeStyle=o,h.closePath(),h.stroke()),h.restore()}function xs(h,t,i,s,e,n,r){h.save(),h.globalCompositeOperation="copy";const l=h.createLinearGradient(0,0,0,e);l.addColorStop(0,n),l.addColorStop(1,r),h.fillStyle=l,h.fillRect(t,i,s,e),h.restore()}class Vi{constructor(t,i){this.J(t,i)}J(t,i){this.zt=t,this.Xt=i}At(t,i){return this.zt.yt?t.P+t.L+t.B:0}X(t,i,s,e){if(!this.zt.yt||this.zt.Kt.length===0)return;const n=this.zt.V,r=this.Xt.t,l=t.useBitmapCoordinateSpace(o=>{const a=o.context;a.font=i.R;const u=this.Gt(o,i,s,e),c=u.Jt;return u.Qt?Li(a,c.ti,c.ii,c.ni,c.si,r,c.ei,[c.ht,0,0,c.ht],r):Li(a,c.ri,c.ii,c.ni,c.si,r,c.ei,[0,c.ht,c.ht,0],r),this.zt.hi&&(a.fillStyle=n,a.fillRect(c.ri,c.li,c.ai-c.ri,c.oi)),this.zt._i&&(a.fillStyle=i.O,a.fillRect(u.Qt?c.ui-c.ei:0,c.ii,c.ei,c.ci-c.ii)),u});t.useMediaCoordinateSpace(({context:o})=>{const a=l.di;o.font=i.R,o.textAlign=l.Qt?"right":"left",o.textBaseline="middle",o.fillStyle=n,o.fillText(this.zt.Kt,a.fi,(a.ii+a.ci)/2+a.pi)})}Gt(t,i,s,e){var n;const{context:r,bitmapSize:l,mediaSize:o,horizontalPixelRatio:a,verticalPixelRatio:u}=t,c=this.zt.hi||!this.zt.mi?i.T:0,f=this.zt.bi?i.C:0,d=i.L+this.Xt.wi,m=i.B+this.Xt.gi,p=i.A,b=i.I,g=this.zt.Kt,w=i.P,_=s.Mi(r,g),y=Math.ceil(s.xi(r,g)),x=w+d+m,V=i.C+p+b+y+c,E=Math.max(1,Math.floor(u));let C=Math.round(x*u);C%2!=E%2&&(C+=1);const D=f>0?Math.max(1,Math.floor(f*a)):0,U=Math.round(V*a),yi=Math.round(c*a),Hs=(n=this.Xt.Si)!==null&&n!==void 0?n:this.Xt.ki,Si=Math.round(Hs*u)-Math.floor(.5*u),Tt=Math.floor(Si+E/2-C/2),_i=Tt+C,dt=e==="right",Mi=dt?o.width-f:f,tt=dt?l.width-D:D;let $t,Pt,Wt;return dt?($t=tt-U,Pt=tt-yi,Wt=Mi-c-p-f):($t=tt+U,Pt=tt+yi,Wt=Mi+c+p),{Qt:dt,Jt:{ii:Tt,li:Si,ci:_i,ni:U,si:C,ht:2*a,ei:D,ti:$t,ri:tt,ai:Pt,oi:E,ui:l.width},di:{ii:Tt/u,ci:_i/u,fi:Wt,pi:_}}}}class Lt{constructor(t){this.yi={ki:0,t:"#000",gi:0,wi:0},this.Ci={Kt:"",yt:!1,hi:!0,mi:!1,Ot:"",V:"#FFF",_i:!1,bi:!1},this.Ti={Kt:"",yt:!1,hi:!1,mi:!0,Ot:"",V:"#FFF",_i:!0,bi:!0},this.ft=!0,this.Pi=new(t||Vi)(this.Ci,this.yi),this.Ri=new(t||Vi)(this.Ti,this.yi)}Kt(){return this.Di(),this.Ci.Kt}ki(){return this.Di(),this.yi.ki}bt(){this.ft=!0}At(t,i=!1){return Math.max(this.Pi.At(t,i),this.Ri.At(t,i))}Vi(){return this.yi.Si||0}Oi(t){this.yi.Si=t}Bi(){return this.Di(),this.Ci.yt||this.Ti.yt}Ai(){return this.Di(),this.Ci.yt}gt(t){return this.Di(),this.Ci.hi=this.Ci.hi&&t.W().ticksVisible,this.Ti.hi=this.Ti.hi&&t.W().ticksVisible,this.Pi.J(this.Ci,this.yi),this.Ri.J(this.Ti,this.yi),this.Pi}Ii(){return this.Di(),this.Pi.J(this.Ci,this.yi),this.Ri.J(this.Ti,this.yi),this.Ri}Di(){this.ft&&(this.Ci.hi=!0,this.Ti.hi=!1,this.zi(this.Ci,this.Ti,this.yi))}}class ge extends Lt{constructor(t,i,s){super(),this.jt=t,this.Li=i,this.Ei=s}zi(t,i,s){if(t.yt=!1,this.jt.W().mode===2)return;const e=this.jt.W().horzLine;if(!e.labelVisible)return;const n=this.Li.Ct();if(!this.jt.yt()||this.Li.Ni()||n===null)return;const r=Et(e.labelBackgroundColor);s.t=r.t,t.V=r.i;const l=2/12*this.Li.P();s.wi=l,s.gi=l;const o=this.Ei(this.Li);s.ki=o.ki,t.Kt=this.Li.Fi(o._t,n),t.yt=!0}}const we=/[1-9]/g;class zs{constructor(){this.zt=null}J(t){this.zt=t}X(t,i){if(this.zt===null||this.zt.yt===!1||this.zt.Kt.length===0)return;const s=t.useMediaCoordinateSpace(({context:f})=>(f.font=i.R,Math.round(i.Wi.xi(f,v(this.zt).Kt,we))));if(s<=0)return;const e=i.ji,n=s+2*e,r=n/2,l=this.zt.Hi;let o=this.zt.ki,a=Math.floor(o-r)+.5;a<0?(o+=Math.abs(0-a),a=Math.floor(o-r)+.5):a+n>l&&(o-=Math.abs(l-(a+n)),a=Math.floor(o-r)+.5);const u=a+n,c=Math.ceil(0+i.C+i.T+i.L+i.P+i.B);t.useBitmapCoordinateSpace(({context:f,horizontalPixelRatio:d,verticalPixelRatio:m})=>{const p=v(this.zt);f.fillStyle=p.t;const b=Math.round(a*d),g=Math.round(0*m),w=Math.round(u*d),_=Math.round(c*m),y=Math.round(2*d);if(f.beginPath(),f.moveTo(b,g),f.lineTo(b,_-y),f.arcTo(b,_,b+y,_,y),f.lineTo(w-y,_),f.arcTo(w,_,w,_-y,y),f.lineTo(w,g),f.fill(),p.hi){const x=Math.round(p.ki*d),V=g,E=Math.round((V+i.T)*m);f.fillStyle=p.V;const C=Math.max(1,Math.floor(d)),D=Math.floor(.5*d);f.fillRect(x-D,V,C,E-V)}}),t.useMediaCoordinateSpace(({context:f})=>{const d=v(this.zt),m=0+i.C+i.T+i.L+i.P/2;f.font=i.R,f.textAlign="left",f.textBaseline="middle",f.fillStyle=d.V;const p=i.Wi.Mi(f,"Apr0");f.translate(a+e,m+p),f.fillText(d.Kt,0,0)})}}class ye{constructor(t,i,s){this.ft=!0,this.Wt=new zs,this.Ft={yt:!1,t:"#4c525e",V:"white",Kt:"",Hi:0,ki:NaN,hi:!0},this.vt=t,this.$i=i,this.Ei=s}bt(){this.ft=!0}gt(){return this.ft&&(this.Mt(),this.ft=!1),this.Wt.J(this.Ft),this.Wt}Mt(){const t=this.Ft;if(t.yt=!1,this.vt.W().mode===2)return;const i=this.vt.W().vertLine;if(!i.labelVisible)return;const s=this.$i.St();if(s.Ni())return;t.Hi=s.Hi();const e=this.Ei();if(e===null)return;t.ki=e.ki;const n=s.Ui(this.vt.xt());t.Kt=s.qi(v(n)),t.yt=!0;const r=Et(i.labelBackgroundColor);t.t=r.t,t.V=r.i,t.hi=s.W().ticksVisible}}class ui{constructor(){this.Yi=null,this.Zi=0}Xi(){return this.Zi}Ki(t){this.Zi=t}Dt(){return this.Yi}Gi(t){this.Yi=t}Ji(t){return[]}Qi(){return[]}yt(){return!0}}var Ni;(function(h){h[h.Normal=0]="Normal",h[h.Magnet=1]="Magnet",h[h.Hidden=2]="Hidden"})(Ni||(Ni={}));class Se extends ui{constructor(t,i){super(),this.tn=null,this.nn=NaN,this.sn=0,this.en=!0,this.rn=new Map,this.hn=!1,this.ln=NaN,this.an=NaN,this._n=NaN,this.un=NaN,this.$i=t,this.cn=i,this.dn=new me(t,this),this.fn=((e,n)=>r=>{const l=n(),o=e();if(r===v(this.tn).vn())return{_t:o,ki:l};{const a=v(r.Ct());return{_t:r.pn(l,a),ki:l}}})(()=>this.nn,()=>this.an);const s=((e,n)=>()=>{const r=this.$i.St().mn(e()),l=n();return r&&Number.isFinite(l)?{ot:r,ki:l}:null})(()=>this.sn,()=>this.Yt());this.bn=new ye(this,t,s),this.wn=new ve(this)}W(){return this.cn}gn(t,i){this._n=t,this.un=i}Mn(){this._n=NaN,this.un=NaN}xn(){return this._n}Sn(){return this.un}kn(t,i,s){this.hn||(this.hn=!0),this.en=!0,this.yn(t,i,s)}xt(){return this.sn}Yt(){return this.ln}Zt(){return this.an}yt(){return this.en}Cn(){this.en=!1,this.Tn(),this.nn=NaN,this.ln=NaN,this.an=NaN,this.tn=null,this.Mn()}Pn(t){return this.tn!==null?[this.wn,this.dn]:[]}Ut(t){return t===this.tn&&this.cn.horzLine.visible}qt(){return this.cn.vertLine.visible}Rn(t,i){this.en&&this.tn===t||this.rn.clear();const s=[];return this.tn===t&&s.push(this.Dn(this.rn,i,this.fn)),s}Qi(){return this.en?[this.bn]:[]}Ht(){return this.tn}Vn(){this.wn.bt(),this.rn.forEach(t=>t.bt()),this.bn.bt(),this.dn.bt()}On(t){return t&&!t.vn().Ni()?t.vn():null}yn(t,i,s){this.Bn(t,i,s)&&this.Vn()}Bn(t,i,s){const e=this.ln,n=this.an,r=this.nn,l=this.sn,o=this.tn,a=this.On(s);this.sn=t,this.ln=isNaN(t)?NaN:this.$i.St().It(t),this.tn=s;const u=a!==null?a.Ct():null;return a!==null&&u!==null?(this.nn=i,this.an=a.Rt(i,u)):(this.nn=NaN,this.an=NaN),e!==this.ln||n!==this.an||l!==this.sn||r!==this.nn||o!==this.tn}Tn(){const t=this.$i.wt().map(s=>s.In().An()).filter(ae),i=t.length===0?null:Math.max(...t);this.sn=i!==null?i:NaN}Dn(t,i,s){let e=t.get(i);return e===void 0&&(e=new ge(this,i,s),t.set(i,e)),e}}function Vt(h){return h==="left"||h==="right"}class z{constructor(t){this.zn=new Map,this.Ln=[],this.En=t}Nn(t,i){const s=function(e,n){return e===void 0?n:{Fn:Math.max(e.Fn,n.Fn),Wn:e.Wn||n.Wn}}(this.zn.get(t),i);this.zn.set(t,s)}jn(){return this.En}Hn(t){const i=this.zn.get(t);return i===void 0?{Fn:this.En}:{Fn:Math.max(this.En,i.Fn),Wn:i.Wn}}$n(){this.Un(),this.Ln=[{qn:0}]}Yn(t){this.Un(),this.Ln=[{qn:1,Vt:t}]}Zn(t){this.Xn(),this.Ln.push({qn:5,Vt:t})}Un(){this.Xn(),this.Ln.push({qn:6})}Kn(){this.Un(),this.Ln=[{qn:4}]}Gn(t){this.Un(),this.Ln.push({qn:2,Vt:t})}Jn(t){this.Un(),this.Ln.push({qn:3,Vt:t})}Qn(){return this.Ln}ts(t){for(const i of t.Ln)this.ns(i);this.En=Math.max(this.En,t.En),t.zn.forEach((i,s)=>{this.Nn(s,i)})}static ss(){return new z(2)}static es(){return new z(3)}ns(t){switch(t.qn){case 0:this.$n();break;case 1:this.Yn(t.Vt);break;case 2:this.Gn(t.Vt);break;case 3:this.Jn(t.Vt);break;case 4:this.Kn();break;case 5:this.Zn(t.Vt);break;case 6:this.Xn()}}Xn(){const t=this.Ln.findIndex(i=>i.qn===5);t!==-1&&this.Ln.splice(t,1)}}const Ri=".";function O(h,t){if(!P(h))return"n/a";if(!lt(t))throw new TypeError("invalid length");if(t<0||t>16)throw new TypeError("invalid length");return t===0?h.toString():("0000000000000000"+h.toString()).slice(-t)}class Nt{constructor(t,i){if(i||(i=1),P(t)&&lt(t)||(t=100),t<0)throw new TypeError("invalid base");this.Li=t,this.rs=i,this.hs()}format(t){const i=t<0?"−":"";return t=Math.abs(t),i+this.ls(t)}hs(){if(this._s=0,this.Li>0&&this.rs>0){let t=this.Li;for(;t>1;)t/=10,this._s++}}ls(t){const i=this.Li/this.rs;let s=Math.floor(t),e="";const n=this._s!==void 0?this._s:NaN;if(i>1){let r=+(Math.round(t*i)-s*i).toFixed(this._s);r>=i&&(r-=i,s+=1),e=Ri+O(+r.toFixed(this._s)*this.rs,n)}else s=Math.round(s*i)/i,n>0&&(e=Ri+O(0,n));return s.toFixed(0)+e}}class Cs extends Nt{constructor(t=100){super(t)}format(t){return`${super.format(t)}%`}}class _e{constructor(t){this.us=t}format(t){let i="";return t<0&&(i="-",t=-t),t<995?i+this.cs(t):t<999995?i+this.cs(t/1e3)+"K":t<999999995?(t=1e3*Math.round(t/1e3),i+this.cs(t/1e6)+"M"):(t=1e6*Math.round(t/1e6),i+this.cs(t/1e9)+"B")}cs(t){let i;const s=Math.pow(10,this.us);return i=(t=Math.round(t*s)/s)>=1e-15&&t<1?t.toFixed(this.us).replace(/\.?0+$/,""):String(t),i.replace(/(\.[1-9]*)0+$/,(e,n)=>n)}}function Es(h,t,i,s,e,n,r){if(t.length===0||s.from>=t.length||s.to<=0)return;const{context:l,horizontalPixelRatio:o,verticalPixelRatio:a}=h,u=t[s.from];let c=n(h,u),f=u;if(s.to-s.from<2){const d=e/2;l.beginPath();const m={nt:u.nt-d,st:u.st},p={nt:u.nt+d,st:u.st};l.moveTo(m.nt*o,m.st*a),l.lineTo(p.nt*o,p.st*a),r(h,c,m,p)}else{const d=(p,b)=>{r(h,c,f,b),l.beginPath(),c=p,f=b};let m=f;l.beginPath(),l.moveTo(u.nt*o,u.st*a);for(let p=s.from+1;p<s.to;++p){m=t[p];const b=n(h,m);switch(i){case 0:l.lineTo(m.nt*o,m.st*a);break;case 1:l.lineTo(m.nt*o,t[p-1].st*a),b!==c&&(d(b,m),l.lineTo(m.nt*o,t[p-1].st*a)),l.lineTo(m.nt*o,m.st*a);break;case 2:{const[g,w]=Me(t,p-1,p);l.bezierCurveTo(g.nt*o,g.st*a,w.nt*o,w.st*a,m.nt*o,m.st*a);break}}i!==1&&b!==c&&(d(b,m),l.moveTo(m.nt*o,m.st*a))}(f!==m||f===m&&i===1)&&r(h,c,f,m)}}const Ti=6;function Ot(h,t){return{nt:h.nt-t.nt,st:h.st-t.st}}function $i(h,t){return{nt:h.nt/t,st:h.st/t}}function Me(h,t,i){const s=Math.max(0,t-1),e=Math.min(h.length-1,i+1);var n,r;return[(n=h[t],r=$i(Ot(h[i],h[s]),Ti),{nt:n.nt+r.nt,st:n.st+r.st}),Ot(h[i],$i(Ot(h[e],h[t]),Ti))]}function xe(h,t,i,s,e){const{context:n,horizontalPixelRatio:r,verticalPixelRatio:l}=t;n.lineTo(e.nt*r,h*l),n.lineTo(s.nt*r,h*l),n.closePath(),n.fillStyle=i,n.fill()}class ks extends T{constructor(){super(...arguments),this.G=null}J(t){this.G=t}K(t){var i;if(this.G===null)return;const{it:s,tt:e,ds:n,et:r,Nt:l,fs:o}=this.G,a=(i=this.G.vs)!==null&&i!==void 0?i:this.G.ps?0:t.mediaSize.height;if(e===null)return;const u=t.context;u.lineCap="butt",u.lineJoin="round",u.lineWidth=r,X(u,l),u.lineWidth=1,Es(t,s,o,e,n,this.bs.bind(this),xe.bind(null,a))}}function si(h,t,i){return Math.min(Math.max(h,t),i)}function pt(h,t,i){return t-h<=i}function Ls(h){const t=Math.ceil(h);return t%2==0?t-1:t}class ci{ws(t,i){const s=this.gs,{Ms:e,xs:n,Ss:r,ks:l,ys:o,vs:a}=i;if(this.Cs===void 0||s===void 0||s.Ms!==e||s.xs!==n||s.Ss!==r||s.ks!==l||s.vs!==a||s.ys!==o){const u=t.context.createLinearGradient(0,0,0,o);if(u.addColorStop(0,e),a!=null){const c=si(a*t.verticalPixelRatio/o,0,1);u.addColorStop(c,n),u.addColorStop(c,r)}u.addColorStop(1,l),this.Cs=u,this.gs=i}return this.Cs}}class ze extends ks{constructor(){super(...arguments),this.Ts=new ci}bs(t,i){return this.Ts.ws(t,{Ms:i.Ps,xs:"",Ss:"",ks:i.Rs,ys:t.bitmapSize.height})}}function Ce(h,t){const i=h.context;i.strokeStyle=t,i.stroke()}class Vs extends T{constructor(){super(...arguments),this.G=null}J(t){this.G=t}K(t){if(this.G===null)return;const{it:i,tt:s,ds:e,fs:n,et:r,Nt:l,Ds:o}=this.G;if(s===null)return;const a=t.context;a.lineCap="butt",a.lineWidth=r*t.verticalPixelRatio,X(a,l),a.lineJoin="round";const u=this.Vs.bind(this);n!==void 0&&Es(t,i,n,s,e,u,Ce),o&&function(c,f,d,m,p){const{horizontalPixelRatio:b,verticalPixelRatio:g,context:w}=c;let _=null;const y=Math.max(1,Math.floor(b))%2/2,x=d*g+y;for(let V=m.to-1;V>=m.from;--V){const E=f[V];if(E){const C=p(c,E);C!==_&&(w.beginPath(),_!==null&&w.fill(),w.fillStyle=C,_=C);const D=Math.round(E.nt*b)+y,U=E.st*g;w.moveTo(D,U),w.arc(D,U,x,0,2*Math.PI)}}w.fill()}(t,i,o,s,u)}}class Ns extends Vs{Vs(t,i){return i.lt}}function Rs(h,t,i,s,e=0,n=t.length){let r=n-e;for(;0<r;){const l=r>>1,o=e+l;s(t[o],i)===h?(e=o+1,r-=l+1):r=l}return e}const ft=Rs.bind(null,!0),Ts=Rs.bind(null,!1);function Ee(h,t){return h.ot<t}function ke(h,t){return t<h.ot}function $s(h,t,i){const s=t.Os(),e=t.ui(),n=ft(h,s,Ee),r=Ts(h,e,ke);if(!i)return{from:n,to:r};let l=n,o=r;return n>0&&n<h.length&&h[n].ot>=s&&(l=n-1),r>0&&r<h.length&&h[r-1].ot<=e&&(o=r+1),{from:l,to:o}}class fi{constructor(t,i,s){this.Bs=!0,this.As=!0,this.Is=!0,this.zs=[],this.Ls=null,this.Es=t,this.Ns=i,this.Fs=s}bt(t){this.Bs=!0,t==="data"&&(this.As=!0),t==="options"&&(this.Is=!0)}gt(){return this.Es.yt()?(this.Ws(),this.Ls===null?null:this.js):null}Hs(){this.zs=this.zs.map(t=>Object.assign(Object.assign({},t),this.Es.Us().$s(t.ot)))}qs(){this.Ls=null}Ws(){this.As&&(this.Ys(),this.As=!1),this.Is&&(this.Hs(),this.Is=!1),this.Bs&&(this.Zs(),this.Bs=!1)}Zs(){const t=this.Es.Dt(),i=this.Ns.St();if(this.qs(),i.Ni()||t.Ni())return;const s=i.Xs();if(s===null||this.Es.In().Ks()===0)return;const e=this.Es.Ct();e!==null&&(this.Ls=$s(this.zs,s,this.Fs),this.Gs(t,i,e.Vt),this.Js())}}class Rt extends fi{constructor(t,i){super(t,i,!0)}Gs(t,i,s){i.Qs(this.zs,ot(this.Ls)),t.te(this.zs,s,ot(this.Ls))}ie(t,i){return{ot:t,_t:i,nt:NaN,st:NaN}}Ys(){const t=this.Es.Us();this.zs=this.Es.In().ne().map(i=>{const s=i.Vt[3];return this.se(i.ee,s,t)})}}class Le extends Rt{constructor(t,i){super(t,i),this.js=new ai,this.re=new ze,this.he=new Ns,this.js.Z([this.re,this.he])}se(t,i,s){return Object.assign(Object.assign({},this.ie(t,i)),s.$s(t))}Js(){const t=this.Es.W();this.re.J({fs:t.lineType,it:this.zs,Nt:t.lineStyle,et:t.lineWidth,vs:null,ps:t.invertFilledArea,tt:this.Ls,ds:this.Ns.St().le()}),this.he.J({fs:t.lineVisible?t.lineType:void 0,it:this.zs,Nt:t.lineStyle,et:t.lineWidth,tt:this.Ls,ds:this.Ns.St().le(),Ds:t.pointMarkersVisible?t.pointMarkersRadius||t.lineWidth/2+2:void 0})}}class Ve extends T{constructor(){super(...arguments),this.zt=null,this.ae=0,this.oe=0}J(t){this.zt=t}K({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(this.zt===null||this.zt.In.length===0||this.zt.tt===null)return;this.ae=this._e(i),this.ae>=2&&Math.max(1,Math.floor(i))%2!=this.ae%2&&this.ae--,this.oe=this.zt.ue?Math.min(this.ae,Math.floor(i)):this.ae;let e=null;const n=this.oe<=this.ae&&this.zt.le>=Math.floor(1.5*i);for(let r=this.zt.tt.from;r<this.zt.tt.to;++r){const l=this.zt.In[r];e!==l.ce&&(t.fillStyle=l.ce,e=l.ce);const o=Math.floor(.5*this.oe),a=Math.round(l.nt*i),u=a-o,c=this.oe,f=u+c-1,d=Math.min(l.de,l.fe),m=Math.max(l.de,l.fe),p=Math.round(d*s)-o,b=Math.round(m*s)+o,g=Math.max(b-p,this.oe);t.fillRect(u,p,c,g);const w=Math.ceil(1.5*this.ae);if(n){if(this.zt.ve){const V=a-w;let E=Math.max(p,Math.round(l.pe*s)-o),C=E+c-1;C>p+g-1&&(C=p+g-1,E=C-c+1),t.fillRect(V,E,u-V,C-E+1)}const _=a+w;let y=Math.max(p,Math.round(l.me*s)-o),x=y+c-1;x>p+g-1&&(x=p+g-1,y=x-c+1),t.fillRect(f+1,y,_-f,x-y+1)}}}_e(t){const i=Math.floor(t);return Math.max(i,Math.floor(function(s,e){return Math.floor(.3*s*e)}(v(this.zt).le,t)))}}class Ps extends fi{constructor(t,i){super(t,i,!1)}Gs(t,i,s){i.Qs(this.zs,ot(this.Ls)),t.be(this.zs,s,ot(this.Ls))}we(t,i,s){return{ot:t,ge:i.Vt[0],Me:i.Vt[1],xe:i.Vt[2],Se:i.Vt[3],nt:NaN,pe:NaN,de:NaN,fe:NaN,me:NaN}}Ys(){const t=this.Es.Us();this.zs=this.Es.In().ne().map(i=>this.se(i.ee,i,t))}}class Ne extends Ps{constructor(){super(...arguments),this.js=new Ve}se(t,i,s){return Object.assign(Object.assign({},this.we(t,i,s)),s.$s(t))}Js(){const t=this.Es.W();this.js.J({In:this.zs,le:this.Ns.St().le(),ve:t.openVisible,ue:t.thinBars,tt:this.Ls})}}class Re extends ks{constructor(){super(...arguments),this.Ts=new ci}bs(t,i){const s=this.G;return this.Ts.ws(t,{Ms:i.ke,xs:i.ye,Ss:i.Ce,ks:i.Te,ys:t.bitmapSize.height,vs:s.vs})}}class Te extends Vs{constructor(){super(...arguments),this.Pe=new ci}Vs(t,i){const s=this.G;return this.Pe.ws(t,{Ms:i.Re,xs:i.Re,Ss:i.De,ks:i.De,ys:t.bitmapSize.height,vs:s.vs})}}class $e extends Rt{constructor(t,i){super(t,i),this.js=new ai,this.Ve=new Re,this.Oe=new Te,this.js.Z([this.Ve,this.Oe])}se(t,i,s){return Object.assign(Object.assign({},this.ie(t,i)),s.$s(t))}Js(){const t=this.Es.Ct();if(t===null)return;const i=this.Es.W(),s=this.Es.Dt().Rt(i.baseValue.price,t.Vt),e=this.Ns.St().le();this.Ve.J({it:this.zs,et:i.lineWidth,Nt:i.lineStyle,fs:i.lineType,vs:s,ps:!1,tt:this.Ls,ds:e}),this.Oe.J({it:this.zs,et:i.lineWidth,Nt:i.lineStyle,fs:i.lineVisible?i.lineType:void 0,Ds:i.pointMarkersVisible?i.pointMarkersRadius||i.lineWidth/2+2:void 0,vs:s,tt:this.Ls,ds:e})}}class Pe extends T{constructor(){super(...arguments),this.zt=null,this.ae=0}J(t){this.zt=t}K(t){if(this.zt===null||this.zt.In.length===0||this.zt.tt===null)return;const{horizontalPixelRatio:i}=t;this.ae=function(n,r){if(n>=2.5&&n<=4)return Math.floor(3*r);const l=1-.2*Math.atan(Math.max(4,n)-4)/(.5*Math.PI),o=Math.floor(n*l*r),a=Math.floor(n*r),u=Math.min(o,a);return Math.max(Math.floor(r),u)}(this.zt.le,i),this.ae>=2&&Math.floor(i)%2!=this.ae%2&&this.ae--;const s=this.zt.In;this.zt.Be&&this.Ae(t,s,this.zt.tt),this.zt._i&&this.Ie(t,s,this.zt.tt);const e=this.ze(i);(!this.zt._i||this.ae>2*e)&&this.Le(t,s,this.zt.tt)}Ae(t,i,s){if(this.zt===null)return;const{context:e,horizontalPixelRatio:n,verticalPixelRatio:r}=t;let l="",o=Math.min(Math.floor(n),Math.floor(this.zt.le*n));o=Math.max(Math.floor(n),Math.min(o,this.ae));const a=Math.floor(.5*o);let u=null;for(let c=s.from;c<s.to;c++){const f=i[c];f.Ee!==l&&(e.fillStyle=f.Ee,l=f.Ee);const d=Math.round(Math.min(f.pe,f.me)*r),m=Math.round(Math.max(f.pe,f.me)*r),p=Math.round(f.de*r),b=Math.round(f.fe*r);let g=Math.round(n*f.nt)-a;const w=g+o-1;u!==null&&(g=Math.max(u+1,g),g=Math.min(g,w));const _=w-g+1;e.fillRect(g,p,_,d-p),e.fillRect(g,m+1,_,b-m),u=w}}ze(t){let i=Math.floor(1*t);this.ae<=2*i&&(i=Math.floor(.5*(this.ae-1)));const s=Math.max(Math.floor(t),i);return this.ae<=2*s?Math.max(Math.floor(t),Math.floor(1*t)):s}Ie(t,i,s){if(this.zt===null)return;const{context:e,horizontalPixelRatio:n,verticalPixelRatio:r}=t;let l="";const o=this.ze(n);let a=null;for(let u=s.from;u<s.to;u++){const c=i[u];c.Ne!==l&&(e.fillStyle=c.Ne,l=c.Ne);let f=Math.round(c.nt*n)-Math.floor(.5*this.ae);const d=f+this.ae-1,m=Math.round(Math.min(c.pe,c.me)*r),p=Math.round(Math.max(c.pe,c.me)*r);if(a!==null&&(f=Math.max(a+1,f),f=Math.min(f,d)),this.zt.le*n>2*o)be(e,f,m,d-f+1,p-m+1,o);else{const b=d-f+1;e.fillRect(f,m,b,p-m+1)}a=d}}Le(t,i,s){if(this.zt===null)return;const{context:e,horizontalPixelRatio:n,verticalPixelRatio:r}=t;let l="";const o=this.ze(n);for(let a=s.from;a<s.to;a++){const u=i[a];let c=Math.round(Math.min(u.pe,u.me)*r),f=Math.round(Math.max(u.pe,u.me)*r),d=Math.round(u.nt*n)-Math.floor(.5*this.ae),m=d+this.ae-1;if(u.ce!==l){const p=u.ce;e.fillStyle=p,l=p}this.zt._i&&(d+=o,c+=o,m-=o,f-=o),c>f||e.fillRect(d,c,m-d+1,f-c+1)}}}class We extends Ps{constructor(){super(...arguments),this.js=new Pe}se(t,i,s){return Object.assign(Object.assign({},this.we(t,i,s)),s.$s(t))}Js(){const t=this.Es.W();this.js.J({In:this.zs,le:this.Ns.St().le(),Be:t.wickVisible,_i:t.borderVisible,tt:this.Ls})}}class Oe{constructor(t,i){this.Fe=t,this.Li=i}X(t,i,s){this.Fe.draw(t,this.Li,i,s)}}class Dt extends fi{constructor(t,i,s){super(t,i,!1),this.wn=s,this.js=new Oe(this.wn.renderer(),e=>{const n=t.Ct();return n===null?null:t.Dt().Rt(e,n.Vt)})}We(t){return this.wn.priceValueBuilder(t)}je(t){return this.wn.isWhitespace(t)}Ys(){const t=this.Es.Us();this.zs=this.Es.In().ne().map(i=>Object.assign(Object.assign({ot:i.ee,nt:NaN},t.$s(i.ee)),{He:i.$e}))}Gs(t,i){i.Qs(this.zs,ot(this.Ls))}Js(){this.wn.update({bars:this.zs.map(De),barSpacing:this.Ns.St().le(),visibleRange:this.Ls},this.Es.W())}}function De(h){return{x:h.nt,time:h.ot,originalData:h.He,barColor:h.ce}}class Be extends T{constructor(){super(...arguments),this.zt=null,this.Ue=[]}J(t){this.zt=t,this.Ue=[]}K({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){if(this.zt===null||this.zt.it.length===0||this.zt.tt===null)return;this.Ue.length||this.qe(i);const e=Math.max(1,Math.floor(s)),n=Math.round(this.zt.Ye*s)-Math.floor(e/2),r=n+e;for(let l=this.zt.tt.from;l<this.zt.tt.to;l++){const o=this.zt.it[l],a=this.Ue[l-this.zt.tt.from],u=Math.round(o.st*s);let c,f;t.fillStyle=o.ce,u<=n?(c=u,f=r):(c=n,f=u-Math.floor(e/2)+e),t.fillRect(a.Os,c,a.ui-a.Os+1,f-c)}}qe(t){if(this.zt===null||this.zt.it.length===0||this.zt.tt===null)return void(this.Ue=[]);const i=Math.ceil(this.zt.le*t)<=1?0:Math.max(1,Math.floor(t)),s=Math.round(this.zt.le*t)-i;this.Ue=new Array(this.zt.tt.to-this.zt.tt.from);for(let n=this.zt.tt.from;n<this.zt.tt.to;n++){const r=this.zt.it[n],l=Math.round(r.nt*t);let o,a;if(s%2){const u=(s-1)/2;o=l-u,a=l+u}else{const u=s/2;o=l-u,a=l+u-1}this.Ue[n-this.zt.tt.from]={Os:o,ui:a,Ze:l,Xe:r.nt*t,ot:r.ot}}for(let n=this.zt.tt.from+1;n<this.zt.tt.to;n++){const r=this.Ue[n-this.zt.tt.from],l=this.Ue[n-this.zt.tt.from-1];r.ot===l.ot+1&&r.Os-l.ui!==i+1&&(l.Ze>l.Xe?l.ui=r.Os-i-1:r.Os=l.ui+i+1)}let e=Math.ceil(this.zt.le*t);for(let n=this.zt.tt.from;n<this.zt.tt.to;n++){const r=this.Ue[n-this.zt.tt.from];r.ui<r.Os&&(r.ui=r.Os);const l=r.ui-r.Os+1;e=Math.min(l,e)}if(i>0&&e<4)for(let n=this.zt.tt.from;n<this.zt.tt.to;n++){const r=this.Ue[n-this.zt.tt.from];r.ui-r.Os+1>e&&(r.Ze>r.Xe?r.ui-=1:r.Os+=1)}}}class Ie extends Rt{constructor(){super(...arguments),this.js=new Be}se(t,i,s){return Object.assign(Object.assign({},this.ie(t,i)),s.$s(t))}Js(){const t={it:this.zs,le:this.Ns.St().le(),tt:this.Ls,Ye:this.Es.Dt().Rt(this.Es.W().base,v(this.Es.Ct()).Vt)};this.js.J(t)}}class Ke extends Rt{constructor(){super(...arguments),this.js=new Ns}se(t,i,s){return Object.assign(Object.assign({},this.ie(t,i)),s.$s(t))}Js(){const t=this.Es.W(),i={it:this.zs,Nt:t.lineStyle,fs:t.lineVisible?t.lineType:void 0,et:t.lineWidth,Ds:t.pointMarkersVisible?t.pointMarkersRadius||t.lineWidth/2+2:void 0,tt:this.Ls,ds:this.Ns.St().le()};this.js.J(i)}}const je=/[2-9]/g;class at{constructor(t=50){this.Ke=0,this.Ge=1,this.Je=1,this.Qe={},this.tr=new Map,this.ir=t}nr(){this.Ke=0,this.tr.clear(),this.Ge=1,this.Je=1,this.Qe={}}xi(t,i,s){return this.sr(t,i,s).width}Mi(t,i,s){const e=this.sr(t,i,s);return((e.actualBoundingBoxAscent||0)-(e.actualBoundingBoxDescent||0))/2}sr(t,i,s){const e=s||je,n=String(i).replace(e,"0");if(this.tr.has(n))return k(this.tr.get(n)).er;if(this.Ke===this.ir){const l=this.Qe[this.Je];delete this.Qe[this.Je],this.tr.delete(l),this.Je++,this.Ke--}t.save(),t.textBaseline="middle";const r=t.measureText(n);return t.restore(),r.width===0&&i.length||(this.tr.set(n,{er:r,rr:this.Ge}),this.Qe[this.Ge]=n,this.Ke++,this.Ge++),r}}class Fe{constructor(t){this.hr=null,this.k=null,this.lr="right",this.ar=t}_r(t,i,s){this.hr=t,this.k=i,this.lr=s}X(t){this.k!==null&&this.hr!==null&&this.hr.X(t,this.k,this.ar,this.lr)}}class Ws{constructor(t,i,s){this.ur=t,this.ar=new at(50),this.cr=i,this.F=s,this.j=-1,this.Wt=new Fe(this.ar)}gt(){const t=this.F.dr(this.cr);if(t===null)return null;const i=t.vr(this.cr)?t.pr():this.cr.Dt();if(i===null)return null;const s=t.mr(i);if(s==="overlay")return null;const e=this.F.br();return e.P!==this.j&&(this.j=e.P,this.ar.nr()),this.Wt._r(this.ur.Ii(),e,s),this.Wt}}class Xe extends T{constructor(){super(...arguments),this.zt=null}J(t){this.zt=t}wr(t,i){var s;if(!(!((s=this.zt)===null||s===void 0)&&s.yt))return null;const{st:e,et:n,gr:r}=this.zt;return i>=e-n-7&&i<=e+n+7?{Mr:this.zt,gr:r}:null}K({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:e}){if(this.zt===null||this.zt.yt===!1)return;const n=Math.round(this.zt.st*e);n<0||n>i.height||(t.lineCap="butt",t.strokeStyle=this.zt.V,t.lineWidth=Math.floor(this.zt.et*s),X(t,this.zt.Nt),Ss(t,n,0,i.width))}}class di{constructor(t){this.Sr={st:0,V:"rgba(0, 0, 0, 0)",et:1,Nt:0,yt:!1},this.kr=new Xe,this.ft=!0,this.Es=t,this.Ns=t.$t(),this.kr.J(this.Sr)}bt(){this.ft=!0}gt(){return this.Es.yt()?(this.ft&&(this.yr(),this.ft=!1),this.kr):null}}class He extends di{constructor(t){super(t)}yr(){this.Sr.yt=!1;const t=this.Es.Dt(),i=t.Cr().Cr;if(i!==2&&i!==3)return;const s=this.Es.W();if(!s.baseLineVisible||!this.Es.yt())return;const e=this.Es.Ct();e!==null&&(this.Sr.yt=!0,this.Sr.st=t.Rt(e.Vt,e.Vt),this.Sr.V=s.baseLineColor,this.Sr.et=s.baseLineWidth,this.Sr.Nt=s.baseLineStyle)}}class Ae extends T{constructor(){super(...arguments),this.zt=null}J(t){this.zt=t}$e(){return this.zt}K({context:t,horizontalPixelRatio:i,verticalPixelRatio:s}){const e=this.zt;if(e===null)return;const n=Math.max(1,Math.floor(i)),r=n%2/2,l=Math.round(e.Xe.x*i)+r,o=e.Xe.y*s;t.fillStyle=e.Tr,t.beginPath();const a=Math.max(2,1.5*e.Pr)*i;t.arc(l,o,a,0,2*Math.PI,!1),t.fill(),t.fillStyle=e.Rr,t.beginPath(),t.arc(l,o,e.ht*i,0,2*Math.PI,!1),t.fill(),t.lineWidth=n,t.strokeStyle=e.Dr,t.beginPath(),t.arc(l,o,e.ht*i+n/2,0,2*Math.PI,!1),t.stroke()}}const Ue=[{Vr:0,Or:.25,Br:4,Ar:10,Ir:.25,zr:0,Lr:.4,Er:.8},{Vr:.25,Or:.525,Br:10,Ar:14,Ir:0,zr:0,Lr:.8,Er:0},{Vr:.525,Or:1,Br:14,Ar:14,Ir:0,zr:0,Lr:0,Er:0}];function Pi(h,t,i,s){return function(e,n){if(e==="transparent")return e;const r=rt(e),l=r[3];return`rgba(${r[0]}, ${r[1]}, ${r[2]}, ${n*l})`}(h,i+(s-i)*t)}function Wi(h,t){const i=h%2600/2600;let s;for(const o of Ue)if(i>=o.Vr&&i<=o.Or){s=o;break}I(s!==void 0,"Last price animation internal logic error");const e=(i-s.Vr)/(s.Or-s.Vr);return{Rr:Pi(t,e,s.Ir,s.zr),Dr:Pi(t,e,s.Lr,s.Er),ht:(n=e,r=s.Br,l=s.Ar,r+(l-r)*n)};var n,r,l}class qe{constructor(t){this.Wt=new Ae,this.ft=!0,this.Nr=!0,this.Fr=performance.now(),this.Wr=this.Fr-1,this.jr=t}Hr(){this.Wr=this.Fr-1,this.bt()}$r(){if(this.bt(),this.jr.W().lastPriceAnimation===2){const t=performance.now(),i=this.Wr-t;if(i>0)return void(i<650&&(this.Wr+=2600));this.Fr=t,this.Wr=t+2600}}bt(){this.ft=!0}Ur(){this.Nr=!0}yt(){return this.jr.W().lastPriceAnimation!==0}qr(){switch(this.jr.W().lastPriceAnimation){case 0:return!1;case 1:return!0;case 2:return performance.now()<=this.Wr}}gt(){return this.ft?(this.Mt(),this.ft=!1,this.Nr=!1):this.Nr&&(this.Yr(),this.Nr=!1),this.Wt}Mt(){this.Wt.J(null);const t=this.jr.$t().St(),i=t.Xs(),s=this.jr.Ct();if(i===null||s===null)return;const e=this.jr.Zr(!0);if(e.Xr||!i.Kr(e.ee))return;const n={x:t.It(e.ee),y:this.jr.Dt().Rt(e._t,s.Vt)},r=e.V,l=this.jr.W().lineWidth,o=Wi(this.Gr(),r);this.Wt.J({Tr:r,Pr:l,Rr:o.Rr,Dr:o.Dr,ht:o.ht,Xe:n})}Yr(){const t=this.Wt.$e();if(t!==null){const i=Wi(this.Gr(),t.Tr);t.Rr=i.Rr,t.Dr=i.Dr,t.ht=i.ht}}Gr(){return this.qr()?performance.now()-this.Fr:2599}}function st(h,t){return Ls(Math.min(Math.max(h,12),30)*t)}function ut(h,t){switch(h){case"arrowDown":case"arrowUp":return st(t,1);case"circle":return st(t,.8);case"square":return st(t,.7)}}function Os(h){return function(t){const i=Math.ceil(t);return i%2!=0?i-1:i}(st(h,1))}function Oi(h){return Math.max(st(h,.1),3)}function Di(h,t,i){return t?h:i?Math.ceil(h/2):0}function Ds(h,t,i,s,e){const n=ut("square",i),r=(n-1)/2,l=h-r,o=t-r;return s>=l&&s<=l+n&&e>=o&&e<=o+n}function Bi(h,t,i,s){const e=(ut("arrowUp",s)-1)/2*i.Jr,n=(Ls(s/2)-1)/2*i.Jr;t.beginPath(),h?(t.moveTo(i.nt-e,i.st),t.lineTo(i.nt,i.st-e),t.lineTo(i.nt+e,i.st),t.lineTo(i.nt+n,i.st),t.lineTo(i.nt+n,i.st+e),t.lineTo(i.nt-n,i.st+e),t.lineTo(i.nt-n,i.st)):(t.moveTo(i.nt-e,i.st),t.lineTo(i.nt,i.st+e),t.lineTo(i.nt+e,i.st),t.lineTo(i.nt+n,i.st),t.lineTo(i.nt+n,i.st-e),t.lineTo(i.nt-n,i.st-e),t.lineTo(i.nt-n,i.st)),t.fill()}function Je(h,t,i,s,e,n){return Ds(t,i,s,e,n)}class Ge extends T{constructor(){super(...arguments),this.zt=null,this.ar=new at,this.j=-1,this.H="",this.Qr=""}J(t){this.zt=t}_r(t,i){this.j===t&&this.H===i||(this.j=t,this.H=i,this.Qr=Y(t,i),this.ar.nr())}wr(t,i){if(this.zt===null||this.zt.tt===null)return null;for(let s=this.zt.tt.from;s<this.zt.tt.to;s++){const e=this.zt.it[s];if(Ye(e,t,i))return{Mr:e.th,gr:e.gr}}return null}K({context:t,horizontalPixelRatio:i,verticalPixelRatio:s},e,n){if(this.zt!==null&&this.zt.tt!==null){t.textBaseline="middle",t.font=this.Qr;for(let r=this.zt.tt.from;r<this.zt.tt.to;r++){const l=this.zt.it[r];l.Kt!==void 0&&(l.Kt.Hi=this.ar.xi(t,l.Kt.ih),l.Kt.At=this.j,l.Kt.nt=l.nt-l.Kt.Hi/2),Qe(l,t,i,s)}}}}function Qe(h,t,i,s){t.fillStyle=h.V,h.Kt!==void 0&&function(e,n,r,l,o,a){e.save(),e.scale(o,a),e.fillText(n,r,l),e.restore()}(t,h.Kt.ih,h.Kt.nt,h.Kt.st,i,s),function(e,n,r){if(e.Ks!==0){switch(e.nh){case"arrowDown":return void Bi(!1,n,r,e.Ks);case"arrowUp":return void Bi(!0,n,r,e.Ks);case"circle":return void function(l,o,a){const u=(ut("circle",a)-1)/2;l.beginPath(),l.arc(o.nt,o.st,u*o.Jr,0,2*Math.PI,!1),l.fill()}(n,r,e.Ks);case"square":return void function(l,o,a){const u=ut("square",a),c=(u-1)*o.Jr/2,f=o.nt-c,d=o.st-c;l.fillRect(f,d,u*o.Jr,u*o.Jr)}(n,r,e.Ks)}e.nh}}(h,t,function(e,n,r){const l=Math.max(1,Math.floor(n))%2/2;return{nt:Math.round(e.nt*n)+l,st:e.st*r,Jr:n}}(h,i,s))}function Ye(h,t,i){return!(h.Kt===void 0||!function(s,e,n,r,l,o){const a=r/2;return l>=s&&l<=s+n&&o>=e-a&&o<=e+a}(h.Kt.nt,h.Kt.st,h.Kt.Hi,h.Kt.At,t,i))||function(s,e,n){if(s.Ks===0)return!1;switch(s.nh){case"arrowDown":case"arrowUp":return Je(0,s.nt,s.st,s.Ks,e,n);case"circle":return function(r,l,o,a,u){const c=2+ut("circle",o)/2,f=r-a,d=l-u;return Math.sqrt(f*f+d*d)<=c}(s.nt,s.st,s.Ks,e,n);case"square":return Ds(s.nt,s.st,s.Ks,e,n)}}(h,t,i)}function Ze(h,t,i,s,e,n,r,l,o){const a=P(i)?i:i.Se,u=P(i)?i:i.Me,c=P(i)?i:i.xe,f=P(t.size)?Math.max(t.size,0):1,d=Os(l.le())*f,m=d/2;switch(h.Ks=d,t.position){case"inBar":return h.st=r.Rt(a,o),void(h.Kt!==void 0&&(h.Kt.st=h.st+m+n+.6*e));case"aboveBar":return h.st=r.Rt(u,o)-m-s.sh,h.Kt!==void 0&&(h.Kt.st=h.st-m-.6*e,s.sh+=1.2*e),void(s.sh+=d+n);case"belowBar":return h.st=r.Rt(c,o)+m+s.eh,h.Kt!==void 0&&(h.Kt.st=h.st+m+n+.6*e,s.eh+=1.2*e),void(s.eh+=d+n)}t.position}class th{constructor(t,i){this.ft=!0,this.rh=!0,this.hh=!0,this.ah=null,this.oh=null,this.Wt=new Ge,this.jr=t,this.$i=i,this.zt={it:[],tt:null}}bt(t){this.ft=!0,this.hh=!0,t==="data"&&(this.rh=!0,this.oh=null)}gt(t){if(!this.jr.yt())return null;this.ft&&this._h();const i=this.$i.W().layout;return this.Wt._r(i.fontSize,i.fontFamily),this.Wt.J(this.zt),this.Wt}uh(){if(this.hh){if(this.jr.dh().length>0){const t=this.$i.St().le(),i=Oi(t),s=1.5*Os(t)+2*i,e=this.fh();this.ah={above:Di(s,e.aboveBar,e.inBar),below:Di(s,e.belowBar,e.inBar)}}else this.ah=null;this.hh=!1}return this.ah}fh(){return this.oh===null&&(this.oh=this.jr.dh().reduce((t,i)=>(t[i.position]||(t[i.position]=!0),t),{inBar:!1,aboveBar:!1,belowBar:!1})),this.oh}_h(){const t=this.jr.Dt(),i=this.$i.St(),s=this.jr.dh();this.rh&&(this.zt.it=s.map(u=>({ot:u.time,nt:0,st:0,Ks:0,nh:u.shape,V:u.color,th:u.th,gr:u.id,Kt:void 0})),this.rh=!1);const e=this.$i.W().layout;this.zt.tt=null;const n=i.Xs();if(n===null)return;const r=this.jr.Ct();if(r===null||this.zt.it.length===0)return;let l=NaN;const o=Oi(i.le()),a={sh:o,eh:o};this.zt.tt=$s(this.zt.it,n,!0);for(let u=this.zt.tt.from;u<this.zt.tt.to;u++){const c=s[u];c.time!==l&&(a.sh=o,a.eh=o,l=c.time);const f=this.zt.it[u];f.nt=i.It(c.time),c.text!==void 0&&c.text.length>0&&(f.Kt={ih:c.text,nt:0,st:0,Hi:0,At:0});const d=this.jr.ph(c.time);d!==null&&Ze(f,c,d,a,e.fontSize,o,t,i,r.Vt)}this.ft=!1}}class ih extends di{constructor(t){super(t)}yr(){const t=this.Sr;t.yt=!1;const i=this.Es.W();if(!i.priceLineVisible||!this.Es.yt())return;const s=this.Es.Zr(i.priceLineSource===0);s.Xr||(t.yt=!0,t.st=s.ki,t.V=this.Es.mh(s.V),t.et=i.priceLineWidth,t.Nt=i.priceLineStyle)}}class sh extends Lt{constructor(t){super(),this.jt=t}zi(t,i,s){t.yt=!1,i.yt=!1;const e=this.jt;if(!e.yt())return;const n=e.W(),r=n.lastValueVisible,l=e.bh()!=="",o=n.seriesLastValueMode===0,a=e.Zr(!1);if(a.Xr)return;r&&(t.Kt=this.wh(a,r,o),t.yt=t.Kt.length!==0),(l||o)&&(i.Kt=this.gh(a,r,l,o),i.yt=i.Kt.length>0);const u=e.mh(a.V),c=Et(u);s.t=c.t,s.ki=a.ki,i.Ot=e.$t().Bt(a.ki/e.Dt().At()),t.Ot=u,t.V=c.i,i.V=c.i}gh(t,i,s,e){let n="";const r=this.jt.bh();return s&&r.length!==0&&(n+=`${r} `),i&&e&&(n+=this.jt.Dt().Mh()?t.xh:t.Sh),n.trim()}wh(t,i,s){return i?s?this.jt.Dt().Mh()?t.Sh:t.xh:t.Kt:""}}function Ii(h,t,i,s){const e=Number.isFinite(t),n=Number.isFinite(i);return e&&n?h(t,i):e||n?e?t:i:s}class L{constructor(t,i){this.kh=t,this.yh=i}Ch(t){return t!==null&&this.kh===t.kh&&this.yh===t.yh}Th(){return new L(this.kh,this.yh)}Ph(){return this.kh}Rh(){return this.yh}Dh(){return this.yh-this.kh}Ni(){return this.yh===this.kh||Number.isNaN(this.yh)||Number.isNaN(this.kh)}ts(t){return t===null?this:new L(Ii(Math.min,this.Ph(),t.Ph(),-1/0),Ii(Math.max,this.Rh(),t.Rh(),1/0))}Vh(t){if(!P(t)||this.yh-this.kh===0)return;const i=.5*(this.yh+this.kh);let s=this.yh-i,e=this.kh-i;s*=t,e*=t,this.yh=i+s,this.kh=i+e}Oh(t){P(t)&&(this.yh+=t,this.kh+=t)}Bh(){return{minValue:this.kh,maxValue:this.yh}}static Ah(t){return t===null?null:new L(t.minValue,t.maxValue)}}class zt{constructor(t,i){this.Ih=t,this.zh=i||null}Lh(){return this.Ih}Eh(){return this.zh}Bh(){return this.Ih===null?null:{priceRange:this.Ih.Bh(),margins:this.zh||void 0}}static Ah(t){return t===null?null:new zt(L.Ah(t.priceRange),t.margins)}}class eh extends di{constructor(t,i){super(t),this.Nh=i}yr(){const t=this.Sr;t.yt=!1;const i=this.Nh.W();if(!this.Es.yt()||!i.lineVisible)return;const s=this.Nh.Fh();s!==null&&(t.yt=!0,t.st=s,t.V=i.color,t.et=i.lineWidth,t.Nt=i.lineStyle,t.gr=this.Nh.W().id)}}class hh extends Lt{constructor(t,i){super(),this.jr=t,this.Nh=i}zi(t,i,s){t.yt=!1,i.yt=!1;const e=this.Nh.W(),n=e.axisLabelVisible,r=e.title!=="",l=this.jr;if(!n||!l.yt())return;const o=this.Nh.Fh();if(o===null)return;r&&(i.Kt=e.title,i.yt=!0),i.Ot=l.$t().Bt(o/l.Dt().At()),t.Kt=this.Wh(e.price),t.yt=!0;const a=Et(e.axisLabelColor||e.color);s.t=a.t;const u=e.axisLabelTextColor||a.i;t.V=u,i.V=u,s.ki=o}Wh(t){const i=this.jr.Ct();return i===null?"":this.jr.Dt().Fi(t,i.Vt)}}class nh{constructor(t,i){this.jr=t,this.cn=i,this.jh=new eh(t,this),this.ur=new hh(t,this),this.Hh=new Ws(this.ur,t,t.$t())}$h(t){R(this.cn,t),this.bt(),this.jr.$t().Uh()}W(){return this.cn}qh(){return this.jh}Yh(){return this.Hh}Zh(){return this.ur}bt(){this.jh.bt(),this.ur.bt()}Fh(){const t=this.jr,i=t.Dt();if(t.$t().St().Ni()||i.Ni())return null;const s=t.Ct();return s===null?null:i.Rt(this.cn.price,s.Vt)}}class rh extends ui{constructor(t){super(),this.$i=t}$t(){return this.$i}}const lh={Bar:(h,t,i,s)=>{var e;const n=t.upColor,r=t.downColor,l=v(h(i,s)),o=J(l.Vt[0])<=J(l.Vt[3]);return{ce:(e=l.V)!==null&&e!==void 0?e:o?n:r}},Candlestick:(h,t,i,s)=>{var e,n,r;const l=t.upColor,o=t.downColor,a=t.borderUpColor,u=t.borderDownColor,c=t.wickUpColor,f=t.wickDownColor,d=v(h(i,s)),m=J(d.Vt[0])<=J(d.Vt[3]);return{ce:(e=d.V)!==null&&e!==void 0?e:m?l:o,Ne:(n=d.Ot)!==null&&n!==void 0?n:m?a:u,Ee:(r=d.Xh)!==null&&r!==void 0?r:m?c:f}},Custom:(h,t,i,s)=>{var e;return{ce:(e=v(h(i,s)).V)!==null&&e!==void 0?e:t.color}},Area:(h,t,i,s)=>{var e,n,r,l;const o=v(h(i,s));return{ce:(e=o.lt)!==null&&e!==void 0?e:t.lineColor,lt:(n=o.lt)!==null&&n!==void 0?n:t.lineColor,Ps:(r=o.Ps)!==null&&r!==void 0?r:t.topColor,Rs:(l=o.Rs)!==null&&l!==void 0?l:t.bottomColor}},Baseline:(h,t,i,s)=>{var e,n,r,l,o,a;const u=v(h(i,s));return{ce:u.Vt[3]>=t.baseValue.price?t.topLineColor:t.bottomLineColor,Re:(e=u.Re)!==null&&e!==void 0?e:t.topLineColor,De:(n=u.De)!==null&&n!==void 0?n:t.bottomLineColor,ke:(r=u.ke)!==null&&r!==void 0?r:t.topFillColor1,ye:(l=u.ye)!==null&&l!==void 0?l:t.topFillColor2,Ce:(o=u.Ce)!==null&&o!==void 0?o:t.bottomFillColor1,Te:(a=u.Te)!==null&&a!==void 0?a:t.bottomFillColor2}},Line:(h,t,i,s)=>{var e,n;const r=v(h(i,s));return{ce:(e=r.V)!==null&&e!==void 0?e:t.color,lt:(n=r.V)!==null&&n!==void 0?n:t.color}},Histogram:(h,t,i,s)=>{var e;return{ce:(e=v(h(i,s)).V)!==null&&e!==void 0?e:t.color}}};class oh{constructor(t){this.Kh=(i,s)=>s!==void 0?s.Vt:this.jr.In().Gh(i),this.jr=t,this.Jh=lh[t.Qh()]}$s(t,i){return this.Jh(this.Kh,this.jr.W(),t,i)}}var Ki;(function(h){h[h.NearestLeft=-1]="NearestLeft",h[h.None=0]="None",h[h.NearestRight=1]="NearestRight"})(Ki||(Ki={}));const B=30;class ah{constructor(){this.tl=[],this.il=new Map,this.nl=new Map}sl(){return this.Ks()>0?this.tl[this.tl.length-1]:null}el(){return this.Ks()>0?this.rl(0):null}An(){return this.Ks()>0?this.rl(this.tl.length-1):null}Ks(){return this.tl.length}Ni(){return this.Ks()===0}Kr(t){return this.hl(t,0)!==null}Gh(t){return this.ll(t)}ll(t,i=0){const s=this.hl(t,i);return s===null?null:Object.assign(Object.assign({},this.al(s)),{ee:this.rl(s)})}ne(){return this.tl}ol(t,i,s){if(this.Ni())return null;let e=null;for(const n of s)e=vt(e,this._l(t,i,n));return e}J(t){this.nl.clear(),this.il.clear(),this.tl=t}rl(t){return this.tl[t].ee}al(t){return this.tl[t]}hl(t,i){const s=this.ul(t);if(s===null&&i!==0)switch(i){case-1:return this.cl(t);case 1:return this.dl(t);default:throw new TypeError("Unknown search mode")}return s}cl(t){let i=this.fl(t);return i>0&&(i-=1),i!==this.tl.length&&this.rl(i)<t?i:null}dl(t){const i=this.vl(t);return i!==this.tl.length&&t<this.rl(i)?i:null}ul(t){const i=this.fl(t);return i===this.tl.length||t<this.tl[i].ee?null:i}fl(t){return ft(this.tl,t,(i,s)=>i.ee<s)}vl(t){return Ts(this.tl,t,(i,s)=>i.ee>s)}pl(t,i,s){let e=null;for(let n=t;n<i;n++){const r=this.tl[n].Vt[s];Number.isNaN(r)||(e===null?e={ml:r,bl:r}:(r<e.ml&&(e.ml=r),r>e.bl&&(e.bl=r)))}return e}_l(t,i,s){if(this.Ni())return null;let e=null;const n=v(this.el()),r=v(this.An()),l=Math.max(t,n),o=Math.min(i,r),a=Math.ceil(l/B)*B,u=Math.max(a,Math.floor(o/B)*B);{const f=this.fl(l),d=this.vl(Math.min(o,a,i));e=vt(e,this.pl(f,d,s))}let c=this.il.get(s);c===void 0&&(c=new Map,this.il.set(s,c));for(let f=Math.max(a+1,l);f<u;f+=B){const d=Math.floor(f/B);let m=c.get(d);if(m===void 0){const p=this.fl(d*B),b=this.vl((d+1)*B-1);m=this.pl(p,b,s),c.set(d,m)}e=vt(e,m)}{const f=this.fl(u),d=this.vl(o);e=vt(e,this.pl(f,d,s))}return e}}function vt(h,t){return h===null?t:t===null?h:{ml:Math.min(h.ml,t.ml),bl:Math.max(h.bl,t.bl)}}class uh{constructor(t){this.wl=t}X(t,i,s){this.wl.draw(t)}gl(t,i,s){var e,n;(n=(e=this.wl).drawBackground)===null||n===void 0||n.call(e,t)}}class Bt{constructor(t){this.tr=null,this.wn=t}gt(){var t;const i=this.wn.renderer();if(i===null)return null;if(((t=this.tr)===null||t===void 0?void 0:t.Ml)===i)return this.tr.xl;const s=new uh(i);return this.tr={Ml:i,xl:s},s}Sl(){var t,i,s;return(s=(i=(t=this.wn).zOrder)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:"normal"}}function Bs(h){var t,i,s,e,n;return{Kt:h.text(),ki:h.coordinate(),Si:(t=h.fixedCoordinate)===null||t===void 0?void 0:t.call(h),V:h.textColor(),t:h.backColor(),yt:(s=(i=h.visible)===null||i===void 0?void 0:i.call(h))===null||s===void 0||s,hi:(n=(e=h.tickVisible)===null||e===void 0?void 0:e.call(h))===null||n===void 0||n}}class ch{constructor(t,i){this.Wt=new zs,this.kl=t,this.yl=i}gt(){return this.Wt.J(Object.assign({Hi:this.yl.Hi()},Bs(this.kl))),this.Wt}}class fh extends Lt{constructor(t,i){super(),this.kl=t,this.Li=i}zi(t,i,s){const e=Bs(this.kl);s.t=e.t,t.V=e.V;const n=2/12*this.Li.P();s.wi=n,s.gi=n,s.ki=e.ki,s.Si=e.Si,t.Kt=e.Kt,t.yt=e.yt,t.hi=e.hi}}class dh{constructor(t,i){this.Cl=null,this.Tl=null,this.Pl=null,this.Rl=null,this.Dl=null,this.Vl=t,this.jr=i}Ol(){return this.Vl}Vn(){var t,i;(i=(t=this.Vl).updateAllViews)===null||i===void 0||i.call(t)}Pn(){var t,i,s,e;const n=(s=(i=(t=this.Vl).paneViews)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:[];if(((e=this.Cl)===null||e===void 0?void 0:e.Ml)===n)return this.Cl.xl;const r=n.map(l=>new Bt(l));return this.Cl={Ml:n,xl:r},r}Qi(){var t,i,s,e;const n=(s=(i=(t=this.Vl).timeAxisViews)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:[];if(((e=this.Tl)===null||e===void 0?void 0:e.Ml)===n)return this.Tl.xl;const r=this.jr.$t().St(),l=n.map(o=>new ch(o,r));return this.Tl={Ml:n,xl:l},l}Rn(){var t,i,s,e;const n=(s=(i=(t=this.Vl).priceAxisViews)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:[];if(((e=this.Pl)===null||e===void 0?void 0:e.Ml)===n)return this.Pl.xl;const r=this.jr.Dt(),l=n.map(o=>new fh(o,r));return this.Pl={Ml:n,xl:l},l}Bl(){var t,i,s,e;const n=(s=(i=(t=this.Vl).priceAxisPaneViews)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:[];if(((e=this.Rl)===null||e===void 0?void 0:e.Ml)===n)return this.Rl.xl;const r=n.map(l=>new Bt(l));return this.Rl={Ml:n,xl:r},r}Al(){var t,i,s,e;const n=(s=(i=(t=this.Vl).timeAxisPaneViews)===null||i===void 0?void 0:i.call(t))!==null&&s!==void 0?s:[];if(((e=this.Dl)===null||e===void 0?void 0:e.Ml)===n)return this.Dl.xl;const r=n.map(l=>new Bt(l));return this.Dl={Ml:n,xl:r},r}Il(t,i){var s,e,n;return(n=(e=(s=this.Vl).autoscaleInfo)===null||e===void 0?void 0:e.call(s,t,i))!==null&&n!==void 0?n:null}wr(t,i){var s,e,n;return(n=(e=(s=this.Vl).hitTest)===null||e===void 0?void 0:e.call(s,t,i))!==null&&n!==void 0?n:null}}function It(h,t,i,s){h.forEach(e=>{t(e).forEach(n=>{n.Sl()===i&&s.push(n)})})}function Kt(h){return h.Pn()}function mh(h){return h.Bl()}function ph(h){return h.Al()}class mi extends rh{constructor(t,i,s,e,n){super(t),this.zt=new ah,this.jh=new ih(this),this.zl=[],this.Ll=new He(this),this.El=null,this.Nl=null,this.Fl=[],this.Wl=[],this.jl=null,this.Hl=[],this.cn=i,this.$l=s;const r=new sh(this);this.rn=[r],this.Hh=new Ws(r,this,t),s!=="Area"&&s!=="Line"&&s!=="Baseline"||(this.El=new qe(this)),this.Ul(),this.ql(n)}S(){this.jl!==null&&clearTimeout(this.jl)}mh(t){return this.cn.priceLineColor||t}Zr(t){const i={Xr:!0},s=this.Dt();if(this.$t().St().Ni()||s.Ni()||this.zt.Ni())return i;const e=this.$t().St().Xs(),n=this.Ct();if(e===null||n===null)return i;let r,l;if(t){const c=this.zt.sl();if(c===null)return i;r=c,l=c.ee}else{const c=this.zt.ll(e.ui(),-1);if(c===null||(r=this.zt.Gh(c.ee),r===null))return i;l=c.ee}const o=r.Vt[3],a=this.Us().$s(l,{Vt:r}),u=s.Rt(o,n.Vt);return{Xr:!1,_t:o,Kt:s.Fi(o,n.Vt),xh:s.Yl(o),Sh:s.Zl(o,n.Vt),V:a.ce,ki:u,ee:l}}Us(){return this.Nl!==null||(this.Nl=new oh(this)),this.Nl}W(){return this.cn}$h(t){const i=t.priceScaleId;i!==void 0&&i!==this.cn.priceScaleId&&this.$t().Xl(this,i),R(this.cn,t),t.priceFormat!==void 0&&(this.Ul(),this.$t().Kl()),this.$t().Gl(this),this.$t().Jl(),this.wn.bt("options")}J(t,i){this.zt.J(t),this.Ql(),this.wn.bt("data"),this.dn.bt("data"),this.El!==null&&(i&&i.ta?this.El.$r():t.length===0&&this.El.Hr());const s=this.$t().dr(this);this.$t().ia(s),this.$t().Gl(this),this.$t().Jl(),this.$t().Uh()}na(t){this.Fl=t,this.Ql();const i=this.$t().dr(this);this.dn.bt("data"),this.$t().ia(i),this.$t().Gl(this),this.$t().Jl(),this.$t().Uh()}sa(){return this.Fl}dh(){return this.Wl}ea(t){const i=new nh(this,t);return this.zl.push(i),this.$t().Gl(this),i}ra(t){const i=this.zl.indexOf(t);i!==-1&&this.zl.splice(i,1),this.$t().Gl(this)}Qh(){return this.$l}Ct(){const t=this.ha();return t===null?null:{Vt:t.Vt[3],la:t.ot}}ha(){const t=this.$t().St().Xs();if(t===null)return null;const i=t.Os();return this.zt.ll(i,1)}In(){return this.zt}ph(t){const i=this.zt.Gh(t);return i===null?null:this.$l==="Bar"||this.$l==="Candlestick"||this.$l==="Custom"?{ge:i.Vt[0],Me:i.Vt[1],xe:i.Vt[2],Se:i.Vt[3]}:i.Vt[3]}aa(t){const i=[];It(this.Hl,Kt,"top",i);const s=this.El;return s!==null&&s.yt()&&(this.jl===null&&s.qr()&&(this.jl=setTimeout(()=>{this.jl=null,this.$t().oa()},0)),s.Ur(),i.unshift(s)),i}Pn(){const t=[];this._a()||t.push(this.Ll),t.push(this.wn,this.jh,this.dn);const i=this.zl.map(s=>s.qh());return t.push(...i),It(this.Hl,Kt,"normal",t),t}ua(){return this.ca(Kt,"bottom")}da(t){return this.ca(mh,t)}fa(t){return this.ca(ph,t)}va(t,i){return this.Hl.map(s=>s.wr(t,i)).filter(s=>s!==null)}Ji(t){return[this.Hh,...this.zl.map(i=>i.Yh())]}Rn(t,i){if(i!==this.Yi&&!this._a())return[];const s=[...this.rn];for(const e of this.zl)s.push(e.Zh());return this.Hl.forEach(e=>{s.push(...e.Rn())}),s}Qi(){const t=[];return this.Hl.forEach(i=>{t.push(...i.Qi())}),t}Il(t,i){if(this.cn.autoscaleInfoProvider!==void 0){const s=this.cn.autoscaleInfoProvider(()=>{const e=this.pa(t,i);return e===null?null:e.Bh()});return zt.Ah(s)}return this.pa(t,i)}ma(){return this.cn.priceFormat.minMove}ba(){return this.wa}Vn(){var t;this.wn.bt(),this.dn.bt();for(const i of this.rn)i.bt();for(const i of this.zl)i.bt();this.jh.bt(),this.Ll.bt(),(t=this.El)===null||t===void 0||t.bt(),this.Hl.forEach(i=>i.Vn())}Dt(){return v(super.Dt())}kt(t){if(!((this.$l==="Line"||this.$l==="Area"||this.$l==="Baseline")&&this.cn.crosshairMarkerVisible))return null;const i=this.zt.Gh(t);return i===null?null:{_t:i.Vt[3],ht:this.ga(),Ot:this.Ma(),Pt:this.xa(),Tt:this.Sa(t)}}bh(){return this.cn.title}yt(){return this.cn.visible}ka(t){this.Hl.push(new dh(t,this))}ya(t){this.Hl=this.Hl.filter(i=>i.Ol()!==t)}Ca(){if(this.wn instanceof Dt)return t=>this.wn.We(t)}Ta(){if(this.wn instanceof Dt)return t=>this.wn.je(t)}_a(){return!Vt(this.Dt().Pa())}pa(t,i){if(!lt(t)||!lt(i)||this.zt.Ni())return null;const s=this.$l==="Line"||this.$l==="Area"||this.$l==="Baseline"||this.$l==="Histogram"?[3]:[2,1],e=this.zt.ol(t,i,s);let n=e!==null?new L(e.ml,e.bl):null;if(this.Qh()==="Histogram"){const l=this.cn.base,o=new L(l,l);n=n!==null?n.ts(o):o}let r=this.dn.uh();return this.Hl.forEach(l=>{const o=l.Il(t,i);if(o?.priceRange){const d=new L(o.priceRange.minValue,o.priceRange.maxValue);n=n!==null?n.ts(d):d}var a,u,c,f;o?.margins&&(a=r,u=o.margins,r={above:Math.max((c=a?.above)!==null&&c!==void 0?c:0,u.above),below:Math.max((f=a?.below)!==null&&f!==void 0?f:0,u.below)})}),new zt(n,r)}ga(){switch(this.$l){case"Line":case"Area":case"Baseline":return this.cn.crosshairMarkerRadius}return 0}Ma(){switch(this.$l){case"Line":case"Area":case"Baseline":{const t=this.cn.crosshairMarkerBorderColor;if(t.length!==0)return t}}return null}xa(){switch(this.$l){case"Line":case"Area":case"Baseline":return this.cn.crosshairMarkerBorderWidth}return 0}Sa(t){switch(this.$l){case"Line":case"Area":case"Baseline":{const i=this.cn.crosshairMarkerBackgroundColor;if(i.length!==0)return i}}return this.Us().$s(t).ce}Ul(){switch(this.cn.priceFormat.type){case"custom":this.wa={format:this.cn.priceFormat.formatter};break;case"volume":this.wa=new _e(this.cn.priceFormat.precision);break;case"percent":this.wa=new Cs(this.cn.priceFormat.precision);break;default:{const t=Math.pow(10,this.cn.priceFormat.precision);this.wa=new Nt(t,this.cn.priceFormat.minMove*t)}}this.Yi!==null&&this.Yi.Ra()}Ql(){const t=this.$t().St();if(!t.Da()||this.zt.Ni())return void(this.Wl=[]);const i=v(this.zt.el());this.Wl=this.Fl.map((s,e)=>{const n=v(t.Va(s.time,!0)),r=n<i?1:-1;return{time:v(this.zt.ll(n,r)).ee,position:s.position,shape:s.shape,color:s.color,id:s.id,th:e,text:s.text,size:s.size,originalTime:s.originalTime}})}ql(t){switch(this.dn=new th(this,this.$t()),this.$l){case"Bar":this.wn=new Ne(this,this.$t());break;case"Candlestick":this.wn=new We(this,this.$t());break;case"Line":this.wn=new Ke(this,this.$t());break;case"Custom":this.wn=new Dt(this,this.$t(),k(t));break;case"Area":this.wn=new Le(this,this.$t());break;case"Baseline":this.wn=new $e(this,this.$t());break;case"Histogram":this.wn=new Ie(this,this.$t());break;default:throw Error("Unknown chart style assigned: "+this.$l)}}ca(t,i){const s=[];return It(this.Hl,t,i,s),s}}class vh{constructor(t){this.cn=t}Oa(t,i,s){let e=t;if(this.cn.mode===0)return e;const n=s.vn(),r=n.Ct();if(r===null)return e;const l=n.Rt(t,r),o=s.Ba().filter(u=>u instanceof mi).reduce((u,c)=>{if(s.vr(c)||!c.yt())return u;const f=c.Dt(),d=c.In();if(f.Ni()||!d.Kr(i))return u;const m=d.Gh(i);if(m===null)return u;const p=J(c.Ct());return u.concat([f.Rt(m.Vt[3],p.Vt)])},[]);if(o.length===0)return e;o.sort((u,c)=>Math.abs(u-l)-Math.abs(c-l));const a=o[0];return e=n.pn(a,r),e}}class bh extends T{constructor(){super(...arguments),this.zt=null}J(t){this.zt=t}K({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:e}){if(this.zt===null)return;const n=Math.max(1,Math.floor(s));t.lineWidth=n,function(r,l){r.save(),r.lineWidth%2&&r.translate(.5,.5),l(),r.restore()}(t,()=>{const r=v(this.zt);if(r.Aa){t.strokeStyle=r.Ia,X(t,r.za),t.beginPath();for(const l of r.La){const o=Math.round(l.Ea*s);t.moveTo(o,-n),t.lineTo(o,i.height+n)}t.stroke()}if(r.Na){t.strokeStyle=r.Fa,X(t,r.Wa),t.beginPath();for(const l of r.ja){const o=Math.round(l.Ea*e);t.moveTo(-n,o),t.lineTo(i.width+n,o)}t.stroke()}})}}class gh{constructor(t){this.Wt=new bh,this.ft=!0,this.tn=t}bt(){this.ft=!0}gt(){if(this.ft){const t=this.tn.$t().W().grid,i={Na:t.horzLines.visible,Aa:t.vertLines.visible,Fa:t.horzLines.color,Ia:t.vertLines.color,Wa:t.horzLines.style,za:t.vertLines.style,ja:this.tn.vn().Ha(),La:(this.tn.$t().St().Ha()||[]).map(s=>({Ea:s.coord}))};this.Wt.J(i),this.ft=!1}return this.Wt}}class wh{constructor(t){this.wn=new gh(t)}qh(){return this.wn}}const jt={$a:4,Ua:1e-4};function G(h,t){const i=100*(h-t)/t;return t<0?-i:i}function yh(h,t){const i=G(h.Ph(),t),s=G(h.Rh(),t);return new L(i,s)}function et(h,t){const i=100*(h-t)/t+100;return t<0?-i:i}function Sh(h,t){const i=et(h.Ph(),t),s=et(h.Rh(),t);return new L(i,s)}function Ct(h,t){const i=Math.abs(h);if(i<1e-15)return 0;const s=Math.log10(i+t.Ua)+t.$a;return h<0?-s:s}function ht(h,t){const i=Math.abs(h);if(i<1e-15)return 0;const s=Math.pow(10,i-t.$a)-t.Ua;return h<0?-s:s}function it(h,t){if(h===null)return null;const i=Ct(h.Ph(),t),s=Ct(h.Rh(),t);return new L(i,s)}function bt(h,t){if(h===null)return null;const i=ht(h.Ph(),t),s=ht(h.Rh(),t);return new L(i,s)}function Ft(h){if(h===null)return jt;const t=Math.abs(h.Rh()-h.Ph());if(t>=1||t<1e-15)return jt;const i=Math.ceil(Math.abs(Math.log10(t))),s=jt.$a+i;return{$a:s,Ua:1/Math.pow(10,s)}}class Xt{constructor(t,i){if(this.qa=t,this.Ya=i,function(s){if(s<0)return!1;for(let e=s;e>1;e/=10)if(e%10!=0)return!1;return!0}(this.qa))this.Za=[2,2.5,2];else{this.Za=[];for(let s=this.qa;s!==1;){if(s%2==0)this.Za.push(2),s/=2;else{if(s%5!=0)throw new Error("unexpected base");this.Za.push(2,2.5),s/=5}if(this.Za.length>100)throw new Error("something wrong with base")}}}Xa(t,i,s){const e=this.qa===0?0:1/this.qa;let n=Math.pow(10,Math.max(0,Math.ceil(Math.log10(t-i)))),r=0,l=this.Ya[0];for(;;){const c=pt(n,e,1e-14)&&n>e+1e-14,f=pt(n,s*l,1e-14),d=pt(n,1,1e-14);if(!(c&&f&&d))break;n/=l,l=this.Ya[++r%this.Ya.length]}if(n<=e+1e-14&&(n=e),n=Math.max(1,n),this.Za.length>0&&(o=n,a=1,u=1e-14,Math.abs(o-a)<u))for(r=0,l=this.Za[0];pt(n,s*l,1e-14)&&n>e+1e-14;)n/=l,l=this.Za[++r%this.Za.length];var o,a,u;return n}}class ji{constructor(t,i,s,e){this.Ka=[],this.Li=t,this.qa=i,this.Ga=s,this.Ja=e}Xa(t,i){if(t<i)throw new Error("high < low");const s=this.Li.At(),e=(t-i)*this.Qa()/s,n=new Xt(this.qa,[2,2.5,2]),r=new Xt(this.qa,[2,2,2.5]),l=new Xt(this.qa,[2.5,2,2]),o=[];return o.push(n.Xa(t,i,e),r.Xa(t,i,e),l.Xa(t,i,e)),function(a){if(a.length<1)throw Error("array is empty");let u=a[0];for(let c=1;c<a.length;++c)a[c]<u&&(u=a[c]);return u}(o)}io(){const t=this.Li,i=t.Ct();if(i===null)return void(this.Ka=[]);const s=t.At(),e=this.Ga(s-1,i),n=this.Ga(0,i),r=this.Li.W().entireTextOnly?this.no()/2:0,l=r,o=s-1-r,a=Math.max(e,n),u=Math.min(e,n);if(a===u)return void(this.Ka=[]);let c=this.Xa(a,u),f=a%c;f+=f<0?c:0;const d=a>=u?1:-1;let m=null,p=0;for(let b=a-f;b>u;b-=c){const g=this.Ja(b,i,!0);m!==null&&Math.abs(g-m)<this.Qa()||g<l||g>o||(p<this.Ka.length?(this.Ka[p].Ea=g,this.Ka[p].so=t.eo(b)):this.Ka.push({Ea:g,so:t.eo(b)}),p++,m=g,t.ro()&&(c=this.Xa(b*d,u)))}this.Ka.length=p}Ha(){return this.Ka}no(){return this.Li.P()}Qa(){return Math.ceil(2.5*this.no())}}function Is(h){return h.slice().sort((t,i)=>v(t.Xi())-v(i.Xi()))}var Fi;(function(h){h[h.Normal=0]="Normal",h[h.Logarithmic=1]="Logarithmic",h[h.Percentage=2]="Percentage",h[h.IndexedTo100=3]="IndexedTo100"})(Fi||(Fi={}));const Xi=new Cs,Hi=new Nt(100,1);class _h{constructor(t,i,s,e){this.ho=0,this.lo=null,this.Ih=null,this.ao=null,this.oo={_o:!1,uo:null},this.co=0,this.do=0,this.fo=new M,this.vo=new M,this.po=[],this.mo=null,this.bo=null,this.wo=null,this.Mo=null,this.wa=Hi,this.xo=Ft(null),this.So=t,this.cn=i,this.ko=s,this.yo=e,this.Co=new ji(this,100,this.To.bind(this),this.Po.bind(this))}Pa(){return this.So}W(){return this.cn}$h(t){if(R(this.cn,t),this.Ra(),t.mode!==void 0&&this.Ro({Cr:t.mode}),t.scaleMargins!==void 0){const i=k(t.scaleMargins.top),s=k(t.scaleMargins.bottom);if(i<0||i>1)throw new Error(`Invalid top margin - expect value between 0 and 1, given=${i}`);if(s<0||s>1)throw new Error(`Invalid bottom margin - expect value between 0 and 1, given=${s}`);if(i+s>1)throw new Error(`Invalid margins - sum of margins must be less than 1, given=${i+s}`);this.Do(),this.bo=null}}Vo(){return this.cn.autoScale}ro(){return this.cn.mode===1}Mh(){return this.cn.mode===2}Oo(){return this.cn.mode===3}Cr(){return{Wn:this.cn.autoScale,Bo:this.cn.invertScale,Cr:this.cn.mode}}Ro(t){const i=this.Cr();let s=null;t.Wn!==void 0&&(this.cn.autoScale=t.Wn),t.Cr!==void 0&&(this.cn.mode=t.Cr,t.Cr!==2&&t.Cr!==3||(this.cn.autoScale=!0),this.oo._o=!1),i.Cr===1&&t.Cr!==i.Cr&&(function(n,r){if(n===null)return!1;const l=ht(n.Ph(),r),o=ht(n.Rh(),r);return isFinite(l)&&isFinite(o)}(this.Ih,this.xo)?(s=bt(this.Ih,this.xo),s!==null&&this.Ao(s)):this.cn.autoScale=!0),t.Cr===1&&t.Cr!==i.Cr&&(s=it(this.Ih,this.xo),s!==null&&this.Ao(s));const e=i.Cr!==this.cn.mode;e&&(i.Cr===2||this.Mh())&&this.Ra(),e&&(i.Cr===3||this.Oo())&&this.Ra(),t.Bo!==void 0&&i.Bo!==t.Bo&&(this.cn.invertScale=t.Bo,this.Io()),this.vo.m(i,this.Cr())}zo(){return this.vo}P(){return this.ko.fontSize}At(){return this.ho}Lo(t){this.ho!==t&&(this.ho=t,this.Do(),this.bo=null)}Eo(){if(this.lo)return this.lo;const t=this.At()-this.No()-this.Fo();return this.lo=t,t}Lh(){return this.Wo(),this.Ih}Ao(t,i){const s=this.Ih;(i||s===null&&t!==null||s!==null&&!s.Ch(t))&&(this.bo=null,this.Ih=t)}Ni(){return this.Wo(),this.ho===0||!this.Ih||this.Ih.Ni()}jo(t){return this.Bo()?t:this.At()-1-t}Rt(t,i){return this.Mh()?t=G(t,i):this.Oo()&&(t=et(t,i)),this.Po(t,i)}te(t,i,s){this.Wo();const e=this.Fo(),n=v(this.Lh()),r=n.Ph(),l=n.Rh(),o=this.Eo()-1,a=this.Bo(),u=o/(l-r),c=s===void 0?0:s.from,f=s===void 0?t.length:s.to,d=this.Ho();for(let m=c;m<f;m++){const p=t[m],b=p._t;if(isNaN(b))continue;let g=b;d!==null&&(g=d(p._t,i));const w=e+u*(g-r),_=a?w:this.ho-1-w;p.st=_}}be(t,i,s){this.Wo();const e=this.Fo(),n=v(this.Lh()),r=n.Ph(),l=n.Rh(),o=this.Eo()-1,a=this.Bo(),u=o/(l-r),c=s===void 0?0:s.from,f=s===void 0?t.length:s.to,d=this.Ho();for(let m=c;m<f;m++){const p=t[m];let b=p.ge,g=p.Me,w=p.xe,_=p.Se;d!==null&&(b=d(p.ge,i),g=d(p.Me,i),w=d(p.xe,i),_=d(p.Se,i));let y=e+u*(b-r),x=a?y:this.ho-1-y;p.pe=x,y=e+u*(g-r),x=a?y:this.ho-1-y,p.de=x,y=e+u*(w-r),x=a?y:this.ho-1-y,p.fe=x,y=e+u*(_-r),x=a?y:this.ho-1-y,p.me=x}}pn(t,i){const s=this.To(t,i);return this.$o(s,i)}$o(t,i){let s=t;return this.Mh()?s=function(e,n){return n<0&&(e=-e),e/100*n+n}(s,i):this.Oo()&&(s=function(e,n){return e-=100,n<0&&(e=-e),e/100*n+n}(s,i)),s}Ba(){return this.po}Uo(){if(this.mo)return this.mo;let t=[];for(let i=0;i<this.po.length;i++){const s=this.po[i];s.Xi()===null&&s.Ki(i+1),t.push(s)}return t=Is(t),this.mo=t,this.mo}qo(t){this.po.indexOf(t)===-1&&(this.po.push(t),this.Ra(),this.Yo())}Zo(t){const i=this.po.indexOf(t);if(i===-1)throw new Error("source is not attached to scale");this.po.splice(i,1),this.po.length===0&&(this.Ro({Wn:!0}),this.Ao(null)),this.Ra(),this.Yo()}Ct(){let t=null;for(const i of this.po){const s=i.Ct();s!==null&&(t===null||s.la<t.la)&&(t=s)}return t===null?null:t.Vt}Bo(){return this.cn.invertScale}Ha(){const t=this.Ct()===null;if(this.bo!==null&&(t||this.bo.Xo===t))return this.bo.Ha;this.Co.io();const i=this.Co.Ha();return this.bo={Ha:i,Xo:t},this.fo.m(),i}Ko(){return this.fo}Go(t){this.Mh()||this.Oo()||this.wo===null&&this.ao===null&&(this.Ni()||(this.wo=this.ho-t,this.ao=v(this.Lh()).Th()))}Jo(t){if(this.Mh()||this.Oo()||this.wo===null)return;this.Ro({Wn:!1}),(t=this.ho-t)<0&&(t=0);let i=(this.wo+.2*(this.ho-1))/(t+.2*(this.ho-1));const s=v(this.ao).Th();i=Math.max(i,.1),s.Vh(i),this.Ao(s)}Qo(){this.Mh()||this.Oo()||(this.wo=null,this.ao=null)}t_(t){this.Vo()||this.Mo===null&&this.ao===null&&(this.Ni()||(this.Mo=t,this.ao=v(this.Lh()).Th()))}i_(t){if(this.Vo()||this.Mo===null)return;const i=v(this.Lh()).Dh()/(this.Eo()-1);let s=t-this.Mo;this.Bo()&&(s*=-1);const e=s*i,n=v(this.ao).Th();n.Oh(e),this.Ao(n,!0),this.bo=null}n_(){this.Vo()||this.Mo!==null&&(this.Mo=null,this.ao=null)}ba(){return this.wa||this.Ra(),this.wa}Fi(t,i){switch(this.cn.mode){case 2:return this.s_(G(t,i));case 3:return this.ba().format(et(t,i));default:return this.Wh(t)}}eo(t){switch(this.cn.mode){case 2:return this.s_(t);case 3:return this.ba().format(t);default:return this.Wh(t)}}Yl(t){return this.Wh(t,v(this.e_()).ba())}Zl(t,i){return t=G(t,i),this.s_(t,Xi)}r_(){return this.po}h_(t){this.oo={uo:t,_o:!1}}Vn(){this.po.forEach(t=>t.Vn())}Ra(){this.bo=null;const t=this.e_();let i=100;t!==null&&(i=Math.round(1/t.ma())),this.wa=Hi,this.Mh()?(this.wa=Xi,i=100):this.Oo()?(this.wa=new Nt(100,1),i=100):t!==null&&(this.wa=t.ba()),this.Co=new ji(this,i,this.To.bind(this),this.Po.bind(this)),this.Co.io()}Yo(){this.mo=null}e_(){return this.po[0]||null}No(){return this.Bo()?this.cn.scaleMargins.bottom*this.At()+this.do:this.cn.scaleMargins.top*this.At()+this.co}Fo(){return this.Bo()?this.cn.scaleMargins.top*this.At()+this.co:this.cn.scaleMargins.bottom*this.At()+this.do}Wo(){this.oo._o||(this.oo._o=!0,this.l_())}Do(){this.lo=null}Po(t,i){if(this.Wo(),this.Ni())return 0;t=this.ro()&&t?Ct(t,this.xo):t;const s=v(this.Lh()),e=this.Fo()+(this.Eo()-1)*(t-s.Ph())/s.Dh();return this.jo(e)}To(t,i){if(this.Wo(),this.Ni())return 0;const s=this.jo(t),e=v(this.Lh()),n=e.Ph()+e.Dh()*((s-this.Fo())/(this.Eo()-1));return this.ro()?ht(n,this.xo):n}Io(){this.bo=null,this.Co.io()}l_(){const t=this.oo.uo;if(t===null)return;let i=null;const s=this.r_();let e=0,n=0;for(const o of s){if(!o.yt())continue;const a=o.Ct();if(a===null)continue;const u=o.Il(t.Os(),t.ui());let c=u&&u.Lh();if(c!==null){switch(this.cn.mode){case 1:c=it(c,this.xo);break;case 2:c=yh(c,a.Vt);break;case 3:c=Sh(c,a.Vt)}if(i=i===null?c:i.ts(v(c)),u!==null){const f=u.Eh();f!==null&&(e=Math.max(e,f.above),n=Math.max(n,f.below))}}}if(e===this.co&&n===this.do||(this.co=e,this.do=n,this.bo=null,this.Do()),i!==null){if(i.Ph()===i.Rh()){const o=this.e_(),a=5*(o===null||this.Mh()||this.Oo()?1:o.ma());this.ro()&&(i=bt(i,this.xo)),i=new L(i.Ph()-a,i.Rh()+a),this.ro()&&(i=it(i,this.xo))}if(this.ro()){const o=bt(i,this.xo),a=Ft(o);if(r=a,l=this.xo,r.$a!==l.$a||r.Ua!==l.Ua){const u=this.ao!==null?bt(this.ao,this.xo):null;this.xo=a,i=it(o,a),u!==null&&(this.ao=it(u,a))}}this.Ao(i)}else this.Ih===null&&(this.Ao(new L(-.5,.5)),this.xo=Ft(null));var r,l;this.oo._o=!0}Ho(){return this.Mh()?G:this.Oo()?et:this.ro()?t=>Ct(t,this.xo):null}a_(t,i,s){return i===void 0?(s===void 0&&(s=this.ba()),s.format(t)):i(t)}Wh(t,i){return this.a_(t,this.yo.priceFormatter,i)}s_(t,i){return this.a_(t,this.yo.percentageFormatter,i)}}class Mh{constructor(t,i){this.po=[],this.o_=new Map,this.ho=0,this.__=0,this.u_=1e3,this.mo=null,this.c_=new M,this.yl=t,this.$i=i,this.d_=new wh(this);const s=i.W();this.f_=this.v_("left",s.leftPriceScale),this.p_=this.v_("right",s.rightPriceScale),this.f_.zo().l(this.m_.bind(this,this.f_),this),this.p_.zo().l(this.m_.bind(this,this.p_),this),this.b_(s)}b_(t){if(t.leftPriceScale&&this.f_.$h(t.leftPriceScale),t.rightPriceScale&&this.p_.$h(t.rightPriceScale),t.localization&&(this.f_.Ra(),this.p_.Ra()),t.overlayPriceScales){const i=Array.from(this.o_.values());for(const s of i){const e=v(s[0].Dt());e.$h(t.overlayPriceScales),t.localization&&e.Ra()}}}w_(t){switch(t){case"left":return this.f_;case"right":return this.p_}return this.o_.has(t)?k(this.o_.get(t))[0].Dt():null}S(){this.$t().g_().p(this),this.f_.zo().p(this),this.p_.zo().p(this),this.po.forEach(t=>{t.S&&t.S()}),this.c_.m()}M_(){return this.u_}x_(t){this.u_=t}$t(){return this.$i}Hi(){return this.__}At(){return this.ho}S_(t){this.__=t,this.k_()}Lo(t){this.ho=t,this.f_.Lo(t),this.p_.Lo(t),this.po.forEach(i=>{if(this.vr(i)){const s=i.Dt();s!==null&&s.Lo(t)}}),this.k_()}Ba(){return this.po}vr(t){const i=t.Dt();return i===null||this.f_!==i&&this.p_!==i}qo(t,i,s){const e=s!==void 0?s:this.C_().y_+1;this.T_(t,i,e)}Zo(t){const i=this.po.indexOf(t);I(i!==-1,"removeDataSource: invalid data source"),this.po.splice(i,1);const s=v(t.Dt()).Pa();if(this.o_.has(s)){const n=k(this.o_.get(s)),r=n.indexOf(t);r!==-1&&(n.splice(r,1),n.length===0&&this.o_.delete(s))}const e=t.Dt();e&&e.Ba().indexOf(t)>=0&&e.Zo(t),e!==null&&(e.Yo(),this.P_(e)),this.mo=null}mr(t){return t===this.f_?"left":t===this.p_?"right":"overlay"}R_(){return this.f_}D_(){return this.p_}V_(t,i){t.Go(i)}O_(t,i){t.Jo(i),this.k_()}B_(t){t.Qo()}A_(t,i){t.t_(i)}I_(t,i){t.i_(i),this.k_()}z_(t){t.n_()}k_(){this.po.forEach(t=>{t.Vn()})}vn(){let t=null;return this.$i.W().rightPriceScale.visible&&this.p_.Ba().length!==0?t=this.p_:this.$i.W().leftPriceScale.visible&&this.f_.Ba().length!==0?t=this.f_:this.po.length!==0&&(t=this.po[0].Dt()),t===null&&(t=this.p_),t}pr(){let t=null;return this.$i.W().rightPriceScale.visible?t=this.p_:this.$i.W().leftPriceScale.visible&&(t=this.f_),t}P_(t){t!==null&&t.Vo()&&this.L_(t)}E_(t){const i=this.yl.Xs();t.Ro({Wn:!0}),i!==null&&t.h_(i),this.k_()}N_(){this.L_(this.f_),this.L_(this.p_)}F_(){this.P_(this.f_),this.P_(this.p_),this.po.forEach(t=>{this.vr(t)&&this.P_(t.Dt())}),this.k_(),this.$i.Uh()}Uo(){return this.mo===null&&(this.mo=Is(this.po)),this.mo}W_(){return this.c_}j_(){return this.d_}L_(t){const i=t.r_();if(i&&i.length>0&&!this.yl.Ni()){const s=this.yl.Xs();s!==null&&t.h_(s)}t.Vn()}C_(){const t=this.Uo();if(t.length===0)return{H_:0,y_:0};let i=0,s=0;for(let e=0;e<t.length;e++){const n=t[e].Xi();n!==null&&(n<i&&(i=n),n>s&&(s=n))}return{H_:i,y_:s}}T_(t,i,s){let e=this.w_(i);if(e===null&&(e=this.v_(i,this.$i.W().overlayPriceScales)),this.po.push(t),!Vt(i)){const n=this.o_.get(i)||[];n.push(t),this.o_.set(i,n)}e.qo(t),t.Gi(e),t.Ki(s),this.P_(e),this.mo=null}m_(t,i,s){i.Cr!==s.Cr&&this.L_(t)}v_(t,i){const s=Object.assign({visible:!0,autoScale:!0},W(i)),e=new _h(t,s,this.$i.W().layout,this.$i.W().localization);return e.Lo(this.At()),e}}class xh{constructor(t,i,s=50){this.Ke=0,this.Ge=1,this.Je=1,this.tr=new Map,this.Qe=new Map,this.U_=t,this.q_=i,this.ir=s}Y_(t){const i=t.time,s=this.q_.cacheKey(i),e=this.tr.get(s);if(e!==void 0)return e.Z_;if(this.Ke===this.ir){const r=this.Qe.get(this.Je);this.Qe.delete(this.Je),this.tr.delete(k(r)),this.Je++,this.Ke--}const n=this.U_(t);return this.tr.set(s,{Z_:n,rr:this.Ge}),this.Qe.set(this.Ge,s),this.Ke++,this.Ge++,n}}class nt{constructor(t,i){I(t<=i,"right should be >= left"),this.X_=t,this.K_=i}Os(){return this.X_}ui(){return this.K_}G_(){return this.K_-this.X_+1}Kr(t){return this.X_<=t&&t<=this.K_}Ch(t){return this.X_===t.Os()&&this.K_===t.ui()}}function Ai(h,t){return h===null||t===null?h===t:h.Ch(t)}class zh{constructor(){this.J_=new Map,this.tr=null,this.Q_=!1}tu(t){this.Q_=t,this.tr=null}iu(t,i){this.nu(i),this.tr=null;for(let s=i;s<t.length;++s){const e=t[s];let n=this.J_.get(e.timeWeight);n===void 0&&(n=[],this.J_.set(e.timeWeight,n)),n.push({index:s,time:e.time,weight:e.timeWeight,originalTime:e.originalTime})}}su(t,i){const s=Math.ceil(i/t);return this.tr!==null&&this.tr.eu===s||(this.tr={Ha:this.ru(s),eu:s}),this.tr.Ha}nu(t){if(t===0)return void this.J_.clear();const i=[];this.J_.forEach((s,e)=>{t<=s[0].index?i.push(e):s.splice(ft(s,t,n=>n.index<t),1/0)});for(const s of i)this.J_.delete(s)}ru(t){let i=[];for(const s of Array.from(this.J_.keys()).sort((e,n)=>n-e)){if(!this.J_.get(s))continue;const e=i;i=[];const n=e.length;let r=0;const l=k(this.J_.get(s)),o=l.length;let a=1/0,u=-1/0;for(let c=0;c<o;c++){const f=l[c],d=f.index;for(;r<n;){const m=e[r],p=m.index;if(!(p<d)){a=p;break}r++,i.push(m),u=p,a=1/0}if(a-d>=t&&d-u>=t)i.push(f),u=d;else if(this.Q_)return e}for(;r<n;r++)i.push(e[r])}return i}}class Q{constructor(t){this.hu=t}lu(){return this.hu===null?null:new nt(Math.floor(this.hu.Os()),Math.ceil(this.hu.ui()))}au(){return this.hu}static ou(){return new Q(null)}}function Ch(h,t){return h.weight>t.weight?h:t}class Eh{constructor(t,i,s,e){this.__=0,this._u=null,this.uu=[],this.Mo=null,this.wo=null,this.cu=new zh,this.du=new Map,this.fu=Q.ou(),this.vu=!0,this.pu=new M,this.mu=new M,this.bu=new M,this.wu=null,this.gu=null,this.Mu=[],this.cn=i,this.yo=s,this.xu=i.rightOffset,this.Su=i.barSpacing,this.$i=t,this.q_=e,this.ku(),this.cu.tu(i.uniformDistribution)}W(){return this.cn}yu(t){R(this.yo,t),this.Cu(),this.ku()}$h(t,i){var s;R(this.cn,t),this.cn.fixLeftEdge&&this.Tu(),this.cn.fixRightEdge&&this.Pu(),t.barSpacing!==void 0&&this.$i.Gn(t.barSpacing),t.rightOffset!==void 0&&this.$i.Jn(t.rightOffset),t.minBarSpacing!==void 0&&this.$i.Gn((s=t.barSpacing)!==null&&s!==void 0?s:this.Su),this.Cu(),this.ku(),this.bu.m()}mn(t){var i,s;return(s=(i=this.uu[t])===null||i===void 0?void 0:i.time)!==null&&s!==void 0?s:null}Ui(t){var i;return(i=this.uu[t])!==null&&i!==void 0?i:null}Va(t,i){if(this.uu.length<1)return null;if(this.q_.key(t)>this.q_.key(this.uu[this.uu.length-1].time))return i?this.uu.length-1:null;const s=ft(this.uu,this.q_.key(t),(e,n)=>this.q_.key(e.time)<n);return this.q_.key(t)<this.q_.key(this.uu[s].time)?i?s:null:s}Ni(){return this.__===0||this.uu.length===0||this._u===null}Da(){return this.uu.length>0}Xs(){return this.Ru(),this.fu.lu()}Du(){return this.Ru(),this.fu.au()}Vu(){const t=this.Xs();if(t===null)return null;const i={from:t.Os(),to:t.ui()};return this.Ou(i)}Ou(t){const i=Math.round(t.from),s=Math.round(t.to),e=v(this.Bu()),n=v(this.Au());return{from:v(this.Ui(Math.max(e,i))),to:v(this.Ui(Math.min(n,s)))}}Iu(t){return{from:v(this.Va(t.from,!0)),to:v(this.Va(t.to,!0))}}Hi(){return this.__}S_(t){if(!isFinite(t)||t<=0||this.__===t)return;const i=this.Du(),s=this.__;if(this.__=t,this.vu=!0,this.cn.lockVisibleTimeRangeOnResize&&s!==0){const e=this.Su*t/s;this.Su=e}if(this.cn.fixLeftEdge&&i!==null&&i.Os()<=0){const e=s-t;this.xu-=Math.round(e/this.Su)+1,this.vu=!0}this.zu(),this.Lu()}It(t){if(this.Ni()||!lt(t))return 0;const i=this.Eu()+this.xu-t;return this.__-(i+.5)*this.Su-1}Qs(t,i){const s=this.Eu(),e=i===void 0?0:i.from,n=i===void 0?t.length:i.to;for(let r=e;r<n;r++){const l=t[r].ot,o=s+this.xu-l,a=this.__-(o+.5)*this.Su-1;t[r].nt=a}}Nu(t){return Math.ceil(this.Fu(t))}Jn(t){this.vu=!0,this.xu=t,this.Lu(),this.$i.Wu(),this.$i.Uh()}le(){return this.Su}Gn(t){this.ju(t),this.Lu(),this.$i.Wu(),this.$i.Uh()}Hu(){return this.xu}Ha(){if(this.Ni())return null;if(this.gu!==null)return this.gu;const t=this.Su,i=5*(this.$i.W().layout.fontSize+4)/8*(this.cn.tickMarkMaxCharacterLength||8),s=Math.round(i/t),e=v(this.Xs()),n=Math.max(e.Os(),e.Os()-s),r=Math.max(e.ui(),e.ui()-s),l=this.cu.su(t,i),o=this.Bu()+s,a=this.Au()-s,u=this.$u(),c=this.cn.fixLeftEdge||u,f=this.cn.fixRightEdge||u;let d=0;for(const m of l){if(!(n<=m.index&&m.index<=r))continue;let p;d<this.Mu.length?(p=this.Mu[d],p.coord=this.It(m.index),p.label=this.Uu(m),p.weight=m.weight):(p={needAlignCoordinate:!1,coord:this.It(m.index),label:this.Uu(m),weight:m.weight},this.Mu.push(p)),this.Su>i/2&&!u?p.needAlignCoordinate=!1:p.needAlignCoordinate=c&&m.index<=o||f&&m.index>=a,d++}return this.Mu.length=d,this.gu=this.Mu,this.Mu}qu(){this.vu=!0,this.Gn(this.cn.barSpacing),this.Jn(this.cn.rightOffset)}Yu(t){this.vu=!0,this._u=t,this.Lu(),this.Tu()}Zu(t,i){const s=this.Fu(t),e=this.le(),n=e+i*(e/10);this.Gn(n),this.cn.rightBarStaysOnScroll||this.Jn(this.Hu()+(s-this.Fu(t)))}Go(t){this.Mo&&this.n_(),this.wo===null&&this.wu===null&&(this.Ni()||(this.wo=t,this.Xu()))}Jo(t){if(this.wu===null)return;const i=si(this.__-t,0,this.__),s=si(this.__-v(this.wo),0,this.__);i!==0&&s!==0&&this.Gn(this.wu.le*i/s)}Qo(){this.wo!==null&&(this.wo=null,this.Ku())}t_(t){this.Mo===null&&this.wu===null&&(this.Ni()||(this.Mo=t,this.Xu()))}i_(t){if(this.Mo===null)return;const i=(this.Mo-t)/this.le();this.xu=v(this.wu).Hu+i,this.vu=!0,this.Lu()}n_(){this.Mo!==null&&(this.Mo=null,this.Ku())}Gu(){this.Ju(this.cn.rightOffset)}Ju(t,i=400){if(!isFinite(t))throw new RangeError("offset is required and must be finite number");if(!isFinite(i)||i<=0)throw new RangeError("animationDuration (optional) must be finite positive number");const s=this.xu,e=performance.now();this.$i.Zn({Qu:n=>(n-e)/i>=1,tc:n=>{const r=(n-e)/i;return r>=1?t:s+(t-s)*r}})}bt(t,i){this.vu=!0,this.uu=t,this.cu.iu(t,i),this.Lu()}nc(){return this.pu}sc(){return this.mu}ec(){return this.bu}Eu(){return this._u||0}rc(t){const i=t.G_();this.ju(this.__/i),this.xu=t.ui()-this.Eu(),this.Lu(),this.vu=!0,this.$i.Wu(),this.$i.Uh()}hc(){const t=this.Bu(),i=this.Au();t!==null&&i!==null&&this.rc(new nt(t,i+this.cn.rightOffset))}lc(t){const i=new nt(t.from,t.to);this.rc(i)}qi(t){return this.yo.timeFormatter!==void 0?this.yo.timeFormatter(t.originalTime):this.q_.formatHorzItem(t.time)}$u(){const{handleScroll:t,handleScale:i}=this.$i.W();return!(t.horzTouchDrag||t.mouseWheel||t.pressedMouseMove||t.vertTouchDrag||i.axisDoubleClickReset.time||i.axisPressedMouseMove.time||i.mouseWheel||i.pinch)}Bu(){return this.uu.length===0?null:0}Au(){return this.uu.length===0?null:this.uu.length-1}ac(t){return(this.__-1-t)/this.Su}Fu(t){const i=this.ac(t),s=this.Eu()+this.xu-i;return Math.round(1e6*s)/1e6}ju(t){const i=this.Su;this.Su=t,this.zu(),i!==this.Su&&(this.vu=!0,this.oc())}Ru(){if(!this.vu)return;if(this.vu=!1,this.Ni())return void this._c(Q.ou());const t=this.Eu(),i=this.__/this.Su,s=this.xu+t,e=new nt(s-i+1,s);this._c(new Q(e))}zu(){const t=this.uc();if(this.Su<t&&(this.Su=t,this.vu=!0),this.__!==0){const i=.5*this.__;this.Su>i&&(this.Su=i,this.vu=!0)}}uc(){return this.cn.fixLeftEdge&&this.cn.fixRightEdge&&this.uu.length!==0?this.__/this.uu.length:this.cn.minBarSpacing}Lu(){const t=this.cc();t!==null&&this.xu<t&&(this.xu=t,this.vu=!0);const i=this.dc();this.xu>i&&(this.xu=i,this.vu=!0)}cc(){const t=this.Bu(),i=this._u;return t===null||i===null?null:t-i-1+(this.cn.fixLeftEdge?this.__/this.Su:Math.min(2,this.uu.length))}dc(){return this.cn.fixRightEdge?0:this.__/this.Su-Math.min(2,this.uu.length)}Xu(){this.wu={le:this.le(),Hu:this.Hu()}}Ku(){this.wu=null}Uu(t){let i=this.du.get(t.weight);return i===void 0&&(i=new xh(s=>this.fc(s),this.q_),this.du.set(t.weight,i)),i.Y_(t)}fc(t){return this.q_.formatTickmark(t,this.yo)}_c(t){const i=this.fu;this.fu=t,Ai(i.lu(),this.fu.lu())||this.pu.m(),Ai(i.au(),this.fu.au())||this.mu.m(),this.oc()}oc(){this.gu=null}Cu(){this.oc(),this.du.clear()}ku(){this.q_.updateFormatter(this.yo)}Tu(){if(!this.cn.fixLeftEdge)return;const t=this.Bu();if(t===null)return;const i=this.Xs();if(i===null)return;const s=i.Os()-t;if(s<0){const e=this.xu-s-1;this.Jn(e)}this.zu()}Pu(){this.Lu(),this.zu()}}class kh{X(t,i,s){t.useMediaCoordinateSpace(e=>this.K(e,i,s))}gl(t,i,s){t.useMediaCoordinateSpace(e=>this.vc(e,i,s))}vc(t,i,s){}}class Lh extends kh{constructor(t){super(),this.mc=new Map,this.zt=t}K(t){}vc(t){if(!this.zt.yt)return;const{context:i,mediaSize:s}=t;let e=0;for(const r of this.zt.bc){if(r.Kt.length===0)continue;i.font=r.R;const l=this.wc(i,r.Kt);l>s.width?r.Zu=s.width/l:r.Zu=1,e+=r.gc*r.Zu}let n=0;switch(this.zt.Mc){case"top":n=0;break;case"center":n=Math.max((s.height-e)/2,0);break;case"bottom":n=Math.max(s.height-e,0)}i.fillStyle=this.zt.V;for(const r of this.zt.bc){i.save();let l=0;switch(this.zt.xc){case"left":i.textAlign="left",l=r.gc/2;break;case"center":i.textAlign="center",l=s.width/2;break;case"right":i.textAlign="right",l=s.width-1-r.gc/2}i.translate(l,n),i.textBaseline="top",i.font=r.R,i.scale(r.Zu,r.Zu),i.fillText(r.Kt,0,r.Sc),i.restore(),n+=r.gc*r.Zu}}wc(t,i){const s=this.kc(t.font);let e=s.get(i);return e===void 0&&(e=t.measureText(i).width,s.set(i,e)),e}kc(t){let i=this.mc.get(t);return i===void 0&&(i=new Map,this.mc.set(t,i)),i}}class Vh{constructor(t){this.ft=!0,this.Ft={yt:!1,V:"",bc:[],Mc:"center",xc:"center"},this.Wt=new Lh(this.Ft),this.jt=t}bt(){this.ft=!0}gt(){return this.ft&&(this.Mt(),this.ft=!1),this.Wt}Mt(){const t=this.jt.W(),i=this.Ft;i.yt=t.visible,i.yt&&(i.V=t.color,i.xc=t.horzAlign,i.Mc=t.vertAlign,i.bc=[{Kt:t.text,R:Y(t.fontSize,t.fontFamily,t.fontStyle),gc:1.2*t.fontSize,Sc:0,Zu:0}])}}class Nh extends ui{constructor(t,i){super(),this.cn=i,this.wn=new Vh(this)}Rn(){return[]}Pn(){return[this.wn]}W(){return this.cn}Vn(){this.wn.bt()}}var Ui,qi,Ji,Gi,Qi;(function(h){h[h.OnTouchEnd=0]="OnTouchEnd",h[h.OnNextTap=1]="OnNextTap"})(Ui||(Ui={}));class Rh{constructor(t,i,s){this.yc=[],this.Cc=[],this.__=0,this.Tc=null,this.Pc=new M,this.Rc=new M,this.Dc=null,this.Vc=t,this.cn=i,this.q_=s,this.Oc=new ue(this),this.yl=new Eh(this,i.timeScale,this.cn.localization,s),this.vt=new Se(this,i.crosshair),this.Bc=new vh(i.crosshair),this.Ac=new Nh(this,i.watermark),this.Ic(),this.yc[0].x_(2e3),this.zc=this.Lc(0),this.Ec=this.Lc(1)}Kl(){this.Nc(z.es())}Uh(){this.Nc(z.ss())}oa(){this.Nc(new z(1))}Gl(t){const i=this.Fc(t);this.Nc(i)}Wc(){return this.Tc}jc(t){const i=this.Tc;this.Tc=t,i!==null&&this.Gl(i.Hc),t!==null&&this.Gl(t.Hc)}W(){return this.cn}$h(t){R(this.cn,t),this.yc.forEach(i=>i.b_(t)),t.timeScale!==void 0&&this.yl.$h(t.timeScale),t.localization!==void 0&&this.yl.yu(t.localization),(t.leftPriceScale||t.rightPriceScale)&&this.Pc.m(),this.zc=this.Lc(0),this.Ec=this.Lc(1),this.Kl()}$c(t,i){if(t==="left")return void this.$h({leftPriceScale:i});if(t==="right")return void this.$h({rightPriceScale:i});const s=this.Uc(t);s!==null&&(s.Dt.$h(i),this.Pc.m())}Uc(t){for(const i of this.yc){const s=i.w_(t);if(s!==null)return{Ht:i,Dt:s}}return null}St(){return this.yl}qc(){return this.yc}Yc(){return this.Ac}Zc(){return this.vt}Xc(){return this.Rc}Kc(t,i){t.Lo(i),this.Wu()}S_(t){this.__=t,this.yl.S_(this.__),this.yc.forEach(i=>i.S_(t)),this.Wu()}Ic(t){const i=new Mh(this.yl,this);t!==void 0?this.yc.splice(t,0,i):this.yc.push(i);const s=t===void 0?this.yc.length-1:t,e=z.es();return e.Nn(s,{Fn:0,Wn:!0}),this.Nc(e),i}V_(t,i,s){t.V_(i,s)}O_(t,i,s){t.O_(i,s),this.Jl(),this.Nc(this.Gc(t,2))}B_(t,i){t.B_(i),this.Nc(this.Gc(t,2))}A_(t,i,s){i.Vo()||t.A_(i,s)}I_(t,i,s){i.Vo()||(t.I_(i,s),this.Jl(),this.Nc(this.Gc(t,2)))}z_(t,i){i.Vo()||(t.z_(i),this.Nc(this.Gc(t,2)))}E_(t,i){t.E_(i),this.Nc(this.Gc(t,2))}Jc(t){this.yl.Go(t)}Qc(t,i){const s=this.St();if(s.Ni()||i===0)return;const e=s.Hi();t=Math.max(1,Math.min(t,e)),s.Zu(t,i),this.Wu()}td(t){this.nd(0),this.sd(t),this.ed()}rd(t){this.yl.Jo(t),this.Wu()}hd(){this.yl.Qo(),this.Uh()}nd(t){this.yl.t_(t)}sd(t){this.yl.i_(t),this.Wu()}ed(){this.yl.n_(),this.Uh()}wt(){return this.Cc}ld(t,i,s,e,n){this.vt.gn(t,i);let r=NaN,l=this.yl.Nu(t);const o=this.yl.Xs();o!==null&&(l=Math.min(Math.max(o.Os(),l),o.ui()));const a=e.vn(),u=a.Ct();u!==null&&(r=a.pn(i,u)),r=this.Bc.Oa(r,l,e),this.vt.kn(l,r,e),this.oa(),n||this.Rc.m(this.vt.xt(),{x:t,y:i},s)}ad(t,i,s){const e=s.vn(),n=e.Ct(),r=e.Rt(t,v(n)),l=this.yl.Va(i,!0),o=this.yl.It(v(l));this.ld(o,r,null,s,!0)}od(t){this.Zc().Cn(),this.oa(),t||this.Rc.m(null,null,null)}Jl(){const t=this.vt.Ht();if(t!==null){const i=this.vt.xn(),s=this.vt.Sn();this.ld(i,s,null,t)}this.vt.Vn()}_d(t,i,s){const e=this.yl.mn(0);i!==void 0&&s!==void 0&&this.yl.bt(i,s);const n=this.yl.mn(0),r=this.yl.Eu(),l=this.yl.Xs();if(l!==null&&e!==null&&n!==null){const o=l.Kr(r),a=this.q_.key(e)>this.q_.key(n),u=t!==null&&t>r&&!a,c=this.yl.W().allowShiftVisibleRangeOnWhitespaceReplacement,f=o&&(s!==void 0||c)&&this.yl.W().shiftVisibleRangeOnNewBar;if(u&&!f){const d=t-r;this.yl.Jn(this.yl.Hu()-d)}}this.yl.Yu(t)}ia(t){t!==null&&t.F_()}dr(t){const i=this.yc.find(s=>s.Uo().includes(t));return i===void 0?null:i}Wu(){this.Ac.Vn(),this.yc.forEach(t=>t.F_()),this.Jl()}S(){this.yc.forEach(t=>t.S()),this.yc.length=0,this.cn.localization.priceFormatter=void 0,this.cn.localization.percentageFormatter=void 0,this.cn.localization.timeFormatter=void 0}ud(){return this.Oc}br(){return this.Oc.W()}g_(){return this.Pc}dd(t,i,s){const e=this.yc[0],n=this.fd(i,t,e,s);return this.Cc.push(n),this.Cc.length===1?this.Kl():this.Uh(),n}vd(t){const i=this.dr(t),s=this.Cc.indexOf(t);I(s!==-1,"Series not found"),this.Cc.splice(s,1),v(i).Zo(t),t.S&&t.S()}Xl(t,i){const s=v(this.dr(t));s.Zo(t);const e=this.Uc(i);if(e===null){const n=t.Xi();s.qo(t,i,n)}else{const n=e.Ht===s?t.Xi():void 0;e.Ht.qo(t,i,n)}}hc(){const t=z.ss();t.$n(),this.Nc(t)}pd(t){const i=z.ss();i.Yn(t),this.Nc(i)}Kn(){const t=z.ss();t.Kn(),this.Nc(t)}Gn(t){const i=z.ss();i.Gn(t),this.Nc(i)}Jn(t){const i=z.ss();i.Jn(t),this.Nc(i)}Zn(t){const i=z.ss();i.Zn(t),this.Nc(i)}Un(){const t=z.ss();t.Un(),this.Nc(t)}md(){return this.cn.rightPriceScale.visible?"right":"left"}bd(){return this.Ec}q(){return this.zc}Bt(t){const i=this.Ec,s=this.zc;if(i===s)return i;if(t=Math.max(0,Math.min(100,Math.round(100*t))),this.Dc===null||this.Dc.Ps!==s||this.Dc.Rs!==i)this.Dc={Ps:s,Rs:i,wd:new Map};else{const n=this.Dc.wd.get(t);if(n!==void 0)return n}const e=function(n,r,l){const[o,a,u,c]=rt(n),[f,d,m,p]=rt(r),b=[N(o+l*(f-o)),N(a+l*(d-a)),N(u+l*(m-u)),_s(c+l*(p-c))];return`rgba(${b[0]}, ${b[1]}, ${b[2]}, ${b[3]})`}(s,i,t/100);return this.Dc.wd.set(t,e),e}Gc(t,i){const s=new z(i);if(t!==null){const e=this.yc.indexOf(t);s.Nn(e,{Fn:i})}return s}Fc(t,i){return i===void 0&&(i=2),this.Gc(this.dr(t),i)}Nc(t){this.Vc&&this.Vc(t),this.yc.forEach(i=>i.j_().qh().bt())}fd(t,i,s,e){const n=new mi(this,t,i,s,e),r=t.priceScaleId!==void 0?t.priceScaleId:this.md();return s.qo(n,r),Vt(r)||n.$h(t),n}Lc(t){const i=this.cn.layout;return i.background.type==="gradient"?t===0?i.background.topColor:i.background.bottomColor:i.background.color}}function ei(h){return!P(h)&&!ct(h)}function Ks(h){return P(h)}(function(h){h[h.Disabled=0]="Disabled",h[h.Continuous=1]="Continuous",h[h.OnDataUpdate=2]="OnDataUpdate"})(qi||(qi={})),function(h){h[h.LastBar=0]="LastBar",h[h.LastVisible=1]="LastVisible"}(Ji||(Ji={})),function(h){h.Solid="solid",h.VerticalGradient="gradient"}(Gi||(Gi={})),function(h){h[h.Year=0]="Year",h[h.Month=1]="Month",h[h.DayOfMonth=2]="DayOfMonth",h[h.Time=3]="Time",h[h.TimeWithSeconds=4]="TimeWithSeconds"}(Qi||(Qi={}));const Yi=h=>h.getUTCFullYear();function Th(h,t,i){return t.replace(/yyyy/g,(s=>O(Yi(s),4))(h)).replace(/yy/g,(s=>O(Yi(s)%100,2))(h)).replace(/MMMM/g,((s,e)=>new Date(s.getUTCFullYear(),s.getUTCMonth(),1).toLocaleString(e,{month:"long"}))(h,i)).replace(/MMM/g,((s,e)=>new Date(s.getUTCFullYear(),s.getUTCMonth(),1).toLocaleString(e,{month:"short"}))(h,i)).replace(/MM/g,(s=>O((e=>e.getUTCMonth()+1)(s),2))(h)).replace(/dd/g,(s=>O((e=>e.getUTCDate())(s),2))(h))}class js{constructor(t="yyyy-MM-dd",i="default"){this.gd=t,this.Md=i}Y_(t){return Th(t,this.gd,this.Md)}}class $h{constructor(t){this.xd=t||"%h:%m:%s"}Y_(t){return this.xd.replace("%h",O(t.getUTCHours(),2)).replace("%m",O(t.getUTCMinutes(),2)).replace("%s",O(t.getUTCSeconds(),2))}}const Ph={Sd:"yyyy-MM-dd",kd:"%h:%m:%s",yd:" ",Cd:"default"};class Wh{constructor(t={}){const i=Object.assign(Object.assign({},Ph),t);this.Td=new js(i.Sd,i.Cd),this.Pd=new $h(i.kd),this.Rd=i.yd}Y_(t){return`${this.Td.Y_(t)}${this.Rd}${this.Pd.Y_(t)}`}}function gt(h){return 60*h*60*1e3}function Ht(h){return 60*h*1e3}const wt=[{Dd:(Zi=1,1e3*Zi),Vd:10},{Dd:Ht(1),Vd:20},{Dd:Ht(5),Vd:21},{Dd:Ht(30),Vd:22},{Dd:gt(1),Vd:30},{Dd:gt(3),Vd:31},{Dd:gt(6),Vd:32},{Dd:gt(12),Vd:33}];var Zi;function ts(h,t){if(h.getUTCFullYear()!==t.getUTCFullYear())return 70;if(h.getUTCMonth()!==t.getUTCMonth())return 60;if(h.getUTCDate()!==t.getUTCDate())return 50;for(let i=wt.length-1;i>=0;--i)if(Math.floor(t.getTime()/wt[i].Dd)!==Math.floor(h.getTime()/wt[i].Dd))return wt[i].Vd;return 0}function At(h){let t=h;if(ct(h)&&(t=pi(h)),!ei(t))throw new Error("time must be of type BusinessDay");const i=new Date(Date.UTC(t.year,t.month-1,t.day,0,0,0,0));return{Od:Math.round(i.getTime()/1e3),Bd:t}}function is(h){if(!Ks(h))throw new Error("time must be of type isUTCTimestamp");return{Od:h}}function pi(h){const t=new Date(h);if(isNaN(t.getTime()))throw new Error(`Invalid date string=${h}, expected format=yyyy-mm-dd`);return{day:t.getUTCDate(),month:t.getUTCMonth()+1,year:t.getUTCFullYear()}}function ss(h){ct(h.time)&&(h.time=pi(h.time))}class es{options(){return this.cn}setOptions(t){this.cn=t,this.updateFormatter(t.localization)}preprocessData(t){Array.isArray(t)?function(i){i.forEach(ss)}(t):ss(t)}createConverterToInternalObj(t){return v(function(i){return i.length===0?null:ei(i[0].time)||ct(i[0].time)?At:is}(t))}key(t){return typeof t=="object"&&"Od"in t?t.Od:this.key(this.convertHorzItemToInternal(t))}cacheKey(t){const i=t;return i.Bd===void 0?new Date(1e3*i.Od).getTime():new Date(Date.UTC(i.Bd.year,i.Bd.month-1,i.Bd.day)).getTime()}convertHorzItemToInternal(t){return Ks(i=t)?is(i):ei(i)?At(i):At(pi(i));var i}updateFormatter(t){if(!this.cn)return;const i=t.dateFormat;this.cn.timeScale.timeVisible?this.Ad=new Wh({Sd:i,kd:this.cn.timeScale.secondsVisible?"%h:%m:%s":"%h:%m",yd:"   ",Cd:t.locale}):this.Ad=new js(i,t.locale)}formatHorzItem(t){const i=t;return this.Ad.Y_(new Date(1e3*i.Od))}formatTickmark(t,i){const s=function(n,r,l){switch(n){case 0:case 10:return r?l?4:3:2;case 20:case 21:case 22:case 30:case 31:case 32:case 33:return r?3:2;case 50:return 2;case 60:return 1;case 70:return 0}}(t.weight,this.cn.timeScale.timeVisible,this.cn.timeScale.secondsVisible),e=this.cn.timeScale;if(e.tickMarkFormatter!==void 0){const n=e.tickMarkFormatter(t.originalTime,s,i.locale);if(n!==null)return n}return function(n,r,l){const o={};switch(r){case 0:o.year="numeric";break;case 1:o.month="short";break;case 2:o.day="numeric";break;case 3:o.hour12=!1,o.hour="2-digit",o.minute="2-digit";break;case 4:o.hour12=!1,o.hour="2-digit",o.minute="2-digit",o.second="2-digit"}const a=n.Bd===void 0?new Date(1e3*n.Od):new Date(Date.UTC(n.Bd.year,n.Bd.month-1,n.Bd.day));return new Date(a.getUTCFullYear(),a.getUTCMonth(),a.getUTCDate(),a.getUTCHours(),a.getUTCMinutes(),a.getUTCSeconds(),a.getUTCMilliseconds()).toLocaleString(l,o)}(t.time,s,i.locale)}maxTickMarkWeight(t){let i=t.reduce(Ch,t[0]).weight;return i>30&&i<50&&(i=30),i}fillWeightsForPoints(t,i){(function(s,e=0){if(s.length===0)return;let n=e===0?null:s[e-1].time.Od,r=n!==null?new Date(1e3*n):null,l=0;for(let o=e;o<s.length;++o){const a=s[o],u=new Date(1e3*a.time.Od);r!==null&&(a.timeWeight=ts(u,r)),l+=a.time.Od-(n||a.time.Od),n=a.time.Od,r=u}if(e===0&&s.length>1){const o=Math.ceil(l/(s.length-1)),a=new Date(1e3*(s[0].time.Od-o));s[0].timeWeight=ts(new Date(1e3*s[0].time.Od),a)}})(t,i)}static Id(t){return R({localization:{dateFormat:"dd MMM 'yy"}},t??{})}}const Z=typeof window<"u";function hs(){return!!Z&&window.navigator.userAgent.toLowerCase().indexOf("firefox")>-1}function Ut(){return!!Z&&/iPhone|iPad|iPod/.test(window.navigator.platform)}function hi(h){return h+h%2}function qt(h,t){return h.zd-t.zd}function Jt(h,t,i){const s=(h.zd-t.zd)/(h.ot-t.ot);return Math.sign(s)*Math.min(Math.abs(s),i)}class Oh{constructor(t,i,s,e){this.Ld=null,this.Ed=null,this.Nd=null,this.Fd=null,this.Wd=null,this.jd=0,this.Hd=0,this.$d=t,this.Ud=i,this.qd=s,this.rs=e}Yd(t,i){if(this.Ld!==null){if(this.Ld.ot===i)return void(this.Ld.zd=t);if(Math.abs(this.Ld.zd-t)<this.rs)return}this.Fd=this.Nd,this.Nd=this.Ed,this.Ed=this.Ld,this.Ld={ot:i,zd:t}}Vr(t,i){if(this.Ld===null||this.Ed===null||i-this.Ld.ot>50)return;let s=0;const e=Jt(this.Ld,this.Ed,this.Ud),n=qt(this.Ld,this.Ed),r=[e],l=[n];if(s+=n,this.Nd!==null){const a=Jt(this.Ed,this.Nd,this.Ud);if(Math.sign(a)===Math.sign(e)){const u=qt(this.Ed,this.Nd);if(r.push(a),l.push(u),s+=u,this.Fd!==null){const c=Jt(this.Nd,this.Fd,this.Ud);if(Math.sign(c)===Math.sign(e)){const f=qt(this.Nd,this.Fd);r.push(c),l.push(f),s+=f}}}}let o=0;for(let a=0;a<r.length;++a)o+=l[a]/s*r[a];Math.abs(o)<this.$d||(this.Wd={zd:t,ot:i},this.Hd=o,this.jd=function(a,u){const c=Math.log(u);return Math.log(1*c/-a)/c}(Math.abs(o),this.qd))}tc(t){const i=v(this.Wd),s=t-i.ot;return i.zd+this.Hd*(Math.pow(this.qd,s)-1)/Math.log(this.qd)}Qu(t){return this.Wd===null||this.Zd(t)===this.jd}Zd(t){const i=t-v(this.Wd).ot;return Math.min(i,this.jd)}}class Dh{constructor(t,i){this.Xd=void 0,this.Kd=void 0,this.Gd=void 0,this.en=!1,this.Jd=t,this.Qd=i,this.tf()}bt(){this.tf()}if(){this.Xd&&this.Jd.removeChild(this.Xd),this.Kd&&this.Jd.removeChild(this.Kd),this.Xd=void 0,this.Kd=void 0}nf(){return this.en!==this.sf()||this.Gd!==this.ef()}ef(){return Ms(rt(this.Qd.W().layout.textColor))>160?"dark":"light"}sf(){return this.Qd.W().layout.attributionLogo}rf(){const t=new URL(location.href);return t.hostname?"&utm_source="+t.hostname+t.pathname:""}tf(){this.nf()&&(this.if(),this.en=this.sf(),this.en&&(this.Gd=this.ef(),this.Kd=document.createElement("style"),this.Kd.innerText="a#tv-attr-logo{--fill:#131722;--stroke:#fff;position:absolute;left:10px;bottom:10px;height:19px;width:35px;margin:0;padding:0;border:0;z-index:3;}a#tv-attr-logo[data-dark]{--fill:#D1D4DC;--stroke:#131722;}",this.Xd=document.createElement("a"),this.Xd.href=`https://www.tradingview.com/?utm_medium=lwc-link&utm_campaign=lwc-chart${this.rf()}`,this.Xd.title="Charting by TradingView",this.Xd.id="tv-attr-logo",this.Xd.target="_blank",this.Xd.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 35 19" width="35" height="19" fill="none"><g fill-rule="evenodd" clip-path="url(#a)" clip-rule="evenodd"><path fill="var(--stroke)" d="M2 0H0v10h6v9h21.4l.5-1.3 6-15 1-2.7H23.7l-.5 1.3-.2.6a5 5 0 0 0-7-.9V0H2Zm20 17h4l5.2-13 .8-2h-7l-1 2.5-.2.5-1.5 3.8-.3.7V17Zm-.8-10a3 3 0 0 0 .7-2.7A3 3 0 1 0 16.8 7h4.4ZM14 7V2H2v6h6v9h4V7h2Z"/><path fill="var(--fill)" d="M14 2H2v6h6v9h6V2Zm12 15h-7l6-15h7l-6 15Zm-7-9a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></g><defs><clipPath id="a"><path fill="var(--stroke)" d="M0 0h35v19H0z"/></clipPath></defs></svg>',this.Xd.toggleAttribute("data-dark",this.Gd==="dark"),this.Jd.appendChild(this.Kd),this.Jd.appendChild(this.Xd)))}}function H(h,t){const i=v(h.ownerDocument).createElement("canvas");h.appendChild(i);const s=Js(i,{options:{allowResizeObserver:!1},transform:(e,n)=>({width:Math.max(e.width,n.width),height:Math.max(e.height,n.height)})});return s.resizeCanvasElement(t),s}function A(h){var t;h.width=1,h.height=1,(t=h.getContext("2d"))===null||t===void 0||t.clearRect(0,0,1,1)}function ni(h,t,i,s){h.gl&&h.gl(t,i,s)}function xt(h,t,i,s){h.X(t,i,s)}function ri(h,t,i,s){const e=h(i,s);for(const n of e){const r=n.gt();r!==null&&t(r)}}function Bh(h){Z&&window.chrome!==void 0&&h.addEventListener("mousedown",t=>{if(t.button===1)return t.preventDefault(),!1})}class vi{constructor(t,i,s){this.hf=0,this.lf=null,this.af={nt:Number.NEGATIVE_INFINITY,st:Number.POSITIVE_INFINITY},this._f=0,this.uf=null,this.cf={nt:Number.NEGATIVE_INFINITY,st:Number.POSITIVE_INFINITY},this.df=null,this.ff=!1,this.vf=null,this.pf=null,this.mf=!1,this.bf=!1,this.wf=!1,this.gf=null,this.Mf=null,this.xf=null,this.Sf=null,this.kf=null,this.yf=null,this.Cf=null,this.Tf=0,this.Pf=!1,this.Rf=!1,this.Df=!1,this.Vf=0,this.Of=null,this.Bf=!Ut(),this.Af=e=>{this.If(e)},this.zf=e=>{if(this.Lf(e)){const n=this.Ef(e);if(++this._f,this.uf&&this._f>1){const{Nf:r}=this.Ff($(e),this.cf);r<30&&!this.wf&&this.Wf(n,this.Hf.jf),this.$f()}}else{const n=this.Ef(e);if(++this.hf,this.lf&&this.hf>1){const{Nf:r}=this.Ff($(e),this.af);r<5&&!this.bf&&this.Uf(n,this.Hf.qf),this.Yf()}}},this.Zf=t,this.Hf=i,this.cn=s,this.Xf()}S(){this.gf!==null&&(this.gf(),this.gf=null),this.Mf!==null&&(this.Mf(),this.Mf=null),this.Sf!==null&&(this.Sf(),this.Sf=null),this.kf!==null&&(this.kf(),this.kf=null),this.yf!==null&&(this.yf(),this.yf=null),this.xf!==null&&(this.xf(),this.xf=null),this.Kf(),this.Yf()}Gf(t){this.Sf&&this.Sf();const i=this.Jf.bind(this);if(this.Sf=()=>{this.Zf.removeEventListener("mousemove",i)},this.Zf.addEventListener("mousemove",i),this.Lf(t))return;const s=this.Ef(t);this.Uf(s,this.Hf.Qf),this.Bf=!0}Yf(){this.lf!==null&&clearTimeout(this.lf),this.hf=0,this.lf=null,this.af={nt:Number.NEGATIVE_INFINITY,st:Number.POSITIVE_INFINITY}}$f(){this.uf!==null&&clearTimeout(this.uf),this._f=0,this.uf=null,this.cf={nt:Number.NEGATIVE_INFINITY,st:Number.POSITIVE_INFINITY}}Jf(t){if(this.Df||this.pf!==null||this.Lf(t))return;const i=this.Ef(t);this.Uf(i,this.Hf.tv),this.Bf=!0}iv(t){const i=Gt(t.changedTouches,v(this.Of));if(i===null||(this.Vf=yt(t),this.Cf!==null)||this.Rf)return;this.Pf=!0;const s=this.Ff($(i),v(this.pf)),{nv:e,sv:n,Nf:r}=s;if(this.mf||!(r<5)){if(!this.mf){const l=.5*e,o=n>=l&&!this.cn.ev(),a=l>n&&!this.cn.rv();o||a||(this.Rf=!0),this.mf=!0,this.wf=!0,this.Kf(),this.$f()}if(!this.Rf){const l=this.Ef(t,i);this.Wf(l,this.Hf.hv),q(t)}}}lv(t){if(t.button!==0)return;const i=this.Ff($(t),v(this.vf)),{Nf:s}=i;if(s>=5&&(this.bf=!0,this.Yf()),this.bf){const e=this.Ef(t);this.Uf(e,this.Hf.av)}}Ff(t,i){const s=Math.abs(i.nt-t.nt),e=Math.abs(i.st-t.st);return{nv:s,sv:e,Nf:s+e}}ov(t){let i=Gt(t.changedTouches,v(this.Of));if(i===null&&t.touches.length===0&&(i=t.changedTouches[0]),i===null)return;this.Of=null,this.Vf=yt(t),this.Kf(),this.pf=null,this.yf&&(this.yf(),this.yf=null);const s=this.Ef(t,i);if(this.Wf(s,this.Hf._v),++this._f,this.uf&&this._f>1){const{Nf:e}=this.Ff($(i),this.cf);e<30&&!this.wf&&this.Wf(s,this.Hf.jf),this.$f()}else this.wf||(this.Wf(s,this.Hf.uv),this.Hf.uv&&q(t));this._f===0&&q(t),t.touches.length===0&&this.ff&&(this.ff=!1,q(t))}If(t){if(t.button!==0)return;const i=this.Ef(t);if(this.vf=null,this.Df=!1,this.kf&&(this.kf(),this.kf=null),hs()&&this.Zf.ownerDocument.documentElement.removeEventListener("mouseleave",this.Af),!this.Lf(t))if(this.Uf(i,this.Hf.cv),++this.hf,this.lf&&this.hf>1){const{Nf:s}=this.Ff($(t),this.af);s<5&&!this.bf&&this.Uf(i,this.Hf.qf),this.Yf()}else this.bf||this.Uf(i,this.Hf.dv)}Kf(){this.df!==null&&(clearTimeout(this.df),this.df=null)}fv(t){if(this.Of!==null)return;const i=t.changedTouches[0];this.Of=i.identifier,this.Vf=yt(t);const s=this.Zf.ownerDocument.documentElement;this.wf=!1,this.mf=!1,this.Rf=!1,this.pf=$(i),this.yf&&(this.yf(),this.yf=null);{const n=this.iv.bind(this),r=this.ov.bind(this);this.yf=()=>{s.removeEventListener("touchmove",n),s.removeEventListener("touchend",r)},s.addEventListener("touchmove",n,{passive:!1}),s.addEventListener("touchend",r,{passive:!1}),this.Kf(),this.df=setTimeout(this.vv.bind(this,t),240)}const e=this.Ef(t,i);this.Wf(e,this.Hf.pv),this.uf||(this._f=0,this.uf=setTimeout(this.$f.bind(this),500),this.cf=$(i))}mv(t){if(t.button!==0)return;const i=this.Zf.ownerDocument.documentElement;hs()&&i.addEventListener("mouseleave",this.Af),this.bf=!1,this.vf=$(t),this.kf&&(this.kf(),this.kf=null);{const e=this.lv.bind(this),n=this.If.bind(this);this.kf=()=>{i.removeEventListener("mousemove",e),i.removeEventListener("mouseup",n)},i.addEventListener("mousemove",e),i.addEventListener("mouseup",n)}if(this.Df=!0,this.Lf(t))return;const s=this.Ef(t);this.Uf(s,this.Hf.bv),this.lf||(this.hf=0,this.lf=setTimeout(this.Yf.bind(this),500),this.af=$(t))}Xf(){this.Zf.addEventListener("mouseenter",this.Gf.bind(this)),this.Zf.addEventListener("touchcancel",this.Kf.bind(this));{const t=this.Zf.ownerDocument,i=s=>{this.Hf.wv&&(s.composed&&this.Zf.contains(s.composedPath()[0])||s.target&&this.Zf.contains(s.target)||this.Hf.wv())};this.Mf=()=>{t.removeEventListener("touchstart",i)},this.gf=()=>{t.removeEventListener("mousedown",i)},t.addEventListener("mousedown",i),t.addEventListener("touchstart",i,{passive:!0})}Ut()&&(this.xf=()=>{this.Zf.removeEventListener("dblclick",this.zf)},this.Zf.addEventListener("dblclick",this.zf)),this.Zf.addEventListener("mouseleave",this.gv.bind(this)),this.Zf.addEventListener("touchstart",this.fv.bind(this),{passive:!0}),Bh(this.Zf),this.Zf.addEventListener("mousedown",this.mv.bind(this)),this.Mv(),this.Zf.addEventListener("touchmove",()=>{},{passive:!1})}Mv(){this.Hf.xv===void 0&&this.Hf.Sv===void 0&&this.Hf.kv===void 0||(this.Zf.addEventListener("touchstart",t=>this.yv(t.touches),{passive:!0}),this.Zf.addEventListener("touchmove",t=>{if(t.touches.length===2&&this.Cf!==null&&this.Hf.Sv!==void 0){const i=ns(t.touches[0],t.touches[1])/this.Tf;this.Hf.Sv(this.Cf,i),q(t)}},{passive:!1}),this.Zf.addEventListener("touchend",t=>{this.yv(t.touches)}))}yv(t){t.length===1&&(this.Pf=!1),t.length!==2||this.Pf||this.ff?this.Cv():this.Tv(t)}Tv(t){const i=this.Zf.getBoundingClientRect()||{left:0,top:0};this.Cf={nt:(t[0].clientX-i.left+(t[1].clientX-i.left))/2,st:(t[0].clientY-i.top+(t[1].clientY-i.top))/2},this.Tf=ns(t[0],t[1]),this.Hf.xv!==void 0&&this.Hf.xv(),this.Kf()}Cv(){this.Cf!==null&&(this.Cf=null,this.Hf.kv!==void 0&&this.Hf.kv())}gv(t){if(this.Sf&&this.Sf(),this.Lf(t)||!this.Bf)return;const i=this.Ef(t);this.Uf(i,this.Hf.Pv),this.Bf=!Ut()}vv(t){const i=Gt(t.touches,v(this.Of));if(i===null)return;const s=this.Ef(t,i);this.Wf(s,this.Hf.Rv),this.wf=!0,this.ff=!0}Lf(t){return t.sourceCapabilities&&t.sourceCapabilities.firesTouchEvents!==void 0?t.sourceCapabilities.firesTouchEvents:yt(t)<this.Vf+500}Wf(t,i){i&&i.call(this.Hf,t)}Uf(t,i){i&&i.call(this.Hf,t)}Ef(t,i){const s=i||t,e=this.Zf.getBoundingClientRect()||{left:0,top:0};return{clientX:s.clientX,clientY:s.clientY,pageX:s.pageX,pageY:s.pageY,screenX:s.screenX,screenY:s.screenY,localX:s.clientX-e.left,localY:s.clientY-e.top,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey,metaKey:t.metaKey,Dv:!t.type.startsWith("mouse")&&t.type!=="contextmenu"&&t.type!=="click",Vv:t.type,Ov:s.target,Bv:t.view,Av:()=>{t.type!=="touchstart"&&q(t)}}}}function ns(h,t){const i=h.clientX-t.clientX,s=h.clientY-t.clientY;return Math.sqrt(i*i+s*s)}function q(h){h.cancelable&&h.preventDefault()}function $(h){return{nt:h.pageX,st:h.pageY}}function yt(h){return h.timeStamp||performance.now()}function Gt(h,t){for(let i=0;i<h.length;++i)if(h[i].identifier===t)return h[i];return null}function St(h){return{Hc:h.Hc,Iv:{gr:h.zv.externalId},Lv:h.zv.cursorStyle}}function Ih(h,t,i){for(const s of h){const e=s.gt();if(e!==null&&e.wr){const n=e.wr(t,i);if(n!==null)return{Bv:s,Iv:n}}}return null}function Qt(h,t){return i=>{var s,e,n,r;return((e=(s=i.Dt())===null||s===void 0?void 0:s.Pa())!==null&&e!==void 0?e:"")!==t?[]:(r=(n=i.da)===null||n===void 0?void 0:n.call(i,h))!==null&&r!==void 0?r:[]}}function rs(h,t,i,s){if(!h.length)return;let e=0;const n=i/2,r=h[0].At(s,!0);let l=t===1?n-(h[0].Vi()-r/2):h[0].Vi()-r/2-n;l=Math.max(0,l);for(let o=1;o<h.length;o++){const a=h[o],u=h[o-1],c=u.At(s,!1),f=a.Vi(),d=u.Vi();if(t===1?f>d-c:f<d+c){const m=d-c*t;a.Oi(m);const p=m-t*c/2;if((t===1?p<0:p>i)&&l>0){const b=t===1?-1-p:p-i,g=Math.min(b,l);for(let w=e;w<h.length;w++)h[w].Oi(h[w].Vi()+t*g);l-=g}}else e=o,l=t===1?d-c-f:f-(d+c)}}class ls{constructor(t,i,s,e){this.Li=null,this.Ev=null,this.Nv=!1,this.Fv=new at(200),this.Qr=null,this.Wv=0,this.jv=!1,this.Hv=()=>{this.jv||this.tn.$v().$t().Uh()},this.Uv=()=>{this.jv||this.tn.$v().$t().Uh()},this.tn=t,this.cn=i,this.ko=i.layout,this.Oc=s,this.qv=e==="left",this.Yv=Qt("normal",e),this.Zv=Qt("top",e),this.Xv=Qt("bottom",e),this.Kv=document.createElement("div"),this.Kv.style.height="100%",this.Kv.style.overflow="hidden",this.Kv.style.width="25px",this.Kv.style.left="0",this.Kv.style.position="relative",this.Gv=H(this.Kv,S({width:16,height:16})),this.Gv.subscribeSuggestedBitmapSizeChanged(this.Hv);const n=this.Gv.canvasElement;n.style.position="absolute",n.style.zIndex="1",n.style.left="0",n.style.top="0",this.Jv=H(this.Kv,S({width:16,height:16})),this.Jv.subscribeSuggestedBitmapSizeChanged(this.Uv);const r=this.Jv.canvasElement;r.style.position="absolute",r.style.zIndex="2",r.style.left="0",r.style.top="0";const l={bv:this.Qv.bind(this),pv:this.Qv.bind(this),av:this.tp.bind(this),hv:this.tp.bind(this),wv:this.ip.bind(this),cv:this.np.bind(this),_v:this.np.bind(this),qf:this.sp.bind(this),jf:this.sp.bind(this),Qf:this.ep.bind(this),Pv:this.rp.bind(this)};this.hp=new vi(this.Jv.canvasElement,l,{ev:()=>!this.cn.handleScroll.vertTouchDrag,rv:()=>!0})}S(){this.hp.S(),this.Jv.unsubscribeSuggestedBitmapSizeChanged(this.Uv),A(this.Jv.canvasElement),this.Jv.dispose(),this.Gv.unsubscribeSuggestedBitmapSizeChanged(this.Hv),A(this.Gv.canvasElement),this.Gv.dispose(),this.Li!==null&&this.Li.Ko().p(this),this.Li=null}lp(){return this.Kv}P(){return this.ko.fontSize}ap(){const t=this.Oc.W();return this.Qr!==t.R&&(this.Fv.nr(),this.Qr=t.R),t}op(){if(this.Li===null)return 0;let t=0;const i=this.ap(),s=v(this.Gv.canvasElement.getContext("2d"));s.save();const e=this.Li.Ha();s.font=this._p(),e.length>0&&(t=Math.max(this.Fv.xi(s,e[0].so),this.Fv.xi(s,e[e.length-1].so)));const n=this.up();for(let a=n.length;a--;){const u=this.Fv.xi(s,n[a].Kt());u>t&&(t=u)}const r=this.Li.Ct();if(r!==null&&this.Ev!==null&&(l=this.cn.crosshair).mode!==2&&l.horzLine.visible&&l.horzLine.labelVisible){const a=this.Li.pn(1,r),u=this.Li.pn(this.Ev.height-2,r);t=Math.max(t,this.Fv.xi(s,this.Li.Fi(Math.floor(Math.min(a,u))+.11111111111111,r)),this.Fv.xi(s,this.Li.Fi(Math.ceil(Math.max(a,u))-.11111111111111,r)))}var l;s.restore();const o=t||34;return hi(Math.ceil(i.C+i.T+i.A+i.I+5+o))}cp(t){this.Ev!==null&&j(this.Ev,t)||(this.Ev=t,this.jv=!0,this.Gv.resizeCanvasElement(t),this.Jv.resizeCanvasElement(t),this.jv=!1,this.Kv.style.width=`${t.width}px`,this.Kv.style.height=`${t.height}px`)}dp(){return v(this.Ev).width}Gi(t){this.Li!==t&&(this.Li!==null&&this.Li.Ko().p(this),this.Li=t,t.Ko().l(this.fo.bind(this),this))}Dt(){return this.Li}nr(){const t=this.tn.fp();this.tn.$v().$t().E_(t,v(this.Dt()))}vp(t){if(this.Ev===null)return;if(t!==1){this.pp(),this.Gv.applySuggestedBitmapSize();const s=F(this.Gv);s!==null&&(s.useBitmapCoordinateSpace(e=>{this.mp(e),this.Ie(e)}),this.tn.bp(s,this.Xv),this.wp(s),this.tn.bp(s,this.Yv),this.gp(s))}this.Jv.applySuggestedBitmapSize();const i=F(this.Jv);i!==null&&(i.useBitmapCoordinateSpace(({context:s,bitmapSize:e})=>{s.clearRect(0,0,e.width,e.height)}),this.Mp(i),this.tn.bp(i,this.Zv))}xp(){return this.Gv.bitmapSize}Sp(t,i,s){const e=this.xp();e.width>0&&e.height>0&&t.drawImage(this.Gv.canvasElement,i,s)}bt(){var t;(t=this.Li)===null||t===void 0||t.Ha()}Qv(t){if(this.Li===null||this.Li.Ni()||!this.cn.handleScale.axisPressedMouseMove.price)return;const i=this.tn.$v().$t(),s=this.tn.fp();this.Nv=!0,i.V_(s,this.Li,t.localY)}tp(t){if(this.Li===null||!this.cn.handleScale.axisPressedMouseMove.price)return;const i=this.tn.$v().$t(),s=this.tn.fp(),e=this.Li;i.O_(s,e,t.localY)}ip(){if(this.Li===null||!this.cn.handleScale.axisPressedMouseMove.price)return;const t=this.tn.$v().$t(),i=this.tn.fp(),s=this.Li;this.Nv&&(this.Nv=!1,t.B_(i,s))}np(t){if(this.Li===null||!this.cn.handleScale.axisPressedMouseMove.price)return;const i=this.tn.$v().$t(),s=this.tn.fp();this.Nv=!1,i.B_(s,this.Li)}sp(t){this.cn.handleScale.axisDoubleClickReset.price&&this.nr()}ep(t){this.Li!==null&&(!this.tn.$v().$t().W().handleScale.axisPressedMouseMove.price||this.Li.Mh()||this.Li.Oo()||this.kp(1))}rp(t){this.kp(0)}up(){const t=[],i=this.Li===null?void 0:this.Li;return(s=>{for(let e=0;e<s.length;++e){const n=s[e].Rn(this.tn.fp(),i);for(let r=0;r<n.length;r++)t.push(n[r])}})(this.tn.fp().Uo()),t}mp({context:t,bitmapSize:i}){const{width:s,height:e}=i,n=this.tn.fp().$t(),r=n.q(),l=n.bd();r===l?kt(t,0,0,s,e,r):xs(t,0,0,s,e,r,l)}Ie({context:t,bitmapSize:i,horizontalPixelRatio:s}){if(this.Ev===null||this.Li===null||!this.Li.W().borderVisible)return;t.fillStyle=this.Li.W().borderColor;const e=Math.max(1,Math.floor(this.ap().C*s));let n;n=this.qv?i.width-e:0,t.fillRect(n,0,e,i.height)}wp(t){if(this.Ev===null||this.Li===null)return;const i=this.Li.Ha(),s=this.Li.W(),e=this.ap(),n=this.qv?this.Ev.width-e.T:0;s.borderVisible&&s.ticksVisible&&t.useBitmapCoordinateSpace(({context:r,horizontalPixelRatio:l,verticalPixelRatio:o})=>{r.fillStyle=s.borderColor;const a=Math.max(1,Math.floor(o)),u=Math.floor(.5*o),c=Math.round(e.T*l);r.beginPath();for(const f of i)r.rect(Math.floor(n*l),Math.round(f.Ea*o)-u,c,a);r.fill()}),t.useMediaCoordinateSpace(({context:r})=>{var l;r.font=this._p(),r.fillStyle=(l=s.textColor)!==null&&l!==void 0?l:this.ko.textColor,r.textAlign=this.qv?"right":"left",r.textBaseline="middle";const o=this.qv?Math.round(n-e.A):Math.round(n+e.T+e.A),a=i.map(u=>this.Fv.Mi(r,u.so));for(let u=i.length;u--;){const c=i[u];r.fillText(c.so,o,c.Ea+a[u])}})}pp(){if(this.Ev===null||this.Li===null)return;const t=[],i=this.Li.Uo().slice(),s=this.tn.fp(),e=this.ap();this.Li===s.pr()&&this.tn.fp().Uo().forEach(r=>{s.vr(r)&&i.push(r)});const n=this.Li;i.forEach(r=>{r.Rn(s,n).forEach(l=>{l.Oi(null),l.Bi()&&t.push(l)})}),t.forEach(r=>r.Oi(r.ki())),this.Li.W().alignLabels&&this.yp(t,e)}yp(t,i){if(this.Ev===null)return;const s=this.Ev.height/2,e=t.filter(r=>r.ki()<=s),n=t.filter(r=>r.ki()>s);e.sort((r,l)=>l.ki()-r.ki()),n.sort((r,l)=>r.ki()-l.ki());for(const r of t){const l=Math.floor(r.At(i)/2),o=r.ki();o>-l&&o<l&&r.Oi(l),o>this.Ev.height-l&&o<this.Ev.height+l&&r.Oi(this.Ev.height-l)}rs(e,1,this.Ev.height,i),rs(n,-1,this.Ev.height,i)}gp(t){if(this.Ev===null)return;const i=this.up(),s=this.ap(),e=this.qv?"right":"left";i.forEach(n=>{n.Ai()&&n.gt(v(this.Li)).X(t,s,this.Fv,e)})}Mp(t){if(this.Ev===null||this.Li===null)return;const i=this.tn.$v().$t(),s=[],e=this.tn.fp(),n=i.Zc().Rn(e,this.Li);n.length&&s.push(n);const r=this.ap(),l=this.qv?"right":"left";s.forEach(o=>{o.forEach(a=>{a.gt(v(this.Li)).X(t,r,this.Fv,l)})})}kp(t){this.Kv.style.cursor=t===1?"ns-resize":"default"}fo(){const t=this.op();this.Wv<t&&this.tn.$v().$t().Kl(),this.Wv=t}_p(){return Y(this.ko.fontSize,this.ko.fontFamily)}}function Kh(h,t){var i,s;return(s=(i=h.ua)===null||i===void 0?void 0:i.call(h,t))!==null&&s!==void 0?s:[]}function _t(h,t){var i,s;return(s=(i=h.Pn)===null||i===void 0?void 0:i.call(h,t))!==null&&s!==void 0?s:[]}function jh(h,t){var i,s;return(s=(i=h.Ji)===null||i===void 0?void 0:i.call(h,t))!==null&&s!==void 0?s:[]}function Fh(h,t){var i,s;return(s=(i=h.aa)===null||i===void 0?void 0:i.call(h,t))!==null&&s!==void 0?s:[]}class bi{constructor(t,i){this.Ev=S({width:0,height:0}),this.Cp=null,this.Tp=null,this.Pp=null,this.Rp=null,this.Dp=!1,this.Vp=new M,this.Op=new M,this.Bp=0,this.Ap=!1,this.Ip=null,this.zp=!1,this.Lp=null,this.Ep=null,this.jv=!1,this.Hv=()=>{this.jv||this.Np===null||this.$i().Uh()},this.Uv=()=>{this.jv||this.Np===null||this.$i().Uh()},this.Qd=t,this.Np=i,this.Np.W_().l(this.Fp.bind(this),this,!0),this.Wp=document.createElement("td"),this.Wp.style.padding="0",this.Wp.style.position="relative";const s=document.createElement("div");s.style.width="100%",s.style.height="100%",s.style.position="relative",s.style.overflow="hidden",this.jp=document.createElement("td"),this.jp.style.padding="0",this.Hp=document.createElement("td"),this.Hp.style.padding="0",this.Wp.appendChild(s),this.Gv=H(s,S({width:16,height:16})),this.Gv.subscribeSuggestedBitmapSizeChanged(this.Hv);const e=this.Gv.canvasElement;e.style.position="absolute",e.style.zIndex="1",e.style.left="0",e.style.top="0",this.Jv=H(s,S({width:16,height:16})),this.Jv.subscribeSuggestedBitmapSizeChanged(this.Uv);const n=this.Jv.canvasElement;n.style.position="absolute",n.style.zIndex="2",n.style.left="0",n.style.top="0",this.$p=document.createElement("tr"),this.$p.appendChild(this.jp),this.$p.appendChild(this.Wp),this.$p.appendChild(this.Hp),this.Up(),this.hp=new vi(this.Jv.canvasElement,this,{ev:()=>this.Ip===null&&!this.Qd.W().handleScroll.vertTouchDrag,rv:()=>this.Ip===null&&!this.Qd.W().handleScroll.horzTouchDrag})}S(){this.Cp!==null&&this.Cp.S(),this.Tp!==null&&this.Tp.S(),this.Pp=null,this.Jv.unsubscribeSuggestedBitmapSizeChanged(this.Uv),A(this.Jv.canvasElement),this.Jv.dispose(),this.Gv.unsubscribeSuggestedBitmapSizeChanged(this.Hv),A(this.Gv.canvasElement),this.Gv.dispose(),this.Np!==null&&this.Np.W_().p(this),this.hp.S()}fp(){return v(this.Np)}qp(t){var i,s;this.Np!==null&&this.Np.W_().p(this),this.Np=t,this.Np!==null&&this.Np.W_().l(bi.prototype.Fp.bind(this),this,!0),this.Up(),this.Qd.Yp().indexOf(this)===this.Qd.Yp().length-1?(this.Pp=(i=this.Pp)!==null&&i!==void 0?i:new Dh(this.Wp,this.Qd),this.Pp.bt()):((s=this.Pp)===null||s===void 0||s.if(),this.Pp=null)}$v(){return this.Qd}lp(){return this.$p}Up(){if(this.Np!==null&&(this.Zp(),this.$i().wt().length!==0)){if(this.Cp!==null){const t=this.Np.R_();this.Cp.Gi(v(t))}if(this.Tp!==null){const t=this.Np.D_();this.Tp.Gi(v(t))}}}Xp(){this.Cp!==null&&this.Cp.bt(),this.Tp!==null&&this.Tp.bt()}M_(){return this.Np!==null?this.Np.M_():0}x_(t){this.Np&&this.Np.x_(t)}Qf(t){if(!this.Np)return;this.Kp();const i=t.localX,s=t.localY;this.Gp(i,s,t)}bv(t){this.Kp(),this.Jp(),this.Gp(t.localX,t.localY,t)}tv(t){var i;if(!this.Np)return;this.Kp();const s=t.localX,e=t.localY;this.Gp(s,e,t);const n=this.wr(s,e);this.Qd.Qp((i=n?.Lv)!==null&&i!==void 0?i:null),this.$i().jc(n&&{Hc:n.Hc,Iv:n.Iv})}dv(t){this.Np!==null&&(this.Kp(),this.tm(t))}qf(t){this.Np!==null&&this.im(this.Op,t)}jf(t){this.qf(t)}av(t){this.Kp(),this.nm(t),this.Gp(t.localX,t.localY,t)}cv(t){this.Np!==null&&(this.Kp(),this.Ap=!1,this.sm(t))}uv(t){this.Np!==null&&this.tm(t)}Rv(t){if(this.Ap=!0,this.Ip===null){const i={x:t.localX,y:t.localY};this.rm(i,i,t)}}Pv(t){this.Np!==null&&(this.Kp(),this.Np.$t().jc(null),this.hm())}lm(){return this.Vp}am(){return this.Op}xv(){this.Bp=1,this.$i().Un()}Sv(t,i){if(!this.Qd.W().handleScale.pinch)return;const s=5*(i-this.Bp);this.Bp=i,this.$i().Qc(t.nt,s)}pv(t){this.Ap=!1,this.zp=this.Ip!==null,this.Jp();const i=this.$i().Zc();this.Ip!==null&&i.yt()&&(this.Lp={x:i.Yt(),y:i.Zt()},this.Ip={x:t.localX,y:t.localY})}hv(t){if(this.Np===null)return;const i=t.localX,s=t.localY;if(this.Ip===null)this.nm(t);else{this.zp=!1;const e=v(this.Lp),n=e.x+(i-this.Ip.x),r=e.y+(s-this.Ip.y);this.Gp(n,r,t)}}_v(t){this.$v().W().trackingMode.exitMode===0&&(this.zp=!0),this.om(),this.sm(t)}wr(t,i){const s=this.Np;return s===null?null:function(e,n,r){const l=e.Uo(),o=function(a,u,c){var f,d;let m,p;for(const w of a){const _=(d=(f=w.va)===null||f===void 0?void 0:f.call(w,u,c))!==null&&d!==void 0?d:[];for(const y of _)b=y.zOrder,(!(g=m?.zOrder)||b==="top"&&g!=="top"||b==="normal"&&g==="bottom")&&(m=y,p=w)}var b,g;return m&&p?{zv:m,Hc:p}:null}(l,n,r);if(o?.zv.zOrder==="top")return St(o);for(const a of l){if(o&&o.Hc===a&&o.zv.zOrder!=="bottom"&&!o.zv.isBackground)return St(o);const u=Ih(a.Pn(e),n,r);if(u!==null)return{Hc:a,Bv:u.Bv,Iv:u.Iv};if(o&&o.Hc===a&&o.zv.zOrder!=="bottom"&&o.zv.isBackground)return St(o)}return o?.zv?St(o):null}(s,t,i)}_m(t,i){v(i==="left"?this.Cp:this.Tp).cp(S({width:t,height:this.Ev.height}))}um(){return this.Ev}cp(t){j(this.Ev,t)||(this.Ev=t,this.jv=!0,this.Gv.resizeCanvasElement(t),this.Jv.resizeCanvasElement(t),this.jv=!1,this.Wp.style.width=t.width+"px",this.Wp.style.height=t.height+"px")}dm(){const t=v(this.Np);t.P_(t.R_()),t.P_(t.D_());for(const i of t.Ba())if(t.vr(i)){const s=i.Dt();s!==null&&t.P_(s),i.Vn()}}xp(){return this.Gv.bitmapSize}Sp(t,i,s){const e=this.xp();e.width>0&&e.height>0&&t.drawImage(this.Gv.canvasElement,i,s)}vp(t){if(t===0||this.Np===null)return;if(t>1&&this.dm(),this.Cp!==null&&this.Cp.vp(t),this.Tp!==null&&this.Tp.vp(t),t!==1){this.Gv.applySuggestedBitmapSize();const s=F(this.Gv);s!==null&&(s.useBitmapCoordinateSpace(e=>{this.mp(e)}),this.Np&&(this.fm(s,Kh),this.vm(s),this.pm(s),this.fm(s,_t),this.fm(s,jh)))}this.Jv.applySuggestedBitmapSize();const i=F(this.Jv);i!==null&&(i.useBitmapCoordinateSpace(({context:s,bitmapSize:e})=>{s.clearRect(0,0,e.width,e.height)}),this.bm(i),this.fm(i,Fh))}wm(){return this.Cp}gm(){return this.Tp}bp(t,i){this.fm(t,i)}Fp(){this.Np!==null&&this.Np.W_().p(this),this.Np=null}tm(t){this.im(this.Vp,t)}im(t,i){const s=i.localX,e=i.localY;t.M()&&t.m(this.$i().St().Nu(s),{x:s,y:e},i)}mp({context:t,bitmapSize:i}){const{width:s,height:e}=i,n=this.$i(),r=n.q(),l=n.bd();r===l?kt(t,0,0,s,e,l):xs(t,0,0,s,e,r,l)}vm(t){const i=v(this.Np).j_().qh().gt();i!==null&&i.X(t,!1)}pm(t){const i=this.$i().Yc();this.Mm(t,_t,ni,i),this.Mm(t,_t,xt,i)}bm(t){this.Mm(t,_t,xt,this.$i().Zc())}fm(t,i){const s=v(this.Np).Uo();for(const e of s)this.Mm(t,i,ni,e);for(const e of s)this.Mm(t,i,xt,e)}Mm(t,i,s,e){const n=v(this.Np),r=n.$t().Wc(),l=r!==null&&r.Hc===e,o=r!==null&&l&&r.Iv!==void 0?r.Iv.Mr:void 0;ri(i,a=>s(a,t,l,o),e,n)}Zp(){if(this.Np===null)return;const t=this.Qd,i=this.Np.R_().W().visible,s=this.Np.D_().W().visible;i||this.Cp===null||(this.jp.removeChild(this.Cp.lp()),this.Cp.S(),this.Cp=null),s||this.Tp===null||(this.Hp.removeChild(this.Tp.lp()),this.Tp.S(),this.Tp=null);const e=t.$t().ud();i&&this.Cp===null&&(this.Cp=new ls(this,t.W(),e,"left"),this.jp.appendChild(this.Cp.lp())),s&&this.Tp===null&&(this.Tp=new ls(this,t.W(),e,"right"),this.Hp.appendChild(this.Tp.lp()))}xm(t){return t.Dv&&this.Ap||this.Ip!==null}Sm(t){return Math.max(0,Math.min(t,this.Ev.width-1))}km(t){return Math.max(0,Math.min(t,this.Ev.height-1))}Gp(t,i,s){this.$i().ld(this.Sm(t),this.km(i),s,v(this.Np))}hm(){this.$i().od()}om(){this.zp&&(this.Ip=null,this.hm())}rm(t,i,s){this.Ip=t,this.zp=!1,this.Gp(i.x,i.y,s);const e=this.$i().Zc();this.Lp={x:e.Yt(),y:e.Zt()}}$i(){return this.Qd.$t()}sm(t){if(!this.Dp)return;const i=this.$i(),s=this.fp();if(i.z_(s,s.vn()),this.Rp=null,this.Dp=!1,i.ed(),this.Ep!==null){const e=performance.now(),n=i.St();this.Ep.Vr(n.Hu(),e),this.Ep.Qu(e)||i.Zn(this.Ep)}}Kp(){this.Ip=null}Jp(){if(this.Np){if(this.$i().Un(),document.activeElement!==document.body&&document.activeElement!==document.documentElement)v(document.activeElement).blur();else{const t=document.getSelection();t!==null&&t.removeAllRanges()}!this.Np.vn().Ni()&&this.$i().St().Ni()}}nm(t){if(this.Np===null)return;const i=this.$i(),s=i.St();if(s.Ni())return;const e=this.Qd.W(),n=e.handleScroll,r=e.kineticScroll;if((!n.pressedMouseMove||t.Dv)&&(!n.horzTouchDrag&&!n.vertTouchDrag||!t.Dv))return;const l=this.Np.vn(),o=performance.now();if(this.Rp!==null||this.xm(t)||(this.Rp={x:t.clientX,y:t.clientY,Od:o,ym:t.localX,Cm:t.localY}),this.Rp!==null&&!this.Dp&&(this.Rp.x!==t.clientX||this.Rp.y!==t.clientY)){if(t.Dv&&r.touch||!t.Dv&&r.mouse){const a=s.le();this.Ep=new Oh(.2/a,7/a,.997,15/a),this.Ep.Yd(s.Hu(),this.Rp.Od)}else this.Ep=null;l.Ni()||i.A_(this.Np,l,t.localY),i.nd(t.localX),this.Dp=!0}this.Dp&&(l.Ni()||i.I_(this.Np,l,t.localY),i.sd(t.localX),this.Ep!==null&&this.Ep.Yd(s.Hu(),o))}}class os{constructor(t,i,s,e,n){this.ft=!0,this.Ev=S({width:0,height:0}),this.Hv=()=>this.vp(3),this.qv=t==="left",this.Oc=s.ud,this.cn=i,this.Tm=e,this.Pm=n,this.Kv=document.createElement("div"),this.Kv.style.width="25px",this.Kv.style.height="100%",this.Kv.style.overflow="hidden",this.Gv=H(this.Kv,S({width:16,height:16})),this.Gv.subscribeSuggestedBitmapSizeChanged(this.Hv)}S(){this.Gv.unsubscribeSuggestedBitmapSizeChanged(this.Hv),A(this.Gv.canvasElement),this.Gv.dispose()}lp(){return this.Kv}um(){return this.Ev}cp(t){j(this.Ev,t)||(this.Ev=t,this.Gv.resizeCanvasElement(t),this.Kv.style.width=`${t.width}px`,this.Kv.style.height=`${t.height}px`,this.ft=!0)}vp(t){if(t<3&&!this.ft||this.Ev.width===0||this.Ev.height===0)return;this.ft=!1,this.Gv.applySuggestedBitmapSize();const i=F(this.Gv);i!==null&&i.useBitmapCoordinateSpace(s=>{this.mp(s),this.Ie(s)})}xp(){return this.Gv.bitmapSize}Sp(t,i,s){const e=this.xp();e.width>0&&e.height>0&&t.drawImage(this.Gv.canvasElement,i,s)}Ie({context:t,bitmapSize:i,horizontalPixelRatio:s,verticalPixelRatio:e}){if(!this.Tm())return;t.fillStyle=this.cn.timeScale.borderColor;const n=Math.floor(this.Oc.W().C*s),r=Math.floor(this.Oc.W().C*e),l=this.qv?i.width-n:0;t.fillRect(l,0,n,r)}mp({context:t,bitmapSize:i}){kt(t,0,0,i.width,i.height,this.Pm())}}function gi(h){return t=>{var i,s;return(s=(i=t.fa)===null||i===void 0?void 0:i.call(t,h))!==null&&s!==void 0?s:[]}}const Xh=gi("normal"),Hh=gi("top"),Ah=gi("bottom");class Uh{constructor(t,i){this.Rm=null,this.Dm=null,this.k=null,this.Vm=!1,this.Ev=S({width:0,height:0}),this.Om=new M,this.Fv=new at(5),this.jv=!1,this.Hv=()=>{this.jv||this.Qd.$t().Uh()},this.Uv=()=>{this.jv||this.Qd.$t().Uh()},this.Qd=t,this.q_=i,this.cn=t.W().layout,this.Xd=document.createElement("tr"),this.Bm=document.createElement("td"),this.Bm.style.padding="0",this.Am=document.createElement("td"),this.Am.style.padding="0",this.Kv=document.createElement("td"),this.Kv.style.height="25px",this.Kv.style.padding="0",this.Im=document.createElement("div"),this.Im.style.width="100%",this.Im.style.height="100%",this.Im.style.position="relative",this.Im.style.overflow="hidden",this.Kv.appendChild(this.Im),this.Gv=H(this.Im,S({width:16,height:16})),this.Gv.subscribeSuggestedBitmapSizeChanged(this.Hv);const s=this.Gv.canvasElement;s.style.position="absolute",s.style.zIndex="1",s.style.left="0",s.style.top="0",this.Jv=H(this.Im,S({width:16,height:16})),this.Jv.subscribeSuggestedBitmapSizeChanged(this.Uv);const e=this.Jv.canvasElement;e.style.position="absolute",e.style.zIndex="2",e.style.left="0",e.style.top="0",this.Xd.appendChild(this.Bm),this.Xd.appendChild(this.Kv),this.Xd.appendChild(this.Am),this.zm(),this.Qd.$t().g_().l(this.zm.bind(this),this),this.hp=new vi(this.Jv.canvasElement,this,{ev:()=>!0,rv:()=>!this.Qd.W().handleScroll.horzTouchDrag})}S(){this.hp.S(),this.Rm!==null&&this.Rm.S(),this.Dm!==null&&this.Dm.S(),this.Jv.unsubscribeSuggestedBitmapSizeChanged(this.Uv),A(this.Jv.canvasElement),this.Jv.dispose(),this.Gv.unsubscribeSuggestedBitmapSizeChanged(this.Hv),A(this.Gv.canvasElement),this.Gv.dispose()}lp(){return this.Xd}Lm(){return this.Rm}Em(){return this.Dm}bv(t){if(this.Vm)return;this.Vm=!0;const i=this.Qd.$t();!i.St().Ni()&&this.Qd.W().handleScale.axisPressedMouseMove.time&&i.Jc(t.localX)}pv(t){this.bv(t)}wv(){const t=this.Qd.$t();!t.St().Ni()&&this.Vm&&(this.Vm=!1,this.Qd.W().handleScale.axisPressedMouseMove.time&&t.hd())}av(t){const i=this.Qd.$t();!i.St().Ni()&&this.Qd.W().handleScale.axisPressedMouseMove.time&&i.rd(t.localX)}hv(t){this.av(t)}cv(){this.Vm=!1;const t=this.Qd.$t();t.St().Ni()&&!this.Qd.W().handleScale.axisPressedMouseMove.time||t.hd()}_v(){this.cv()}qf(){this.Qd.W().handleScale.axisDoubleClickReset.time&&this.Qd.$t().Kn()}jf(){this.qf()}Qf(){this.Qd.$t().W().handleScale.axisPressedMouseMove.time&&this.kp(1)}Pv(){this.kp(0)}um(){return this.Ev}Nm(){return this.Om}Fm(t,i,s){j(this.Ev,t)||(this.Ev=t,this.jv=!0,this.Gv.resizeCanvasElement(t),this.Jv.resizeCanvasElement(t),this.jv=!1,this.Kv.style.width=`${t.width}px`,this.Kv.style.height=`${t.height}px`,this.Om.m(t)),this.Rm!==null&&this.Rm.cp(S({width:i,height:t.height})),this.Dm!==null&&this.Dm.cp(S({width:s,height:t.height}))}Wm(){const t=this.jm();return Math.ceil(t.C+t.T+t.P+t.L+t.B+t.Hm)}bt(){this.Qd.$t().St().Ha()}xp(){return this.Gv.bitmapSize}Sp(t,i,s){const e=this.xp();e.width>0&&e.height>0&&t.drawImage(this.Gv.canvasElement,i,s)}vp(t){if(t===0)return;if(t!==1){this.Gv.applySuggestedBitmapSize();const s=F(this.Gv);s!==null&&(s.useBitmapCoordinateSpace(e=>{this.mp(e),this.Ie(e),this.$m(s,Ah)}),this.wp(s),this.$m(s,Xh)),this.Rm!==null&&this.Rm.vp(t),this.Dm!==null&&this.Dm.vp(t)}this.Jv.applySuggestedBitmapSize();const i=F(this.Jv);i!==null&&(i.useBitmapCoordinateSpace(({context:s,bitmapSize:e})=>{s.clearRect(0,0,e.width,e.height)}),this.Um([...this.Qd.$t().wt(),this.Qd.$t().Zc()],i),this.$m(i,Hh))}$m(t,i){const s=this.Qd.$t().wt();for(const e of s)ri(i,n=>ni(n,t,!1,void 0),e,void 0);for(const e of s)ri(i,n=>xt(n,t,!1,void 0),e,void 0)}mp({context:t,bitmapSize:i}){kt(t,0,0,i.width,i.height,this.Qd.$t().bd())}Ie({context:t,bitmapSize:i,verticalPixelRatio:s}){if(this.Qd.W().timeScale.borderVisible){t.fillStyle=this.qm();const e=Math.max(1,Math.floor(this.jm().C*s));t.fillRect(0,0,i.width,e)}}wp(t){const i=this.Qd.$t().St(),s=i.Ha();if(!s||s.length===0)return;const e=this.q_.maxTickMarkWeight(s),n=this.jm(),r=i.W();r.borderVisible&&r.ticksVisible&&t.useBitmapCoordinateSpace(({context:l,horizontalPixelRatio:o,verticalPixelRatio:a})=>{l.strokeStyle=this.qm(),l.fillStyle=this.qm();const u=Math.max(1,Math.floor(o)),c=Math.floor(.5*o);l.beginPath();const f=Math.round(n.T*a);for(let d=s.length;d--;){const m=Math.round(s[d].coord*o);l.rect(m-c,0,u,f)}l.fill()}),t.useMediaCoordinateSpace(({context:l})=>{const o=n.C+n.T+n.L+n.P/2;l.textAlign="center",l.textBaseline="middle",l.fillStyle=this.$(),l.font=this._p();for(const a of s)if(a.weight<e){const u=a.needAlignCoordinate?this.Ym(l,a.coord,a.label):a.coord;l.fillText(a.label,u,o)}this.Qd.W().timeScale.allowBoldLabels&&(l.font=this.Zm());for(const a of s)if(a.weight>=e){const u=a.needAlignCoordinate?this.Ym(l,a.coord,a.label):a.coord;l.fillText(a.label,u,o)}})}Ym(t,i,s){const e=this.Fv.xi(t,s),n=e/2,r=Math.floor(i-n)+.5;return r<0?i+=Math.abs(0-r):r+e>this.Ev.width&&(i-=Math.abs(this.Ev.width-(r+e))),i}Um(t,i){const s=this.jm();for(const e of t)for(const n of e.Qi())n.gt().X(i,s)}qm(){return this.Qd.W().timeScale.borderColor}$(){return this.cn.textColor}j(){return this.cn.fontSize}_p(){return Y(this.j(),this.cn.fontFamily)}Zm(){return Y(this.j(),this.cn.fontFamily,"bold")}jm(){this.k===null&&(this.k={C:1,N:NaN,L:NaN,B:NaN,ji:NaN,T:5,P:NaN,R:"",Wi:new at,Hm:0});const t=this.k,i=this._p();if(t.R!==i){const s=this.j();t.P=s,t.R=i,t.L=3*s/12,t.B=3*s/12,t.ji=9*s/12,t.N=0,t.Hm=4*s/12,t.Wi.nr()}return this.k}kp(t){this.Kv.style.cursor=t===1?"ew-resize":"default"}zm(){const t=this.Qd.$t(),i=t.W();i.leftPriceScale.visible||this.Rm===null||(this.Bm.removeChild(this.Rm.lp()),this.Rm.S(),this.Rm=null),i.rightPriceScale.visible||this.Dm===null||(this.Am.removeChild(this.Dm.lp()),this.Dm.S(),this.Dm=null);const s={ud:this.Qd.$t().ud()},e=()=>i.leftPriceScale.borderVisible&&t.St().W().borderVisible,n=()=>t.bd();i.leftPriceScale.visible&&this.Rm===null&&(this.Rm=new os("left",i,s,e,n),this.Bm.appendChild(this.Rm.lp())),i.rightPriceScale.visible&&this.Dm===null&&(this.Dm=new os("right",i,s,e,n),this.Am.appendChild(this.Dm.lp()))}}const qh=!!Z&&!!navigator.userAgentData&&navigator.userAgentData.brands.some(h=>h.brand.includes("Chromium"))&&!!Z&&(!((Yt=navigator?.userAgentData)===null||Yt===void 0)&&Yt.platform?navigator.userAgentData.platform==="Windows":navigator.userAgent.toLowerCase().indexOf("win")>=0);var Yt;class Jh{constructor(t,i,s){var e;this.Xm=[],this.Km=0,this.ho=0,this.__=0,this.Gm=0,this.Jm=0,this.Qm=null,this.tb=!1,this.Vp=new M,this.Op=new M,this.Rc=new M,this.ib=null,this.nb=null,this.Jd=t,this.cn=i,this.q_=s,this.Xd=document.createElement("div"),this.Xd.classList.add("tv-lightweight-charts"),this.Xd.style.overflow="hidden",this.Xd.style.direction="ltr",this.Xd.style.width="100%",this.Xd.style.height="100%",(e=this.Xd).style.userSelect="none",e.style.webkitUserSelect="none",e.style.msUserSelect="none",e.style.MozUserSelect="none",e.style.webkitTapHighlightColor="transparent",this.sb=document.createElement("table"),this.sb.setAttribute("cellspacing","0"),this.Xd.appendChild(this.sb),this.eb=this.rb.bind(this),Zt(this.cn)&&this.hb(!0),this.$i=new Rh(this.Vc.bind(this),this.cn,s),this.$t().Xc().l(this.lb.bind(this),this),this.ab=new Uh(this,this.q_),this.sb.appendChild(this.ab.lp());const n=i.autoSize&&this.ob();let r=this.cn.width,l=this.cn.height;if(n||r===0||l===0){const o=t.getBoundingClientRect();r=r||o.width,l=l||o.height}this._b(r,l),this.ub(),t.appendChild(this.Xd),this.cb(),this.$i.St().ec().l(this.$i.Kl.bind(this.$i),this),this.$i.g_().l(this.$i.Kl.bind(this.$i),this)}$t(){return this.$i}W(){return this.cn}Yp(){return this.Xm}fb(){return this.ab}S(){this.hb(!1),this.Km!==0&&window.cancelAnimationFrame(this.Km),this.$i.Xc().p(this),this.$i.St().ec().p(this),this.$i.g_().p(this),this.$i.S();for(const t of this.Xm)this.sb.removeChild(t.lp()),t.lm().p(this),t.am().p(this),t.S();this.Xm=[],v(this.ab).S(),this.Xd.parentElement!==null&&this.Xd.parentElement.removeChild(this.Xd),this.Rc.S(),this.Vp.S(),this.Op.S(),this.pb()}_b(t,i,s=!1){if(this.ho===i&&this.__===t)return;const e=function(l){const o=Math.floor(l.width),a=Math.floor(l.height);return S({width:o-o%2,height:a-a%2})}(S({width:t,height:i}));this.ho=e.height,this.__=e.width;const n=this.ho+"px",r=this.__+"px";v(this.Xd).style.height=n,v(this.Xd).style.width=r,this.sb.style.height=n,this.sb.style.width=r,s?this.mb(z.es(),performance.now()):this.$i.Kl()}vp(t){t===void 0&&(t=z.es());for(let i=0;i<this.Xm.length;i++)this.Xm[i].vp(t.Hn(i).Fn);this.cn.timeScale.visible&&this.ab.vp(t.jn())}$h(t){const i=Zt(this.cn);this.$i.$h(t);const s=Zt(this.cn);s!==i&&this.hb(s),this.cb(),this.bb(t)}lm(){return this.Vp}am(){return this.Op}Xc(){return this.Rc}wb(){this.Qm!==null&&(this.mb(this.Qm,performance.now()),this.Qm=null);const t=this.gb(null),i=document.createElement("canvas");i.width=t.width,i.height=t.height;const s=v(i.getContext("2d"));return this.gb(s),i}Mb(t){return t==="left"&&!this.xb()||t==="right"&&!this.Sb()||this.Xm.length===0?0:v(t==="left"?this.Xm[0].wm():this.Xm[0].gm()).dp()}kb(){return this.cn.autoSize&&this.ib!==null}yb(){return this.Xd}Qp(t){this.nb=t,this.nb?this.yb().style.setProperty("cursor",t):this.yb().style.removeProperty("cursor")}Cb(){return this.nb}Tb(){return k(this.Xm[0]).um()}bb(t){(t.autoSize!==void 0||!this.ib||t.width===void 0&&t.height===void 0)&&(t.autoSize&&!this.ib&&this.ob(),t.autoSize===!1&&this.ib!==null&&this.pb(),t.autoSize||t.width===void 0&&t.height===void 0||this._b(t.width||this.__,t.height||this.ho))}gb(t){let i=0,s=0;const e=this.Xm[0],n=(l,o)=>{let a=0;for(let u=0;u<this.Xm.length;u++){const c=this.Xm[u],f=v(l==="left"?c.wm():c.gm()),d=f.xp();t!==null&&f.Sp(t,o,a),a+=d.height}};this.xb()&&(n("left",0),i+=v(e.wm()).xp().width);for(let l=0;l<this.Xm.length;l++){const o=this.Xm[l],a=o.xp();t!==null&&o.Sp(t,i,s),s+=a.height}i+=e.xp().width,this.Sb()&&(n("right",i),i+=v(e.gm()).xp().width);const r=(l,o,a)=>{v(l==="left"?this.ab.Lm():this.ab.Em()).Sp(v(t),o,a)};if(this.cn.timeScale.visible){const l=this.ab.xp();if(t!==null){let o=0;this.xb()&&(r("left",o,s),o=v(e.wm()).xp().width),this.ab.Sp(t,o,s),o+=l.width,this.Sb()&&r("right",o,s)}s+=l.height}return S({width:i,height:s})}Pb(){let t=0,i=0,s=0;for(const m of this.Xm)this.xb()&&(i=Math.max(i,v(m.wm()).op(),this.cn.leftPriceScale.minimumWidth)),this.Sb()&&(s=Math.max(s,v(m.gm()).op(),this.cn.rightPriceScale.minimumWidth)),t+=m.M_();i=hi(i),s=hi(s);const e=this.__,n=this.ho,r=Math.max(e-i-s,0),l=this.cn.timeScale.visible;let o=l?Math.max(this.ab.Wm(),this.cn.timeScale.minimumHeight):0;var a;o=(a=o)+a%2;const u=0+o,c=n<u?0:n-u,f=c/t;let d=0;for(let m=0;m<this.Xm.length;++m){const p=this.Xm[m];p.qp(this.$i.qc()[m]);let b=0,g=0;g=m===this.Xm.length-1?c-d:Math.round(p.M_()*f),b=Math.max(g,2),d+=b,p.cp(S({width:r,height:b})),this.xb()&&p._m(i,"left"),this.Sb()&&p._m(s,"right"),p.fp()&&this.$i.Kc(p.fp(),b)}this.ab.Fm(S({width:l?r:0,height:o}),l?i:0,l?s:0),this.$i.S_(r),this.Gm!==i&&(this.Gm=i),this.Jm!==s&&(this.Jm=s)}hb(t){t?this.Xd.addEventListener("wheel",this.eb,{passive:!1}):this.Xd.removeEventListener("wheel",this.eb)}Rb(t){switch(t.deltaMode){case t.DOM_DELTA_PAGE:return 120;case t.DOM_DELTA_LINE:return 32}return qh?1/window.devicePixelRatio:1}rb(t){if(!(t.deltaX!==0&&this.cn.handleScroll.mouseWheel||t.deltaY!==0&&this.cn.handleScale.mouseWheel))return;const i=this.Rb(t),s=i*t.deltaX/100,e=-i*t.deltaY/100;if(t.cancelable&&t.preventDefault(),e!==0&&this.cn.handleScale.mouseWheel){const n=Math.sign(e)*Math.min(1,Math.abs(e)),r=t.clientX-this.Xd.getBoundingClientRect().left;this.$t().Qc(r,n)}s!==0&&this.cn.handleScroll.mouseWheel&&this.$t().td(-80*s)}mb(t,i){var s;const e=t.jn();e===3&&this.Db(),e!==3&&e!==2||(this.Vb(t),this.Ob(t,i),this.ab.bt(),this.Xm.forEach(n=>{n.Xp()}),((s=this.Qm)===null||s===void 0?void 0:s.jn())===3&&(this.Qm.ts(t),this.Db(),this.Vb(this.Qm),this.Ob(this.Qm,i),t=this.Qm,this.Qm=null)),this.vp(t)}Ob(t,i){for(const s of t.Qn())this.ns(s,i)}Vb(t){const i=this.$i.qc();for(let s=0;s<i.length;s++)t.Hn(s).Wn&&i[s].N_()}ns(t,i){const s=this.$i.St();switch(t.qn){case 0:s.hc();break;case 1:s.lc(t.Vt);break;case 2:s.Gn(t.Vt);break;case 3:s.Jn(t.Vt);break;case 4:s.qu();break;case 5:t.Vt.Qu(i)||s.Jn(t.Vt.tc(i))}}Vc(t){this.Qm!==null?this.Qm.ts(t):this.Qm=t,this.tb||(this.tb=!0,this.Km=window.requestAnimationFrame(i=>{if(this.tb=!1,this.Km=0,this.Qm!==null){const s=this.Qm;this.Qm=null,this.mb(s,i);for(const e of s.Qn())if(e.qn===5&&!e.Vt.Qu(i)){this.$t().Zn(e.Vt);break}}}))}Db(){this.ub()}ub(){const t=this.$i.qc(),i=t.length,s=this.Xm.length;for(let e=i;e<s;e++){const n=k(this.Xm.pop());this.sb.removeChild(n.lp()),n.lm().p(this),n.am().p(this),n.S()}for(let e=s;e<i;e++){const n=new bi(this,t[e]);n.lm().l(this.Bb.bind(this),this),n.am().l(this.Ab.bind(this),this),this.Xm.push(n),this.sb.insertBefore(n.lp(),this.ab.lp())}for(let e=0;e<i;e++){const n=t[e],r=this.Xm[e];r.fp()!==n?r.qp(n):r.Up()}this.cb(),this.Pb()}Ib(t,i,s){var e;const n=new Map;t!==null&&this.$i.wt().forEach(u=>{const c=u.In().ll(t);c!==null&&n.set(u,c)});let r;if(t!==null){const u=(e=this.$i.St().Ui(t))===null||e===void 0?void 0:e.originalTime;u!==void 0&&(r=u)}const l=this.$t().Wc(),o=l!==null&&l.Hc instanceof mi?l.Hc:void 0,a=l!==null&&l.Iv!==void 0?l.Iv.gr:void 0;return{zb:r,ee:t??void 0,Lb:i??void 0,Eb:o,Nb:n,Fb:a,Wb:s??void 0}}Bb(t,i,s){this.Vp.m(()=>this.Ib(t,i,s))}Ab(t,i,s){this.Op.m(()=>this.Ib(t,i,s))}lb(t,i,s){this.Rc.m(()=>this.Ib(t,i,s))}cb(){const t=this.cn.timeScale.visible?"":"none";this.ab.lp().style.display=t}xb(){return this.Xm[0].fp().R_().W().visible}Sb(){return this.Xm[0].fp().D_().W().visible}ob(){return"ResizeObserver"in window&&(this.ib=new ResizeObserver(t=>{const i=t.find(s=>s.target===this.Jd);i&&this._b(i.contentRect.width,i.contentRect.height)}),this.ib.observe(this.Jd,{box:"border-box"}),!0)}pb(){this.ib!==null&&this.ib.disconnect(),this.ib=null}}function Zt(h){return!!(h.handleScroll.mouseWheel||h.handleScale.mouseWheel)}function Gh(h){return function(t){return t.open!==void 0}(h)||function(t){return t.value!==void 0}(h)}function Fs(h,t){var i={};for(var s in h)Object.prototype.hasOwnProperty.call(h,s)&&t.indexOf(s)<0&&(i[s]=h[s]);if(h!=null&&typeof Object.getOwnPropertySymbols=="function"){var e=0;for(s=Object.getOwnPropertySymbols(h);e<s.length;e++)t.indexOf(s[e])<0&&Object.prototype.propertyIsEnumerable.call(h,s[e])&&(i[s[e]]=h[s[e]])}return i}function as(h,t,i,s){const e=i.value,n={ee:t,ot:h,Vt:[e,e,e,e],zb:s};return i.color!==void 0&&(n.V=i.color),n}function Qh(h,t,i,s){const e=i.value,n={ee:t,ot:h,Vt:[e,e,e,e],zb:s};return i.lineColor!==void 0&&(n.lt=i.lineColor),i.topColor!==void 0&&(n.Ps=i.topColor),i.bottomColor!==void 0&&(n.Rs=i.bottomColor),n}function Yh(h,t,i,s){const e=i.value,n={ee:t,ot:h,Vt:[e,e,e,e],zb:s};return i.topLineColor!==void 0&&(n.Re=i.topLineColor),i.bottomLineColor!==void 0&&(n.De=i.bottomLineColor),i.topFillColor1!==void 0&&(n.ke=i.topFillColor1),i.topFillColor2!==void 0&&(n.ye=i.topFillColor2),i.bottomFillColor1!==void 0&&(n.Ce=i.bottomFillColor1),i.bottomFillColor2!==void 0&&(n.Te=i.bottomFillColor2),n}function Zh(h,t,i,s){const e={ee:t,ot:h,Vt:[i.open,i.high,i.low,i.close],zb:s};return i.color!==void 0&&(e.V=i.color),e}function tn(h,t,i,s){const e={ee:t,ot:h,Vt:[i.open,i.high,i.low,i.close],zb:s};return i.color!==void 0&&(e.V=i.color),i.borderColor!==void 0&&(e.Ot=i.borderColor),i.wickColor!==void 0&&(e.Xh=i.wickColor),e}function sn(h,t,i,s,e){const n=k(e)(i),r=Math.max(...n),l=Math.min(...n),o=n[n.length-1],a=[o,r,l,o],u=i,{time:c,color:f}=u;return{ee:t,ot:h,Vt:a,zb:s,$e:Fs(u,["time","color"]),V:f}}function Mt(h){return h.Vt!==void 0}function us(h,t){return t.customValues!==void 0&&(h.jb=t.customValues),h}function K(h){return(t,i,s,e,n,r)=>function(l,o){return o?o(l):(a=l).open===void 0&&a.value===void 0;var a}(s,r)?us({ot:t,ee:i,zb:e},s):us(h(t,i,s,e,n),s)}function cs(h){return{Candlestick:K(tn),Bar:K(Zh),Area:K(Qh),Baseline:K(Yh),Histogram:K(as),Line:K(as),Custom:K(sn)}[h]}function fs(h){return{ee:0,Hb:new Map,la:h}}function ds(h,t){if(h!==void 0&&h.length!==0)return{$b:t.key(h[0].ot),Ub:t.key(h[h.length-1].ot)}}function ms(h){let t;return h.forEach(i=>{t===void 0&&(t=i.zb)}),k(t)}class en{constructor(t){this.qb=new Map,this.Yb=new Map,this.Zb=new Map,this.Xb=[],this.q_=t}S(){this.qb.clear(),this.Yb.clear(),this.Zb.clear(),this.Xb=[]}Kb(t,i){let s=this.qb.size!==0,e=!1;const n=this.Yb.get(t);if(n!==void 0)if(this.Yb.size===1)s=!1,e=!0,this.qb.clear();else for(const o of this.Xb)o.pointData.Hb.delete(t)&&(e=!0);let r=[];if(i.length!==0){const o=i.map(d=>d.time),a=this.q_.createConverterToInternalObj(i),u=cs(t.Qh()),c=t.Ca(),f=t.Ta();r=i.map((d,m)=>{const p=a(d.time),b=this.q_.key(p);let g=this.qb.get(b);g===void 0&&(g=fs(p),this.qb.set(b,g),e=!0);const w=u(p,g.ee,d,o[m],c,f);return g.Hb.set(t,w),w})}s&&this.Gb(),this.Jb(t,r);let l=-1;if(e){const o=[];this.qb.forEach(a=>{o.push({timeWeight:0,time:a.la,pointData:a,originalTime:ms(a.Hb)})}),o.sort((a,u)=>this.q_.key(a.time)-this.q_.key(u.time)),l=this.Qb(o)}return this.tw(t,l,function(o,a,u){const c=ds(o,u),f=ds(a,u);if(c!==void 0&&f!==void 0)return{ta:c.Ub>=f.Ub&&c.$b>=f.$b}}(this.Yb.get(t),n,this.q_))}vd(t){return this.Kb(t,[])}iw(t,i){const s=i;(function(p){p.zb===void 0&&(p.zb=p.time)})(s),this.q_.preprocessData(i);const e=this.q_.createConverterToInternalObj([i])(i.time),n=this.Zb.get(t);if(n!==void 0&&this.q_.key(e)<this.q_.key(n))throw new Error(`Cannot update oldest data, last time=${n}, new time=${e}`);let r=this.qb.get(this.q_.key(e));const l=r===void 0;r===void 0&&(r=fs(e),this.qb.set(this.q_.key(e),r));const o=cs(t.Qh()),a=t.Ca(),u=t.Ta(),c=o(e,r.ee,i,s.zb,a,u);r.Hb.set(t,c),this.nw(t,c);const f={ta:Mt(c)};if(!l)return this.tw(t,-1,f);const d={timeWeight:0,time:r.la,pointData:r,originalTime:ms(r.Hb)},m=ft(this.Xb,this.q_.key(d.time),(p,b)=>this.q_.key(p.time)<b);this.Xb.splice(m,0,d);for(let p=m;p<this.Xb.length;++p)ti(this.Xb[p].pointData,p);return this.q_.fillWeightsForPoints(this.Xb,m),this.tw(t,m,f)}nw(t,i){let s=this.Yb.get(t);s===void 0&&(s=[],this.Yb.set(t,s));const e=s.length!==0?s[s.length-1]:null;e===null||this.q_.key(i.ot)>this.q_.key(e.ot)?Mt(i)&&s.push(i):Mt(i)?s[s.length-1]=i:s.splice(-1,1),this.Zb.set(t,i.ot)}Jb(t,i){i.length!==0?(this.Yb.set(t,i.filter(Mt)),this.Zb.set(t,i[i.length-1].ot)):(this.Yb.delete(t),this.Zb.delete(t))}Gb(){for(const t of this.Xb)t.pointData.Hb.size===0&&this.qb.delete(this.q_.key(t.time))}Qb(t){let i=-1;for(let s=0;s<this.Xb.length&&s<t.length;++s){const e=this.Xb[s],n=t[s];if(this.q_.key(e.time)!==this.q_.key(n.time)){i=s;break}n.timeWeight=e.timeWeight,ti(n.pointData,s)}if(i===-1&&this.Xb.length!==t.length&&(i=Math.min(this.Xb.length,t.length)),i===-1)return-1;for(let s=i;s<t.length;++s)ti(t[s].pointData,s);return this.q_.fillWeightsForPoints(t,i),this.Xb=t,i}sw(){if(this.Yb.size===0)return null;let t=0;return this.Yb.forEach(i=>{i.length!==0&&(t=Math.max(t,i[i.length-1].ee))}),t}tw(t,i,s){const e={ew:new Map,St:{Eu:this.sw()}};if(i!==-1)this.Yb.forEach((n,r)=>{e.ew.set(r,{$e:n,rw:r===t?s:void 0})}),this.Yb.has(t)||e.ew.set(t,{$e:[],rw:s}),e.St.hw=this.Xb,e.St.lw=i;else{const n=this.Yb.get(t);e.ew.set(t,{$e:n||[],rw:s})}return e}}function ti(h,t){h.ee=t,h.Hb.forEach(i=>{i.ee=t})}function wi(h){const t={value:h.Vt[3],time:h.zb};return h.jb!==void 0&&(t.customValues=h.jb),t}function ps(h){const t=wi(h);return h.V!==void 0&&(t.color=h.V),t}function hn(h){const t=wi(h);return h.lt!==void 0&&(t.lineColor=h.lt),h.Ps!==void 0&&(t.topColor=h.Ps),h.Rs!==void 0&&(t.bottomColor=h.Rs),t}function nn(h){const t=wi(h);return h.Re!==void 0&&(t.topLineColor=h.Re),h.De!==void 0&&(t.bottomLineColor=h.De),h.ke!==void 0&&(t.topFillColor1=h.ke),h.ye!==void 0&&(t.topFillColor2=h.ye),h.Ce!==void 0&&(t.bottomFillColor1=h.Ce),h.Te!==void 0&&(t.bottomFillColor2=h.Te),t}function Xs(h){const t={open:h.Vt[0],high:h.Vt[1],low:h.Vt[2],close:h.Vt[3],time:h.zb};return h.jb!==void 0&&(t.customValues=h.jb),t}function rn(h){const t=Xs(h);return h.V!==void 0&&(t.color=h.V),t}function ln(h){const t=Xs(h),{V:i,Ot:s,Xh:e}=h;return i!==void 0&&(t.color=i),s!==void 0&&(t.borderColor=s),e!==void 0&&(t.wickColor=e),t}function li(h){return{Area:hn,Line:ps,Baseline:nn,Histogram:ps,Bar:rn,Candlestick:ln,Custom:on}[h]}function on(h){const t=h.zb;return Object.assign(Object.assign({},h.$e),{time:t})}const an={vertLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},horzLine:{color:"#9598A1",width:1,style:3,visible:!0,labelVisible:!0,labelBackgroundColor:"#131722"},mode:1},un={vertLines:{color:"#D6DCDE",style:0,visible:!0},horzLines:{color:"#D6DCDE",style:0,visible:!0}},cn={background:{type:"solid",color:"#FFFFFF"},textColor:"#191919",fontSize:12,fontFamily:oi,attributionLogo:!0},ii={autoScale:!0,mode:0,invertScale:!1,alignLabels:!0,borderVisible:!0,borderColor:"#2B2B43",entireTextOnly:!1,visible:!1,ticksVisible:!1,scaleMargins:{bottom:.1,top:.2},minimumWidth:0},fn={rightOffset:0,barSpacing:6,minBarSpacing:.5,fixLeftEdge:!1,fixRightEdge:!1,lockVisibleTimeRangeOnResize:!1,rightBarStaysOnScroll:!1,borderVisible:!0,borderColor:"#2B2B43",visible:!0,timeVisible:!1,secondsVisible:!0,shiftVisibleRangeOnNewBar:!0,allowShiftVisibleRangeOnWhitespaceReplacement:!1,ticksVisible:!1,uniformDistribution:!1,minimumHeight:0,allowBoldLabels:!0},dn={color:"rgba(0, 0, 0, 0)",visible:!1,fontSize:48,fontFamily:oi,fontStyle:"",text:"",horzAlign:"center",vertAlign:"center"};function vs(){return{width:0,height:0,autoSize:!1,layout:cn,crosshair:an,grid:un,overlayPriceScales:Object.assign({},ii),leftPriceScale:Object.assign(Object.assign({},ii),{visible:!1}),rightPriceScale:Object.assign(Object.assign({},ii),{visible:!0}),timeScale:fn,watermark:dn,localization:{locale:Z?navigator.language:"",dateFormat:"dd MMM 'yy"},handleScroll:{mouseWheel:!0,pressedMouseMove:!0,horzTouchDrag:!0,vertTouchDrag:!0},handleScale:{axisPressedMouseMove:{time:!0,price:!0},axisDoubleClickReset:{time:!0,price:!0},mouseWheel:!0,pinch:!0},kineticScroll:{mouse:!1,touch:!0},trackingMode:{exitMode:1}}}class mn{constructor(t,i){this.aw=t,this.ow=i}applyOptions(t){this.aw.$t().$c(this.ow,t)}options(){return this.Li().W()}width(){return Vt(this.ow)?this.aw.Mb(this.ow):0}Li(){return v(this.aw.$t().Uc(this.ow)).Dt}}function bs(h,t,i){const s=Fs(h,["time","originalTime"]),e=Object.assign({time:t},s);return i!==void 0&&(e.originalTime=i),e}const pn={color:"#FF0000",price:0,lineStyle:2,lineWidth:1,lineVisible:!0,axisLabelVisible:!0,title:"",axisLabelColor:"",axisLabelTextColor:""};class vn{constructor(t){this.Nh=t}applyOptions(t){this.Nh.$h(t)}options(){return this.Nh.W()}_w(){return this.Nh}}class bn{constructor(t,i,s,e,n){this.uw=new M,this.Es=t,this.cw=i,this.dw=s,this.q_=n,this.fw=e}S(){this.uw.S()}priceFormatter(){return this.Es.ba()}priceToCoordinate(t){const i=this.Es.Ct();return i===null?null:this.Es.Dt().Rt(t,i.Vt)}coordinateToPrice(t){const i=this.Es.Ct();return i===null?null:this.Es.Dt().pn(t,i.Vt)}barsInLogicalRange(t){if(t===null)return null;const i=new Q(new nt(t.from,t.to)).lu(),s=this.Es.In();if(s.Ni())return null;const e=s.ll(i.Os(),1),n=s.ll(i.ui(),-1),r=v(s.el()),l=v(s.An());if(e!==null&&n!==null&&e.ee>n.ee)return{barsBefore:t.from-r,barsAfter:l-t.to};const o={barsBefore:e===null||e.ee===r?t.from-r:e.ee-r,barsAfter:n===null||n.ee===l?l-t.to:l-n.ee};return e!==null&&n!==null&&(o.from=e.zb,o.to=n.zb),o}setData(t){this.q_,this.Es.Qh(),this.cw.pw(this.Es,t),this.mw("full")}update(t){this.Es.Qh(),this.cw.bw(this.Es,t),this.mw("update")}dataByIndex(t,i){const s=this.Es.In().ll(t,i);return s===null?null:li(this.seriesType())(s)}data(){const t=li(this.seriesType());return this.Es.In().ne().map(i=>t(i))}subscribeDataChanged(t){this.uw.l(t)}unsubscribeDataChanged(t){this.uw.v(t)}setMarkers(t){this.q_;const i=t.map(s=>bs(s,this.q_.convertHorzItemToInternal(s.time),s.time));this.Es.na(i)}markers(){return this.Es.sa().map(t=>bs(t,t.originalTime,void 0))}applyOptions(t){this.Es.$h(t)}options(){return W(this.Es.W())}priceScale(){return this.dw.priceScale(this.Es.Dt().Pa())}createPriceLine(t){const i=R(W(pn),t),s=this.Es.ea(i);return new vn(s)}removePriceLine(t){this.Es.ra(t._w())}seriesType(){return this.Es.Qh()}attachPrimitive(t){this.Es.ka(t),t.attached&&t.attached({chart:this.fw,series:this,requestUpdate:()=>this.Es.$t().Kl()})}detachPrimitive(t){this.Es.ya(t),t.detached&&t.detached()}mw(t){this.uw.M()&&this.uw.m(t)}}class gn{constructor(t,i,s){this.ww=new M,this.mu=new M,this.Om=new M,this.$i=t,this.yl=t.St(),this.ab=i,this.yl.nc().l(this.gw.bind(this)),this.yl.sc().l(this.Mw.bind(this)),this.ab.Nm().l(this.xw.bind(this)),this.q_=s}S(){this.yl.nc().p(this),this.yl.sc().p(this),this.ab.Nm().p(this),this.ww.S(),this.mu.S(),this.Om.S()}scrollPosition(){return this.yl.Hu()}scrollToPosition(t,i){i?this.yl.Ju(t,1e3):this.$i.Jn(t)}scrollToRealTime(){this.yl.Gu()}getVisibleRange(){const t=this.yl.Vu();return t===null?null:{from:t.from.originalTime,to:t.to.originalTime}}setVisibleRange(t){const i={from:this.q_.convertHorzItemToInternal(t.from),to:this.q_.convertHorzItemToInternal(t.to)},s=this.yl.Iu(i);this.$i.pd(s)}getVisibleLogicalRange(){const t=this.yl.Du();return t===null?null:{from:t.Os(),to:t.ui()}}setVisibleLogicalRange(t){I(t.from<=t.to,"The from index cannot be after the to index."),this.$i.pd(t)}resetTimeScale(){this.$i.Kn()}fitContent(){this.$i.hc()}logicalToCoordinate(t){const i=this.$i.St();return i.Ni()?null:i.It(t)}coordinateToLogical(t){return this.yl.Ni()?null:this.yl.Nu(t)}timeToCoordinate(t){const i=this.q_.convertHorzItemToInternal(t),s=this.yl.Va(i,!1);return s===null?null:this.yl.It(s)}coordinateToTime(t){const i=this.$i.St(),s=i.Nu(t),e=i.Ui(s);return e===null?null:e.originalTime}width(){return this.ab.um().width}height(){return this.ab.um().height}subscribeVisibleTimeRangeChange(t){this.ww.l(t)}unsubscribeVisibleTimeRangeChange(t){this.ww.v(t)}subscribeVisibleLogicalRangeChange(t){this.mu.l(t)}unsubscribeVisibleLogicalRangeChange(t){this.mu.v(t)}subscribeSizeChange(t){this.Om.l(t)}unsubscribeSizeChange(t){this.Om.v(t)}applyOptions(t){this.yl.$h(t)}options(){return Object.assign(Object.assign({},W(this.yl.W())),{barSpacing:this.yl.le()})}gw(){this.ww.M()&&this.ww.m(this.getVisibleRange())}Mw(){this.mu.M()&&this.mu.m(this.getVisibleLogicalRange())}xw(t){this.Om.m(t.width,t.height)}}function wn(h){if(h===void 0||h.type==="custom")return;const t=h;t.minMove!==void 0&&t.precision===void 0&&(t.precision=function(i){if(i>=1)return 0;let s=0;for(;s<8;s++){const e=Math.round(i);if(Math.abs(e-i)<1e-8)return s;i*=10}return s}(t.minMove))}function gs(h){return function(t){if(mt(t.handleScale)){const s=t.handleScale;t.handleScale={axisDoubleClickReset:{time:s,price:s},axisPressedMouseMove:{time:s,price:s},mouseWheel:s,pinch:s}}else if(t.handleScale!==void 0){const{axisPressedMouseMove:s,axisDoubleClickReset:e}=t.handleScale;mt(s)&&(t.handleScale.axisPressedMouseMove={time:s,price:s}),mt(e)&&(t.handleScale.axisDoubleClickReset={time:e,price:e})}const i=t.handleScroll;mt(i)&&(t.handleScroll={horzTouchDrag:i,vertTouchDrag:i,mouseWheel:i,pressedMouseMove:i})}(h),h}class yn{constructor(t,i,s){this.Sw=new Map,this.kw=new Map,this.yw=new M,this.Cw=new M,this.Tw=new M,this.Pw=new en(i);const e=s===void 0?W(vs()):R(W(vs()),gs(s));this.q_=i,this.aw=new Jh(t,e,i),this.aw.lm().l(r=>{this.yw.M()&&this.yw.m(this.Rw(r()))},this),this.aw.am().l(r=>{this.Cw.M()&&this.Cw.m(this.Rw(r()))},this),this.aw.Xc().l(r=>{this.Tw.M()&&this.Tw.m(this.Rw(r()))},this);const n=this.aw.$t();this.Dw=new gn(n,this.aw.fb(),this.q_)}remove(){this.aw.lm().p(this),this.aw.am().p(this),this.aw.Xc().p(this),this.Dw.S(),this.aw.S(),this.Sw.clear(),this.kw.clear(),this.yw.S(),this.Cw.S(),this.Tw.S(),this.Pw.S()}resize(t,i,s){this.autoSizeActive()||this.aw._b(t,i,s)}addCustomSeries(t,i){const s=J(t),e=Object.assign(Object.assign({},ws),s.defaultOptions());return this.Vw("Custom",e,i,s)}addAreaSeries(t){return this.Vw("Area",se,t)}addBaselineSeries(t){return this.Vw("Baseline",ee,t)}addBarSeries(t){return this.Vw("Bar",te,t)}addCandlestickSeries(t={}){return function(i){i.borderColor!==void 0&&(i.borderUpColor=i.borderColor,i.borderDownColor=i.borderColor),i.wickColor!==void 0&&(i.wickUpColor=i.wickColor,i.wickDownColor=i.wickColor)}(t),this.Vw("Candlestick",Zs,t)}addHistogramSeries(t){return this.Vw("Histogram",he,t)}addLineSeries(t){return this.Vw("Line",ie,t)}removeSeries(t){const i=k(this.Sw.get(t)),s=this.Pw.vd(i);this.aw.$t().vd(i),this.Ow(s),this.Sw.delete(t),this.kw.delete(i)}pw(t,i){this.Ow(this.Pw.Kb(t,i))}bw(t,i){this.Ow(this.Pw.iw(t,i))}subscribeClick(t){this.yw.l(t)}unsubscribeClick(t){this.yw.v(t)}subscribeCrosshairMove(t){this.Tw.l(t)}unsubscribeCrosshairMove(t){this.Tw.v(t)}subscribeDblClick(t){this.Cw.l(t)}unsubscribeDblClick(t){this.Cw.v(t)}priceScale(t){return new mn(this.aw,t)}timeScale(){return this.Dw}applyOptions(t){this.aw.$h(gs(t))}options(){return this.aw.W()}takeScreenshot(){return this.aw.wb()}autoSizeActive(){return this.aw.kb()}chartElement(){return this.aw.yb()}paneSize(){const t=this.aw.Tb();return{height:t.height,width:t.width}}setCrosshairPosition(t,i,s){const e=this.Sw.get(s);if(e===void 0)return;const n=this.aw.$t().dr(e);n!==null&&this.aw.$t().ad(t,i,n)}clearCrosshairPosition(){this.aw.$t().od(!0)}Vw(t,i,s={},e){wn(s.priceFormat);const n=R(W(ys),W(i),s),r=this.aw.$t().dd(t,n,e),l=new bn(r,this,this,this,this.q_);return this.Sw.set(l,r),this.kw.set(r,l),l}Ow(t){const i=this.aw.$t();i._d(t.St.Eu,t.St.hw,t.St.lw),t.ew.forEach((s,e)=>e.J(s.$e,s.rw)),i.Wu()}Bw(t){return k(this.kw.get(t))}Rw(t){const i=new Map;t.Nb.forEach((e,n)=>{const r=n.Qh(),l=li(r)(e);if(r!=="Custom")I(Gh(l));else{const o=n.Ta();I(!o||o(l)===!1)}i.set(this.Bw(n),l)});const s=t.Eb!==void 0&&this.kw.has(t.Eb)?this.Bw(t.Eb):void 0;return{time:t.zb,logical:t.ee,point:t.Lb,hoveredSeries:s,hoveredObjectId:t.Fb,seriesData:i,sourceEvent:t.Wb}}}function Sn(h,t,i){let s;if(ct(h)){const n=document.getElementById(h);I(n!==null,`Cannot find element in DOM with id=${h}`),s=n}else s=h;const e=new yn(s,t,i);return t.setOptions(e.options()),e}function _n(h,t){return Sn(h,new es,es.Id(t))}Object.assign(Object.assign({},ys),ws);export{_n as V};
