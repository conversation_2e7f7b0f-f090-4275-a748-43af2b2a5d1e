import { JSX } from 'solid-js'

interface SimpleLayoutProps {
  children: JSX.Element
}

export default function SimpleLayout(props: SimpleLayoutProps) {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      background: '#f5f7fa'
    }}>
      {/* 简单的头部 */}
      <header style={{
        background: 'white',
        padding: '16px 24px',
        borderBottom: '1px solid #e8e8e8',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{
          margin: 0,
          color: '#1890ff',
          fontSize: '24px',
          fontWeight: 'bold'
        }}>
          量化交易平台
        </h1>
      </header>

      {/* 主要内容区域 */}
      <main style={{
        flex: 1,
        padding: '24px',
        overflow: 'auto'
      }}>
        {props.children}
      </main>
    </div>
  )
}