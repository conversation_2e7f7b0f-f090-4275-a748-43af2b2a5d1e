import{c as L,k as Te,t as k,i as I,a as T,F as pe,b as C,e as t,S as xe,s as qe,d as Ae}from"./vendor-solid-gbiNAvvL.js";import{c as i}from"./index-CLkUOMvU.js";import"./vendor-editor-BmK5o4Wa.js";class De{constructor(a,s){this.parameterRanges=a,this.evaluateFunction=s}async optimize(a){const s=[],o=this.generateParameterCombinations();let l=0;for(const n of o){if(l>=a.maxIterations)break;try{const v=await this.evaluateFunction(n),p=this.extractScore(v,a.targetMetric);s.push({parameters:n,score:p,metrics:v}),l++}catch(v){console.warn("参数组合评估失败:",n,v)}}return s.sort((n,v)=>a.maximize?v.score-n.score:n.score-v.score),s}generateParameterCombinations(){const a=[],s=(o,l)=>{if(o>=this.parameterRanges.length){a.push({...l});return}const n=this.parameterRanges[o],v=n.step||(n.type==="int"?1:(n.max-n.min)/10);for(let p=n.min;p<=n.max;p+=v){const j=n.type==="int"?Math.round(p):p;l[n.name]=j,s(o+1,l)}};return s(0,{}),a}extractScore(a,s){return a[s]||0}}class Ve{constructor(a,s,o={}){this.parameterRanges=a,this.evaluateFunction=s,this.populationSize=o.populationSize||50,this.mutationRate=o.mutationRate||.1,this.crossoverRate=o.crossoverRate||.8}async optimize(a){let s=this.initializePopulation();const o=[];for(let l=0;l<a.maxIterations;l++){const n=await this.evaluatePopulation(s,a.targetMetric),v=n[0];o.push(v),s=this.evolvePopulation(n,a.maximize)}return o.sort((l,n)=>a.maximize?n.score-l.score:l.score-n.score)}initializePopulation(){const a=[];for(let s=0;s<this.populationSize;s++){const o={};for(const l of this.parameterRanges){const n=Math.random()*(l.max-l.min)+l.min;o[l.name]=l.type==="int"?Math.round(n):n}a.push(o)}return a}async evaluatePopulation(a,s){const o=[];for(const l of a)try{const n=await this.evaluateFunction(l),v=n[s]||0;o.push({parameters:l,score:v,metrics:n})}catch{o.push({parameters:l,score:-1/0,metrics:{}})}return o.sort((l,n)=>n.score-l.score)}evolvePopulation(a,s){const o=[],l=Math.floor(this.populationSize*.1);for(let n=0;n<l;n++)o.push({...a[n].parameters});for(;o.length<this.populationSize;){const n=this.selectParent(a),v=this.selectParent(a);let p=this.crossover(n.parameters,v.parameters);p=this.mutate(p),o.push(p)}return o}selectParent(a){const o=[];for(let l=0;l<3;l++){const n=Math.floor(Math.random()*a.length);o.push(a[n])}return o.sort((l,n)=>n.score-l.score)[0]}crossover(a,s){const o={};for(const l of this.parameterRanges)if(Math.random()<this.crossoverRate){const n=Math.random(),v=n*a[l.name]+(1-n)*s[l.name];o[l.name]=l.type==="int"?Math.round(v):v}else o[l.name]=Math.random()<.5?a[l.name]:s[l.name];return o}mutate(a){const s={...a};for(const o of this.parameterRanges)if(Math.random()<this.mutationRate){const l=(o.max-o.min)*.1,n=this.gaussianRandom()*l;let v=s[o.name]+n;v=Math.max(o.min,Math.min(o.max,v)),s[o.name]=o.type==="int"?Math.round(v):v}return s}gaussianRandom(){let a=0,s=0;for(;a===0;)a=Math.random();for(;s===0;)s=Math.random();return Math.sqrt(-2*Math.log(a))*Math.cos(2*Math.PI*s)}}class Be{constructor(a,s){this.observations=[],this.parameterRanges=a,this.evaluateFunction=s}async optimize(a){const s=[],o=Math.min(10,a.maxIterations);for(let l=0;l<o;l++){const n=this.randomSample(),v=await this.evaluateFunction(n),p=v[a.targetMetric]||0;this.observations.push({params:n,score:p}),s.push({parameters:n,score:p,metrics:v})}for(let l=o;l<a.maxIterations;l++){const n=this.acquireNext(),v=await this.evaluateFunction(n),p=v[a.targetMetric]||0;this.observations.push({params:n,score:p}),s.push({parameters:n,score:p,metrics:v})}return s.sort((l,n)=>a.maximize?n.score-l.score:l.score-n.score)}randomSample(){const a={};for(const s of this.parameterRanges){const o=Math.random()*(s.max-s.min)+s.min;a[s.name]=s.type==="int"?Math.round(o):o}return a}acquireNext(){if(this.observations.length===0)return this.randomSample();const a=this.observations.reduce((o,l)=>l.score>o.score?l:o),s={};for(const o of this.parameterRanges){const l=a.params[o.name],n=(o.max-o.min)*.2,v=l+this.gaussianRandom()*n,p=Math.max(o.min,Math.min(o.max,v));s[o.name]=o.type==="int"?Math.round(p):p}return s}gaussianRandom(){let a=0,s=0;for(;a===0;)a=Math.random();for(;s===0;)s=Math.random();return Math.sqrt(-2*Math.log(a))*Math.cos(2*Math.PI*s)}}var Ne=k("<button>开始优化"),Ge=k("<div><div>优化进行中... <!>%</div><div><div>"),Ye=k("<div><h4>最佳参数组合</h4><div></div><div><span>: </span><span>"),Je=k("<div><h3>优化结果</h3><div>"),Ke=k("<div><div><h3>参数优化配置</h3><div><div><label>优化方法</label><select><option value=grid>网格搜索</option><option value=genetic>遗传算法</option><option value=bayesian>贝叶斯优化</option></select></div><div><label>目标指标</label><select><option value=sharpeRatio>夏普比率</option><option value=calmarRatio>卡尔马比率</option><option value=totalReturn>总收益率</option><option value=winRate>胜率</option></select></div><div><label>最大迭代次数</label><input type=number min=10 max=1000></div></div><div><div><h4>参数范围</h4><button>添加参数</button></div><div></div></div><div>"),Qe=k("<div><input type=text placeholder=参数名><input type=number placeholder=最小值 step=0.01><input type=number placeholder=最大值 step=0.01><input type=number placeholder=步长 step=0.01><select><option value=int>整数</option><option value=float>小数</option></select><button>×"),Ue=k("<button>停止优化"),Xe=k("<div><div></div><div>"),Ze=k("<div><div><div></div><div></div></div><div>");function et(B){const[a,s]=L("grid"),[o,l]=L("sharpeRatio"),[n,v]=L(100),[p,j]=L(!1),[N,G]=L(0),[Q,de]=L([]),[q,A]=L([{name:"shortPeriod",min:5,max:20,step:1,type:"int"},{name:"longPeriod",min:20,max:60,step:1,type:"int"},{name:"stopLoss",min:.02,max:.1,step:.01,type:"float"},{name:"takeProfit",min:.05,max:.2,step:.01,type:"float"}]),U=()=>{A(R=>[...R,{name:`param${R.length+1}`,min:0,max:100,step:1,type:"int"}])},X=R=>{A(z=>z.filter(($,_)=>_!==R))},D=(R,z,$)=>{A(_=>_.map((O,F)=>F===R?{...O,[z]:$}:O))},Z=async R=>{await new Promise(E=>setTimeout(E,100));const z=Math.random(),$=(z-.3)*.5,_=.1+z*.2,O=$/_,F=-z*.15,H=$/Math.abs(F);return{totalReturn:$,sharpeRatio:O,calmarRatio:H,volatility:_,maxDrawdown:F,winRate:.4+z*.4}},me=async()=>{j(!0),G(0),de([]);const R={method:a(),maxIterations:n(),targetMetric:o(),maximize:["sharpeRatio","calmarRatio","totalReturn","winRate"].includes(o())};try{let z;switch(a()){case"grid":z=new De(q(),Z);break;case"genetic":z=new Ve(q(),Z);break;case"bayesian":z=new Be(q(),Z);break}const $=await z.optimize(R);de($),B.onOptimizationComplete?.($)}catch(z){console.error("优化过程出错:",z)}finally{j(!1),G(100)}},ce=()=>{j(!1)},V=Te(()=>Q()[0]);return(()=>{var R=Ke(),z=R.firstChild,$=z.firstChild,_=$.nextSibling,O=_.firstChild,F=O.firstChild,H=F.nextSibling,E=O.nextSibling,ee=E.firstChild,te=ee.nextSibling,ie=E.nextSibling,oe=ie.firstChild,re=oe.nextSibling,Y=_.nextSibling,J=Y.firstChild,ve=J.firstChild,ne=ve.nextSibling,K=J.nextSibling,ae=Y.nextSibling;return H.addEventListener("change",r=>s(r.target.value)),te.addEventListener("change",r=>l(r.target.value)),re.addEventListener("change",r=>v(parseInt(r.target.value))),ne.$$click=U,I(K,T(pe,{get each(){return q()},children:(r,h)=>(()=>{var e=Qe(),m=e.firstChild,g=m.nextSibling,u=g.nextSibling,x=u.nextSibling,b=x.nextSibling,P=b.nextSibling;return m.addEventListener("change",c=>D(h(),"name",c.target.value)),g.addEventListener("change",c=>D(h(),"min",parseFloat(c.target.value))),u.addEventListener("change",c=>D(h(),"max",parseFloat(c.target.value))),x.addEventListener("change",c=>D(h(),"step",parseFloat(c.target.value)||void 0)),b.addEventListener("change",c=>D(h(),"type",c.target.value)),P.$$click=()=>X(h()),C(c=>{var d=i({display:"grid",gridTemplateColumns:"1fr 1fr 1fr 1fr 80px 40px",gap:"8px",alignItems:"center",p:"12px",bg:"#fafafa",borderRadius:"8px"}),f=i({p:"6px 8px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px"}),y=i({p:"6px 8px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px"}),w=i({p:"6px 8px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px"}),M=i({p:"6px 8px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px"}),S=i({p:"6px 8px",border:"1px solid #d9d9d9",borderRadius:"4px",fontSize:"12px"}),W=i({p:"6px",bg:"#ff4d4f",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",cursor:"pointer",_hover:{bg:"#ff7875"}});return d!==c.e&&t(e,c.e=d),f!==c.t&&t(m,c.t=f),y!==c.a&&t(g,c.a=y),w!==c.o&&t(u,c.o=w),M!==c.i&&t(x,c.i=M),S!==c.n&&t(b,c.n=S),W!==c.s&&t(P,c.s=W),c},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0}),C(()=>m.value=r.name),C(()=>g.value=r.min),C(()=>u.value=r.max),C(()=>x.value=r.step||""),C(()=>b.value=r.type),e})()})),I(ae,T(xe,{get when(){return!p()},get fallback(){return(()=>{var r=Ue();return r.$$click=ce,C(()=>t(r,i({px:"24px",py:"12px",bg:"#ff4d4f",color:"white",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:"pointer",_hover:{bg:"#ff7875"}}))),r})()},get children(){var r=Ne();return r.$$click=me,C(h=>{var e=q().length===0,m=i({px:"24px",py:"12px",bg:"#52c41a",color:"white",border:"none",borderRadius:"8px",fontSize:"14px",fontWeight:"500",cursor:"pointer",_hover:{bg:"#73d13d"},_disabled:{bg:"#d9d9d9",cursor:"not-allowed"}});return e!==h.e&&(r.disabled=h.e=e),m!==h.t&&t(r,h.t=m),h},{e:void 0,t:void 0}),r}})),I(z,T(xe,{get when(){return p()},get children(){var r=Ge(),h=r.firstChild,e=h.firstChild,m=e.nextSibling;m.nextSibling;var g=h.nextSibling,u=g.firstChild;return I(h,N,m),C(x=>{var b=i({mt:"16px",p:"16px",bg:"#f6ffed",borderRadius:"8px",border:"1px solid #b7eb8f"}),P=i({fontSize:"14px",color:"#52c41a",mb:"8px"}),c=i({width:"100%",height:"8px",bg:"#f0f0f0",borderRadius:"4px",overflow:"hidden"}),d=i({height:"100%",bg:"#52c41a",transition:"width 0.3s ease"}),f=`${N()}%`;return b!==x.e&&t(r,x.e=b),P!==x.t&&t(h,x.t=P),c!==x.a&&t(g,x.a=c),d!==x.o&&t(u,x.o=d),f!==x.i&&qe(u,"width",x.i=f),x},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),r}}),null),I(R,T(xe,{get when(){return Q().length>0},get children(){var r=Je(),h=r.firstChild,e=h.nextSibling;return I(r,T(xe,{get when(){return V()},get children(){var m=Ye(),g=m.firstChild,u=g.nextSibling,x=u.nextSibling,b=x.firstChild,P=b.firstChild,c=b.nextSibling;return I(u,T(pe,{get each(){return Object.entries(V().parameters)},children:([d,f])=>(()=>{var y=Xe(),w=y.firstChild,M=w.nextSibling;return I(w,d),I(M,()=>typeof f=="number"?f.toFixed(3):f),C(S=>{var W=i({textAlign:"center"}),le=i({fontSize:"12px",color:"#8c8c8c",mb:"4px"}),se=i({fontSize:"16px",fontWeight:"600",color:"#262626"});return W!==S.e&&t(y,S.e=W),le!==S.t&&t(w,S.t=le),se!==S.a&&t(M,S.a=se),S},{e:void 0,t:void 0,a:void 0}),y})()})),I(b,o,P),I(c,()=>V().score.toFixed(4)),C(d=>{var f=i({p:"16px",bg:"#f6ffed",borderRadius:"8px",border:"1px solid #b7eb8f",mb:"20px"}),y=i({fontSize:"16px",fontWeight:"600",color:"#52c41a",margin:0,mb:"12px"}),w=i({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"12px"}),M=i({mt:"12px",textAlign:"center"}),S=i({fontSize:"14px",color:"#8c8c8c"}),W=i({fontSize:"18px",fontWeight:"700",color:"#52c41a",ml:"8px"});return f!==d.e&&t(m,d.e=f),y!==d.t&&t(g,d.t=y),w!==d.a&&t(u,d.a=w),M!==d.o&&t(x,d.o=M),S!==d.i&&t(b,d.i=S),W!==d.n&&t(c,d.n=W),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0}),m}}),e),I(e,T(pe,{get each(){return Q().slice(0,10)},children:(m,g)=>(()=>{var u=Ze(),x=u.firstChild,b=x.firstChild,P=b.nextSibling,c=x.nextSibling;return I(b,()=>g()+1),I(P,()=>Object.entries(m.parameters).map(([d,f])=>`${d}=${typeof f=="number"?f.toFixed(2):f}`).join(", ")),I(c,()=>m.score.toFixed(4)),C(d=>{var f=i({display:"flex",alignItems:"center",justifyContent:"space-between",p:"12px",borderBottom:"1px solid #f0f0f0",_hover:{bg:"#fafafa"}}),y=i({display:"flex",alignItems:"center",gap:"12px"}),w=i({width:"24px",height:"24px",bg:g()===0?"#52c41a":"#f0f0f0",color:g()===0?"white":"#8c8c8c",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"12px",fontWeight:"600"}),M=i({fontSize:"14px",color:"#262626"}),S=i({fontSize:"16px",fontWeight:"600",color:g()===0?"#52c41a":"#262626"});return f!==d.e&&t(u,d.e=f),y!==d.t&&t(x,d.t=y),w!==d.a&&t(b,d.a=w),M!==d.o&&t(P,d.o=M),S!==d.i&&t(c,d.i=S),d},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),u})()})),C(m=>{var g=i({bg:"white",borderRadius:"12px",p:"24px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}),u=i({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0,mb:"20px"}),x=i({maxHeight:"400px",overflowY:"auto"});return g!==m.e&&t(r,m.e=g),u!==m.t&&t(h,m.t=u),x!==m.a&&t(e,m.a=x),m},{e:void 0,t:void 0,a:void 0}),r}}),null),C(r=>{var h=i({display:"flex",flexDirection:"column",gap:"24px"}),e=i({bg:"white",borderRadius:"12px",p:"24px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}),m=i({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0,mb:"20px"}),g=i({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",mb:"24px"}),u=i({display:"block",fontSize:"14px",fontWeight:"500",color:"#262626",mb:"8px"}),x=i({width:"100%",p:"8px 12px",border:"1px solid #d9d9d9",borderRadius:"6px",fontSize:"14px"}),b=i({display:"block",fontSize:"14px",fontWeight:"500",color:"#262626",mb:"8px"}),P=i({width:"100%",p:"8px 12px",border:"1px solid #d9d9d9",borderRadius:"6px",fontSize:"14px"}),c=i({display:"block",fontSize:"14px",fontWeight:"500",color:"#262626",mb:"8px"}),d=i({width:"100%",p:"8px 12px",border:"1px solid #d9d9d9",borderRadius:"6px",fontSize:"14px"}),f=i({mb:"24px"}),y=i({display:"flex",alignItems:"center",justifyContent:"space-between",mb:"16px"}),w=i({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),M=i({px:"12px",py:"6px",bg:"#1890ff",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",cursor:"pointer",_hover:{bg:"#40a9ff"}}),S=i({display:"flex",flexDirection:"column",gap:"12px"}),W=i({display:"flex",gap:"12px"});return h!==r.e&&t(R,r.e=h),e!==r.t&&t(z,r.t=e),m!==r.a&&t($,r.a=m),g!==r.o&&t(_,r.o=g),u!==r.i&&t(F,r.i=u),x!==r.n&&t(H,r.n=x),b!==r.s&&t(ee,r.s=b),P!==r.h&&t(te,r.h=P),c!==r.r&&t(oe,r.r=c),d!==r.d&&t(re,r.d=d),f!==r.l&&t(Y,r.l=f),y!==r.u&&t(J,r.u=y),w!==r.c&&t(ve,r.c=w),M!==r.w&&t(ne,r.w=M),S!==r.m&&t(K,r.m=S),W!==r.f&&t(ae,r.f=W),r},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0}),C(()=>H.value=a()),C(()=>te.value=o()),C(()=>re.value=n()),R})()}Ae(["click"]);var tt=k("<div><div><h1>🔧 参数优化</h1><p>使用先进的优化算法找到策略的最佳参数组合</p></div><div><div><div><div><span>🔍</span></div><h3>网格搜索</h3></div><p>系统性地搜索参数空间的每个组合，保证找到全局最优解，适合参数较少的情况。</p></div><div><div><div><span>🧬</span></div><h3>遗传算法</h3></div><p>模拟生物进化过程，通过选择、交叉和变异操作寻找最优解，适合复杂的多参数优化。</p></div><div><div><div><span>🎯</span></div><h3>贝叶斯优化</h3></div><p>基于概率模型的智能搜索，能够在较少的评估次数下找到最优解，适合计算成本高的场景。</p></div></div><div><h3>💡 优化建议</h3><div><div><h4>参数设置</h4><ul><li>合理设置参数范围，避免过大或过小</li><li>考虑参数之间的约束关系</li><li>使用合适的步长，平衡精度和效率</li></ul></div><div><h4>目标选择</h4><ul><li>夏普比率：综合考虑收益和风险</li><li>卡尔马比率：关注回撤控制</li><li>总收益率：追求绝对收益</li></ul></div><div><h4>方法选择</h4><ul><li>参数少（≤4个）：使用网格搜索</li><li>参数多（5-10个）：使用遗传算法</li><li>计算成本高：使用贝叶斯优化</li></ul></div></div></div><div><div><span>⚠️</span><h4>注意事项</h4></div><p>参数优化存在过拟合风险，建议使用样本外数据验证优化结果。优化后的参数可能在历史数据上表现良好，但在未来市场中未必有效。 建议结合多个时间段的数据进行验证，并定期重新优化参数。");function nt(){const[,B]=L([]),[,a]=L(!1),s=o=>{B(o),a(!0)};return(()=>{var o=tt(),l=o.firstChild,n=l.firstChild,v=n.nextSibling,p=l.nextSibling,j=p.firstChild,N=j.firstChild,G=N.firstChild,Q=G.firstChild,de=G.nextSibling,q=N.nextSibling,A=j.nextSibling,U=A.firstChild,X=U.firstChild,D=X.firstChild,Z=X.nextSibling,me=U.nextSibling,ce=A.nextSibling,V=ce.firstChild,R=V.firstChild,z=R.firstChild,$=R.nextSibling,_=V.nextSibling,O=p.nextSibling,F=O.firstChild,H=F.nextSibling,E=H.firstChild,ee=E.firstChild,te=ee.nextSibling,ie=E.nextSibling,oe=ie.firstChild,re=oe.nextSibling,Y=ie.nextSibling,J=Y.firstChild,ve=J.nextSibling,ne=O.nextSibling,K=ne.firstChild,ae=K.firstChild,r=ae.nextSibling,h=K.nextSibling;return I(o,T(et,{onOptimizationComplete:s}),O),C(e=>{var m=i({display:"flex",flexDirection:"column",gap:"24px",maxWidth:"1200px",margin:"0 auto",p:"24px"}),g=i({textAlign:"center",mb:"20px"}),u=i({fontSize:"28px",fontWeight:"700",color:"#262626",margin:0,mb:"8px"}),x=i({fontSize:"16px",color:"#8c8c8c",margin:0}),b=i({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"16px",mb:"24px"}),P=i({bg:"white",borderRadius:"12px",p:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",border:"1px solid #f0f0f0"}),c=i({display:"flex",alignItems:"center",mb:"12px"}),d=i({width:"40px",height:"40px",bg:"#1890ff",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",mr:"12px"}),f=i({fontSize:"20px",color:"white"}),y=i({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),w=i({fontSize:"14px",color:"#8c8c8c",lineHeight:"1.5",margin:0}),M=i({bg:"white",borderRadius:"12px",p:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",border:"1px solid #f0f0f0"}),S=i({display:"flex",alignItems:"center",mb:"12px"}),W=i({width:"40px",height:"40px",bg:"#52c41a",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",mr:"12px"}),le=i({fontSize:"20px",color:"white"}),se=i({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),he=i({fontSize:"14px",color:"#8c8c8c",lineHeight:"1.5",margin:0}),fe=i({bg:"white",borderRadius:"12px",p:"20px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",border:"1px solid #f0f0f0"}),ge=i({display:"flex",alignItems:"center",mb:"12px"}),ue=i({width:"40px",height:"40px",bg:"#722ed1",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",mr:"12px"}),be=i({fontSize:"20px",color:"white"}),Se=i({fontSize:"16px",fontWeight:"600",color:"#262626",margin:0}),ze=i({fontSize:"14px",color:"#8c8c8c",lineHeight:"1.5",margin:0}),Re=i({bg:"white",borderRadius:"12px",p:"24px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}),ye=i({fontSize:"18px",fontWeight:"600",color:"#262626",margin:0,mb:"16px"}),we=i({display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"16px"}),Ce=i({p:"16px",bg:"#f6ffed",borderRadius:"8px",border:"1px solid #b7eb8f"}),Me=i({fontSize:"14px",fontWeight:"600",color:"#52c41a",margin:0,mb:"8px"}),Pe=i({fontSize:"12px",color:"#8c8c8c",lineHeight:"1.5",margin:0,paddingLeft:"16px"}),Ie=i({p:"16px",bg:"#fff7e6",borderRadius:"8px",border:"1px solid #ffd591"}),$e=i({fontSize:"14px",fontWeight:"600",color:"#fa8c16",margin:0,mb:"8px"}),We=i({fontSize:"12px",color:"#8c8c8c",lineHeight:"1.5",margin:0,paddingLeft:"16px"}),_e=i({p:"16px",bg:"#f0f5ff",borderRadius:"8px",border:"1px solid #adc6ff"}),Oe=i({fontSize:"14px",fontWeight:"600",color:"#1890ff",margin:0,mb:"8px"}),Fe=i({fontSize:"12px",color:"#8c8c8c",lineHeight:"1.5",margin:0,paddingLeft:"16px"}),ke=i({bg:"#fff2f0",borderRadius:"8px",p:"16px",border:"1px solid #ffccc7"}),je=i({display:"flex",alignItems:"center",mb:"8px"}),Le=i({fontSize:"16px",mr:"8px"}),He=i({fontSize:"14px",fontWeight:"600",color:"#ff4d4f",margin:0}),Ee=i({fontSize:"12px",color:"#8c8c8c",lineHeight:"1.5",margin:0});return m!==e.e&&t(o,e.e=m),g!==e.t&&t(l,e.t=g),u!==e.a&&t(n,e.a=u),x!==e.o&&t(v,e.o=x),b!==e.i&&t(p,e.i=b),P!==e.n&&t(j,e.n=P),c!==e.s&&t(N,e.s=c),d!==e.h&&t(G,e.h=d),f!==e.r&&t(Q,e.r=f),y!==e.d&&t(de,e.d=y),w!==e.l&&t(q,e.l=w),M!==e.u&&t(A,e.u=M),S!==e.c&&t(U,e.c=S),W!==e.w&&t(X,e.w=W),le!==e.m&&t(D,e.m=le),se!==e.f&&t(Z,e.f=se),he!==e.y&&t(me,e.y=he),fe!==e.g&&t(ce,e.g=fe),ge!==e.p&&t(V,e.p=ge),ue!==e.b&&t(R,e.b=ue),be!==e.T&&t(z,e.T=be),Se!==e.A&&t($,e.A=Se),ze!==e.O&&t(_,e.O=ze),Re!==e.I&&t(O,e.I=Re),ye!==e.S&&t(F,e.S=ye),we!==e.W&&t(H,e.W=we),Ce!==e.C&&t(E,e.C=Ce),Me!==e.B&&t(ee,e.B=Me),Pe!==e.v&&t(te,e.v=Pe),Ie!==e.k&&t(ie,e.k=Ie),$e!==e.x&&t(oe,e.x=$e),We!==e.j&&t(re,e.j=We),_e!==e.q&&t(Y,e.q=_e),Oe!==e.z&&t(J,e.z=Oe),Fe!==e.P&&t(ve,e.P=Fe),ke!==e.H&&t(ne,e.H=ke),je!==e.F&&t(K,e.F=je),Le!==e.M&&t(ae,e.M=Le),He!==e.D&&t(r,e.D=He),Ee!==e.R&&t(h,e.R=Ee),e},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0,b:void 0,T:void 0,A:void 0,O:void 0,I:void 0,S:void 0,W:void 0,C:void 0,B:void 0,v:void 0,k:void 0,x:void 0,j:void 0,q:void 0,z:void 0,P:void 0,H:void 0,F:void 0,M:void 0,D:void 0,R:void 0}),o})()}export{nt as default};
