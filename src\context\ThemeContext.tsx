import { createContext, createSignal, useContext, onMount, type ParentProps } from 'solid-js';

type Theme = 'light' | 'dark';

interface ThemeContextValue {
  theme: () => Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextValue>();

export function ThemeProvider(props: ParentProps) {
  // 简化版本：使用localStorage直接存储
  const getInitialTheme = ()' : Theme => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('theme') as Theme;
      if (stored) return stored;

      // 检测系统主题偏好
      if (window.matchMedia) {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' ' : 'light';
      }
    }

    return 'light';
  };

  const [theme, setThemeSignal] = createSignal<Theme>(getInitialTheme());

  // 设置主题
  const setTheme = (newTheme: Theme) => {
    setThemeSignal(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', newTheme);
    }
    applyTheme(newTheme);
  };

  // 切换主题
  const toggleTheme = () => {
    setTheme(theme() === 'light' ? 'dark' ' : 'light');
  };
  
  // 应用主题到DOM
  const applyTheme = (currentTheme: Theme) => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement;
      
      if (currentTheme === 'dark') {
        root.classList.add('dark');
        root.classList.remove('light');
      } else {
        root.classList.add('light');
        root.classList.remove('dark');
      }
      
      // 更新meta标签的主题色
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute('content', currentTheme === 'dark' ? '#111827' ' : '#3b82f6');
      }
    }
  };
  
  // 监听系统主题变化
  onMount(() => {
    // 应用初始主题
    applyTheme(theme());
    
    // 监听系统主题变化
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e: MediaQueryListEvent) => {
        // 只有在用户没有手动设置主题时才跟随系统
        if (!localStorage.getItem('theme')) {
          setTheme(e.matches ? 'dark' ' : 'light');
        }
      };
      
      mediaQuery.addEventListener('change', handleChange);
      
      // 清理监听器
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  });
  
  const contextValue: ThemeContextValue = {
    theme,
    setTheme,
    toggleTheme,
  };
  
  return (
    <ThemeContext.Provider value={contextValue}>
      {props.children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
