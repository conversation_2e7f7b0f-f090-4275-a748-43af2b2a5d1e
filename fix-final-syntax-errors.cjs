const fs = require('fs');
const path = require('path');

// 修复所有剩余语法错误的模式
function fixFinalSyntaxErrors(content) {
  let fixedContent = content;
  
  // 1. 修复CSS属性名后面的错误格式
  // 模式: property ' : 'value'
  fixedContent = fixedContent.replace(/(\w+)\s*'\s*:\s*'([^']*?)'/g, "$1: '$2'");
  
  // 2. 修复函数参数中的类型注解错误
  // 模式: parameter' : type
  fixedContent = fixedContent.replace(/(\w+)'\s*:\s*(\w+)/g, "$1: $2");
  
  // 3. 修复console.error中的字符串错误
  // 模式: 'text' : '
  fixedContent = fixedContent.replace(/'([^']*?)'\s*:\s*'/g, "'$1:'");
  
  // 4. 修复对象属性中的错误
  // 模式: property : value ' : 'other'
  fixedContent = fixedContent.replace(/:\s*([^']+?)\s*'\s*:\s*'([^']*?)'/g, ": $1 : '$2'");
  
  // 5. 修复更复杂的CSS属性错误
  fixedContent = fixedContent.replace(/(\w+)\s*'\s*:\s*/g, "$1: ");
  
  // 6. 修复字符串连接错误
  fixedContent = fixedContent.replace(/'([^']*?)'\s*'\s*:/g, "'$1':");
  
  // 7. 修复三元运算符中的错误
  fixedContent = fixedContent.replace(/\?\s*'([^']*?)'\s*'\s*:/g, "? '$1' :");
  
  // 8. 修复对象键名错误
  fixedContent = fixedContent.replace(/(\s+)(\w+)\s*'\s*:/g, "$1$2:");
  
  return fixedContent;
}

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    
    // 应用修复
    content = fixFinalSyntaxErrors(content);
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${path.basename(filePath)}: final syntax errors`);
      return true;
    } else {
      console.log(`✓  ${path.basename(filePath)}: no final syntax errors found`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔧 Fixing final syntax errors...\n');
  
  // 需要修复的文件
  const filesToFix = [
    'src/pages/Dashboard.tsx',
    'src/pages/StrategyEditor.tsx',
    'src/pages/BacktestAnalysis.tsx',
    'src/pages/MarketData.tsx',
    'src/pages/ParameterOptimization.tsx',
    'src/components/AdvancedLayout.tsx',
    'src/components/BacktestCharts.tsx',
    'src/components/ParameterOptimizer.tsx',
    'src/components/MarketNews.tsx',
    'src/components/PortfolioOverview.tsx',
    'src/App.tsx'
  ];
  
  let totalFixed = 0;
  
  for (const filePath of filesToFix) {
    if (fs.existsSync(filePath)) {
      if (fixFile(filePath)) {
        totalFixed++;
      }
    } else {
      console.warn(`⚠️  File not found: ${filePath}`);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`✨ Complete! Fixed ${totalFixed} files with final syntax errors.`);
  
  if (totalFixed > 0) {
    console.log('\n📋 Next steps:');
    console.log('1. Run "npm run build" to test production build');
    console.log('2. Run "npm run dev" to test development server');
  }
}

main();
