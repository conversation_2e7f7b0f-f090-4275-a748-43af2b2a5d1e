const fs = require('fs');
const path = require('path');

console.log('🚨 Critical repair of remaining syntax errors...');

function criticalRepair(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  const originalContent = content;

  // 1. Fix broken time strings
  // '2023-01-01T00': 00:00Z' -> '2023-01-01T00:00:00Z'
  content = content.replace(/'(\d{4}-\d{2}-\d{2}T\d{2})'\s*:\s*(\d{2}:\d{2}Z?)'/g, "'$1:$2'");
  if (content !== originalContent) modified = true;

  // 2. Fix broken time strings without quotes
  // '09': 30' -> '09:30'
  content = content.replace(/'(\d{2})'\s*:\s*(\d{2})'/g, "'$1:$2'");
  if (content !== originalContent) modified = true;

  // 3. Fix broken URLs
  // 'http': //localhost -> 'http://localhost'
  content = content.replace(/'(https?)'\s*:\s*\/\//g, "'$1://");
  if (content !== originalContent) modified = true;

  // 4. Fix broken CSS gradient values
  // 'background': linear-gradient(...) -> 'background': 'linear-gradient(...)'
  content = content.replace(/'([a-zA-Z-]+)':\s*(linear-gradient\([^)]+\))/g, "'$1': '$2'");
  if (content !== originalContent) modified = true;

  // 5. Fix broken rgba values
  // 'color': rgba(...) -> 'color': 'rgba(...)'
  content = content.replace(/'([a-zA-Z-]+)':\s*(rgba?\([^)]+\))/g, "'$1': '$2'");
  if (content !== originalContent) modified = true;

  // 6. Fix broken template literal expressions
  // ${expr}': -> ${expr}',
  content = content.replace(/\$\{[^}]+\}'\s*:/g, (match) => match.replace("':", "',"));
  if (content !== originalContent) modified = true;

  // 7. Fix broken object properties with Chinese characters
  // 'name': 动量策略', -> 'name': '动量策略',
  content = content.replace(/'([a-zA-Z_]+)':\s*([^\s'"][^,}]*[^\s'",}])'/g, "'$1': '$2'");
  if (content !== originalContent) modified = true;

  // 8. Fix broken percentage values
  // +12.5%' -> '+12.5%'
  content = content.replace(/([+-]?\d+\.?\d*%)'/g, "'$1'");
  if (content !== originalContent) modified = true;

  // 9. Fix broken console.log statements
  // console.log('text': more) -> console.log('text', more)
  content = content.replace(/console\.log\('([^']+)'\s*:\s*([^)]+)\)/g, "console.log('$1', $2)");
  if (content !== originalContent) modified = true;

  // 10. Fix broken array.push statements
  // push('key': value) -> push('key', value)
  content = content.replace(/\.push\('([^']+)'\s*:\s*([^)]+)\)/g, ".push('$1', $2)");
  if (content !== originalContent) modified = true;

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  }
  return false;
}

function findTsxFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Find all TypeScript/TSX files in src directory
const srcDir = path.join(__dirname, 'src');
if (!fs.existsSync(srcDir)) {
  console.error('❌ src directory not found');
  process.exit(1);
}

const files = findTsxFiles(srcDir);
console.log(`📁 Found ${files.length} TypeScript/TSX files`);

let repairedCount = 0;
for (const file of files) {
  try {
    if (criticalRepair(file)) {
      console.log(`✅ Repaired: ${path.relative(__dirname, file)}`);
      repairedCount++;
    }
  } catch (error) {
    console.error(`❌ Error repairing ${file}:`, error.message);
  }
}

console.log(`\n🔧 Critical repair complete: ${repairedCount}/${files.length} files repaired`);
