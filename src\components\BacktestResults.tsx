/**
 * 回测结果展示组件
 * 专门用于展示回测分析结果和性能指标
 */

import { createSignal, createEffect, For, Show } from 'solid-js';
import { css } from '../../styled-system/css';
import FinancialChart from './FinancialChart';

export interface BacktestMetrics {
  totalReturn: number;
  annualizedReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
  profitFactor: number;
  totalTrades: number;
  avgTradeDuration: number;
  volatility: number;
  calmarRatio: number;
  sortinoRatio: number;
  beta: number;
  alpha: number;
  informationRatio: number;
}

export interface TradeRecord {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  timestamp: number;
  pnl: number;
  pnlPercent: number;
  duration: number;
  reason: string;
}

export interface BacktestResult {
  id: string;
  strategyName: string;
  startDate: string;
  endDate: string;
  initialCapital: number;
  finalCapital: number;
  metrics: BacktestMetrics;
  trades: TradeRecord[];
  equityCurve: Array<{ time: number; value: number }>;
  drawdownCurve: Array<{ time: number; value: number }>;
  monthlyReturns: Array<{ month: string; return: number }>;
  status: 'running' | 'completed' | 'failed';
  createdAt: number;
}

interface BacktestResultsProps {
  result: BacktestResult;
  theme? ' : 'light' | 'dark';
  onExport?: (format: 'pdf' | 'excel' | 'json') => void;
  onShare?: () => void;
  compact?: boolean;
}

export default function BacktestResults(props: BacktestResultsProps) {
  const [activeTab, setActiveTab] = createSignal<'overview' | 'trades' | 'charts' | 'analysis'>('overview');
  const [sortBy, setSortBy] = createSignal<'timestamp' | 'pnl' | 'duration'>('timestamp');
  const [sortOrder, setSortOrder] = createSignal<'asc' | 'desc'>('desc');

  // 格式化数字
  const formatNumber = (value: number, decimals = 2, suffix = '') => {
    if (isNaN(value)) return 'N/A';
    return value.toFixed(decimals) + suffix;
  };

  // 格式化百分比
  const formatPercent = (value: number, decimals = 2) => {
    if (isNaN(value)) return 'N/A';
    return (value * 100).toFixed(decimals) + '%';
  };

  // 格式化货币
  const formatCurrency = (value: number) => {
    if (isNaN(value)) return 'N/A';
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(value);
  };

  // 格式化持续时间
  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}分钟`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}小时`;
    return `${Math.floor(minutes / 1440)}天`;
  };

  // 获取指标颜色
  const getMetricColor = (value: number, isPositive = true) => {
    if (isNaN(value)) return props.theme === 'dark' ? '#6b7280 ' : '#9ca3af';
    const positive = isPositive ? value > 0 ' : value < 0;
    return positive ? '#10b981 ' : '#ef4444';
  };

  // 排序交易记录
  const sortedTrades = () => {
    const trades = [...props.result.trades];
    const order = sortOrder();
    const by = sortBy();

    return trades.sort((a, b) => {
      let aVal: number, bVal: number;
      
      switch (by) {
        case 'timestamp' :
          aVal = a.timestamp;
          bVal = b.timestamp;
          break;
        case 'pnl' :
          aVal = a.pnl;
          bVal = b.pnl;
          break;
        case 'duration :
          aVal = a.duration;
          bVal = b.duration;
          break;
        default:
          return 0;
      }

      return order === 'asc' ? aVal - bVal : bVal - aVal;
    });
  };

  // 计算胜率分布
  const winLossDistribution = () => {
    const wins = props.result.trades.filter(t => t.pnl > 0).length;
    const losses = props.result.trades.filter(t => t.pnl < 0).length;
    const total = props.result.trades.length;
    
    return {
      wins,
      losses,
      winRate: total > 0 ? wins / total : 0,
      avgWin: props.result.trades.filter(t => t.pnl > 0).reduce((sum, t) => sum + t.pnl, 0) / (wins || 1),
      avgLoss: props.result.trades.filter(t => t.pnl < 0).reduce((sum, t) => sum + t.pnl, 0) / (losses || 1)
    };
  };

  const containerClass = css({
    backgroundColor: props.theme === 'dark' ? '#111827' : '#ffffff',
    border: `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
    borderRadius: '12px',
    overflow: 'hidden'
  });

  const tabClass = (isActive: boolean) => css({
    padding: '12px 24px',
    cursor: 'pointer',
    borderBottom: isActive ? '2px solid #3b82f6 ' : '2px solid transparent',
    backgroundColor: isActive 
      ? (props.theme === 'dark' ? '#1f2937 ' : '#f8fafc')
      ' : 'transparent',
    color: isActive 
      ? (props.theme === 'dark' ? '#ffffff ' : '#1f2937')
      ' : (props.theme === 'dark' ? '#9ca3af ' : '#6b7280'),
    transition: 'all 0.2s ease',
    '&:hover: {
      backgroundColor: props.theme === 'dark' ? '#1f2937 ' : '#f8fafc'
    }
  });

  const metricCardClass = css({
    padding: '16px',
    backgroundColor: props.theme === 'dark' ? '#1f2937 ' : '#f8fafc',
    borderRadius: '8px',
    border: `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`
  });

  return (
    <div class={containerClass}>
      {/* 头部信息 */}
      <div style={{
        padding: '20px',
        borderBottom: `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          marginBottom: '12px'
        }}>
          <div>
            <h2 style={{
              fontSize: '20px',
              fontWeight: '600',
              color: props.theme === 'dark' ? '#ffffff ' : '#1f2937',
              margin: '0 0 4px 0'
            }}>
              {props.result.strategyName}
            </h2>
            <p style={{
              color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280',
              margin: 0,
              fontSize: '14px'
            }}>
              {props.result.startDate} - {props.result.endDate}
            </p>
          </div>
          
          <div style={{ display: 'flex', gap: '8px' }}>
            <Show when={props.onExport}>
              <button
                onClick={() => props.onExport?.('pdf')}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#3b82f6',
                  color: '#ffffff',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                导出报告
              </button>
            </Show>
            
            <Show when={props.onShare}>
              <button
                onClick={props.onShare}
                style={{
                  padding: '8px 16px',
                  backgroundColor: 'transparent',
                  color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280',
                  border: `1px solid ${props.theme === 'dark' ? '#374151' : '#d1d5db'}`,
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                分享
              </button>
            </Show>
          </div>
        </div>

        {/* 核心指标概览 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px'
        }}>
          <div class={metricCardClass}>
            <div style={{ fontSize: '12px', color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>
              总收益率
            </div>
            <div style={{
              fontSize: '24px',
              fontWeight: '600',
              color: getMetricColor(props.result.metrics.totalReturn)
            }}>
              {formatPercent(props.result.metrics.totalReturn)}
            </div>
          </div>

          <div class={metricCardClass}>
            <div style={{ fontSize: '12px', color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>
              夏普比率
            </div>
            <div style={{
              fontSize: '24px',
              fontWeight: '600',
              color: getMetricColor(props.result.metrics.sharpeRatio)
            }}>
              {formatNumber(props.result.metrics.sharpeRatio)}
            </div>
          </div>

          <div class={metricCardClass}>
            <div style={{ fontSize: '12px', color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>
              最大回撤
            </div>
            <div style={{
              fontSize: '24px',
              fontWeight: '600',
              color: getMetricColor(props.result.metrics.maxDrawdown, false)
            }}>
              {formatPercent(Math.abs(props.result.metrics.maxDrawdown))}
            </div>
          </div>

          <div class={metricCardClass}>
            <div style={{ fontSize: '12px', color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>
              胜率
            </div>
            <div style={{
              fontSize: '24px',
              fontWeight: '600',
              color: getMetricColor(props.result.metrics.winRate)
            }}>
              {formatPercent(props.result.metrics.winRate)}
            </div>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div style={{
        display: 'flex',
        borderBottom: `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`
      }}>
        <div
          class={tabClass(activeTab() === 'overview')}
          onClick={() => setActiveTab('overview')}
        >
          概览
        </div>
        <div
          class={tabClass(activeTab() === 'trades')}
          onClick={() => setActiveTab('trades')}
        >
          交易记录
        </div>
        <div
          class={tabClass(activeTab() === 'charts')}
          onClick={() => setActiveTab('charts')}
        >
          图表分析
        </div>
        <div
          class={tabClass(activeTab() === 'analysis')}
          onClick={() => setActiveTab('analysis')}
        >
          深度分析
        </div>
      </div>

      {/* 标签页内容 */}
      <div style={{ padding: '20px' }}>
        <Show when={activeTab() === 'overview'}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '20px'
          }}>
            {/* 详细指标 */}
            <div>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: props.theme === 'dark' ? '#ffffff ' : '#1f2937',
                marginBottom: '16px'
              }}>
                性能指标
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>年化收益率</span>
                  <span style={{ color: getMetricColor(props.result.metrics.annualizedReturn) }}>
                    {formatPercent(props.result.metrics.annualizedReturn)}
                  </span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>波动率</span>
                  <span>{formatPercent(props.result.metrics.volatility)}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>盈亏比</span>
                  <span style={{ color: getMetricColor(props.result.metrics.profitFactor) }}>
                    {formatNumber(props.result.metrics.profitFactor)}
                  </span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>总交易次数</span>
                  <span>{props.result.metrics.totalTrades}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>平均持仓时间</span>
                  <span>{formatDuration(props.result.metrics.avgTradeDuration)}</span>
                </div>
              </div>
            </div>

            {/* 资金曲线预览 */}
            <div>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: props.theme === 'dark' ? '#ffffff ' : '#1f2937',
                marginBottom: '16px'
              }}>
                资金曲线
              </h3>
              <div style={{ height: '200px' }}>
                <FinancialChart
                  data={props.result.equityCurve.map(point => ({
                    time: point.time,
                    open: point.value,
                    high: point.value,
                    low: point.value,
                    close: point.value
                  }))}
                  height={200}
                  theme={props.theme}
                  showVolume={false}
                />
              </div>
            </div>
          </div>
        </Show>

        <Show when={activeTab() === 'trades'}>
          <div>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '16px'
            }}>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: props.theme === 'dark' ? '#ffffff ' : '#1f2937',
                margin: 0
              }}>
                交易记录 ({props.result.trades.length})
              </h3>
              
              <div style={{ display: 'flex', gap: '8px' }}>
                <select
                  value={sortBy()}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  style={{
                    padding: '6px 12px',
                    borderRadius: '6px',
                    border: `1px solid ${props.theme === 'dark' ? '#374151' : '#d1d5db'}`,
                    backgroundColor: props.theme === 'dark' ? '#1f2937 ' : '#ffffff',
                    color: props.theme === 'dark' ? '#ffffff ' : '#1f2937'
                  }}
                >
                  <option value="timestamp">时间</option>
                  <option value="pnl">盈亏</option>
                  <option value="duration">持续时间</option>
                </select>
                
                <button
                  onClick={() => setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')}
                  style={{
                    padding: '6px 12px',
                    borderRadius: '6px',
                    border: `1px solid ${props.theme === 'dark' ? '#374151' : '#d1d5db'}`,
                    backgroundColor: props.theme === 'dark' ? '#1f2937 ' : '#ffffff',
                    color: props.theme === 'dark' ? '#ffffff ' : '#1f2937',
                    cursor: 'pointer'
                  }}
                >
                  {sortOrder() === 'asc' ? '↑' : '↓'}
                </button>
              </div>
            </div>

            <div style={{
              maxHeight: '400px',
              overflowY: 'auto',
              border: `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`,
              borderRadius: '8px'
            }}>
              <table style={{
                width: '100%',
                'border-collapse' : 'collapse'
              }}>
                <thead style={{
                  backgroundColor: props.theme === 'dark' ? '#1f2937 ' : '#f8fafc',
                  position: 'sticky',
                  top: 0
                }}>
                  <tr>
                    <th style={{ padding: '12px', textAlign: 'left', fontSize: '12px' }}>时间</th>
                    <th style={{ padding: '12px', textAlign: 'left', fontSize: '12px' }}>标的</th>
                    <th style={{ padding: '12px', textAlign: 'left', fontSize: '12px' }}>方向</th>
                    <th style={{ padding: '12px', textAlign: 'right', fontSize: '12px' }}>数量</th>
                    <th style={{ padding: '12px', textAlign: 'right', fontSize: '12px' }}>价格</th>
                    <th style={{ padding: '12px', textAlign: 'right', fontSize: '12px' }}>盈亏</th>
                    <th style={{ padding: '12px', textAlign: 'right', fontSize: '12px' }}>持续时间</th>
                  </tr>
                </thead>
                <tbody>
                  <For each={sortedTrades()}>
                    {(trade) => (
                      <tr style={{
                        borderBottom: `1px solid ${props.theme === 'dark' ? '#374151' : '#e5e7eb'}`
                      }}>
                        <td style={{ padding: '12px', fontSize: '14px' }}>
                          {new Date(trade.timestamp).toLocaleString()}
                        </td>
                        <td style={{ padding: '12px', fontSize: '14px' }}>{trade.symbol}</td>
                        <td style={{ padding: '12px', fontSize: '14px' }}>
                          <span style={{
                            padding: '2px 8px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            backgroundColor: trade.side === 'buy' ? '#dcfce7 ' : '#fee2e2',
                            color: trade.side === 'buy' ? '#166534' : '#991b1b'
                          }}>
                            {trade.side === 'buy' ? '买入' : '卖出'}
                          </span>
                        </td>
                        <td style={{ padding: '12px', textAlign: 'right', fontSize: '14px' }}>
                          {trade.quantity}
                        </td>
                        <td style={{ padding: '12px', textAlign: 'right', fontSize: '14px' }}>
                          {formatCurrency(trade.price)}
                        </td>
                        <td style={{
                          padding: '12px',
                          textAlign: 'right',
                          fontSize: '14px',
                          color: getMetricColor(trade.pnl)
                        }}>
                          {formatCurrency(trade.pnl)}
                        </td>
                        <td style={{ padding: '12px', textAlign: 'right', fontSize: '14px' }}>
                          {formatDuration(trade.duration)}
                        </td>
                      </tr>
                    )}
                  </For>
                </tbody>
              </table>
            </div>
          </div>
        </Show>

        <Show when={activeTab() === 'charts'}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr',
            gap: '20px'
          }}>
            <div>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: props.theme === 'dark' ? '#ffffff ' : '#1f2937',
                marginBottom: '16px'
              }}>
                资金曲线
              </h3>
              <FinancialChart
                data={props.result.equityCurve.map(point => ({
                  time: point.time,
                  open: point.value,
                  high: point.value,
                  low: point.value,
                  close: point.value
                }))}
                height={300}
                theme={props.theme}
                showVolume={false}
              />
            </div>

            <div>
              <h3 style={{
                fontSize: '16px',
                fontWeight: '600',
                color: props.theme === 'dark' ? '#ffffff ' : '#1f2937',
                marginBottom: '16px'
              }}>
                回撤曲线
              </h3>
              <FinancialChart
                data={props.result.drawdownCurve.map(point => ({
                  time: point.time,
                  open: point.value,
                  high: point.value,
                  low: point.value,
                  close: point.value
                }))}
                height={300}
                theme={props.theme}
                showVolume={false}
              />
            </div>
          </div>
        </Show>

        <Show when={activeTab() === 'analysis'}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '20px'
          }}>
            {/* 胜负分析 */}
            <div class={metricCardClass}>
              <h4 style={{
                fontSize: '14px',
                fontWeight: '600',
                color: props.theme === 'dark' ? '#ffffff ' : '#1f2937',
                marginBottom: '12px'
              }}>
                胜负分析
              </h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>盈利交易</span>
                  <span style={{ color: '#10b981' }}>{winLossDistribution().wins}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>亏损交易</span>
                  <span style={{ color: '#ef4444' }}>{winLossDistribution().losses}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>平均盈利</span>
                  <span style={{ color: '#10b981' }}>
                    {formatCurrency(winLossDistribution().avgWin)}
                  </span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>平均亏损</span>
                  <span style={{ color: '#ef4444' }}>
                    {formatCurrency(winLossDistribution().avgLoss)}
                  </span>
                </div>
              </div>
            </div>

            {/* 风险指标 */}
            <div class={metricCardClass}>
              <h4 style={{
                fontSize: '14px',
                fontWeight: '600',
                color: props.theme === 'dark' ? '#ffffff ' : '#1f2937',
                marginBottom: '12px'
              }}>
                风险指标
              </h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>卡尔玛比率</span>
                  <span>{formatNumber(props.result.metrics.calmarRatio)}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>索提诺比率</span>
                  <span>{formatNumber(props.result.metrics.sortinoRatio)}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>Beta系数</span>
                  <span>{formatNumber(props.result.metrics.beta)}</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span style={{ color: props.theme === 'dark' ? '#9ca3af ' : '#6b7280' }}>Alpha系数</span>
                  <span style={{ color: getMetricColor(props.result.metrics.alpha) }}>
                    {formatNumber(props.result.metrics.alpha)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </Show>
      </div>
    </div>
  );
}
