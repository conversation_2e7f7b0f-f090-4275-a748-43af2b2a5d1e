{"name": "@pandacss/core", "version": "0.39.2", "description": "core functions for extract-it", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "exports": {".": {"source": "./src/index.ts", "types": "./dist/index.d.ts", "require": "./dist/index.js", "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "./package.json": "./package.json"}, "files": ["dist"], "sideEffects": false, "homepage": "https://panda-css.com", "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/panda.git", "directory": "packages/core"}, "publishConfig": {"access": "public"}, "dependencies": {"@csstools/postcss-cascade-layers": "4.0.6", "browserslist": "4.23.0", "hookable": "5.5.3", "lightningcss": "1.23.0", "lodash.merge": "4.6.2", "outdent": "0.8.0", "postcss": "8.4.38", "postcss-discard-duplicates": "7.0.0", "postcss-discard-empty": "7.0.0", "postcss-merge-rules": "7.0.0", "postcss-minify-selectors": "7.0.0", "postcss-nested": "6.0.1", "postcss-normalize-whitespace": "7.0.0", "postcss-selector-parser": "6.0.16", "ts-pattern": "5.0.8", "@pandacss/is-valid-prop": "^0.39.2", "@pandacss/logger": "0.39.2", "@pandacss/shared": "0.39.2", "@pandacss/token-dictionary": "0.39.2", "@pandacss/types": "0.39.2"}, "devDependencies": {"@types/lodash.merge": "4.6.9"}, "scripts": {"build": "tsup --dts", "build-fast": "tsup --no-dts", "dev": "pnpm build-fast --watch"}}