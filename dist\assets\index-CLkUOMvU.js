const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Dashboard-C8VfdgjS.js","assets/vendor-solid-gbiNAvvL.js","assets/vendor-editor-BmK5o4Wa.js","assets/vendor-editor-Bwtp0z3V.css","assets/StrategyEditor-DuVUYDrp.js","assets/BacktestAnalysis-ClGSilDf.js","assets/vendor-charts-DZALwqbE.js","assets/ParameterOptimization-DGSgdRlq.js","assets/MarketData-uRqh6fo4.js"])))=>i.map(i=>d[i]);
import{d as Be,c as ce,u as Ee,t as C,i as y,a as b,S as B,b as k,e as d,F as X,A as pe,m as Ie,R as ze,f as E,l as O,s as c,r as Te}from"./vendor-solid-gbiNAvvL.js";import{_ as $}from"./vendor-editor-BmK5o4Wa.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))a(o);new MutationObserver(o=>{for(const n of o)if(n.type==="childList")for(const s of n.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&a(s)}).observe(document,{childList:!0,subtree:!0});function i(o){const n={};return o.integrity&&(n.integrity=o.integrity),o.referrerPolicy&&(n.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?n.credentials="include":o.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function a(o){if(o.ep)return;o.ep=!0;const n=i(o);fetch(o.href,n)}})();function j(e){return typeof e=="object"&&e!=null&&!Array.isArray(e)}function Pe(e){return Object.fromEntries(Object.entries(e??{}).filter(([r,i])=>i!==void 0))}var Oe=e=>e==="base";function $e(e){return e.slice().filter(r=>!Oe(r))}function ge(e){return String.fromCharCode(e+(e>25?39:97))}function Ae(e){let r="",i;for(i=Math.abs(e);i>52;i=i/52|0)r=ge(i%52)+r;return ge(i%52)+r}function Le(e,r){let i=r.length;for(;i;)e=e*33^r.charCodeAt(--i);return e}function We(e){return Ae(Le(5381,e)>>>0)}var ue=/\s*!(important)?/i;function je(e){return typeof e=="string"?ue.test(e):!1}function Me(e){return typeof e=="string"?e.replace(ue,"").trim():e}function me(e){return typeof e=="string"?e.replaceAll(" ","_"):e}var Y=e=>{const r=new Map;return(...a)=>{const o=JSON.stringify(a);if(r.has(o))return r.get(o);const n=e(...a);return r.set(o,n),n}};function he(...e){return e.filter(Boolean).reduce((i,a)=>(Object.keys(a).forEach(o=>{const n=i[o],s=a[o];j(n)&&j(s)?i[o]=he(n,s):i[o]=s}),i),{})}var De=e=>e!=null;function xe(e,r,i={}){const{stop:a,getKey:o}=i;function n(s,p=[]){if(j(s)||Array.isArray(s)){const m={};for(const[v,g]of Object.entries(s)){const h=o?.(v,g)??v,_=[...p,h];if(a?.(s,_))return r(s,p);const w=n(g,_);De(w)&&(m[h]=w)}return m}return r(s,p)}return n(e)}function Fe(e,r){return e.reduce((i,a,o)=>{const n=r[o];return a!=null&&(i[n]=a),i},{})}function ye(e,r,i=!0){const{utility:a,conditions:o}=r,{hasShorthand:n,resolveShorthand:s}=a;return xe(e,p=>Array.isArray(p)?Fe(p,o.breakpoints.keys):p,{stop:p=>Array.isArray(p),getKey:i?p=>n?s(p):p:void 0})}var Xe={shift:e=>e,finalize:e=>e,breakpoints:{keys:[]}},Ye=e=>typeof e=="string"?e.replaceAll(/[\n\s]+/g," "):e;function Ne(e){const{utility:r,hash:i,conditions:a=Xe}=e,o=s=>[r.prefix,s].filter(Boolean).join("-"),n=(s,p)=>{let m;if(i){const v=[...a.finalize(s),p];m=o(r.toHash(v,We))}else m=[...a.finalize(s),o(p)].join(":");return m};return Y(({base:s,...p}={})=>{const m=Object.assign(p,s),v=ye(m,e),g=new Set;return xe(v,(h,_)=>{const w=je(h);if(h==null)return;const[M,...I]=a.shift(_),A=$e(I),L=r.transform(M,Me(Ye(h)));let R=n(A,L.className);w&&(R=`${R}!`),g.add(R)}),Array.from(g).join(" ")})}function Ve(...e){return e.flat().filter(r=>j(r)&&Object.keys(Pe(r)).length>0)}function Ge(e){function r(o){const n=Ve(...o);return n.length===1?n:n.map(s=>ye(s,e))}function i(...o){return he(...r(o))}function a(...o){return Object.assign({},...r(o))}return{mergeCss:Y(i),assignCss:a}}var qe=/([A-Z])/g,He=/^ms-/,Ke=Y(e=>e.startsWith("--")?e:e.replace(qe,"-$1").replace(He,"-ms-").toLowerCase()),Ue="cm,mm,Q,in,pc,pt,px,em,ex,ch,rem,lh,rlh,vw,vh,vmin,vmax,vb,vi,svw,svh,lvw,lvh,dvw,dvh,cqw,cqh,cqi,cqb,cqmin,cqmax,%";`${Ue.split(",").join("|")}`;const Ze="_dark,_light,_hover,_focus,_focusWithin,_focusVisible,_disabled,_active,_visited,_target,_readOnly,_readWrite,_empty,_checked,_enabled,_expanded,_highlighted,_before,_after,_firstLetter,_firstLine,_marker,_selection,_file,_backdrop,_first,_last,_only,_even,_odd,_firstOfType,_lastOfType,_onlyOfType,_peerFocus,_peerHover,_peerActive,_peerFocusWithin,_peerFocusVisible,_peerDisabled,_peerChecked,_peerInvalid,_peerExpanded,_peerPlaceholderShown,_groupFocus,_groupHover,_groupActive,_groupFocusWithin,_groupFocusVisible,_groupDisabled,_groupChecked,_groupExpanded,_groupInvalid,_indeterminate,_required,_valid,_invalid,_autofill,_inRange,_outOfRange,_placeholder,_placeholderShown,_pressed,_selected,_default,_optional,_open,_closed,_fullscreen,_loading,_currentPage,_currentStep,_motionReduce,_motionSafe,_print,_landscape,_portrait,_osDark,_osLight,_highContrast,_lessContrast,_moreContrast,_ltr,_rtl,_scrollbar,_scrollbarThumb,_scrollbarTrack,_horizontal,_vertical,_starting,sm,smOnly,smDown,md,mdOnly,mdDown,lg,lgOnly,lgDown,xl,xlOnly,xlDown,2xl,2xlOnly,2xlDown,smToMd,smToLg,smToXl,smTo2xl,mdToLg,mdToXl,mdTo2xl,lgToXl,lgTo2xl,xlTo2xl,@/xs,@/sm,@/md,@/lg,@/xl,@/2xl,@/3xl,@/4xl,@/5xl,@/6xl,@/7xl,@/8xl,base",ve=new Set(Ze.split(","));function fe(e){return ve.has(e)||/^@|&|&$/.test(e)}const Je=/^_/,Qe=/&|@/;function er(e){return e.map(r=>ve.has(r)?r.replace(Je,""):Qe.test(r)?`[${me(r.trim())}]`:r)}function rr(e){return e.sort((r,i)=>{const a=fe(r),o=fe(i);return a&&!o?1:!a&&o?-1:0})}const tr="aspectRatio:aspect,boxDecorationBreak:decoration,zIndex:z,boxSizing:box,objectPosition:obj-pos,objectFit:obj-fit,overscrollBehavior:overscroll,overscrollBehaviorX:overscroll-x,overscrollBehaviorY:overscroll-y,position:pos/1,top:top,left:left,insetInline:inset-x/insetX,insetBlock:inset-y/insetY,inset:inset,insetBlockEnd:inset-b,insetBlockStart:inset-t,insetInlineEnd:end/insetEnd/1,insetInlineStart:start/insetStart/1,right:right,bottom:bottom,float:float,visibility:vis,display:d,hideFrom:hide,hideBelow:show,flexBasis:basis,flex:flex,flexDirection:flex/flexDir,flexGrow:grow,flexShrink:shrink,gridTemplateColumns:grid-cols,gridTemplateRows:grid-rows,gridColumn:col-span,gridRow:row-span,gridColumnStart:col-start,gridColumnEnd:col-end,gridAutoFlow:grid-flow,gridAutoColumns:auto-cols,gridAutoRows:auto-rows,gap:gap,gridGap:gap,gridRowGap:gap-x,gridColumnGap:gap-y,rowGap:gap-x,columnGap:gap-y,justifyContent:justify,alignContent:content,alignItems:items,alignSelf:self,padding:p/1,paddingLeft:pl/1,paddingRight:pr/1,paddingTop:pt/1,paddingBottom:pb/1,paddingBlock:py/1/paddingY,paddingBlockEnd:pb,paddingBlockStart:pt,paddingInline:px/paddingX/1,paddingInlineEnd:pe/1/paddingEnd,paddingInlineStart:ps/1/paddingStart,marginLeft:ml/1,marginRight:mr/1,marginTop:mt/1,marginBottom:mb/1,margin:m/1,marginBlock:my/1/marginY,marginBlockEnd:mb,marginBlockStart:mt,marginInline:mx/1/marginX,marginInlineEnd:me/1/marginEnd,marginInlineStart:ms/1/marginStart,spaceX:space-x,spaceY:space-y,outlineWidth:ring-width/ringWidth,outlineColor:ring-color/ringColor,outline:ring/1,outlineOffset:ring-offset/ringOffset,divideX:divide-x,divideY:divide-y,divideColor:divide-color,divideStyle:divide-style,width:w/1,inlineSize:w,minWidth:min-w/minW,minInlineSize:min-w,maxWidth:max-w/maxW,maxInlineSize:max-w,height:h/1,blockSize:h,minHeight:min-h/minH,minBlockSize:min-h,maxHeight:max-h/maxH,maxBlockSize:max-b,color:text,fontFamily:font,fontSize:fs,fontWeight:fw,fontSmoothing:smoothing,fontVariantNumeric:numeric,letterSpacing:tracking,lineHeight:leading,textAlign:text-align,textDecoration:text-decor,textDecorationColor:text-decor-color,textEmphasisColor:text-emphasis-color,textDecorationStyle:decoration-style,textDecorationThickness:decoration-thickness,textUnderlineOffset:underline-offset,textTransform:text-transform,textIndent:indent,textShadow:text-shadow,textShadowColor:text-shadow/textShadowColor,textOverflow:text-overflow,verticalAlign:v-align,wordBreak:break,textWrap:text-wrap,truncate:truncate,lineClamp:clamp,listStyleType:list-type,listStylePosition:list-pos,listStyleImage:list-img,backgroundPosition:bg-pos/bgPosition,backgroundPositionX:bg-pos-x/bgPositionX,backgroundPositionY:bg-pos-y/bgPositionY,backgroundAttachment:bg-attach/bgAttachment,backgroundClip:bg-clip/bgClip,background:bg/1,backgroundColor:bg/bgColor,backgroundOrigin:bg-origin/bgOrigin,backgroundImage:bg-img/bgImage,backgroundRepeat:bg-repeat/bgRepeat,backgroundBlendMode:bg-blend/bgBlendMode,backgroundSize:bg-size/bgSize,backgroundGradient:bg-gradient/bgGradient,textGradient:text-gradient,gradientFromPosition:gradient-from-pos,gradientToPosition:gradient-to-pos,gradientFrom:gradient-from,gradientTo:gradient-to,gradientVia:gradient-via,gradientViaPosition:gradient-via-pos,borderRadius:rounded/1,borderTopLeftRadius:rounded-tl/roundedTopLeft,borderTopRightRadius:rounded-tr/roundedTopRight,borderBottomRightRadius:rounded-br/roundedBottomRight,borderBottomLeftRadius:rounded-bl/roundedBottomLeft,borderTopRadius:rounded-t/roundedTop,borderRightRadius:rounded-r/roundedRight,borderBottomRadius:rounded-b/roundedBottom,borderLeftRadius:rounded-l/roundedLeft,borderStartStartRadius:rounded-ss/roundedStartStart,borderStartEndRadius:rounded-se/roundedStartEnd,borderStartRadius:rounded-s/roundedStart,borderEndStartRadius:rounded-es/roundedEndStart,borderEndEndRadius:rounded-ee/roundedEndEnd,borderEndRadius:rounded-e/roundedEnd,border:border,borderWidth:border-w,borderTopWidth:border-tw,borderLeftWidth:border-lw,borderRightWidth:border-rw,borderBottomWidth:border-bw,borderColor:border,borderInline:border-x/borderX,borderInlineWidth:border-x/borderXWidth,borderInlineColor:border-x/borderXColor,borderBlock:border-y/borderY,borderBlockWidth:border-y/borderYWidth,borderBlockColor:border-y/borderYColor,borderLeft:border-l,borderLeftColor:border-l,borderInlineStart:border-s/borderStart,borderInlineStartWidth:border-s/borderStartWidth,borderInlineStartColor:border-s/borderStartColor,borderRight:border-r,borderRightColor:border-r,borderInlineEnd:border-e/borderEnd,borderInlineEndWidth:border-e/borderEndWidth,borderInlineEndColor:border-e/borderEndColor,borderTop:border-t,borderTopColor:border-t,borderBottom:border-b,borderBottomColor:border-b,borderBlockEnd:border-be,borderBlockEndColor:border-be,borderBlockStart:border-bs,borderBlockStartColor:border-bs,boxShadow:shadow/1,boxShadowColor:shadow-color/shadowColor,mixBlendMode:mix-blend,filter:filter,brightness:brightness,contrast:contrast,grayscale:grayscale,hueRotate:hue-rotate,invert:invert,saturate:saturate,sepia:sepia,dropShadow:drop-shadow,blur:blur,backdropFilter:backdrop,backdropBlur:backdrop-blur,backdropBrightness:backdrop-brightness,backdropContrast:backdrop-contrast,backdropGrayscale:backdrop-grayscale,backdropHueRotate:backdrop-hue-rotate,backdropInvert:backdrop-invert,backdropOpacity:backdrop-opacity,backdropSaturate:backdrop-saturate,backdropSepia:backdrop-sepia,borderCollapse:border,borderSpacing:border-spacing,borderSpacingX:border-spacing-x,borderSpacingY:border-spacing-y,tableLayout:table,transitionTimingFunction:ease,transitionDelay:delay,transitionDuration:duration,transitionProperty:transition-prop,transition:transition,animation:animation,animationName:animation-name,animationTimingFunction:animation-ease,animationDuration:animation-duration,animationDelay:animation-delay,transformOrigin:origin,rotate:rotate,rotateX:rotate-x,rotateY:rotate-y,rotateZ:rotate-z,scale:scale,scaleX:scale-x,scaleY:scale-y,translate:translate,translateX:translate-x/x,translateY:translate-y/y,translateZ:translate-z/z,accentColor:accent,caretColor:caret,scrollBehavior:scroll,scrollbar:scrollbar,scrollMargin:scroll-m,scrollMarginLeft:scroll-ml,scrollMarginRight:scroll-mr,scrollMarginTop:scroll-mt,scrollMarginBottom:scroll-mb,scrollMarginBlock:scroll-my/scrollMarginY,scrollMarginBlockEnd:scroll-mb,scrollMarginBlockStart:scroll-mt,scrollMarginInline:scroll-mx/scrollMarginX,scrollMarginInlineEnd:scroll-me,scrollMarginInlineStart:scroll-ms,scrollPadding:scroll-p,scrollPaddingBlock:scroll-pb/scrollPaddingY,scrollPaddingBlockStart:scroll-pt,scrollPaddingBlockEnd:scroll-pb,scrollPaddingInline:scroll-px/scrollPaddingX,scrollPaddingInlineEnd:scroll-pe,scrollPaddingInlineStart:scroll-ps,scrollPaddingLeft:scroll-pl,scrollPaddingRight:scroll-pr,scrollPaddingTop:scroll-pt,scrollPaddingBottom:scroll-pb,scrollSnapAlign:snap-align,scrollSnapStop:snap-stop,scrollSnapType:snap-type,scrollSnapStrictness:snap-strictness,scrollSnapMargin:snap-m,scrollSnapMarginTop:snap-mt,scrollSnapMarginBottom:snap-mb,scrollSnapMarginLeft:snap-ml,scrollSnapMarginRight:snap-mr,touchAction:touch,userSelect:select,fill:fill,stroke:stroke,strokeWidth:stroke-w,srOnly:sr,debug:debug,appearance:appearance,backfaceVisibility:backface,clipPath:clip-path,hyphens:hyphens,mask:mask,maskImage:mask-image,maskSize:mask-size,textSizeAdjust:text-adjust,container:cq,containerName:cq-name,containerType:cq-type,textStyle:textStyle",_e=new Map,Se=new Map;tr.split(",").forEach(e=>{const[r,i]=e.split(":"),[a,...o]=i.split("/");_e.set(r,a),o.length&&o.forEach(n=>{Se.set(n==="1"?a:n,r)})});const be=e=>Se.get(e)||e,ke={conditions:{shift:rr,finalize:er,breakpoints:{keys:["base","sm","md","lg","xl","2xl"]}},utility:{transform:(e,r)=>{const i=be(e);return{className:`${_e.get(i)||Ke(i)}_${me(r)}`}},hasShorthand:!0,toHash:(e,r)=>r(e.join(":")),resolveShorthand:be}},or=Ne(ke),l=(...e)=>or(we(...e));l.raw=(...e)=>we(...e);const{mergeCss:we}=Ge(ke);var nr=C("<span>量化平台"),ir=C("<div><aside><div><div><div>V</div></div></div><nav><div></div></nav><div><button type=button></button></div></aside><div><header><div><div><h1></h1><div></div></div><div><button type=button>🔔</button><button type=button>⚙️</button><div>U</div></div></div></header><main>"),W=C("<span>"),ar=C("<span>▼"),lr=C("<div>"),sr=C("<div><div><span>"),dr=C("<span>/");function cr(e){const[r,i]=ce(!1),[a,o]=ce({market:!0,trading:!1,strategy:!1}),n=Ee(),s=[{name:"仪表盘",href:"/",icon:"📊",type:"single",color:"#1890ff"},{name:"行情中心",href:"/market",icon:"📈",type:"single",color:"#52c41a"},{name:"交易中心",href:"/trading",icon:"💰",type:"single",color:"#fa8c16"},{name:"策略中心",href:"/strategy",icon:"🧠",type:"single",color:"#722ed1"},{name:"回测分析",href:"/backtest",icon:"🔄",type:"single",color:"#13c2c2"},{name:"参数优化",href:"/optimization",icon:"🔧",type:"single",color:"#fa541c"},{name:"投资组合",href:"/portfolio",icon:"📋",type:"single",color:"#eb2f96"},{name:"风险管理",href:"/risk",icon:"🛡️",type:"single",color:"#f5222d"},{name:"系统设置",href:"/settings",icon:"⚙️",type:"single",color:"#666"}],p=()=>{i(!r())},m=()=>({"/":"投资收益盘","/market":"行情中心","/trading":"交易中心","/strategy":"策略中心","/backtest":"回测分析","/optimization":"参数优化","/portfolio":"投资组合","/risk":"风险管理","/settings":"系统设置"})[n.pathname]||"量化平台",v=()=>{const g=n.pathname;return g==="/"?["首页","投资收益盘"]:g==="/market"?["首页","行情中心"]:g==="/trading"?["首页","交易中心"]:g==="/strategy"?["首页","策略中心"]:g==="/backtest"?["首页","回测分析"]:g==="/portfolio"?["首页","投资组合"]:g==="/risk"?["首页","风险管理"]:g==="/settings"?["首页","系统设置"]:["首页",m()]};return(()=>{var g=ir(),h=g.firstChild,_=h.firstChild,w=_.firstChild,M=w.firstChild,I=_.nextSibling,A=I.firstChild,L=I.nextSibling,R=L.firstChild,N=h.nextSibling,D=N.firstChild,V=D.firstChild,G=V.firstChild,F=G.firstChild,q=F.nextSibling,H=G.nextSibling,K=H.firstChild,U=K.nextSibling,Re=U.nextSibling,Z=D.nextSibling;return y(w,b(B,{get when(){return!r()},get children(){var t=nr();return k(()=>d(t,l({fontSize:"16px",fontWeight:"600",color:"#333"}))),t}}),null),y(A,b(X,{each:s,children:t=>b(B,{get when(){return t.type==="single"},get fallback(){return(()=>{var u=sr(),x=u.firstChild,z=x.firstChild;return x.$$click=()=>toggleGroup(t.key),y(z,()=>t.icon),y(x,b(B,{get when(){return!r()},get children(){return[(()=>{var f=W();return y(f,()=>t.name),k(()=>d(f,l({flex:1}))),f})(),(()=>{var f=ar();return k(()=>d(f,l({fontSize:"12px",transform:a()[t.key]?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.2s ease"}))),f})()]}}),null),y(u,b(B,{get when(){return Ie(()=>!r())()&&a()[t.key]},get children(){var f=lr();return y(f,b(X,{get each(){return t.children},children:S=>b(pe,{get href(){return S.href},get class(){return l({display:"block",px:"12px",py:"6px",mx:"4px",borderRadius:"4px",fontSize:"13px",color:n.pathname===S.href?"#1890ff":"#666",bg:n.pathname===S.href?"#e6f7ff":"transparent",textDecoration:"none",_hover:{bg:n.pathname===S.href?"#e6f7ff":"#f5f5f5"}})},get children(){return S.name}})})),k(()=>d(f,l({ml:"20px",mt:"4px"}))),f}}),null),k(f=>{var S=l({mb:"4px"}),T=l({display:"flex",alignItems:"center",px:"12px",py:"8px",mx:"4px",borderRadius:"6px",cursor:"pointer",fontSize:"14px",fontWeight:"500",color:"#666",_hover:{bg:"#f5f5f5"}}),P=l({mr:"8px",fontSize:"16px"});return S!==f.e&&d(u,f.e=S),T!==f.t&&d(x,f.t=T),P!==f.a&&d(z,f.a=P),f},{e:void 0,t:void 0,a:void 0}),u})()},get children(){return b(pe,{get href(){return t.href},get class(){return l({display:"flex",alignItems:"center",px:"12px",py:"10px",mx:"4px",mb:"4px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",color:n.pathname===t.href?"#1890ff":"#666",bg:n.pathname===t.href?"#e6f7ff":"transparent",textDecoration:"none",_hover:{bg:n.pathname===t.href?"#e6f7ff":"#f5f5f5"}})},get children(){return[(()=>{var u=W();return y(u,()=>t.icon),k(()=>d(u,l({mr:"8px",fontSize:"16px"}))),u})(),b(B,{get when(){return!r()},get children(){var u=W();return y(u,()=>t.name),u}})]}})}})})),R.$$click=p,y(R,()=>r()?"→":"←"),y(F,m),y(q,b(X,{get each(){return v()},children:(t,u)=>[b(B,{get when(){return u()>0},get children(){var x=dr();return k(()=>d(x,l({mx:"4px"}))),x}}),(()=>{var x=W();return y(x,t),k(()=>d(x,l({color:u()===v().length-1?"#1890ff":"#999"}))),x})()]})),y(Z,()=>e.children),k(t=>{var u=l({display:"flex",height:"100vh",bg:"#f5f7fa"}),x=l({width:r()?"64px":"240px",bg:"white",borderRight:"1px solid #e8e8e8",display:"flex",flexDirection:"column",transition:"width 0.3s ease",boxShadow:"2px 0 8px rgba(0, 0, 0, 0.1)",position:"relative",zIndex:10}),z=l({height:"64px",display:"flex",alignItems:"center",px:"16px",borderBottom:"1px solid #e8e8e8"}),f=l({display:"flex",alignItems:"center",gap:"12px"}),S=l({width:"32px",height:"32px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"18px"}),T=l({flex:1,py:"16px",overflowY:"auto"}),P=l({px:"8px"}),J=l({p:"16px",borderTop:"1px solid #e8e8e8"}),Q=l({width:"100%",p:"8px",border:"none",bg:"transparent",borderRadius:"6px",cursor:"pointer",fontSize:"16px",_hover:{bg:"#f5f5f5"}}),ee=l({flex:1,display:"flex",flexDirection:"column"}),re=l({bg:"white",borderBottom:"1px solid #e8e8e8",px:"24px",py:"16px"}),te=l({display:"flex",justifyContent:"space-between",alignItems:"center"}),oe=l({fontSize:"20px",fontWeight:"600",color:"#333",mb:"4px"}),ne=l({fontSize:"12px",color:"#999"}),ie=l({display:"flex",alignItems:"center",gap:"12px"}),ae=l({p:"8px",border:"none",bg:"transparent",borderRadius:"6px",cursor:"pointer",fontSize:"16px",_hover:{bg:"#f5f5f5"}}),le=l({p:"8px",border:"none",bg:"transparent",borderRadius:"6px",cursor:"pointer",fontSize:"16px",_hover:{bg:"#f5f5f5"}}),se=l({width:"32px",height:"32px",bg:"#1890ff",color:"white",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"14px",fontWeight:"bold"}),de=l({flex:1,p:"24px",overflowY:"auto"});return u!==t.e&&d(g,t.e=u),x!==t.t&&d(h,t.t=x),z!==t.a&&d(_,t.a=z),f!==t.o&&d(w,t.o=f),S!==t.i&&d(M,t.i=S),T!==t.n&&d(I,t.n=T),P!==t.s&&d(A,t.s=P),J!==t.h&&d(L,t.h=J),Q!==t.r&&d(R,t.r=Q),ee!==t.d&&d(N,t.d=ee),re!==t.l&&d(D,t.l=re),te!==t.u&&d(V,t.u=te),oe!==t.c&&d(F,t.c=oe),ne!==t.w&&d(q,t.w=ne),ie!==t.m&&d(H,t.m=ie),ae!==t.f&&d(K,t.f=ae),le!==t.y&&d(U,t.y=le),se!==t.g&&d(Re,t.g=se),de!==t.p&&d(Z,t.p=de),t},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0,n:void 0,s:void 0,h:void 0,r:void 0,d:void 0,l:void 0,u:void 0,c:void 0,w:void 0,m:void 0,f:void 0,y:void 0,g:void 0,p:void 0}),g})()}Be(["click"]);var pr=C('<div style="border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.1)"><h1 style=margin-bottom:16px>🚀 新界面测试成功！</h1><p style=font-size:16px>如果您看到这个消息，说明新的应用架构已经正常工作了！</p><div style="grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));margin-top:20px"><div style=border-radius:8px><h3>✅ SolidJS</h3><p style=font-size:14px>框架正常运行</p></div><div style=border-radius:8px><h3>✅ 路由</h3><p style=font-size:14px>路由系统正常</p></div><div style=border-radius:8px><h3>✅ 布局</h3><p style=font-size:14px>布局组件正常');const gr=O(()=>$(()=>import("./Dashboard-C8VfdgjS.js"),__vite__mapDeps([0,1,2,3]))),fr=O(()=>$(()=>import("./StrategyEditor-DuVUYDrp.js"),__vite__mapDeps([4,1,2,3]))),br=O(()=>$(()=>import("./BacktestAnalysis-ClGSilDf.js"),__vite__mapDeps([5,1,6,2,3]))),ur=O(()=>$(()=>import("./ParameterOptimization-DGSgdRlq.js"),__vite__mapDeps([7,1,2,3]))),mr=O(()=>$(()=>import("./MarketData-uRqh6fo4.js"),__vite__mapDeps([8,1,2,3])));function hr(){return(()=>{var e=pr(),r=e.firstChild,i=r.nextSibling,a=i.nextSibling,o=a.firstChild,n=o.firstChild,s=n.nextSibling,p=o.nextSibling,m=p.firstChild,v=m.nextSibling,g=p.nextSibling,h=g.firstChild,_=h.nextSibling;return c(e,"padding","20px"),c(e,"background","white"),c(e,"margin","20px"),c(r,"color","#1890ff"),c(i,"color","#666"),c(a,"display","grid"),c(a,"gap","16px"),c(o,"padding","16px"),c(o,"background","#f0f9ff"),c(o,"border","1px solid #0ea5e9"),c(n,"margin","0 0 8px 0"),c(n,"color","#0ea5e9"),c(s,"margin","0"),c(s,"color","#666"),c(p,"padding","16px"),c(p,"background","#f0fdf4"),c(p,"border","1px solid #22c55e"),c(m,"margin","0 0 8px 0"),c(m,"color","#22c55e"),c(v,"margin","0"),c(v,"color","#666"),c(g,"padding","16px"),c(g,"background","#fefce8"),c(g,"border","1px solid #eab308"),c(h,"margin","0 0 8px 0"),c(h,"color","#eab308"),c(_,"margin","0"),c(_,"color","#666"),e})()}function xr(){return b(ze,{root:cr,get children(){return[b(E,{path:"/",component:gr}),b(E,{path:"/test",component:hr}),b(E,{path:"/strategy",component:fr}),b(E,{path:"/backtest",component:br}),b(E,{path:"/optimization",component:ur}),b(E,{path:"/market",component:mr})]}})}const Ce=document.getElementById("root");if(!Ce)throw new Error("Root element not found");Te(()=>b(xr,{}),Ce);export{l as c};
