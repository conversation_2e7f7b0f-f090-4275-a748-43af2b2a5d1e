const fs = require('fs');
const path = require('path');

// 更完整的CSS属性映射表
const cssPropertyMap = {
  // 布局相关
  'flex-direction': 'flexDirection',
  'flex-wrap': 'flexWrap',
  'flex-grow': 'flexGrow',
  'flex-shrink': 'flexShrink',
  'flex-basis': 'flexBasis',
  'justify-content': 'justifyContent',
  'align-items': 'alignItems',
  'align-content': 'alignContent',
  'align-self': 'alignSelf',
  
  // 边框相关
  'border-radius': 'borderRadius',
  'border-width': 'borderWidth',
  'border-style': 'borderStyle',
  'border-color': 'borderColor',
  'border-top': 'borderTop',
  'border-right': 'borderRight',
  'border-bottom': 'borderBottom',
  'border-left': 'borderLeft',
  
  // 间距相关
  'margin-top': 'marginTop',
  'margin-right': 'marginRight',
  'margin-bottom': 'marginBottom',
  'margin-left': 'marginLeft',
  'padding-top': 'paddingTop',
  'padding-right': 'paddingRight',
  'padding-bottom': 'paddingBottom',
  'padding-left': 'paddingLeft',
  
  // 文本相关
  'font-size': 'fontSize',
  'font-weight': 'fontWeight',
  'font-family': 'fontFamily',
  'font-style': 'fontStyle',
  'line-height': 'lineHeight',
  'text-align': 'textAlign',
  'text-decoration': 'textDecoration',
  'text-transform': 'textTransform',
  'text-overflow': 'textOverflow',
  'white-space': 'whiteSpace',
  'word-wrap': 'wordWrap',
  'word-break': 'wordBreak',
  'letter-spacing': 'letterSpacing',
  
  // 背景相关
  'background-color': 'backgroundColor',
  'background-image': 'backgroundImage',
  'background-position': 'backgroundPosition',
  'background-size': 'backgroundSize',
  'background-repeat': 'backgroundRepeat',
  
  // 位置相关
  'z-index': 'zIndex',
  'min-width': 'minWidth',
  'max-width': 'maxWidth',
  'min-height': 'minHeight',
  'max-height': 'maxHeight',
  
  // Grid相关
  'grid-template-columns': 'gridTemplateColumns',
  'grid-template-rows': 'gridTemplateRows',
  'grid-gap': 'gridGap',
  
  // 其他
  'box-shadow': 'boxShadow',
  'box-sizing': 'boxSizing',
  'overflow-x': 'overflowX',
  'overflow-y': 'overflowY',
  'user-select': 'userSelect',
  'pointer-events': 'pointerEvents',
  'list-style': 'listStyle',
  'list-style-type': 'listStyleType',
  'object-fit': 'objectFit'
};

// 特殊处理的属性（仅去掉引号但保留连字符）
const specialProperties = [
  'flexDirection',
  'alignItems',
  'justifyContent',
  'borderRadius',
  'fontSize',
  'fontWeight',
  'lineHeight',
  'boxShadow',
  'textDecoration',
  'zIndex',
  'gridTemplateColumns'
];

function fixCssProperties(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    let originalContent = content;
    
    // 修复已经驼峰化但有引号和冒号的属性
    specialProperties.forEach(prop => {
      // 匹配 'propertyName': 和 "propertyName": 的模式
      const regex1 = new RegExp(`'${prop}'\\s*:\\s*'`, 'g');
      const regex2 = new RegExp(`"${prop}"\\s*:\\s*'`, 'g');
      const regex3 = new RegExp(`'${prop}'\\s*:\\s*"`, 'g');
      const regex4 = new RegExp(`"${prop}"\\s*:\\s*"`, 'g');
      
      content = content.replace(regex1, `${prop}: '`);
      content = content.replace(regex2, `${prop}: '`);
      content = content.replace(regex3, `${prop}: "`);
      content = content.replace(regex4, `${prop}: "`);
    });
    
    // 处理连字符属性
    for (const [kebabCase, camelCase] of Object.entries(cssPropertyMap)) {
      // 匹配各种引号组合
      const patterns = [
        new RegExp(`'${kebabCase}'\\s*:\\s*'`, 'g'),
        new RegExp(`"${kebabCase}"\\s*:\\s*'`, 'g'),
        new RegExp(`'${kebabCase}'\\s*:\\s*"`, 'g'),
        new RegExp(`"${kebabCase}"\\s*:\\s*"`, 'g'),
        new RegExp(`'${kebabCase}'\\s*:`, 'g'),
        new RegExp(`"${kebabCase}"\\s*:`, 'g')
      ];
      
      patterns.forEach((regex, index) => {
        if (index < 4) {
          // 保留值的引号
          const quote = index % 2 === 0 ? "'" : '"';
          content = content.replace(regex, `${camelCase}: ${quote}`);
        } else {
          // 只去掉属性名的引号
          content = content.replace(regex, `${camelCase}:`);
        }
      });
    }
    
    hasChanges = content !== originalContent;
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${path.basename(filePath)}`);
      return true;
    } else {
      console.log(`⏭️  Skipped: ${path.basename(filePath)} (no changes needed)`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dirPath) {
  const files = fs.readdirSync(dirPath);
  let totalFixed = 0;
  
  for (const file of files) {
    const fullPath = path.join(dirPath, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 递归处理子目录
      if (!file.startsWith('.') && file !== 'node_modules' && file !== 'dist') {
        totalFixed += processDirectory(fullPath);
      }
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      // 处理 TypeScript/TSX 文件
      if (fixCssProperties(fullPath)) {
        totalFixed++;
      }
    }
  }
  
  return totalFixed;
}

// 主函数
function main() {
  console.log('🔧 Starting comprehensive CSS property fix...\n');
  
  const srcPath = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcPath)) {
    console.error('❌ src directory not found!');
    process.exit(1);
  }
  
  const totalFixed = processDirectory(srcPath);
  
  console.log('\n' + '='.repeat(50));
  console.log(`✨ Complete! Fixed ${totalFixed} files.`);
}

main();