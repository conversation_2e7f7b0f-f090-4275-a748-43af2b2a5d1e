const fs = require('fs');
const path = require('path');

// 修复对象属性名语法错误的模式
function fixObjectProperties(content) {
  // 修复对象属性名后面多余的单引号
  // 匹配模式: propertyName' : value
  const propertyPattern = /(\s+)([a-zA-Z][a-zA-Z0-9]*)'(\s*:\s*)/g;
  
  return content.replace(propertyPattern, '$1$2$3');
}

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    
    // 应用修复
    content = fixObjectProperties(content);
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${path.basename(filePath)}: object property syntax errors`);
      return true;
    } else {
      console.log(`✓  ${path.basename(filePath)}: no object property errors found`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔧 Fixing object property syntax errors...\n');
  
  // 需要修复的文件
  const filesToFix = [
    'src/pages/Dashboard.tsx',
    'src/pages/StrategyEditor.tsx',
    'src/pages/BacktestAnalysis.tsx',
    'src/pages/MarketData.tsx',
    'src/pages/ParameterOptimization.tsx',
    'src/components/AdvancedLayout.tsx',
    'src/components/BacktestCharts.tsx',
    'src/components/ParameterOptimizer.tsx',
    'src/components/MarketNews.tsx',
    'src/components/PortfolioOverview.tsx'
  ];
  
  let totalFixed = 0;
  
  for (const filePath of filesToFix) {
    if (fs.existsSync(filePath)) {
      if (fixFile(filePath)) {
        totalFixed++;
      }
    } else {
      console.warn(`⚠️  File not found: ${filePath}`);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`✨ Complete! Fixed ${totalFixed} files with object property errors.`);
  
  if (totalFixed > 0) {
    console.log('\n📋 Next steps:');
    console.log('1. Run "npm run build" to test production build');
    console.log('2. Run "npm run dev" to test development server');
  }
}

main();
