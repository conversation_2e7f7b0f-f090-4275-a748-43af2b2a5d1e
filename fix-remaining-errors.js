import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

// 修复剩余语法错误的函数
function fixRemaining(content) {
  let fixed = content;
  
  // 1. 修复对象属性中的引号错误 (name: 'value', => name: 'value',)
  fixed = fixed.replace(/(\w+): '([^']+)','(\w+): '([^']+)'/g, "$1: '$2', $3: '$4'");
  
  // 2. 修复 {'name: 'value' 这种错误格式
  fixed = fixed.replace(/\{'(\w+): '([^']+)'/g, "{ $1: '$2'");
  
  // 3. 修复 z-index 错误 ('z-index: 1, => 'z-index': 1,)
  fixed = fixed.replace(/'z-index: (\d+),/g, "'z-index': $1,");
  
  // 4. 修复 Monaco 编辑器的 colors 配置
  fixed = fixed.replace(/'(editor\.[^']+): ([^,]+),/g, "'$1': $2,");
  
  // 5. 修复遗漏的属性引号 
  fixed = fixed.replace(/(\w+): '([^']+): '([^']+)',/g, "$1: '$2', $3: '$4',");
  
  // 6. 修复行内的未完成三元运算符
  fixed = fixed.replace(/': 'transparent',/g, ": 'transparent',");
  
  // 7. 修复 interface 声明中的错误
  fixed = fixed.replace(/(\w+)\?' :/g, "$1?:");
  
  return fixed;
}

// 特殊修复某些特定错误
function fixSpecialCases(content, filePath) {
  let fixed = content;
  
  // 修复 AdvancedStrategyEditor 中剩余的 Monaco 配置错误
  if (filePath.includes('AdvancedStrategyEditor.tsx')) {
    fixed = fixed.replace(
      "'editor.background': props.theme === 'dark' ?'#1e1e1e' :'#ffffff',",
      "'editor.background': props.theme === 'dark' ? '#1e1e1e' : '#ffffff',"
    );
    fixed = fixed.replace(
      "'editor.foreground': props.theme === 'dark' ?'#d4d4d4' :'#000000',", 
      "'editor.foreground': props.theme === 'dark' ? '#d4d4d4' : '#000000',"
    );
    fixed = fixed.replace(
      "'editorLineNumber.foreground': props.theme === 'dark' ?'#858585' :'#237893',",
      "'editorLineNumber.foreground': props.theme === 'dark' ? '#858585' : '#237893',"
    );
    
    // 修复三元运算符中的引号问题
    fixed = fixed.replace(/\?'([^']+)' :'([^']+)'/g, "? '$1' : '$2'");
    fixed = fixed.replace(/==='([^']+)' \?'([^']+)' :'([^']+)'/g, "=== '$1' ? '$2' : '$3'");
    
    // 修复其他剩余的类似问题
    fixed = fixed.replace(/': 'transparent',/g, ": 'transparent',");
  }
  
  return fixed;
}

// 获取所有需要修复的文件
const files = glob.sync('src/**/*.tsx', { cwd: process.cwd() });

console.log('开始修复剩余语法错误...\n');

let fixedCount = 0;

files.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  const originalContent = fs.readFileSync(filePath, 'utf8');
  let fixedContent = fixRemaining(originalContent);
  fixedContent = fixSpecialCases(fixedContent, filePath);
  
  if (originalContent !== fixedContent) {
    fs.writeFileSync(filePath, fixedContent);
    console.log(`✅ 修复: ${file}`);
    fixedCount++;
  }
});

console.log(`\n🎉 修复了 ${fixedCount} 个文件的剩余语法错误！`);